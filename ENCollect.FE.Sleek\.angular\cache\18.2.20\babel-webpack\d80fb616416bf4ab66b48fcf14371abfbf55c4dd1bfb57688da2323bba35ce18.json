{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { StickyHeaderComponent } from './sticky-header.component';\ndescribe('StickyHeaderComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [StickyHeaderComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(StickyHeaderComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "StickyHeaderComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\sticky-header\\sticky-header.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { StickyHeaderComponent } from './sticky-header.component';\r\n\r\ndescribe('StickyHeaderComponent', () => {\r\n  let component: StickyHeaderComponent;\r\n  let fixture: ComponentFixture<StickyHeaderComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [StickyHeaderComponent]\r\n    })\r\n    .compileComponents();\r\n    \r\n    fixture = TestBed.createComponent(StickyHeaderComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,qBAAqB,QAAQ,2BAA2B;AAEjEC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACrC,IAAIC,SAAgC;EACpC,IAAIC,OAAgD;EAEpDC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,qBAAqB;KAChC,CAAC,CACDQ,iBAAiB,EAAE;IAEpBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,qBAAqB,CAAC;IACxDE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}