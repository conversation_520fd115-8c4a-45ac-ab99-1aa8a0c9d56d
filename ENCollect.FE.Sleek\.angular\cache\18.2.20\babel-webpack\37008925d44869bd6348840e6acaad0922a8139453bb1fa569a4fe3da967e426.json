{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./mode-of-payments.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./mode-of-payments.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { SettingsService } from '../../settings.service';\nimport { SettingsConfigService } from '../../settingsconfig.service';\nlet ModeOfPaymentsComponent = class ModeOfPaymentsComponent {\n  constructor(toastr, settingService, settingConfigService) {\n    this.toastr = toastr;\n    this.settingService = settingService;\n    this.settingConfigService = settingConfigService;\n    this.breadcrumbData = [{\n      label: \"Settings Payments\",\n      path: \"/settings/payments/mode-of-payments\"\n    }, {\n      label: \"Mode of Payments\",\n      path: \"/settings/payments/mode-of-payments\"\n    }];\n    this.addMode = false;\n    this.deletedData = [];\n    this.modeOfPayments = [];\n    this.loader = {\n      isSearching: false,\n      isSubmit: false\n    };\n    this.addNewModeOfPayment();\n  }\n  ngOnInit() {\n    this.settingService.getCategory(\"ModeOfPayment\").subscribe(response => {\n      if (response.length == 0) {\n        this.addMode = false;\n      }\n      this.modeOfPayments = response;\n      this.loader.isSearching = false;\n    }, err => {\n      this.toastr.error(err);\n      this.loader.isSearching = false;\n    });\n  }\n  addNewModeOfPayment() {\n    this.modeOfPayments.push({\n      id: \"\",\n      name: \"\",\n      code: name,\n      categoryMasterId: \"ModeOfPayment\"\n    });\n  }\n  removeModeOfPayment(obj, i) {\n    if (this.modeOfPayments.length > 1) {\n      if (obj.id != \"\") {\n        obj[\"isDelete\"] = true;\n        this.deletedData.push(obj);\n      }\n      this.modeOfPayments.splice(i, 1);\n    } else {\n      this.toastr.warning(\"Atleast one record is required\");\n    }\n  }\n  submit() {\n    this.loader.isSubmit = true;\n    if (this.addMode) {\n      this.settingService.addCategory(this.modeOfPayments).subscribe(response => {\n        this.toastr.success(\"Mode of Payment updated successfully\");\n        this.loader.isSubmit = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSubmit = false;\n      });\n    } else {\n      var inputParams = [];\n      for (let j = 0; j < this.modeOfPayments.length; j++) {\n        inputParams.push(this.modeOfPayments[j]);\n      }\n      for (let i = 0; i < this.deletedData.length; i++) {\n        inputParams.push(this.deletedData[i]);\n      }\n      this.settingService.updateCategory(inputParams).subscribe(response => {\n        this.toastr.success(\"Mode of Payment updated successfully\");\n        this.loader.isSubmit = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSubmit = false;\n      });\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: SettingsService\n    }, {\n      type: SettingsConfigService\n    }];\n  }\n};\nModeOfPaymentsComponent = __decorate([Component({\n  selector: 'app-mode-of-payments',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ModeOfPaymentsComponent);\nexport { ModeOfPaymentsComponent };", "map": {"version": 3, "names": ["Component", "ToastrService", "SettingsService", "SettingsConfigService", "ModeOfPaymentsComponent", "constructor", "toastr", "settingService", "settingConfigService", "breadcrumbData", "label", "path", "addMode", "deletedData", "modeOfPayments", "loader", "isSearching", "isSubmit", "addNewModeOfPayment", "ngOnInit", "getCategory", "subscribe", "response", "length", "err", "error", "push", "id", "name", "code", "categoryMasterId", "removeModeOfPayment", "obj", "i", "splice", "warning", "submit", "addCategory", "success", "inputParams", "j", "updateCategory", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\payments-config\\mode-of-payments\\mode-of-payments.component.ts"], "sourcesContent": ["export interface Loader{\r\n    isSearching: boolean;\r\n    isSubmit: boolean;\r\n}\r\nimport { Component, OnInit,TemplateRef } from '@angular/core';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\nimport { SettingsService } from '../../settings.service';\r\nimport { SettingsConfigService } from '../../settingsconfig.service';\r\n\r\n@Component({\r\n  selector: 'app-mode-of-payments',\r\n  templateUrl: './mode-of-payments.component.html',\r\n  styleUrls: ['./mode-of-payments.component.css']\r\n})\r\nexport class ModeOfPaymentsComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Settings Payments\", path: \"/settings/payments/mode-of-payments\" },\r\n\t\t{ label: \"Mode of Payments\", path: \"/settings/payments/mode-of-payments\" },\r\n\t  ]\r\n  loader: Loader;\r\n  results: any;\r\n  currentRecords: any;\r\n  modeOfPayments: any;\r\n  addMode =false\r\n  constructor(public toastr: ToastrService,\r\n              private settingService: SettingsService,\r\n              private settingConfigService: SettingsConfigService) {\r\n    this.modeOfPayments = [];\r\n\r\n    this.loader = {\r\n        isSearching: false,\r\n        isSubmit: false\r\n    }\r\n\r\n    this.addNewModeOfPayment()\r\n  }\r\n\r\n  ngOnInit() {\r\n     this.settingService\r\n      .getCategory(\"ModeOfPayment\")\r\n      .subscribe(response => {\r\n        if(response.length==0){\r\n          this.addMode = false\r\n        }\r\n        this.modeOfPayments=response;\r\n        this.loader.isSearching =  false\r\n      },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching =  false\r\n      })\r\n  }\r\n\r\n  addNewModeOfPayment(){\r\n     this.modeOfPayments.push({\r\n          id:\"\",\r\n          name:\"\",\r\n          code: name,\r\n          categoryMasterId:\"ModeOfPayment\"\r\n     })\r\n  }\r\n  deletedData = []\r\n  removeModeOfPayment(obj,i){\r\n    if(this.modeOfPayments.length>1){\r\n       if(obj.id !=\"\"){\r\n        obj[\"isDelete\"] = true\r\n        this.deletedData.push(obj)\r\n      }\r\n      this.modeOfPayments.splice(i, 1);\r\n    }else{\r\n      this.toastr.warning(\"Atleast one record is required\");\r\n    }\r\n  }\r\n\r\n  submit(){\r\n      this.loader.isSubmit =  true\r\n      if(this.addMode){\r\n        this.settingService.addCategory(this.modeOfPayments).subscribe(response => {\r\n          this.toastr.success(\"Mode of Payment updated successfully\")\r\n          this.loader.isSubmit =  false\r\n        },err=>{\r\n          this.toastr.error(err)\r\n          this.loader.isSubmit =  false\r\n        })\r\n      }else{\r\n        var inputParams = []\r\n        for(let j=0; j<this.modeOfPayments.length; j++){\r\n          inputParams.push(this.modeOfPayments[j])\r\n        }\r\n         for(let i=0; i<this.deletedData.length; i++){\r\n          inputParams.push(this.deletedData[i])\r\n        }\r\n        this.settingService.updateCategory(inputParams).subscribe(response => {\r\n          this.toastr.success(\"Mode of Payment updated successfully\")\r\n          this.loader.isSubmit =  false\r\n        },err=>{\r\n          this.toastr.error(err)\r\n          this.loader.isSubmit =  false\r\n        })\r\n      }\r\n\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAIA,SAASA,SAAS,QAA4B,eAAe;AAE7D,SAASC,aAAa,QAAQ,YAAY;AAG1C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,qBAAqB,QAAQ,8BAA8B;AAO7D,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAUlCC,YAAmBC,MAAqB,EACpBC,cAA+B,EAC/BC,oBAA2C;IAF5C,KAAAF,MAAM,GAANA,MAAM;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IAXjC,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAqC,CAAE,EAC3E;MAAED,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAAqC,CAAE,CACxE;IAKF,KAAAC,OAAO,GAAE,KAAK;IAqCd,KAAAC,WAAW,GAAG,EAAE;IAjCd,IAAI,CAACC,cAAc,GAAG,EAAE;IAExB,IAAI,CAACC,MAAM,GAAG;MACVC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE;KACb;IAED,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,QAAQA,CAAA;IACL,IAAI,CAACZ,cAAc,CACjBa,WAAW,CAAC,eAAe,CAAC,CAC5BC,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAGA,QAAQ,CAACC,MAAM,IAAE,CAAC,EAAC;QACpB,IAAI,CAACX,OAAO,GAAG,KAAK;MACtB;MACA,IAAI,CAACE,cAAc,GAACQ,QAAQ;MAC5B,IAAI,CAACP,MAAM,CAACC,WAAW,GAAI,KAAK;IAClC,CAAC,EAACQ,GAAG,IAAE;MACL,IAAI,CAAClB,MAAM,CAACmB,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAACT,MAAM,CAACC,WAAW,GAAI,KAAK;IAClC,CAAC,CAAC;EACN;EAEAE,mBAAmBA,CAAA;IAChB,IAAI,CAACJ,cAAc,CAACY,IAAI,CAAC;MACpBC,EAAE,EAAC,EAAE;MACLC,IAAI,EAAC,EAAE;MACPC,IAAI,EAAED,IAAI;MACVE,gBAAgB,EAAC;KACrB,CAAC;EACL;EAEAC,mBAAmBA,CAACC,GAAG,EAACC,CAAC;IACvB,IAAG,IAAI,CAACnB,cAAc,CAACS,MAAM,GAAC,CAAC,EAAC;MAC7B,IAAGS,GAAG,CAACL,EAAE,IAAG,EAAE,EAAC;QACdK,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;QACtB,IAAI,CAACnB,WAAW,CAACa,IAAI,CAACM,GAAG,CAAC;MAC5B;MACA,IAAI,CAAClB,cAAc,CAACoB,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC,MAAI;MACH,IAAI,CAAC3B,MAAM,CAAC6B,OAAO,CAAC,gCAAgC,CAAC;IACvD;EACF;EAEAC,MAAMA,CAAA;IACF,IAAI,CAACrB,MAAM,CAACE,QAAQ,GAAI,IAAI;IAC5B,IAAG,IAAI,CAACL,OAAO,EAAC;MACd,IAAI,CAACL,cAAc,CAAC8B,WAAW,CAAC,IAAI,CAACvB,cAAc,CAAC,CAACO,SAAS,CAACC,QAAQ,IAAG;QACxE,IAAI,CAAChB,MAAM,CAACgC,OAAO,CAAC,sCAAsC,CAAC;QAC3D,IAAI,CAACvB,MAAM,CAACE,QAAQ,GAAI,KAAK;MAC/B,CAAC,EAACO,GAAG,IAAE;QACL,IAAI,CAAClB,MAAM,CAACmB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACT,MAAM,CAACE,QAAQ,GAAI,KAAK;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAI;MACH,IAAIsB,WAAW,GAAG,EAAE;MACpB,KAAI,IAAIC,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAAC1B,cAAc,CAACS,MAAM,EAAEiB,CAAC,EAAE,EAAC;QAC7CD,WAAW,CAACb,IAAI,CAAC,IAAI,CAACZ,cAAc,CAAC0B,CAAC,CAAC,CAAC;MAC1C;MACC,KAAI,IAAIP,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAACpB,WAAW,CAACU,MAAM,EAAEU,CAAC,EAAE,EAAC;QAC3CM,WAAW,CAACb,IAAI,CAAC,IAAI,CAACb,WAAW,CAACoB,CAAC,CAAC,CAAC;MACvC;MACA,IAAI,CAAC1B,cAAc,CAACkC,cAAc,CAACF,WAAW,CAAC,CAAClB,SAAS,CAACC,QAAQ,IAAG;QACnE,IAAI,CAAChB,MAAM,CAACgC,OAAO,CAAC,sCAAsC,CAAC;QAC3D,IAAI,CAACvB,MAAM,CAACE,QAAQ,GAAI,KAAK;MAC/B,CAAC,EAACO,GAAG,IAAE;QACL,IAAI,CAAClB,MAAM,CAACmB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACT,MAAM,CAACE,QAAQ,GAAI,KAAK;MAC/B,CAAC,CAAC;IACJ;EAEJ;;;;;;;;;;;AAtFWb,uBAAuB,GAAAsC,UAAA,EALnC1C,SAAS,CAAC;EACT2C,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;;CAEjD,CAAC,C,EACWzC,uBAAuB,CAwFnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}