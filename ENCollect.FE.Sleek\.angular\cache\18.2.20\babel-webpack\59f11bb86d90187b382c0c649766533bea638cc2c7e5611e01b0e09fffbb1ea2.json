{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { SendPaymentLinkComponent } from './send-payment-link.component';\ndescribe('SendPaymentLinkComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [SendPaymentLinkComponent]\n    });\n    fixture = TestBed.createComponent(SendPaymentLinkComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "SendPaymentLinkComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\send-payment-link\\send-payment-link.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { SendPaymentLinkComponent } from './send-payment-link.component';\r\n\r\ndescribe('SendPaymentLinkComponent', () => {\r\n  let component: SendPaymentLinkComponent;\r\n  let fixture: ComponentFixture<SendPaymentLinkComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [SendPaymentLinkComponent]\r\n    });\r\n    fixture = TestBed.createComponent(SendPaymentLinkComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,wBAAwB,QAAQ,+BAA+B;AAExEC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,SAAmC;EACvC,IAAIC,OAAmD;EAEvDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,wBAAwB;KACxC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,wBAAwB,CAAC;IAC3DE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}