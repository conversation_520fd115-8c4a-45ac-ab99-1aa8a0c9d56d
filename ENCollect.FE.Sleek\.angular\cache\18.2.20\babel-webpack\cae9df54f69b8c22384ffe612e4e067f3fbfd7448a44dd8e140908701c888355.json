{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { MenuLayoutComponent } from './menu-layout.component';\ndescribe('MenuLayoutComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [MenuLayoutComponent]\n    });\n    fixture = TestBed.createComponent(MenuLayoutComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "MenuLayoutComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\menu-layout\\menu-layout.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { MenuLayoutComponent } from './menu-layout.component';\r\n\r\ndescribe('MenuLayoutComponent', () => {\r\n  let component: MenuLayoutComponent;\r\n  let fixture: ComponentFixture<MenuLayoutComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [MenuLayoutComponent]\r\n    });\r\n    fixture = TestBed.createComponent(MenuLayoutComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,SAA8B;EAClC,IAAIC,OAA8C;EAElDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,mBAAmB;KACnC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,mBAAmB,CAAC;IACtDE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}