{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-template.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-template.component.scss?ngResource\";\nimport { Component, inject, ViewChild, NO_ERRORS_SCHEMA } from \"@angular/core\";\nimport { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from \"@angular/forms\";\nimport { ActivatedRoute, Router } from \"@angular/router\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { SharedModule } from \"src/app/shared\";\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\nimport { TemplatePreviewDirective } from \"src/app/shared/directives/template-preview.directive\";\nimport { RestrictHtmlTagsDirective } from \"src/app/shared/directives/restrict-html-tags.directive\";\nimport { TemplateService } from \"../template.service\";\nimport { Editor, NgxEditorModule } from 'ngx-editor';\nlet CreateTemplateComponent = class CreateTemplateComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.modalService = inject(BsModalService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.templateService = inject(TemplateService);\n    this.toastr = inject(ToastrService);\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Create Communication Template\"\n    }];\n    this.isViewMode = false;\n    this.isEditMode = false;\n    this.templateId = null;\n    this.isLoading = false;\n    this.templateData = null;\n    this.currentTemplateData = null;\n    this.selectedViewLanguageIndex = 0;\n    this.allLanguages = [];\n    this.variables = [];\n    this.isLoadingFields = false;\n    this.selectedVariable = {\n      value: null,\n      index: -1\n    };\n    this.selectedLanguage = null;\n    // NGX Editor properties\n    this.editors = [];\n    this.htmlContents = [];\n    this.isUpdatingFromMapping = false;\n    this.toolbar = [['bold', 'italic'], ['underline'], ['code', 'blockquote'], ['ordered_list', 'bullet_list'], [{\n      heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']\n    }], ['link'], ['align_left', 'align_center', 'align_right', 'align_justify']];\n    this.buildCreateTemplateForm();\n  }\n  ngOnInit() {\n    this.loadDatabaseFields();\n    this.loadLanguageList();\n    this.route.params.subscribe(params => {\n      this.templateId = params['id'] || null;\n      const url = this.router.url;\n      if (url.includes('view-communication-template')) {\n        this.isViewMode = true;\n        this.isEditMode = false;\n        this.updateBreadcrumb('View Communication Template');\n      } else if (url.includes('edit-communication-template')) {\n        this.isViewMode = false;\n        this.isEditMode = true;\n        this.updateBreadcrumb('Edit Communication Template');\n      } else {\n        this.isViewMode = false;\n        this.isEditMode = false;\n        this.updateBreadcrumb('Create Communication Template');\n      }\n      if (this.templateId && (this.isViewMode || this.isEditMode)) {\n        this.loadTemplateData(this.templateId);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.editors.forEach(editor => {\n      if (editor) {\n        editor.destroy();\n      }\n    });\n  }\n  updateBreadcrumb(label) {\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: label\n    }];\n  }\n  loadTemplateData(templateId) {\n    this.isLoading = true;\n    if (this.allLanguages.length === 0) {\n      this.templateService.languageList().subscribe({\n        next: languageResponse => {\n          this.mapLanguageResponse(languageResponse);\n          this.fetchAndPopulateTemplate(templateId);\n        },\n        error: () => {\n          this.allLanguages = [{\n            name: \"English\",\n            code: \"en\"\n          }];\n          this.fetchAndPopulateTemplate(templateId);\n        }\n      });\n    } else {\n      this.fetchAndPopulateTemplate(templateId);\n    }\n  }\n  fetchAndPopulateTemplate(templateId) {\n    this.templateService.fetchTemplateById(templateId).subscribe({\n      next: response => {\n        this.templateData = response;\n        this.populateFormWithTemplateData(response);\n        this.isLoading = false;\n      },\n      error: error => {\n        this.isLoading = false;\n        this.toastr.error(error, 'Error!');\n      }\n    });\n  }\n  get isSMS() {\n    return this.createForm?.get('channelType')?.value?.toLowerCase() === 'sms';\n  }\n  populateFormWithTemplateData(apiResponse) {\n    if (!apiResponse) return;\n    const templateData = apiResponse.data || apiResponse;\n    const templateDetails = templateData.communicationTemplateDetails || [];\n    this.currentTemplateData = templateData;\n    // Convert the boolean value to string \"true\" or \"false\" for radio buttons\n    const allowAccessValue = templateData.isAvailableInAccountDetails === true ? \"true\" : \"false\";\n    this.createForm.patchValue({\n      channelType: templateData.templateType?.toLowerCase() || 'email',\n      templateName: templateData.name || '',\n      allowAccessFromAccount: allowAccessValue,\n      entryPoint: templateData.entryPoint || '',\n      recipientType: templateData.recipientType || ''\n    });\n    const languagesArray = this.createForm.get('languages');\n    while (languagesArray.length !== 0) {\n      languagesArray.removeAt(0);\n    }\n    if (templateDetails.length > 0) {\n      templateDetails.forEach(detail => {\n        let languageCode = 'en';\n        const languageName = detail.language || 'English';\n        if (this.allLanguages && this.allLanguages.length > 0) {\n          const foundLanguage = this.allLanguages.find(lang => lang.name.toLowerCase() === languageName.toLowerCase());\n          if (foundLanguage) {\n            languageCode = foundLanguage.code;\n          }\n        }\n        const languageFormGroup = this.buildLanguageFormGroup({\n          code: languageCode,\n          name: languageName\n        });\n        languageFormGroup.patchValue({\n          languageCode: languageCode,\n          languageName: languageName,\n          emailSubject: detail.subject || '',\n          templateBody: this.templateData.templateType?.toLowerCase() !== 'email' ? detail.body || '' : '',\n          emailBody: this.templateData.templateType?.toLowerCase() === 'email' ? detail.body || '' : ''\n        });\n        // Initialize editor content for email templates\n        if (this.templateData.templateType?.toLowerCase() === 'email') {\n          const index = languagesArray.length;\n          // Convert plain text template to HTML for the editor\n          this.htmlContents[index] = this.plainTextToHtml(detail.body || '');\n        }\n        languagesArray.push(languageFormGroup);\n      });\n    } else {\n      const defaultLanguageGroup = this.buildLanguageFormGroup({\n        code: 'en',\n        name: 'English'\n      });\n      languagesArray.push(defaultLanguageGroup);\n    }\n    if (languagesArray.length > 0) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      const activeLanguage = firstLanguage?.languageCode || 'en';\n      this.createForm.patchValue({\n        activeLanguage\n      });\n    }\n    // Update validation for all language form groups based on channel type\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n    setTimeout(() => {\n      if (this.isViewMode) {\n        this.createForm.disable();\n      } else if (this.isEditMode) {\n        this.createForm.get('channelType')?.disable();\n        this.createForm.get('templateName')?.disable();\n      }\n    }, 200);\n    if (this.isViewMode) {\n      this.updateBreadcrumb('View Communication Template');\n      this.selectedViewLanguageIndex = 0;\n    } else if (this.isEditMode) {\n      this.updateBreadcrumb('Edit Communication Template');\n    }\n    // Initialize editors for email templates after form is populated\n    setTimeout(() => {\n      if (this.templateData.templateType?.toLowerCase() === 'email') {\n        this.initializeEditorsForAllLanguages();\n      }\n    }, 800);\n  }\n  loadDatabaseFields() {\n    this.isLoadingFields = true;\n    this.templateService.getFieldsList().subscribe({\n      next: response => {\n        this.mapFieldsToVariables(response);\n        this.isLoadingFields = false;\n      },\n      error: () => {\n        this.isLoadingFields = false;\n        this.toastr.error('error', 'Error!');\n        this.variables = [];\n      }\n    });\n  }\n  mapFieldsToVariables(apiResponse) {\n    if (!Array.isArray(apiResponse)) {\n      this.variables = [];\n      return;\n    }\n    this.variables = apiResponse.map(field => ({\n      name: field.name || field.fieldName || 'Unknown Field',\n      id: field.id || field.code || field.name?.toUpperCase().replace(/\\s+/g, '_') || 'UNKNOWN',\n      code: field.code || field.id || 'UNKNOWN_CODE'\n    }));\n  }\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\", [Validators.required]],\n      templateName: [null, [Validators.required]],\n      allowAccessFromAccount: [\"true\"],\n      activeLanguage: [\"en\"],\n      languages: this.fb.array([]),\n      entryPoint: ['Account', [Validators.required]],\n      recipientType: ['Customer', [Validators.required]]\n    });\n    this.createForm.get('channelType')?.valueChanges.subscribe(channelType => {\n      this.updateLanguageValidation(channelType);\n      this.handleChannelTypeChange(channelType);\n    });\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n  }\n  updateLanguageValidation(channelType) {\n    const languagesArray = this.createForm.get('languages');\n    languagesArray.controls.forEach(languageControl => {\n      const emailSubjectControl = languageControl.get('emailSubject');\n      const templateBodyControl = languageControl.get('templateBody');\n      const emailBodyControl = languageControl.get('emailBody');\n      if (channelType === 'email') {\n        emailSubjectControl?.setValidators([Validators.required]);\n        emailBodyControl?.setValidators([Validators.required]);\n        templateBodyControl?.clearValidators();\n      } else {\n        emailSubjectControl?.clearValidators();\n        emailBodyControl?.clearValidators();\n        templateBodyControl?.setValidators([Validators.required]);\n      }\n      emailSubjectControl?.updateValueAndValidity();\n      templateBodyControl?.updateValueAndValidity();\n      emailBodyControl?.updateValueAndValidity();\n    });\n  }\n  buildLanguagesFormArray(data) {\n    const formArray = new FormArray([]);\n    if (data && data.length > 0) {\n      data.forEach(o => {\n        formArray.push(this.buildLanguageFormGroup(o));\n      });\n    } else if (this.allLanguages && this.allLanguages.length > 0) {\n      formArray.push(this.buildLanguageFormGroup(this.allLanguages[0]));\n    }\n    return formArray;\n  }\n  buildLanguageFormGroup(data) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null],\n      // Validators will be set by updateLanguageValidation\n      emailBody: [null] // Validators will be set by updateLanguageValidation\n    });\n  }\n  get fValue() {\n    return this.createForm.value;\n  }\n  shouldShowLanguageFields(languageIndex) {\n    if (this.isViewMode) {\n      return languageIndex === 0;\n    }\n    const activeLanguage = this.fValue?.activeLanguage;\n    const currentLanguage = this.fValue?.languages?.[languageIndex];\n    return activeLanguage === currentLanguage?.languageCode;\n  }\n  selectViewLanguage(index) {\n    this.selectedViewLanguageIndex = index;\n  }\n  getSelectedLanguageDetail() {\n    if (this.isViewMode && this.currentTemplateData?.communicationTemplateDetails?.length > 0) {\n      return this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex] || this.currentTemplateData.communicationTemplateDetails[0];\n    }\n    return null;\n  }\n  getFormValidationErrors() {\n    let formErrors = {};\n    Object.keys(this.createForm.controls).forEach(key => {\n      const controlErrors = this.createForm.get(key)?.errors;\n      if (controlErrors) {\n        formErrors[key] = controlErrors;\n      }\n    });\n    const languagesArray = this.createForm.get('languages');\n    if (languagesArray) {\n      languagesArray.controls.forEach((control, index) => {\n        const formGroup = control;\n        Object.keys(formGroup.controls).forEach(fieldKey => {\n          const fieldControl = formGroup.get(fieldKey);\n          if (fieldControl?.errors) {\n            if (!formErrors.languages) formErrors.languages = {};\n            if (!formErrors.languages[index]) formErrors.languages[index] = {};\n            formErrors.languages[index][fieldKey] = fieldControl.errors;\n          }\n        });\n      });\n    }\n    return formErrors;\n  }\n  openMapVariableModal(event, template) {\n    this.selectedVariable = event;\n    this.mapVarModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  assignVariable() {\n    this.mapVarModalRef.hide();\n    if (this.selectedVariable?.value) {\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\n      this.selectedVariable = {\n        value: null,\n        index: -1\n      };\n    }\n  }\n  updateTemplateValue(template, index) {\n    const languageControl = this.createForm.get('languages').at(index);\n    const channelType = this.createForm.get('channelType')?.value;\n    console.log('updateTemplateValue called with:', {\n      template,\n      index,\n      channelType\n    });\n    if (channelType === 'email') {\n      // Update the form control with the plain text template (already in correct format)\n      languageControl.patchValue({\n        emailBody: template\n      });\n      languageControl.get('emailBody')?.updateValueAndValidity();\n      // Temporarily disable the content change handler to avoid circular updates\n      this.isUpdatingFromMapping = true;\n      // Update the editor content directly with the plain text converted to HTML\n      const htmlContent = this.plainTextToHtml(template);\n      this.htmlContents[index] = htmlContent;\n      console.log('Template value updated for email at index:', index);\n      console.log('Plain text template:', template);\n      console.log('HTML content for editor:', htmlContent);\n      // Re-enable the content change handler after a short delay\n      setTimeout(() => {\n        this.isUpdatingFromMapping = false;\n      }, 200);\n    } else {\n      languageControl.patchValue({\n        templateBody: template\n      });\n      languageControl.get('templateBody')?.updateValueAndValidity();\n    }\n    this.createForm.updateValueAndValidity();\n  }\n  openAddLangModal(template) {\n    this.addLangModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  addLanguage() {\n    this.addLangModalRef?.hide();\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\n    const languagesArray = this.createForm.get('languages');\n    const isLanguageAlreadySelected = languagesArray.controls.some(control => control.get('languageCode')?.value === this.selectedLanguage);\n    if (isLanguageAlreadySelected) {\n      this.toastr.error('The same language is already selected.', 'Error!');\n      return;\n    }\n    const langFormGroup = this.buildLanguageFormGroup(language);\n    languagesArray.push(langFormGroup);\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n    this.createForm.updateValueAndValidity();\n    // Initialize editor for new language if channel type is email\n    if (this.createForm.get('channelType')?.value === 'email') {\n      this.initializeEditorForLanguage(languagesArray.length - 1);\n    }\n  }\n  removeLanguage(index) {\n    const languagesArray = this.createForm.get('languages');\n    if (languagesArray.length <= 1) {\n      return;\n    }\n    const removedLanguage = languagesArray.at(index).value;\n    languagesArray.removeAt(index);\n    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      if (firstLanguage) {\n        this.createForm.patchValue({\n          activeLanguage: firstLanguage.languageCode\n        });\n      }\n    }\n    this.createForm.updateValueAndValidity();\n  }\n  createTemplate() {\n    this.markFormGroupTouched(this.createForm);\n    if (this.createForm.invalid) {\n      this.toastr.error('Please fill all required fields.', 'Error');\n      return;\n    }\n    const formValue = this.createForm.value;\n    const hasTemplateBody = formValue.channelType === 'email' ? formValue.languages?.some(lang => lang.emailBody && lang.emailBody.trim()) : formValue.languages?.some(lang => lang.templateBody && lang.templateBody.trim());\n    if (!hasTemplateBody) {\n      this.toastr.error('Please enter template body content.', 'Error');\n      return;\n    }\n    if (this.hasUnmappedVariables(formValue.languages)) {\n      this.toastr.error('Please map all variables and continue creating template.', 'Error');\n      return;\n    }\n    if (formValue.channelType === 'email') {\n      const hasEmailSubject = formValue.languages?.every(lang => lang.emailSubject && lang.emailSubject.trim());\n      if (!hasEmailSubject) {\n        this.toastr.error('Please enter email subject for all languages.', 'Error');\n        return;\n      }\n    }\n    const getTemplateType = channelType => {\n      switch (channelType.toLowerCase()) {\n        case 'email':\n          return 'Email';\n        case 'sms':\n          return 'SMS';\n        case 'letter':\n          return 'Letter';\n        default:\n          return 'Email';\n      }\n    };\n    const getLanguageName = languageCode => {\n      const language = this.allLanguages.find(lang => lang.code === languageCode);\n      return language ? language.name : 'English';\n    };\n    const communicationTemplateDetails = formValue.languages.map(lang => {\n      let bodyContent = '';\n      if (formValue.channelType === 'email') {\n        // For email templates, ensure we send plain text (already converted in form control)\n        bodyContent = lang.emailBody || \"\";\n      } else {\n        bodyContent = lang.templateBody || \"\";\n      }\n      const detail = {\n        \"Language\": getLanguageName(lang.languageCode),\n        \"body\": bodyContent\n      };\n      if (formValue.channelType === 'email') {\n        detail.Subject = lang.emailSubject || \"\";\n      }\n      return detail;\n    });\n    const json = {\n      \"templateType\": getTemplateType(formValue.channelType),\n      \"Name\": formValue.templateName,\n      \"isAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n      \"CommunicationTemplateDetails\": communicationTemplateDetails,\n      \"entryPoint\": formValue.entryPoint,\n      \"recipientType\": formValue.recipientType\n    };\n    this.templateService.saveCommunicationTemplate(json).subscribe({\n      next: () => {\n        this.toastr.success(`The Template \"${json.Name}\" has been created successfully.`, \"Success!\");\n        this.router.navigate(['communication/search-communication-templates']);\n      },\n      error: error => {\n        this.toastr.error(error, \"Error!\");\n      }\n    });\n  }\n  updateTemplate() {\n    this.markFormGroupTouched(this.createForm);\n    if (this.createForm.invalid) {\n      // Debug: Log validation errors to help identify the issue\n      console.log('Form validation errors:', this.getFormValidationErrors());\n      console.log('Form value:', this.createForm.getRawValue());\n      this.toastr.error('Please fill in all required fields.', 'Error');\n      return;\n    }\n    const formValue = this.createForm.getRawValue();\n    const hasTemplateBody = formValue.channelType === 'email' ? formValue.languages?.some(lang => lang.emailBody && lang.emailBody.trim()) : formValue.languages?.some(lang => lang.templateBody && lang.templateBody.trim());\n    if (!hasTemplateBody) {\n      this.toastr.error('Please enter template body content.', 'Error');\n      return;\n    }\n    if (this.hasUnmappedVariables(formValue.languages)) {\n      this.toastr.error('Please map all variables and continue creating template.', 'Error');\n      return;\n    }\n    if (!formValue.channelType) {\n      this.toastr.error('Please select a channel type.', 'Error');\n      return;\n    }\n    if (formValue.channelType === 'email') {\n      const hasEmailSubject = formValue.languages?.every(lang => lang.emailSubject && lang.emailSubject.trim());\n      if (!hasEmailSubject) {\n        this.toastr.error('Please enter email subject for all languages.', 'Error');\n        return;\n      }\n    }\n    const getTemplateType = channelType => {\n      if (!channelType || typeof channelType !== 'string') {\n        return 'Email';\n      }\n      switch (channelType.toLowerCase()) {\n        case 'email':\n          return 'Email';\n        case 'sms':\n          return 'SMS';\n        case 'letter':\n          return 'Letter';\n        default:\n          return 'Email';\n      }\n    };\n    const getLanguageName = languageCode => {\n      if (!languageCode) return 'English';\n      const language = this.allLanguages.find(lang => lang.code === languageCode);\n      return language ? language.name : 'English';\n    };\n    const communicationTemplateDetails = formValue.languages?.map(lang => {\n      let bodyContent = '';\n      if (formValue.channelType === 'email') {\n        // For email templates, ensure we send plain text (already converted in form control)\n        bodyContent = lang.emailBody || \"\";\n      } else {\n        bodyContent = lang.templateBody || \"\";\n      }\n      const detail = {\n        \"Language\": getLanguageName(lang.languageCode),\n        \"body\": bodyContent\n      };\n      if (formValue.channelType === 'email') {\n        detail.Subject = lang.emailSubject || \"\";\n      }\n      return detail;\n    }) || [];\n    const json = {\n      \"id\": this.templateId,\n      \"templateType\": getTemplateType(formValue.channelType),\n      \"Name\": formValue.templateName || \"\",\n      \"IsAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n      \"CommunicationTemplateDetails\": communicationTemplateDetails,\n      \"entryPoint\": formValue.entryPoint,\n      \"recipientType\": formValue.recipientType\n    };\n    this.templateService.updateCcmTemplate(json).subscribe({\n      next: () => {\n        this.toastr.success(`The Template \"${json.Name}\" has been updated successfully.`, \"Success!\");\n        this.router.navigate(['communication/search-communication-templates']);\n      },\n      error: error => {\n        this.toastr.error(error, \"Error!\");\n      }\n    });\n  }\n  loadLanguageList() {\n    this.templateService.languageList().subscribe({\n      next: response => {\n        this.mapLanguageResponse(response);\n      },\n      error: error => {\n        this.toastr.error(error, 'Error');\n        this.allLanguages = [{\n          name: \"English\",\n          code: \"en\"\n        }];\n        this.initializeDefaultLanguage();\n      }\n    });\n  }\n  mapLanguageResponse(apiResponse) {\n    if (!apiResponse || !Array.isArray(apiResponse)) {\n      this.allLanguages = [{\n        name: \"English\",\n        code: \"en\"\n      }];\n      this.initializeDefaultLanguage();\n      return;\n    }\n    this.allLanguages = apiResponse.map(lang => ({\n      name: lang.name || lang.itemName || 'Unknown Language',\n      code: lang.code || lang.itemCode || lang.name?.toLowerCase().substring(0, 2) || 'en'\n    }));\n    if (this.allLanguages.length === 0) {\n      this.allLanguages = [{\n        name: \"English\",\n        code: \"en\"\n      }];\n    }\n    this.initializeDefaultLanguage();\n  }\n  initializeDefaultLanguage() {\n    if (this.createForm && this.allLanguages.length > 0) {\n      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;\n      if (!currentActiveLanguage || !this.allLanguages.find(lang => lang.code === currentActiveLanguage)) {\n        this.createForm.patchValue({\n          activeLanguage: this.allLanguages[0].code\n        });\n        const languagesArray = this.createForm.get('languages');\n        if (languagesArray.length === 0) {\n          const defaultLanguageGroup = this.buildLanguageFormGroup(this.allLanguages[0]);\n          languagesArray.push(defaultLanguageGroup);\n          // Update validation for the newly added language group\n          this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n        }\n      }\n    }\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      if (control instanceof FormGroup || control instanceof FormArray) {\n        this.markFormGroupTouched(control);\n      } else {\n        control?.markAsTouched();\n      }\n    });\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.createForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldErrorMessage(fieldName) {\n    const field = this.createForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n  isLanguageFieldInvalid(languageIndex, fieldName) {\n    const languagesArray = this.createForm.get('languages');\n    const languageGroup = languagesArray.at(languageIndex);\n    const field = languageGroup?.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getLanguageFieldErrorMessage(languageIndex, fieldName) {\n    const languagesArray = this.createForm.get('languages');\n    const languageGroup = languagesArray.at(languageIndex);\n    const field = languageGroup?.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n  hasUnmappedVariables(languages) {\n    if (!languages || languages.length === 0) {\n      return false;\n    }\n    const channelType = this.createForm.get('channelType')?.value;\n    return languages.some(lang => {\n      const templateBody = channelType === 'email' ? lang.emailBody || '' : lang.templateBody || '';\n      return templateBody.includes('<<Var>>');\n    });\n  }\n  getFieldDisplayName(fieldName) {\n    const fieldNames = {\n      'templateName': 'Template Name',\n      'channelType': 'Channel Type',\n      'emailSubject': 'Subject Line',\n      'templateBody': 'Template Body',\n      'entryPoint': 'Entry Point',\n      'recipientType': 'Recipient Type'\n    };\n    return fieldNames[fieldName] || fieldName;\n  }\n  insertVariable() {\n    const activeLanguageIndex = this.getActiveLanguageIndex();\n    if (activeLanguageIndex === -1) return;\n    const channelType = this.createForm.get('channelType')?.value;\n    const languageControl = this.createForm.get('languages').at(activeLanguageIndex);\n    if (channelType === 'email') {\n      // For email templates using ngx-editor\n      const editor = this.editors[activeLanguageIndex];\n      if (editor && editor.view) {\n        try {\n          // Insert the variable as plain text to preserve the angle brackets\n          editor.commands.insertText('<<Var>>').exec();\n          // Debug: Log to console to verify insertion\n          console.log('Variable inserted into editor at index:', activeLanguageIndex);\n          // The editor content will be automatically synced via ngModel binding\n        } catch (error) {\n          console.error('Error inserting variable into editor:', error);\n        }\n      } else {\n        console.warn('Editor not available at index:', activeLanguageIndex);\n        // Fallback: Initialize editor if not available\n        this.initializeEditorForLanguage(activeLanguageIndex);\n      }\n    } else {\n      // For SMS/Letter templates using textarea\n      const control = languageControl.get('templateBody');\n      const currentValue = control.value || '';\n      const textareaElement = document.getElementById('templateBody');\n      if (!textareaElement) return;\n      const cursorPosition = textareaElement.selectionStart;\n      const newValue = currentValue.substring(0, cursorPosition) + '<<Var>>' + currentValue.substring(textareaElement.selectionEnd || cursorPosition);\n      control.setValue(newValue);\n      control.updateValueAndValidity();\n      setTimeout(() => {\n        textareaElement.focus();\n        textareaElement.setSelectionRange(cursorPosition + 7, cursorPosition + 7);\n      }, 0);\n    }\n  }\n  getActiveLanguageIndex() {\n    const activeLanguage = this.createForm.get('activeLanguage').value;\n    const languages = this.createForm.get('languages');\n    for (let i = 0; i < languages.length; i++) {\n      const lang = languages.at(i).get('languageCode').value;\n      if (lang === activeLanguage) {\n        return i;\n      }\n    }\n    return -1;\n  }\n  shouldRestrictHtmlTags() {\n    return this.createForm?.get('channelType')?.value !== 'sms';\n  }\n  getCurrentTemplateBody(languageIndex) {\n    const channelType = this.createForm.get('channelType')?.value;\n    const languageControl = this.createForm.get('languages').at(languageIndex);\n    if (channelType === 'email') {\n      // For email templates, return the plain text content from form control\n      // This ensures template variable mapping and preview work correctly\n      const content = languageControl.get('emailBody')?.value || '';\n      return content; // Already converted to plain text in onEditorContentChange\n    } else {\n      return languageControl.get('templateBody')?.value || '';\n    }\n  }\n  isLanguageFieldInvalidForCurrentChannel(languageIndex) {\n    const channelType = this.createForm.get('channelType')?.value;\n    const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';\n    return this.isLanguageFieldInvalid(languageIndex, fieldName);\n  }\n  getLanguageFieldErrorMessageForCurrentChannel(languageIndex) {\n    const channelType = this.createForm.get('channelType')?.value;\n    const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';\n    return this.getLanguageFieldErrorMessage(languageIndex, fieldName);\n  }\n  // NGX Editor Methods\n  initializeEditorForLanguage(index) {\n    if (!this.editors[index]) {\n      try {\n        this.editors[index] = new Editor({\n          history: true,\n          keyboardShortcuts: true,\n          inputRules: true\n        });\n        this.htmlContents[index] = '';\n        console.log('Editor initialized successfully for index:', index);\n      } catch (error) {\n        console.error('Failed to initialize editor for index:', index, error);\n        // Fallback: try with minimal configuration\n        try {\n          this.editors[index] = new Editor();\n          this.htmlContents[index] = '';\n          console.log('Editor initialized with minimal config for index:', index);\n        } catch (fallbackError) {\n          console.error('Failed to initialize editor even with minimal config:', fallbackError);\n        }\n      }\n    }\n  }\n  initializeEditorsForAllLanguages() {\n    const channelType = this.createForm.get('channelType')?.value;\n    if (channelType === 'email') {\n      const languagesArray = this.createForm.get('languages');\n      for (let i = 0; i < languagesArray.length; i++) {\n        try {\n          this.initializeEditorForLanguage(i);\n          // Set initial content from form control - convert plain text to HTML\n          const emailBodyControl = languagesArray.at(i).get('emailBody');\n          if (emailBodyControl?.value) {\n            // Convert plain text template to HTML for the editor\n            this.htmlContents[i] = this.plainTextToHtml(emailBodyControl.value);\n            console.log('Initialized editor content for index:', i);\n            console.log('Plain text from form:', emailBodyControl.value);\n            console.log('HTML for editor:', this.htmlContents[i]);\n          }\n        } catch (error) {\n          console.error('Error initializing editor for language index:', i, error);\n        }\n      }\n    }\n  }\n  onEditorContentChange(index) {\n    // Skip processing if we're updating from template mapping to avoid circular updates\n    if (this.isUpdatingFromMapping) {\n      console.log('Skipping editor content change - updating from mapping');\n      return;\n    }\n    const languagesArray = this.createForm.get('languages');\n    const emailBodyControl = languagesArray.at(index).get('emailBody');\n    if (emailBodyControl) {\n      // Convert HTML content to plain text for template processing\n      const plainTextContent = this.htmlToPlainText(this.htmlContents[index] || '');\n      // Debug: Log content changes\n      console.log('Editor content changed at index:', index);\n      console.log('Raw HTML content:', this.htmlContents[index]);\n      console.log('Plain text content:', plainTextContent);\n      emailBodyControl.setValue(plainTextContent);\n      emailBodyControl.updateValueAndValidity();\n    }\n  }\n  destroyEditor(index) {\n    if (this.editors[index]) {\n      this.editors[index].destroy();\n      this.editors[index] = null;\n      this.htmlContents[index] = '';\n    }\n  }\n  handleChannelTypeChange(channelType) {\n    if (channelType === 'email') {\n      // Initialize editors for email templates with a longer delay to ensure DOM is ready\n      setTimeout(() => {\n        this.initializeEditorsForAllLanguages();\n      }, 500);\n    } else {\n      // Destroy editors for non-email templates\n      this.editors.forEach((editor, index) => {\n        if (editor) {\n          this.destroyEditor(index);\n        }\n      });\n    }\n  }\n  // Helper method to ensure template variables are properly formatted\n  normalizeTemplateVariables(content) {\n    // Convert HTML entities back to angle brackets for template processing\n    return content.replace(/&lt;&lt;/g, '<<').replace(/&gt;&gt;/g, '>>').replace(/&amp;/g, '&');\n  }\n  // Helper method to convert HTML content to plain text for template processing\n  htmlToPlainText(html) {\n    if (!html) return '';\n    console.log('Converting HTML to plain text:', html);\n    // Create a temporary div element to parse HTML\n    const tempDiv = document.createElement('div');\n    tempDiv.innerHTML = html;\n    // Get plain text content\n    let plainText = tempDiv.textContent || tempDiv.innerText || '';\n    console.log('Extracted plain text:', plainText);\n    // Normalize template variables\n    plainText = this.normalizeTemplateVariables(plainText);\n    console.log('After normalization:', plainText);\n    return plainText;\n  }\n  // Helper method to convert plain text back to HTML for ngx-editor\n  plainTextToHtml(text) {\n    if (!text) return '';\n    console.log('Converting plain text to HTML:', text);\n    // First, escape any existing HTML entities to prevent double-encoding\n    let processedText = text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n    console.log('After HTML escaping:', processedText);\n    // Split text into lines and process each line\n    const lines = processedText.split('\\n');\n    const htmlLines = lines.map(line => {\n      if (line.trim() === '') {\n        return '<p><br></p>'; // Empty line\n      } else {\n        return `<p>${line}</p>`; // Wrap in paragraph\n      }\n    });\n    const result = htmlLines.join('');\n    console.log('Final HTML result:', result);\n    return result;\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      templateVarConfig: [{\n        type: ViewChild,\n        args: ['templateVarConfig', {\n          static: false\n        }]\n      }]\n    };\n  }\n};\nCreateTemplateComponent = __decorate([Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [FormsModule, ReactiveFormsModule, SharedModule, TemplateVarConfigDirective, TemplatePreviewDirective, RestrictHtmlTagsDirective, NgxEditorModule],\n  schemas: [NO_ERRORS_SCHEMA],\n  providers: [TemplateService],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTemplateComponent);\nexport { CreateTemplateComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "NO_ERRORS_SCHEMA", "FormArray", "FormBuilder", "FormGroup", "FormsModule", "ReactiveFormsModule", "Validators", "ActivatedRoute", "Router", "BsModalService", "ToastrService", "SharedModule", "TemplateVarConfigDirective", "TemplatePreviewDirective", "RestrictHtmlTagsDirective", "TemplateService", "Editor", "NgxEditorModule", "CreateTemplateComponent", "constructor", "fb", "modalService", "route", "router", "templateService", "toastr", "breadcrumbData", "label", "isViewMode", "isEditMode", "templateId", "isLoading", "templateData", "currentTemplateData", "selectedViewLanguageIndex", "allLanguages", "variables", "isLoadingFields", "selectedVariable", "value", "index", "selectedLanguage", "editors", "htmlContents", "isUpdatingFromMapping", "toolbar", "heading", "buildCreateTemplateForm", "ngOnInit", "loadDatabaseFields", "loadLanguageList", "params", "subscribe", "url", "includes", "updateBreadcrumb", "loadTemplateData", "ngOnDestroy", "for<PERSON>ach", "editor", "destroy", "length", "languageList", "next", "languageResponse", "mapLanguageResponse", "fetchAndPopulateTemplate", "error", "name", "code", "fetchTemplateById", "response", "populateFormWithTemplateData", "isSMS", "createForm", "get", "toLowerCase", "apiResponse", "data", "templateDetails", "communicationTemplateDetails", "allowAccessValue", "isAvailableInAccountDetails", "patchValue", "channelType", "templateType", "templateName", "allowAccessFromAccount", "entryPoint", "recipientType", "languagesArray", "removeAt", "detail", "languageCode", "languageName", "language", "foundLanguage", "find", "lang", "languageFormGroup", "buildLanguageFormGroup", "emailSubject", "subject", "templateBody", "body", "emailBody", "plainTextToHtml", "push", "defaultLanguageGroup", "firstLanguage", "at", "activeLanguage", "updateLanguageValidation", "setTimeout", "disable", "initializeEditorsForAllLanguages", "getFieldsList", "mapFieldsToVariables", "Array", "isArray", "map", "field", "fieldName", "id", "toUpperCase", "replace", "group", "required", "languages", "array", "valueChanges", "handleChannelTypeChange", "controls", "languageControl", "emailSubjectControl", "templateBodyControl", "emailBodyControl", "setValidators", "clearValidators", "updateValueAndValidity", "buildLanguagesFormArray", "formArray", "o", "fValue", "shouldShowLanguageFields", "languageIndex", "currentLanguage", "selectViewLanguage", "getSelectedLanguageDetail", "getFormValidationErrors", "formErrors", "Object", "keys", "key", "controlErrors", "errors", "control", "formGroup", "<PERSON><PERSON><PERSON>", "fieldControl", "openMapVariableModal", "event", "template", "mapVarModalRef", "show", "animated", "assignVariable", "hide", "templateVarConfig", "onUpdateVariable", "updateTemplateValue", "console", "log", "htmlContent", "openAddLangModal", "addLangModalRef", "addLanguage", "isLanguageAlreadySelected", "some", "langFormGroup", "initializeEditorForLanguage", "removeLanguage", "removedLanguage", "createTemplate", "markFormGroupTouched", "invalid", "formValue", "hasTemplateBody", "trim", "hasUnmappedVariables", "hasEmailSubject", "every", "getTemplateType", "getLanguageName", "bodyContent", "Subject", "json", "saveCommunicationTemplate", "success", "Name", "navigate", "updateTemplate", "getRawValue", "updateCcmTemplate", "initializeDefaultLanguage", "itemName", "itemCode", "substring", "currentActiveLanguage", "<PERSON><PERSON><PERSON><PERSON>ched", "isFieldInvalid", "dirty", "touched", "getFieldErrorMessage", "getFieldDisplayName", "isLanguageFieldInvalid", "languageGroup", "getLanguageFieldErrorMessage", "fieldNames", "insertVariable", "activeLanguageIndex", "getActiveLanguageIndex", "view", "commands", "insertText", "exec", "warn", "currentValue", "textareaElement", "document", "getElementById", "cursorPosition", "selectionStart", "newValue", "selectionEnd", "setValue", "focus", "setSelectionRange", "i", "shouldRestrictHtmlTags", "getCurrentTemplateBody", "content", "isLanguageFieldInvalidForCurrentChannel", "getLanguageFieldErrorMessageForCurrentChannel", "history", "keyboardShortcuts", "inputRules", "fallback<PERSON><PERSON>r", "onEditorContentChange", "plainTextContent", "htmlToPlainText", "destroyEditor", "normalizeTemplateVariables", "html", "tempDiv", "createElement", "innerHTML", "plainText", "textContent", "innerText", "text", "processedText", "lines", "split", "htmlLines", "line", "result", "join", "args", "static", "__decorate", "selector", "standalone", "imports", "schemas", "providers", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\create-template.component.ts"], "sourcesContent": ["import { Component, inject, TemplateRef, ViewChild, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NO_ERRORS_SCHEMA } from \"@angular/core\";\nimport {\n  FormArray,\n  FormBuilder,\n  FormGroup,\n  FormsModule,\n  ReactiveFormsModule,\n  Validators,\n} from \"@angular/forms\";\nimport { ActivatedRoute, Router } from \"@angular/router\";\nimport { BsModalRef, BsModalService } from \"ngx-bootstrap/modal\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { SharedModule } from \"src/app/shared\";\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\nimport { TemplatePreviewDirective } from \"src/app/shared/directives/template-preview.directive\";\nimport { RestrictHtmlTagsDirective } from \"src/app/shared/directives/restrict-html-tags.directive\";\nimport { TemplateService } from \"../template.service\";\nimport { Editor, NgxEditorModule, Toolbar } from 'ngx-editor';\n\n@Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [\n    FormsModule,\n    ReactiveFormsModule,\n    SharedModule,\n    TemplateVarConfigDirective,\n    TemplatePreviewDirective,\n    RestrictHtmlTagsDirective,\n    NgxEditorModule\n  ],\n  schemas: [NO_ERRORS_SCHEMA],\n  providers: [TemplateService],\n  templateUrl: \"./create-template.component.html\",\n  styleUrl: \"./create-template.component.scss\",\n})\nexport class CreateTemplateComponent implements OnInit, OnDestroy {\n  private fb: FormBuilder = inject(FormBuilder);\n  private modalService: BsModalService = inject(BsModalService);\n  private route: ActivatedRoute = inject(ActivatedRoute);\n  private router: Router = inject(Router);\n  private templateService: TemplateService = inject(TemplateService);\n  private toastr: ToastrService = inject(ToastrService);\n\n  breadcrumbData = [\n    { label: \"Communication\" },\n    { label: \"Create Communication Template\" },\n  ];\n\n  isViewMode: boolean = false;\n  isEditMode: boolean = false;\n  templateId: string | null = null;\n  isLoading: boolean = false;\n  templateData: any = null;\n  currentTemplateData: any = null;\n  selectedViewLanguageIndex: number = 0;\n  allLanguages: any[] = [];\n  createForm!: FormGroup;\n  variables: {name: string, id: string, code?: string}[] = [];\n  isLoadingFields = false;\n  selectedVariable: {value: string, index: number} = { value: null, index: -1 };\n  selectedLanguage: string = null;\n  mapVarModalRef!: BsModalRef;\n  addLangModalRef!: BsModalRef;\n  @ViewChild('templateVarConfig', { static: false }) templateVarConfig!: TemplateVarConfigDirective;\n\n  // NGX Editor properties\n  editors: Editor[] = [];\n  htmlContents: string[] = [];\n  isUpdatingFromMapping: boolean = false;\n  toolbar: Toolbar = [\n    ['bold', 'italic'],\n    ['underline'],\n    ['code', 'blockquote'],\n    ['ordered_list', 'bullet_list'],\n    [{ heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] }],\n    ['link'],\n    ['align_left', 'align_center', 'align_right', 'align_justify'],\n  ];\n\n  constructor() {\n    this.buildCreateTemplateForm();\n  }\n\n  ngOnInit() {\n    this.loadDatabaseFields();\n    this.loadLanguageList();\n    this.route.params.subscribe(params => {\n      this.templateId = params['id'] || null;\n\n      const url = this.router.url;\n      if (url.includes('view-communication-template')) {\n        this.isViewMode = true;\n        this.isEditMode = false;\n        this.updateBreadcrumb('View Communication Template');\n      } else if (url.includes('edit-communication-template')) {\n        this.isViewMode = false;\n        this.isEditMode = true;\n        this.updateBreadcrumb('Edit Communication Template');\n      } else {\n        this.isViewMode = false;\n        this.isEditMode = false;\n        this.updateBreadcrumb('Create Communication Template');\n      }\n\n      if (this.templateId && (this.isViewMode || this.isEditMode)) {\n        this.loadTemplateData(this.templateId);\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.editors.forEach(editor => {\n      if (editor) {\n        editor.destroy();\n      }\n    });\n  }\n\n  private updateBreadcrumb(label: string) {\n    this.breadcrumbData = [\n      { label: \"Communication\" },\n      { label: label },\n    ];\n  }\n\n  private loadTemplateData(templateId: string) {\n    this.isLoading = true;\n\n    if (this.allLanguages.length === 0) {\n      this.templateService.languageList().subscribe({\n        next: (languageResponse: any) => {\n          this.mapLanguageResponse(languageResponse);\n          this.fetchAndPopulateTemplate(templateId);\n        },\n        error: () => {\n          this.allLanguages = [{ name: \"English\", code: \"en\" }];\n          this.fetchAndPopulateTemplate(templateId);\n        }\n      });\n    } else {\n      this.fetchAndPopulateTemplate(templateId);\n    }\n  }\n\n  private fetchAndPopulateTemplate(templateId: string) {\n    this.templateService.fetchTemplateById(templateId).subscribe({\n      next: (response: any) => {\n        this.templateData = response;\n        this.populateFormWithTemplateData(response);\n        this.isLoading = false;\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.toastr.error(error, 'Error!');\n      }\n    });\n  }\n\n  get isSMS(): boolean {\n  return this.createForm?.get('channelType')?.value?.toLowerCase() === 'sms';\n}\n\n\n  private populateFormWithTemplateData(apiResponse: any) {\n    if (!apiResponse) return;\n\n    const templateData = apiResponse.data || apiResponse;\n    const templateDetails = templateData.communicationTemplateDetails || [];\n    this.currentTemplateData = templateData;\n\n    // Convert the boolean value to string \"true\" or \"false\" for radio buttons\n    const allowAccessValue = templateData.isAvailableInAccountDetails === true ? \"true\" : \"false\";\n\n    this.createForm.patchValue({\n      channelType: templateData.templateType?.toLowerCase() || 'email',\n      templateName: templateData.name || '',\n      allowAccessFromAccount: allowAccessValue,\n      entryPoint: templateData.entryPoint || '',\n      recipientType: templateData.recipientType || '',\n    });\n\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    while (languagesArray.length !== 0) {\n      languagesArray.removeAt(0);\n    }\n\n    if (templateDetails.length > 0) {\n      templateDetails.forEach((detail: any) => {\n        let languageCode = 'en';\n        const languageName = detail.language || 'English';\n\n        if (this.allLanguages && this.allLanguages.length > 0) {\n          const foundLanguage = this.allLanguages.find(lang =>\n            lang.name.toLowerCase() === languageName.toLowerCase()\n          );\n          if (foundLanguage) {\n            languageCode = foundLanguage.code;\n          }\n        }\n\n        const languageFormGroup = this.buildLanguageFormGroup({\n          code: languageCode,\n          name: languageName\n        });\n\n        languageFormGroup.patchValue({\n          languageCode: languageCode,\n          languageName: languageName,\n          emailSubject: detail.subject || '',\n          templateBody: this.templateData.templateType?.toLowerCase() !== 'email' ? detail.body || '' : '',\n          emailBody: this.templateData.templateType?.toLowerCase() === 'email' ? detail.body || '' : '',\n        });\n\n        // Initialize editor content for email templates\n        if (this.templateData.templateType?.toLowerCase() === 'email') {\n          const index = languagesArray.length;\n          // Convert plain text template to HTML for the editor\n          this.htmlContents[index] = this.plainTextToHtml(detail.body || '');\n        }\n        languagesArray.push(languageFormGroup);\n      });\n    } else {\n      const defaultLanguageGroup = this.buildLanguageFormGroup({\n        code: 'en',\n        name: 'English'\n      });\n      languagesArray.push(defaultLanguageGroup);\n    }\n\n    if (languagesArray.length > 0) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      const activeLanguage = firstLanguage?.languageCode || 'en';\n      this.createForm.patchValue({ activeLanguage });\n    }\n\n    // Update validation for all language form groups based on channel type\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n\n    setTimeout(() => {\n      if (this.isViewMode) {\n        this.createForm.disable();\n      } else if (this.isEditMode) {\n        this.createForm.get('channelType')?.disable();\n        this.createForm.get('templateName')?.disable();\n      }\n    }, 200);\n\n    if (this.isViewMode) {\n      this.updateBreadcrumb('View Communication Template');\n      this.selectedViewLanguageIndex = 0;\n    } else if (this.isEditMode) {\n      this.updateBreadcrumb('Edit Communication Template');\n    }\n\n    // Initialize editors for email templates after form is populated\n    setTimeout(() => {\n      if (this.templateData.templateType?.toLowerCase() === 'email') {\n        this.initializeEditorsForAllLanguages();\n      }\n    }, 800);\n  }\n\n\n  private loadDatabaseFields() {\n    this.isLoadingFields = true;\n\n    this.templateService.getFieldsList().subscribe({\n      next: (response: any) => {\n        this.mapFieldsToVariables(response);\n        this.isLoadingFields = false;\n      },\n      error: () => {\n        this.isLoadingFields = false;\n        this.toastr.error('error', 'Error!');\n        this.variables = [];\n      }\n    });\n  }\n\n  private mapFieldsToVariables(apiResponse: any[]) {\n    if (!Array.isArray(apiResponse)) {\n      this.variables = [];\n      return;\n    }\n\n    this.variables = apiResponse.map(field => ({\n      name: field.name || field.fieldName || 'Unknown Field',\n      id: field.id || field.code || field.name?.toUpperCase().replace(/\\s+/g, '_') || 'UNKNOWN',\n      code: field.code || field.id || 'UNKNOWN_CODE'\n    }));\n  }\n\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\", [Validators.required]],\n      templateName: [null, [Validators.required]],\n      allowAccessFromAccount: [\"true\"],\n      activeLanguage: [\"en\"],\n      languages: this.fb.array([]),\n       entryPoint: ['Account', [Validators.required]],\n      recipientType: ['Customer', [Validators.required]],\n    });\n\n    this.createForm.get('channelType')?.valueChanges.subscribe(channelType => {\n      this.updateLanguageValidation(channelType);\n      this.handleChannelTypeChange(channelType);\n    });\n\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n  }\n\n  private updateLanguageValidation(channelType: string) {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n\n    languagesArray.controls.forEach(languageControl => {\n      const emailSubjectControl = languageControl.get('emailSubject');\n      const templateBodyControl = languageControl.get('templateBody');\n      const emailBodyControl = languageControl.get('emailBody');\n\n      if (channelType === 'email') {\n        emailSubjectControl?.setValidators([Validators.required]);\n        emailBodyControl?.setValidators([Validators.required]);\n        templateBodyControl?.clearValidators();\n      } else {\n        emailSubjectControl?.clearValidators();\n        emailBodyControl?.clearValidators();\n        templateBodyControl?.setValidators([Validators.required]);\n      }\n\n      emailSubjectControl?.updateValueAndValidity();\n      templateBodyControl?.updateValueAndValidity();\n      emailBodyControl?.updateValueAndValidity();\n    });\n  }\n\n  buildLanguagesFormArray(data?: any[]) {\n    const formArray = new FormArray([]);\n\n    if (data && data.length > 0) {\n      data.forEach((o) => {\n        formArray.push(this.buildLanguageFormGroup(o));\n      });\n    } else if (this.allLanguages && this.allLanguages.length > 0) {\n      formArray.push(this.buildLanguageFormGroup(this.allLanguages[0]));\n    }\n\n    return formArray;\n  }\n\n  buildLanguageFormGroup(data?: any) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null], // Validators will be set by updateLanguageValidation\n      emailBody: [null], // Validators will be set by updateLanguageValidation\n    });\n  }\n\n  get fValue(): any {\n    return this.createForm.value;\n  }\n\n  shouldShowLanguageFields(languageIndex: number): boolean {\n    if (this.isViewMode) {\n      return languageIndex === 0;\n    }\n    const activeLanguage = this.fValue?.activeLanguage;\n    const currentLanguage = this.fValue?.languages?.[languageIndex];\n    return activeLanguage === currentLanguage?.languageCode;\n  }\n\n  selectViewLanguage(index: number): void {\n    this.selectedViewLanguageIndex = index;\n  }\n\n  getSelectedLanguageDetail(): any {\n    if (this.isViewMode && this.currentTemplateData?.communicationTemplateDetails?.length > 0) {\n      return this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex] ||\n             this.currentTemplateData.communicationTemplateDetails[0];\n    }\n    return null;\n  }\n\n  getFormValidationErrors() {\n    let formErrors: any = {};\n\n    Object.keys(this.createForm.controls).forEach(key => {\n      const controlErrors = this.createForm.get(key)?.errors;\n      if (controlErrors) {\n        formErrors[key] = controlErrors;\n      }\n    });\n\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    if (languagesArray) {\n      languagesArray.controls.forEach((control, index) => {\n        const formGroup = control as FormGroup;\n        Object.keys(formGroup.controls).forEach(fieldKey => {\n          const fieldControl = formGroup.get(fieldKey);\n          if (fieldControl?.errors) {\n            if (!formErrors.languages) formErrors.languages = {};\n            if (!formErrors.languages[index]) formErrors.languages[index] = {};\n            formErrors.languages[index][fieldKey] = fieldControl.errors;\n          }\n        });\n      });\n    }\n\n    return formErrors;\n  }\n\n  openMapVariableModal(event: {index: number, value: string}, template: TemplateRef<any>) {\n    this.selectedVariable = event;\n    this.mapVarModalRef = this.modalService.show(template, {\n      animated: true,\n    })\n  }\n\n  assignVariable() {\n    this.mapVarModalRef.hide();\n    if (this.selectedVariable?.value) {\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\n      this.selectedVariable = { value: null, index: -1 };\n    }\n  }\n\n  updateTemplateValue(template: string, index: number) {\n    const languageControl = (this.createForm.get('languages') as FormArray).at(index);\n    const channelType = this.createForm.get('channelType')?.value;\n\n    console.log('updateTemplateValue called with:', { template, index, channelType });\n\n    if (channelType === 'email') {\n      // Update the form control with the plain text template (already in correct format)\n      languageControl.patchValue({ emailBody: template });\n      languageControl.get('emailBody')?.updateValueAndValidity();\n\n      // Temporarily disable the content change handler to avoid circular updates\n      this.isUpdatingFromMapping = true;\n\n      // Update the editor content directly with the plain text converted to HTML\n      const htmlContent = this.plainTextToHtml(template);\n      this.htmlContents[index] = htmlContent;\n\n      console.log('Template value updated for email at index:', index);\n      console.log('Plain text template:', template);\n      console.log('HTML content for editor:', htmlContent);\n\n      // Re-enable the content change handler after a short delay\n      setTimeout(() => {\n        this.isUpdatingFromMapping = false;\n      }, 200);\n    } else {\n      languageControl.patchValue({ templateBody: template });\n      languageControl.get('templateBody')?.updateValueAndValidity();\n    }\n    this.createForm.updateValueAndValidity();\n  }\n\n  openAddLangModal(template: TemplateRef<any>) {\n    this.addLangModalRef = this.modalService.show(template, { animated: true });\n  }\n\n  addLanguage() {\n    this.addLangModalRef?.hide();\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\n\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    const isLanguageAlreadySelected = languagesArray.controls.some(control =>\n      control.get('languageCode')?.value === this.selectedLanguage\n    );\n\n    if (isLanguageAlreadySelected) {\n      this.toastr.error('The same language is already selected.', 'Error!');\n      return;\n    }\n\n    const langFormGroup = this.buildLanguageFormGroup(language);\n    languagesArray.push(langFormGroup);\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n    this.createForm.updateValueAndValidity();\n\n    // Initialize editor for new language if channel type is email\n    if (this.createForm.get('channelType')?.value === 'email') {\n      this.initializeEditorForLanguage(languagesArray.length - 1);\n    }\n  }\n\n  removeLanguage(index: number) {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n\n    if (languagesArray.length <= 1) {\n      return;\n    }\n\n    const removedLanguage = languagesArray.at(index).value;\n    languagesArray.removeAt(index);\n\n    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      if (firstLanguage) {\n        this.createForm.patchValue({ activeLanguage: firstLanguage.languageCode });\n      }\n    }\n\n    this.createForm.updateValueAndValidity();\n  }\n\n  createTemplate() {\n    this.markFormGroupTouched(this.createForm);\n\n    if (this.createForm.invalid) {\n      this.toastr.error('Please fill all required fields.', 'Error');\n      return;\n    }\n\n    const formValue = this.createForm.value;\n    const hasTemplateBody = formValue.channelType === 'email'\n      ? formValue.languages?.some((lang: any) => lang.emailBody && lang.emailBody.trim())\n      : formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());\n\n    if (!hasTemplateBody) {\n      this.toastr.error('Please enter template body content.', 'Error');\n      return;\n    }\n\n    if (this.hasUnmappedVariables(formValue.languages)) {\n      this.toastr.error('Please map all variables and continue creating template.', 'Error');\n      return;\n    }\n\n    if (formValue.channelType === 'email') {\n      const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());\n      if (!hasEmailSubject) {\n        this.toastr.error('Please enter email subject for all languages.', 'Error');\n        return;\n      }\n    }\n\n    const getTemplateType = (channelType: string): string => {\n      switch (channelType.toLowerCase()) {\n        case 'email': return 'Email';\n        case 'sms': return 'SMS';\n        case 'letter': return 'Letter';\n        default: return 'Email';\n      }\n    };\n\n    const getLanguageName = (languageCode: string): string => {\n      const language = this.allLanguages.find(lang => lang.code === languageCode);\n      return language ? language.name : 'English';\n    };\n\n    const communicationTemplateDetails = formValue.languages.map((lang: any) => {\n      let bodyContent = '';\n\n      if (formValue.channelType === 'email') {\n        // For email templates, ensure we send plain text (already converted in form control)\n        bodyContent = lang.emailBody || \"\";\n      } else {\n        bodyContent = lang.templateBody || \"\";\n      }\n\n      const detail: any = {\n        \"Language\": getLanguageName(lang.languageCode),\n        \"body\": bodyContent\n      };\n\n      if (formValue.channelType === 'email') {\n        detail.Subject = lang.emailSubject || \"\";\n      }\n\n      return detail;\n    });\n\n    const json = {\n      \"templateType\": getTemplateType(formValue.channelType),\n      \"Name\": formValue.templateName,\n      \"isAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n      \"CommunicationTemplateDetails\": communicationTemplateDetails,\n      \"entryPoint\": formValue.entryPoint,\n      \"recipientType\": formValue.recipientType\n    };\n\n    this.templateService.saveCommunicationTemplate(json).subscribe({\n      next: () => {\n        this.toastr.success(`The Template \"${json.Name}\" has been created successfully.`, \"Success!\");\n        this.router.navigate(['communication/search-communication-templates']);\n      },\n      error: (error) => {\n        this.toastr.error(error, \"Error!\");\n      }\n    });\n  }\n\n  updateTemplate() {\n    this.markFormGroupTouched(this.createForm);\n\n    if (this.createForm.invalid) {\n      // Debug: Log validation errors to help identify the issue\n      console.log('Form validation errors:', this.getFormValidationErrors());\n      console.log('Form value:', this.createForm.getRawValue());\n      this.toastr.error('Please fill in all required fields.', 'Error');\n      return;\n    }\n\n    const formValue = this.createForm.getRawValue();\n    const hasTemplateBody = formValue.channelType === 'email'\n      ? formValue.languages?.some((lang: any) => lang.emailBody && lang.emailBody.trim())\n      : formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());\n\n    if (!hasTemplateBody) {\n      this.toastr.error('Please enter template body content.', 'Error');\n      return;\n    }\n\n    if (this.hasUnmappedVariables(formValue.languages)) {\n      this.toastr.error('Please map all variables and continue creating template.', 'Error');\n      return;\n    }\n\n    if (!formValue.channelType) {\n      this.toastr.error('Please select a channel type.', 'Error');\n      return;\n    }\n\n    if (formValue.channelType === 'email') {\n      const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());\n      if (!hasEmailSubject) {\n        this.toastr.error('Please enter email subject for all languages.', 'Error');\n        return;\n      }\n    }\n\n    const getTemplateType = (channelType: string): string => {\n      if (!channelType || typeof channelType !== 'string') {\n        return 'Email';\n      }\n      switch (channelType.toLowerCase()) {\n        case 'email': return 'Email';\n        case 'sms': return 'SMS';\n        case 'letter': return 'Letter';\n        default: return 'Email';\n      }\n    };\n\n    const getLanguageName = (languageCode: string): string => {\n      if (!languageCode) return 'English';\n      const language = this.allLanguages.find(lang => lang.code === languageCode);\n      return language ? language.name : 'English';\n    };\n\n    const communicationTemplateDetails = formValue.languages?.map((lang: any) => {\n      let bodyContent = '';\n\n      if (formValue.channelType === 'email') {\n        // For email templates, ensure we send plain text (already converted in form control)\n        bodyContent = lang.emailBody || \"\";\n      } else {\n        bodyContent = lang.templateBody || \"\";\n      }\n\n      const detail: any = {\n        \"Language\": getLanguageName(lang.languageCode),\n        \"body\": bodyContent\n      };\n\n      if (formValue.channelType === 'email') {\n        detail.Subject = lang.emailSubject || \"\";\n      }\n\n      return detail;\n    }) || [];\n\n    const json = {\n      \"id\": this.templateId,\n      \"templateType\": getTemplateType(formValue.channelType),\n      \"Name\": formValue.templateName || \"\",\n      \"IsAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n      \"CommunicationTemplateDetails\": communicationTemplateDetails,\n      \"entryPoint\": formValue.entryPoint,\n      \"recipientType\": formValue.recipientType,\n    };\n\n    this.templateService.updateCcmTemplate(json).subscribe({\n      next: () => {\n        this.toastr.success(`The Template \"${json.Name}\" has been updated successfully.`, \"Success!\");\n        this.router.navigate(['communication/search-communication-templates']);\n      },\n      error: (error) => {\n        this.toastr.error(error, \"Error!\");\n      }\n    });\n  }\n\n  private loadLanguageList() {\n    this.templateService.languageList().subscribe({\n      next: (response: any) => {\n        this.mapLanguageResponse(response);\n      },\n      error: (error) => {\n        this.toastr.error(error, 'Error');\n        this.allLanguages = [{ name: \"English\", code: \"en\" }];\n        this.initializeDefaultLanguage();\n      }\n    });\n  }\n\n  private mapLanguageResponse(apiResponse: any) {\n    if (!apiResponse || !Array.isArray(apiResponse)) {\n      this.allLanguages = [{ name: \"English\", code: \"en\" }];\n      this.initializeDefaultLanguage();\n      return;\n    }\n\n    this.allLanguages = apiResponse.map(lang => ({\n      name: lang.name || lang.itemName || 'Unknown Language',\n      code: lang.code || lang.itemCode || lang.name?.toLowerCase().substring(0, 2) || 'en'\n    }));\n\n    if (this.allLanguages.length === 0) {\n      this.allLanguages = [{ name: \"English\", code: \"en\" }];\n    }\n\n    this.initializeDefaultLanguage();\n  }\n\n  private initializeDefaultLanguage() {\n    if (this.createForm && this.allLanguages.length > 0) {\n      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;\n      if (!currentActiveLanguage || !this.allLanguages.find(lang => lang.code === currentActiveLanguage)) {\n        this.createForm.patchValue({ activeLanguage: this.allLanguages[0].code });\n\n        const languagesArray = this.createForm.get('languages') as FormArray;\n        if (languagesArray.length === 0) {\n          const defaultLanguageGroup = this.buildLanguageFormGroup(this.allLanguages[0]);\n          languagesArray.push(defaultLanguageGroup);\n          // Update validation for the newly added language group\n          this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n        }\n      }\n    }\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      if (control instanceof FormGroup || control instanceof FormArray) {\n        this.markFormGroupTouched(control);\n      } else {\n        control?.markAsTouched();\n      }\n    });\n  }\n\n  isFieldInvalid(fieldName: string): boolean {\n    const field = this.createForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n\n  getFieldErrorMessage(fieldName: string): string {\n    const field = this.createForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n\n  isLanguageFieldInvalid(languageIndex: number, fieldName: string): boolean {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    const languageGroup = languagesArray.at(languageIndex) as FormGroup;\n    const field = languageGroup?.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n\n  getLanguageFieldErrorMessage(languageIndex: number, fieldName: string): string {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    const languageGroup = languagesArray.at(languageIndex) as FormGroup;\n    const field = languageGroup?.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n\n  private hasUnmappedVariables(languages: any[]): boolean {\n    if (!languages || languages.length === 0) {\n      return false;\n    }\n\n    const channelType = this.createForm.get('channelType')?.value;\n    return languages.some(lang => {\n      const templateBody = channelType === 'email' ? (lang.emailBody || '') : (lang.templateBody || '');\n      return templateBody.includes('<<Var>>');\n    });\n  }\n\n  private getFieldDisplayName(fieldName: string): string {\n    const fieldNames: { [key: string]: string } = {\n      'templateName': 'Template Name',\n      'channelType': 'Channel Type',\n      'emailSubject': 'Subject Line',\n      'templateBody': 'Template Body',\n      'entryPoint': 'Entry Point',\n      'recipientType': 'Recipient Type'\n    };\n    return fieldNames[fieldName] || fieldName;\n  }\n\n  insertVariable() {\n    const activeLanguageIndex = this.getActiveLanguageIndex();\n    if (activeLanguageIndex === -1) return;\n\n    const channelType = this.createForm.get('channelType')?.value;\n    const languageControl = (this.createForm.get('languages') as FormArray).at(activeLanguageIndex);\n\n    if (channelType === 'email') {\n      // For email templates using ngx-editor\n      const editor = this.editors[activeLanguageIndex];\n      if (editor && editor.view) {\n        try {\n          // Insert the variable as plain text to preserve the angle brackets\n          editor.commands.insertText('<<Var>>').exec();\n\n          // Debug: Log to console to verify insertion\n          console.log('Variable inserted into editor at index:', activeLanguageIndex);\n\n          // The editor content will be automatically synced via ngModel binding\n        } catch (error) {\n          console.error('Error inserting variable into editor:', error);\n        }\n      } else {\n        console.warn('Editor not available at index:', activeLanguageIndex);\n        // Fallback: Initialize editor if not available\n        this.initializeEditorForLanguage(activeLanguageIndex);\n      }\n    } else {\n      // For SMS/Letter templates using textarea\n      const control = languageControl.get('templateBody');\n      const currentValue = control.value || '';\n\n      const textareaElement = document.getElementById('templateBody') as HTMLTextAreaElement;\n      if (!textareaElement) return;\n\n      const cursorPosition = textareaElement.selectionStart;\n\n      const newValue = currentValue.substring(0, cursorPosition) +\n                      '<<Var>>' +\n                      currentValue.substring(textareaElement.selectionEnd || cursorPosition);\n\n      control.setValue(newValue);\n      control.updateValueAndValidity();\n      setTimeout(() => {\n        textareaElement.focus();\n        textareaElement.setSelectionRange(cursorPosition + 7, cursorPosition + 7);\n      }, 0);\n    }\n  }\n\n  getActiveLanguageIndex(): number {\n    const activeLanguage = this.createForm.get('activeLanguage').value;\n    const languages = this.createForm.get('languages') as FormArray;\n\n    for (let i = 0; i < languages.length; i++) {\n      const lang = languages.at(i).get('languageCode').value;\n      if (lang === activeLanguage) {\n        return i;\n      }\n    }\n    return -1;\n  }\n\n  shouldRestrictHtmlTags(): boolean {\n    return this.createForm?.get('channelType')?.value !== 'sms';\n  }\n\n  getCurrentTemplateBody(languageIndex: number): string {\n    const channelType = this.createForm.get('channelType')?.value;\n    const languageControl = (this.createForm.get('languages') as FormArray).at(languageIndex);\n\n    if (channelType === 'email') {\n      // For email templates, return the plain text content from form control\n      // This ensures template variable mapping and preview work correctly\n      const content = languageControl.get('emailBody')?.value || '';\n      return content; // Already converted to plain text in onEditorContentChange\n    } else {\n      return languageControl.get('templateBody')?.value || '';\n    }\n  }\n\n  isLanguageFieldInvalidForCurrentChannel(languageIndex: number): boolean {\n    const channelType = this.createForm.get('channelType')?.value;\n    const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';\n    return this.isLanguageFieldInvalid(languageIndex, fieldName);\n  }\n\n  getLanguageFieldErrorMessageForCurrentChannel(languageIndex: number): string {\n    const channelType = this.createForm.get('channelType')?.value;\n    const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';\n    return this.getLanguageFieldErrorMessage(languageIndex, fieldName);\n  }\n\n  // NGX Editor Methods\n  initializeEditorForLanguage(index: number): void {\n    if (!this.editors[index]) {\n      try {\n        this.editors[index] = new Editor({\n          history: true,\n          keyboardShortcuts: true,\n          inputRules: true,\n        });\n        this.htmlContents[index] = '';\n        console.log('Editor initialized successfully for index:', index);\n      } catch (error) {\n        console.error('Failed to initialize editor for index:', index, error);\n        // Fallback: try with minimal configuration\n        try {\n          this.editors[index] = new Editor();\n          this.htmlContents[index] = '';\n          console.log('Editor initialized with minimal config for index:', index);\n        } catch (fallbackError) {\n          console.error('Failed to initialize editor even with minimal config:', fallbackError);\n        }\n      }\n    }\n  }\n\n  initializeEditorsForAllLanguages(): void {\n    const channelType = this.createForm.get('channelType')?.value;\n    if (channelType === 'email') {\n      const languagesArray = this.createForm.get('languages') as FormArray;\n      for (let i = 0; i < languagesArray.length; i++) {\n        try {\n          this.initializeEditorForLanguage(i);\n          // Set initial content from form control - convert plain text to HTML\n          const emailBodyControl = languagesArray.at(i).get('emailBody');\n          if (emailBodyControl?.value) {\n            // Convert plain text template to HTML for the editor\n            this.htmlContents[i] = this.plainTextToHtml(emailBodyControl.value);\n            console.log('Initialized editor content for index:', i);\n            console.log('Plain text from form:', emailBodyControl.value);\n            console.log('HTML for editor:', this.htmlContents[i]);\n          }\n        } catch (error) {\n          console.error('Error initializing editor for language index:', i, error);\n        }\n      }\n    }\n  }\n\n  onEditorContentChange(index: number): void {\n    // Skip processing if we're updating from template mapping to avoid circular updates\n    if (this.isUpdatingFromMapping) {\n      console.log('Skipping editor content change - updating from mapping');\n      return;\n    }\n\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    const emailBodyControl = languagesArray.at(index).get('emailBody');\n    if (emailBodyControl) {\n      // Convert HTML content to plain text for template processing\n      const plainTextContent = this.htmlToPlainText(this.htmlContents[index] || '');\n\n      // Debug: Log content changes\n      console.log('Editor content changed at index:', index);\n      console.log('Raw HTML content:', this.htmlContents[index]);\n      console.log('Plain text content:', plainTextContent);\n\n      emailBodyControl.setValue(plainTextContent);\n      emailBodyControl.updateValueAndValidity();\n    }\n  }\n\n  destroyEditor(index: number): void {\n    if (this.editors[index]) {\n      this.editors[index].destroy();\n      this.editors[index] = null;\n      this.htmlContents[index] = '';\n    }\n  }\n\n  handleChannelTypeChange(channelType: string): void {\n    if (channelType === 'email') {\n      // Initialize editors for email templates with a longer delay to ensure DOM is ready\n      setTimeout(() => {\n        this.initializeEditorsForAllLanguages();\n      }, 500);\n    } else {\n      // Destroy editors for non-email templates\n      this.editors.forEach((editor, index) => {\n        if (editor) {\n          this.destroyEditor(index);\n        }\n      });\n    }\n  }\n\n  // Helper method to ensure template variables are properly formatted\n  private normalizeTemplateVariables(content: string): string {\n    // Convert HTML entities back to angle brackets for template processing\n    return content\n      .replace(/&lt;&lt;/g, '<<')\n      .replace(/&gt;&gt;/g, '>>')\n      .replace(/&amp;/g, '&');\n  }\n\n  // Helper method to convert HTML content to plain text for template processing\n  private htmlToPlainText(html: string): string {\n    if (!html) return '';\n\n    console.log('Converting HTML to plain text:', html);\n\n    // Create a temporary div element to parse HTML\n    const tempDiv = document.createElement('div');\n    tempDiv.innerHTML = html;\n\n    // Get plain text content\n    let plainText = tempDiv.textContent || tempDiv.innerText || '';\n\n    console.log('Extracted plain text:', plainText);\n\n    // Normalize template variables\n    plainText = this.normalizeTemplateVariables(plainText);\n\n    console.log('After normalization:', plainText);\n\n    return plainText;\n  }\n\n  // Helper method to convert plain text back to HTML for ngx-editor\n  private plainTextToHtml(text: string): string {\n    if (!text) return '';\n\n    console.log('Converting plain text to HTML:', text);\n\n    // First, escape any existing HTML entities to prevent double-encoding\n    let processedText = text\n      .replace(/&/g, '&amp;')\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;');\n\n    console.log('After HTML escaping:', processedText);\n\n    // Split text into lines and process each line\n    const lines = processedText.split('\\n');\n    const htmlLines = lines.map(line => {\n      if (line.trim() === '') {\n        return '<p><br></p>'; // Empty line\n      } else {\n        return `<p>${line}</p>`; // Wrap in paragraph\n      }\n    });\n\n    const result = htmlLines.join('');\n    console.log('Final HTML result:', result);\n\n    return result;\n  }\n\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAeC,SAAS,EAAqBC,gBAAgB,QAAQ,eAAe;AAC9G,SACEC,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAAqBC,cAAc,QAAQ,qBAAqB;AAChE,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,0BAA0B,QAAQ,yDAAyD;AACpG,SAASC,wBAAwB,QAAQ,sDAAsD;AAC/F,SAASC,yBAAyB,QAAQ,wDAAwD;AAClG,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,MAAM,EAAEC,eAAe,QAAiB,YAAY;AAmBtD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EA4ClCC,YAAA;IA3CQ,KAAAC,EAAE,GAAgBtB,MAAM,CAACI,WAAW,CAAC;IACrC,KAAAmB,YAAY,GAAmBvB,MAAM,CAACW,cAAc,CAAC;IACrD,KAAAa,KAAK,GAAmBxB,MAAM,CAACS,cAAc,CAAC;IAC9C,KAAAgB,MAAM,GAAWzB,MAAM,CAACU,MAAM,CAAC;IAC/B,KAAAgB,eAAe,GAAoB1B,MAAM,CAACiB,eAAe,CAAC;IAC1D,KAAAU,MAAM,GAAkB3B,MAAM,CAACY,aAAa,CAAC;IAErD,KAAAgB,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAA+B,CAAE,CAC3C;IAED,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,UAAU,GAAkB,IAAI;IAChC,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,mBAAmB,GAAQ,IAAI;IAC/B,KAAAC,yBAAyB,GAAW,CAAC;IACrC,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,SAAS,GAAgD,EAAE;IAC3D,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,gBAAgB,GAAmC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,CAAC;IAAC,CAAE;IAC7E,KAAAC,gBAAgB,GAAW,IAAI;IAK/B;IACA,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAAC,qBAAqB,GAAY,KAAK;IACtC,KAAAC,OAAO,GAAY,CACjB,CAAC,MAAM,EAAE,QAAQ,CAAC,EAClB,CAAC,WAAW,CAAC,EACb,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,cAAc,EAAE,aAAa,CAAC,EAC/B,CAAC;MAAEC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAAC,CAAE,CAAC,EACnD,CAAC,MAAM,CAAC,EACR,CAAC,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,CAAC,CAC/D;IAGC,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAAC5B,KAAK,CAAC6B,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAI,CAACrB,UAAU,GAAGqB,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI;MAEtC,MAAME,GAAG,GAAG,IAAI,CAAC9B,MAAM,CAAC8B,GAAG;MAC3B,IAAIA,GAAG,CAACC,QAAQ,CAAC,6BAA6B,CAAC,EAAE;QAC/C,IAAI,CAAC1B,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC0B,gBAAgB,CAAC,6BAA6B,CAAC;MACtD,CAAC,MAAM,IAAIF,GAAG,CAACC,QAAQ,CAAC,6BAA6B,CAAC,EAAE;QACtD,IAAI,CAAC1B,UAAU,GAAG,KAAK;QACvB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC0B,gBAAgB,CAAC,6BAA6B,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAAC3B,UAAU,GAAG,KAAK;QACvB,IAAI,CAACC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC0B,gBAAgB,CAAC,+BAA+B,CAAC;MACxD;MAEA,IAAI,IAAI,CAACzB,UAAU,KAAK,IAAI,CAACF,UAAU,IAAI,IAAI,CAACC,UAAU,CAAC,EAAE;QAC3D,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,CAAC1B,UAAU,CAAC;MACxC;IACF,CAAC,CAAC;EACJ;EAEA2B,WAAWA,CAAA;IACT,IAAI,CAACf,OAAO,CAACgB,OAAO,CAACC,MAAM,IAAG;MAC5B,IAAIA,MAAM,EAAE;QACVA,MAAM,CAACC,OAAO,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;EAEQL,gBAAgBA,CAAC5B,KAAa;IACpC,IAAI,CAACD,cAAc,GAAG,CACpB;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAEA;IAAK,CAAE,CACjB;EACH;EAEQ6B,gBAAgBA,CAAC1B,UAAkB;IACzC,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACI,YAAY,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACrC,eAAe,CAACsC,YAAY,EAAE,CAACV,SAAS,CAAC;QAC5CW,IAAI,EAAGC,gBAAqB,IAAI;UAC9B,IAAI,CAACC,mBAAmB,CAACD,gBAAgB,CAAC;UAC1C,IAAI,CAACE,wBAAwB,CAACpC,UAAU,CAAC;QAC3C,CAAC;QACDqC,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAChC,YAAY,GAAG,CAAC;YAAEiC,IAAI,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAI,CAAE,CAAC;UACrD,IAAI,CAACH,wBAAwB,CAACpC,UAAU,CAAC;QAC3C;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACoC,wBAAwB,CAACpC,UAAU,CAAC;IAC3C;EACF;EAEQoC,wBAAwBA,CAACpC,UAAkB;IACjD,IAAI,CAACN,eAAe,CAAC8C,iBAAiB,CAACxC,UAAU,CAAC,CAACsB,SAAS,CAAC;MAC3DW,IAAI,EAAGQ,QAAa,IAAI;QACtB,IAAI,CAACvC,YAAY,GAAGuC,QAAQ;QAC5B,IAAI,CAACC,4BAA4B,CAACD,QAAQ,CAAC;QAC3C,IAAI,CAACxC,SAAS,GAAG,KAAK;MACxB,CAAC;MACDoC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACN,MAAM,CAAC0C,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;MACpC;KACD,CAAC;EACJ;EAEA,IAAIM,KAAKA,CAAA;IACT,OAAO,IAAI,CAACC,UAAU,EAAEC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK,EAAEqC,WAAW,EAAE,KAAK,KAAK;EAC5E;EAGUJ,4BAA4BA,CAACK,WAAgB;IACnD,IAAI,CAACA,WAAW,EAAE;IAElB,MAAM7C,YAAY,GAAG6C,WAAW,CAACC,IAAI,IAAID,WAAW;IACpD,MAAME,eAAe,GAAG/C,YAAY,CAACgD,4BAA4B,IAAI,EAAE;IACvE,IAAI,CAAC/C,mBAAmB,GAAGD,YAAY;IAEvC;IACA,MAAMiD,gBAAgB,GAAGjD,YAAY,CAACkD,2BAA2B,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;IAE7F,IAAI,CAACR,UAAU,CAACS,UAAU,CAAC;MACzBC,WAAW,EAAEpD,YAAY,CAACqD,YAAY,EAAET,WAAW,EAAE,IAAI,OAAO;MAChEU,YAAY,EAAEtD,YAAY,CAACoC,IAAI,IAAI,EAAE;MACrCmB,sBAAsB,EAAEN,gBAAgB;MACxCO,UAAU,EAAExD,YAAY,CAACwD,UAAU,IAAI,EAAE;MACzCC,aAAa,EAAEzD,YAAY,CAACyD,aAAa,IAAI;KAC9C,CAAC;IAEF,MAAMC,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,OAAOe,cAAc,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAClC6B,cAAc,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC5B;IAEA,IAAIZ,eAAe,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC9BkB,eAAe,CAACrB,OAAO,CAAEkC,MAAW,IAAI;QACtC,IAAIC,YAAY,GAAG,IAAI;QACvB,MAAMC,YAAY,GAAGF,MAAM,CAACG,QAAQ,IAAI,SAAS;QAEjD,IAAI,IAAI,CAAC5D,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC0B,MAAM,GAAG,CAAC,EAAE;UACrD,MAAMmC,aAAa,GAAG,IAAI,CAAC7D,YAAY,CAAC8D,IAAI,CAACC,IAAI,IAC/CA,IAAI,CAAC9B,IAAI,CAACQ,WAAW,EAAE,KAAKkB,YAAY,CAAClB,WAAW,EAAE,CACvD;UACD,IAAIoB,aAAa,EAAE;YACjBH,YAAY,GAAGG,aAAa,CAAC3B,IAAI;UACnC;QACF;QAEA,MAAM8B,iBAAiB,GAAG,IAAI,CAACC,sBAAsB,CAAC;UACpD/B,IAAI,EAAEwB,YAAY;UAClBzB,IAAI,EAAE0B;SACP,CAAC;QAEFK,iBAAiB,CAAChB,UAAU,CAAC;UAC3BU,YAAY,EAAEA,YAAY;UAC1BC,YAAY,EAAEA,YAAY;UAC1BO,YAAY,EAAET,MAAM,CAACU,OAAO,IAAI,EAAE;UAClCC,YAAY,EAAE,IAAI,CAACvE,YAAY,CAACqD,YAAY,EAAET,WAAW,EAAE,KAAK,OAAO,GAAGgB,MAAM,CAACY,IAAI,IAAI,EAAE,GAAG,EAAE;UAChGC,SAAS,EAAE,IAAI,CAACzE,YAAY,CAACqD,YAAY,EAAET,WAAW,EAAE,KAAK,OAAO,GAAGgB,MAAM,CAACY,IAAI,IAAI,EAAE,GAAG;SAC5F,CAAC;QAEF;QACA,IAAI,IAAI,CAACxE,YAAY,CAACqD,YAAY,EAAET,WAAW,EAAE,KAAK,OAAO,EAAE;UAC7D,MAAMpC,KAAK,GAAGkD,cAAc,CAAC7B,MAAM;UACnC;UACA,IAAI,CAAClB,YAAY,CAACH,KAAK,CAAC,GAAG,IAAI,CAACkE,eAAe,CAACd,MAAM,CAACY,IAAI,IAAI,EAAE,CAAC;QACpE;QACAd,cAAc,CAACiB,IAAI,CAACR,iBAAiB,CAAC;MACxC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMS,oBAAoB,GAAG,IAAI,CAACR,sBAAsB,CAAC;QACvD/B,IAAI,EAAE,IAAI;QACVD,IAAI,EAAE;OACP,CAAC;MACFsB,cAAc,CAACiB,IAAI,CAACC,oBAAoB,CAAC;IAC3C;IAEA,IAAIlB,cAAc,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMgD,aAAa,GAAGnB,cAAc,CAACoB,EAAE,CAAC,CAAC,CAAC,EAAEvE,KAAK;MACjD,MAAMwE,cAAc,GAAGF,aAAa,EAAEhB,YAAY,IAAI,IAAI;MAC1D,IAAI,CAACnB,UAAU,CAACS,UAAU,CAAC;QAAE4B;MAAc,CAAE,CAAC;IAChD;IAEA;IACA,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAACtC,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK,CAAC;IAExE0E,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACrF,UAAU,EAAE;QACnB,IAAI,CAAC8C,UAAU,CAACwC,OAAO,EAAE;MAC3B,CAAC,MAAM,IAAI,IAAI,CAACrF,UAAU,EAAE;QAC1B,IAAI,CAAC6C,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEuC,OAAO,EAAE;QAC7C,IAAI,CAACxC,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEuC,OAAO,EAAE;MAChD;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,IAAI,CAACtF,UAAU,EAAE;MACnB,IAAI,CAAC2B,gBAAgB,CAAC,6BAA6B,CAAC;MACpD,IAAI,CAACrB,yBAAyB,GAAG,CAAC;IACpC,CAAC,MAAM,IAAI,IAAI,CAACL,UAAU,EAAE;MAC1B,IAAI,CAAC0B,gBAAgB,CAAC,6BAA6B,CAAC;IACtD;IAEA;IACA0D,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACjF,YAAY,CAACqD,YAAY,EAAET,WAAW,EAAE,KAAK,OAAO,EAAE;QAC7D,IAAI,CAACuC,gCAAgC,EAAE;MACzC;IACF,CAAC,EAAE,GAAG,CAAC;EACT;EAGQlE,kBAAkBA,CAAA;IACxB,IAAI,CAACZ,eAAe,GAAG,IAAI;IAE3B,IAAI,CAACb,eAAe,CAAC4F,aAAa,EAAE,CAAChE,SAAS,CAAC;MAC7CW,IAAI,EAAGQ,QAAa,IAAI;QACtB,IAAI,CAAC8C,oBAAoB,CAAC9C,QAAQ,CAAC;QACnC,IAAI,CAAClC,eAAe,GAAG,KAAK;MAC9B,CAAC;MACD8B,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9B,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACZ,MAAM,CAAC0C,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC;QACpC,IAAI,CAAC/B,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEQiF,oBAAoBA,CAACxC,WAAkB;IAC7C,IAAI,CAACyC,KAAK,CAACC,OAAO,CAAC1C,WAAW,CAAC,EAAE;MAC/B,IAAI,CAACzC,SAAS,GAAG,EAAE;MACnB;IACF;IAEA,IAAI,CAACA,SAAS,GAAGyC,WAAW,CAAC2C,GAAG,CAACC,KAAK,KAAK;MACzCrD,IAAI,EAAEqD,KAAK,CAACrD,IAAI,IAAIqD,KAAK,CAACC,SAAS,IAAI,eAAe;MACtDC,EAAE,EAAEF,KAAK,CAACE,EAAE,IAAIF,KAAK,CAACpD,IAAI,IAAIoD,KAAK,CAACrD,IAAI,EAAEwD,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,SAAS;MACzFxD,IAAI,EAAEoD,KAAK,CAACpD,IAAI,IAAIoD,KAAK,CAACE,EAAE,IAAI;KACjC,CAAC,CAAC;EACL;EAEA5E,uBAAuBA,CAAA;IACrB,IAAI,CAAC2B,UAAU,GAAG,IAAI,CAACtD,EAAE,CAAC0G,KAAK,CAAC;MAC9B1C,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC9E,UAAU,CAACyH,QAAQ,CAAC,CAAC;MAC7CzC,YAAY,EAAE,CAAC,IAAI,EAAE,CAAChF,UAAU,CAACyH,QAAQ,CAAC,CAAC;MAC3CxC,sBAAsB,EAAE,CAAC,MAAM,CAAC;MAChCwB,cAAc,EAAE,CAAC,IAAI,CAAC;MACtBiB,SAAS,EAAE,IAAI,CAAC5G,EAAE,CAAC6G,KAAK,CAAC,EAAE,CAAC;MAC3BzC,UAAU,EAAE,CAAC,SAAS,EAAE,CAAClF,UAAU,CAACyH,QAAQ,CAAC,CAAC;MAC/CtC,aAAa,EAAE,CAAC,UAAU,EAAE,CAACnF,UAAU,CAACyH,QAAQ,CAAC;KAClD,CAAC;IAEF,IAAI,CAACrD,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEuD,YAAY,CAAC9E,SAAS,CAACgC,WAAW,IAAG;MACvE,IAAI,CAAC4B,wBAAwB,CAAC5B,WAAW,CAAC;MAC1C,IAAI,CAAC+C,uBAAuB,CAAC/C,WAAW,CAAC;IAC3C,CAAC,CAAC;IAEF,IAAI,CAAC4B,wBAAwB,CAAC,IAAI,CAACtC,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK,CAAC;EAC1E;EAEQyE,wBAAwBA,CAAC5B,WAAmB;IAClD,MAAMM,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IAEpEe,cAAc,CAAC0C,QAAQ,CAAC1E,OAAO,CAAC2E,eAAe,IAAG;MAChD,MAAMC,mBAAmB,GAAGD,eAAe,CAAC1D,GAAG,CAAC,cAAc,CAAC;MAC/D,MAAM4D,mBAAmB,GAAGF,eAAe,CAAC1D,GAAG,CAAC,cAAc,CAAC;MAC/D,MAAM6D,gBAAgB,GAAGH,eAAe,CAAC1D,GAAG,CAAC,WAAW,CAAC;MAEzD,IAAIS,WAAW,KAAK,OAAO,EAAE;QAC3BkD,mBAAmB,EAAEG,aAAa,CAAC,CAACnI,UAAU,CAACyH,QAAQ,CAAC,CAAC;QACzDS,gBAAgB,EAAEC,aAAa,CAAC,CAACnI,UAAU,CAACyH,QAAQ,CAAC,CAAC;QACtDQ,mBAAmB,EAAEG,eAAe,EAAE;MACxC,CAAC,MAAM;QACLJ,mBAAmB,EAAEI,eAAe,EAAE;QACtCF,gBAAgB,EAAEE,eAAe,EAAE;QACnCH,mBAAmB,EAAEE,aAAa,CAAC,CAACnI,UAAU,CAACyH,QAAQ,CAAC,CAAC;MAC3D;MAEAO,mBAAmB,EAAEK,sBAAsB,EAAE;MAC7CJ,mBAAmB,EAAEI,sBAAsB,EAAE;MAC7CH,gBAAgB,EAAEG,sBAAsB,EAAE;IAC5C,CAAC,CAAC;EACJ;EAEAC,uBAAuBA,CAAC9D,IAAY;IAClC,MAAM+D,SAAS,GAAG,IAAI5I,SAAS,CAAC,EAAE,CAAC;IAEnC,IAAI6E,IAAI,IAAIA,IAAI,CAACjB,MAAM,GAAG,CAAC,EAAE;MAC3BiB,IAAI,CAACpB,OAAO,CAAEoF,CAAC,IAAI;QACjBD,SAAS,CAAClC,IAAI,CAAC,IAAI,CAACP,sBAAsB,CAAC0C,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,IAAI,CAAC3G,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC0B,MAAM,GAAG,CAAC,EAAE;MAC5DgF,SAAS,CAAClC,IAAI,CAAC,IAAI,CAACP,sBAAsB,CAAC,IAAI,CAACjE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE;IAEA,OAAO0G,SAAS;EAClB;EAEAzC,sBAAsBA,CAACtB,IAAU;IAC/B,OAAO,IAAI,CAAC1D,EAAE,CAAC0G,KAAK,CAAC;MACnBjC,YAAY,EAAEf,IAAI,EAAET,IAAI;MACxByB,YAAY,EAAEhB,IAAI,EAAEV,IAAI;MACxBiC,YAAY,EAAE,CAAC,IAAI,CAAC;MACpBE,YAAY,EAAE,CAAC,IAAI,CAAC;MAAE;MACtBE,SAAS,EAAE,CAAC,IAAI,CAAC,CAAE;KACpB,CAAC;EACJ;EAEA,IAAIsC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACrE,UAAU,CAACnC,KAAK;EAC9B;EAEAyG,wBAAwBA,CAACC,aAAqB;IAC5C,IAAI,IAAI,CAACrH,UAAU,EAAE;MACnB,OAAOqH,aAAa,KAAK,CAAC;IAC5B;IACA,MAAMlC,cAAc,GAAG,IAAI,CAACgC,MAAM,EAAEhC,cAAc;IAClD,MAAMmC,eAAe,GAAG,IAAI,CAACH,MAAM,EAAEf,SAAS,GAAGiB,aAAa,CAAC;IAC/D,OAAOlC,cAAc,KAAKmC,eAAe,EAAErD,YAAY;EACzD;EAEAsD,kBAAkBA,CAAC3G,KAAa;IAC9B,IAAI,CAACN,yBAAyB,GAAGM,KAAK;EACxC;EAEA4G,yBAAyBA,CAAA;IACvB,IAAI,IAAI,CAACxH,UAAU,IAAI,IAAI,CAACK,mBAAmB,EAAE+C,4BAA4B,EAAEnB,MAAM,GAAG,CAAC,EAAE;MACzF,OAAO,IAAI,CAAC5B,mBAAmB,CAAC+C,4BAA4B,CAAC,IAAI,CAAC9C,yBAAyB,CAAC,IACrF,IAAI,CAACD,mBAAmB,CAAC+C,4BAA4B,CAAC,CAAC,CAAC;IACjE;IACA,OAAO,IAAI;EACb;EAEAqE,uBAAuBA,CAAA;IACrB,IAAIC,UAAU,GAAQ,EAAE;IAExBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9E,UAAU,CAAC0D,QAAQ,CAAC,CAAC1E,OAAO,CAAC+F,GAAG,IAAG;MAClD,MAAMC,aAAa,GAAG,IAAI,CAAChF,UAAU,CAACC,GAAG,CAAC8E,GAAG,CAAC,EAAEE,MAAM;MACtD,IAAID,aAAa,EAAE;QACjBJ,UAAU,CAACG,GAAG,CAAC,GAAGC,aAAa;MACjC;IACF,CAAC,CAAC;IAEF,MAAMhE,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,IAAIe,cAAc,EAAE;MAClBA,cAAc,CAAC0C,QAAQ,CAAC1E,OAAO,CAAC,CAACkG,OAAO,EAAEpH,KAAK,KAAI;QACjD,MAAMqH,SAAS,GAAGD,OAAoB;QACtCL,MAAM,CAACC,IAAI,CAACK,SAAS,CAACzB,QAAQ,CAAC,CAAC1E,OAAO,CAACoG,QAAQ,IAAG;UACjD,MAAMC,YAAY,GAAGF,SAAS,CAAClF,GAAG,CAACmF,QAAQ,CAAC;UAC5C,IAAIC,YAAY,EAAEJ,MAAM,EAAE;YACxB,IAAI,CAACL,UAAU,CAACtB,SAAS,EAAEsB,UAAU,CAACtB,SAAS,GAAG,EAAE;YACpD,IAAI,CAACsB,UAAU,CAACtB,SAAS,CAACxF,KAAK,CAAC,EAAE8G,UAAU,CAACtB,SAAS,CAACxF,KAAK,CAAC,GAAG,EAAE;YAClE8G,UAAU,CAACtB,SAAS,CAACxF,KAAK,CAAC,CAACsH,QAAQ,CAAC,GAAGC,YAAY,CAACJ,MAAM;UAC7D;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAOL,UAAU;EACnB;EAEAU,oBAAoBA,CAACC,KAAqC,EAAEC,QAA0B;IACpF,IAAI,CAAC5H,gBAAgB,GAAG2H,KAAK;IAC7B,IAAI,CAACE,cAAc,GAAG,IAAI,CAAC9I,YAAY,CAAC+I,IAAI,CAACF,QAAQ,EAAE;MACrDG,QAAQ,EAAE;KACX,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACH,cAAc,CAACI,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACjI,gBAAgB,EAAEC,KAAK,EAAE;MAChC,IAAI,CAACiI,iBAAiB,CAACC,gBAAgB,CAAC,IAAI,CAACnI,gBAAgB,CAAC;MAC9D,IAAI,CAACA,gBAAgB,GAAG;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,CAAC;MAAC,CAAE;IACpD;EACF;EAEAkI,mBAAmBA,CAACR,QAAgB,EAAE1H,KAAa;IACjD,MAAM6F,eAAe,GAAI,IAAI,CAAC3D,UAAU,CAACC,GAAG,CAAC,WAAW,CAAe,CAACmC,EAAE,CAACtE,KAAK,CAAC;IACjF,MAAM4C,WAAW,GAAG,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK;IAE7DoI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAAEV,QAAQ;MAAE1H,KAAK;MAAE4C;IAAW,CAAE,CAAC;IAEjF,IAAIA,WAAW,KAAK,OAAO,EAAE;MAC3B;MACAiD,eAAe,CAAClD,UAAU,CAAC;QAAEsB,SAAS,EAAEyD;MAAQ,CAAE,CAAC;MACnD7B,eAAe,CAAC1D,GAAG,CAAC,WAAW,CAAC,EAAEgE,sBAAsB,EAAE;MAE1D;MACA,IAAI,CAAC/F,qBAAqB,GAAG,IAAI;MAEjC;MACA,MAAMiI,WAAW,GAAG,IAAI,CAACnE,eAAe,CAACwD,QAAQ,CAAC;MAClD,IAAI,CAACvH,YAAY,CAACH,KAAK,CAAC,GAAGqI,WAAW;MAEtCF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEpI,KAAK,CAAC;MAChEmI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEV,QAAQ,CAAC;MAC7CS,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,WAAW,CAAC;MAEpD;MACA5D,UAAU,CAAC,MAAK;QACd,IAAI,CAACrE,qBAAqB,GAAG,KAAK;MACpC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLyF,eAAe,CAAClD,UAAU,CAAC;QAAEoB,YAAY,EAAE2D;MAAQ,CAAE,CAAC;MACtD7B,eAAe,CAAC1D,GAAG,CAAC,cAAc,CAAC,EAAEgE,sBAAsB,EAAE;IAC/D;IACA,IAAI,CAACjE,UAAU,CAACiE,sBAAsB,EAAE;EAC1C;EAEAmC,gBAAgBA,CAACZ,QAA0B;IACzC,IAAI,CAACa,eAAe,GAAG,IAAI,CAAC1J,YAAY,CAAC+I,IAAI,CAACF,QAAQ,EAAE;MAAEG,QAAQ,EAAE;IAAI,CAAE,CAAC;EAC7E;EAEAW,WAAWA,CAAA;IACT,IAAI,CAACD,eAAe,EAAER,IAAI,EAAE;IAC5B,MAAMxE,QAAQ,GAAG,IAAI,CAAC5D,YAAY,CAAC8D,IAAI,CAAC6C,CAAC,IAAIA,CAAC,EAAEzE,IAAI,KAAK,IAAI,CAAC5B,gBAAgB,CAAC;IAE/E,MAAMiD,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,MAAMsG,yBAAyB,GAAGvF,cAAc,CAAC0C,QAAQ,CAAC8C,IAAI,CAACtB,OAAO,IACpEA,OAAO,CAACjF,GAAG,CAAC,cAAc,CAAC,EAAEpC,KAAK,KAAK,IAAI,CAACE,gBAAgB,CAC7D;IAED,IAAIwI,yBAAyB,EAAE;MAC7B,IAAI,CAACxJ,MAAM,CAAC0C,KAAK,CAAC,wCAAwC,EAAE,QAAQ,CAAC;MACrE;IACF;IAEA,MAAMgH,aAAa,GAAG,IAAI,CAAC/E,sBAAsB,CAACL,QAAQ,CAAC;IAC3DL,cAAc,CAACiB,IAAI,CAACwE,aAAa,CAAC;IAClC,IAAI,CAACnE,wBAAwB,CAAC,IAAI,CAACtC,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK,CAAC;IACxE,IAAI,CAACmC,UAAU,CAACiE,sBAAsB,EAAE;IAExC;IACA,IAAI,IAAI,CAACjE,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK,KAAK,OAAO,EAAE;MACzD,IAAI,CAAC6I,2BAA2B,CAAC1F,cAAc,CAAC7B,MAAM,GAAG,CAAC,CAAC;IAC7D;EACF;EAEAwH,cAAcA,CAAC7I,KAAa;IAC1B,MAAMkD,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IAEpE,IAAIe,cAAc,CAAC7B,MAAM,IAAI,CAAC,EAAE;MAC9B;IACF;IAEA,MAAMyH,eAAe,GAAG5F,cAAc,CAACoB,EAAE,CAACtE,KAAK,CAAC,CAACD,KAAK;IACtDmD,cAAc,CAACC,QAAQ,CAACnD,KAAK,CAAC;IAE9B,IAAI,IAAI,CAACuG,MAAM,EAAEhC,cAAc,KAAKuE,eAAe,EAAEzF,YAAY,EAAE;MACjE,MAAMgB,aAAa,GAAGnB,cAAc,CAACoB,EAAE,CAAC,CAAC,CAAC,EAAEvE,KAAK;MACjD,IAAIsE,aAAa,EAAE;QACjB,IAAI,CAACnC,UAAU,CAACS,UAAU,CAAC;UAAE4B,cAAc,EAAEF,aAAa,CAAChB;QAAY,CAAE,CAAC;MAC5E;IACF;IAEA,IAAI,CAACnB,UAAU,CAACiE,sBAAsB,EAAE;EAC1C;EAEA4C,cAAcA,CAAA;IACZ,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC9G,UAAU,CAAC;IAE1C,IAAI,IAAI,CAACA,UAAU,CAAC+G,OAAO,EAAE;MAC3B,IAAI,CAAChK,MAAM,CAAC0C,KAAK,CAAC,kCAAkC,EAAE,OAAO,CAAC;MAC9D;IACF;IAEA,MAAMuH,SAAS,GAAG,IAAI,CAAChH,UAAU,CAACnC,KAAK;IACvC,MAAMoJ,eAAe,GAAGD,SAAS,CAACtG,WAAW,KAAK,OAAO,GACrDsG,SAAS,CAAC1D,SAAS,EAAEkD,IAAI,CAAEhF,IAAS,IAAKA,IAAI,CAACO,SAAS,IAAIP,IAAI,CAACO,SAAS,CAACmF,IAAI,EAAE,CAAC,GACjFF,SAAS,CAAC1D,SAAS,EAAEkD,IAAI,CAAEhF,IAAS,IAAKA,IAAI,CAACK,YAAY,IAAIL,IAAI,CAACK,YAAY,CAACqF,IAAI,EAAE,CAAC;IAE3F,IAAI,CAACD,eAAe,EAAE;MACpB,IAAI,CAAClK,MAAM,CAAC0C,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC;MACjE;IACF;IAEA,IAAI,IAAI,CAAC0H,oBAAoB,CAACH,SAAS,CAAC1D,SAAS,CAAC,EAAE;MAClD,IAAI,CAACvG,MAAM,CAAC0C,KAAK,CAAC,0DAA0D,EAAE,OAAO,CAAC;MACtF;IACF;IAEA,IAAIuH,SAAS,CAACtG,WAAW,KAAK,OAAO,EAAE;MACrC,MAAM0G,eAAe,GAAGJ,SAAS,CAAC1D,SAAS,EAAE+D,KAAK,CAAE7F,IAAS,IAAKA,IAAI,CAACG,YAAY,IAAIH,IAAI,CAACG,YAAY,CAACuF,IAAI,EAAE,CAAC;MAChH,IAAI,CAACE,eAAe,EAAE;QACpB,IAAI,CAACrK,MAAM,CAAC0C,KAAK,CAAC,+CAA+C,EAAE,OAAO,CAAC;QAC3E;MACF;IACF;IAEA,MAAM6H,eAAe,GAAI5G,WAAmB,IAAY;MACtD,QAAQA,WAAW,CAACR,WAAW,EAAE;QAC/B,KAAK,OAAO;UAAE,OAAO,OAAO;QAC5B,KAAK,KAAK;UAAE,OAAO,KAAK;QACxB,KAAK,QAAQ;UAAE,OAAO,QAAQ;QAC9B;UAAS,OAAO,OAAO;MACzB;IACF,CAAC;IAED,MAAMqH,eAAe,GAAIpG,YAAoB,IAAY;MACvD,MAAME,QAAQ,GAAG,IAAI,CAAC5D,YAAY,CAAC8D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7B,IAAI,KAAKwB,YAAY,CAAC;MAC3E,OAAOE,QAAQ,GAAGA,QAAQ,CAAC3B,IAAI,GAAG,SAAS;IAC7C,CAAC;IAED,MAAMY,4BAA4B,GAAG0G,SAAS,CAAC1D,SAAS,CAACR,GAAG,CAAEtB,IAAS,IAAI;MACzE,IAAIgG,WAAW,GAAG,EAAE;MAEpB,IAAIR,SAAS,CAACtG,WAAW,KAAK,OAAO,EAAE;QACrC;QACA8G,WAAW,GAAGhG,IAAI,CAACO,SAAS,IAAI,EAAE;MACpC,CAAC,MAAM;QACLyF,WAAW,GAAGhG,IAAI,CAACK,YAAY,IAAI,EAAE;MACvC;MAEA,MAAMX,MAAM,GAAQ;QAClB,UAAU,EAAEqG,eAAe,CAAC/F,IAAI,CAACL,YAAY,CAAC;QAC9C,MAAM,EAAEqG;OACT;MAED,IAAIR,SAAS,CAACtG,WAAW,KAAK,OAAO,EAAE;QACrCQ,MAAM,CAACuG,OAAO,GAAGjG,IAAI,CAACG,YAAY,IAAI,EAAE;MAC1C;MAEA,OAAOT,MAAM;IACf,CAAC,CAAC;IAEF,MAAMwG,IAAI,GAAG;MACX,cAAc,EAAEJ,eAAe,CAACN,SAAS,CAACtG,WAAW,CAAC;MACtD,MAAM,EAAEsG,SAAS,CAACpG,YAAY;MAC9B,6BAA6B,EAAEoG,SAAS,CAACnG,sBAAsB,KAAK,MAAM;MAC1E,8BAA8B,EAAEP,4BAA4B;MAC5D,YAAY,EAAE0G,SAAS,CAAClG,UAAU;MAClC,eAAe,EAAEkG,SAAS,CAACjG;KAC5B;IAED,IAAI,CAACjE,eAAe,CAAC6K,yBAAyB,CAACD,IAAI,CAAC,CAAChJ,SAAS,CAAC;MAC7DW,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACtC,MAAM,CAAC6K,OAAO,CAAC,iBAAiBF,IAAI,CAACG,IAAI,kCAAkC,EAAE,UAAU,CAAC;QAC7F,IAAI,CAAChL,MAAM,CAACiL,QAAQ,CAAC,CAAC,8CAA8C,CAAC,CAAC;MACxE,CAAC;MACDrI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1C,MAAM,CAAC0C,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;MACpC;KACD,CAAC;EACJ;EAEAsI,cAAcA,CAAA;IACZ,IAAI,CAACjB,oBAAoB,CAAC,IAAI,CAAC9G,UAAU,CAAC;IAE1C,IAAI,IAAI,CAACA,UAAU,CAAC+G,OAAO,EAAE;MAC3B;MACAd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACvB,uBAAuB,EAAE,CAAC;MACtEsB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAClG,UAAU,CAACgI,WAAW,EAAE,CAAC;MACzD,IAAI,CAACjL,MAAM,CAAC0C,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC;MACjE;IACF;IAEA,MAAMuH,SAAS,GAAG,IAAI,CAAChH,UAAU,CAACgI,WAAW,EAAE;IAC/C,MAAMf,eAAe,GAAGD,SAAS,CAACtG,WAAW,KAAK,OAAO,GACrDsG,SAAS,CAAC1D,SAAS,EAAEkD,IAAI,CAAEhF,IAAS,IAAKA,IAAI,CAACO,SAAS,IAAIP,IAAI,CAACO,SAAS,CAACmF,IAAI,EAAE,CAAC,GACjFF,SAAS,CAAC1D,SAAS,EAAEkD,IAAI,CAAEhF,IAAS,IAAKA,IAAI,CAACK,YAAY,IAAIL,IAAI,CAACK,YAAY,CAACqF,IAAI,EAAE,CAAC;IAE3F,IAAI,CAACD,eAAe,EAAE;MACpB,IAAI,CAAClK,MAAM,CAAC0C,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC;MACjE;IACF;IAEA,IAAI,IAAI,CAAC0H,oBAAoB,CAACH,SAAS,CAAC1D,SAAS,CAAC,EAAE;MAClD,IAAI,CAACvG,MAAM,CAAC0C,KAAK,CAAC,0DAA0D,EAAE,OAAO,CAAC;MACtF;IACF;IAEA,IAAI,CAACuH,SAAS,CAACtG,WAAW,EAAE;MAC1B,IAAI,CAAC3D,MAAM,CAAC0C,KAAK,CAAC,+BAA+B,EAAE,OAAO,CAAC;MAC3D;IACF;IAEA,IAAIuH,SAAS,CAACtG,WAAW,KAAK,OAAO,EAAE;MACrC,MAAM0G,eAAe,GAAGJ,SAAS,CAAC1D,SAAS,EAAE+D,KAAK,CAAE7F,IAAS,IAAKA,IAAI,CAACG,YAAY,IAAIH,IAAI,CAACG,YAAY,CAACuF,IAAI,EAAE,CAAC;MAChH,IAAI,CAACE,eAAe,EAAE;QACpB,IAAI,CAACrK,MAAM,CAAC0C,KAAK,CAAC,+CAA+C,EAAE,OAAO,CAAC;QAC3E;MACF;IACF;IAEA,MAAM6H,eAAe,GAAI5G,WAAmB,IAAY;MACtD,IAAI,CAACA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QACnD,OAAO,OAAO;MAChB;MACA,QAAQA,WAAW,CAACR,WAAW,EAAE;QAC/B,KAAK,OAAO;UAAE,OAAO,OAAO;QAC5B,KAAK,KAAK;UAAE,OAAO,KAAK;QACxB,KAAK,QAAQ;UAAE,OAAO,QAAQ;QAC9B;UAAS,OAAO,OAAO;MACzB;IACF,CAAC;IAED,MAAMqH,eAAe,GAAIpG,YAAoB,IAAY;MACvD,IAAI,CAACA,YAAY,EAAE,OAAO,SAAS;MACnC,MAAME,QAAQ,GAAG,IAAI,CAAC5D,YAAY,CAAC8D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7B,IAAI,KAAKwB,YAAY,CAAC;MAC3E,OAAOE,QAAQ,GAAGA,QAAQ,CAAC3B,IAAI,GAAG,SAAS;IAC7C,CAAC;IAED,MAAMY,4BAA4B,GAAG0G,SAAS,CAAC1D,SAAS,EAAER,GAAG,CAAEtB,IAAS,IAAI;MAC1E,IAAIgG,WAAW,GAAG,EAAE;MAEpB,IAAIR,SAAS,CAACtG,WAAW,KAAK,OAAO,EAAE;QACrC;QACA8G,WAAW,GAAGhG,IAAI,CAACO,SAAS,IAAI,EAAE;MACpC,CAAC,MAAM;QACLyF,WAAW,GAAGhG,IAAI,CAACK,YAAY,IAAI,EAAE;MACvC;MAEA,MAAMX,MAAM,GAAQ;QAClB,UAAU,EAAEqG,eAAe,CAAC/F,IAAI,CAACL,YAAY,CAAC;QAC9C,MAAM,EAAEqG;OACT;MAED,IAAIR,SAAS,CAACtG,WAAW,KAAK,OAAO,EAAE;QACrCQ,MAAM,CAACuG,OAAO,GAAGjG,IAAI,CAACG,YAAY,IAAI,EAAE;MAC1C;MAEA,OAAOT,MAAM;IACf,CAAC,CAAC,IAAI,EAAE;IAER,MAAMwG,IAAI,GAAG;MACX,IAAI,EAAE,IAAI,CAACtK,UAAU;MACrB,cAAc,EAAEkK,eAAe,CAACN,SAAS,CAACtG,WAAW,CAAC;MACtD,MAAM,EAAEsG,SAAS,CAACpG,YAAY,IAAI,EAAE;MACpC,6BAA6B,EAAEoG,SAAS,CAACnG,sBAAsB,KAAK,MAAM;MAC1E,8BAA8B,EAAEP,4BAA4B;MAC5D,YAAY,EAAE0G,SAAS,CAAClG,UAAU;MAClC,eAAe,EAAEkG,SAAS,CAACjG;KAC5B;IAED,IAAI,CAACjE,eAAe,CAACmL,iBAAiB,CAACP,IAAI,CAAC,CAAChJ,SAAS,CAAC;MACrDW,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACtC,MAAM,CAAC6K,OAAO,CAAC,iBAAiBF,IAAI,CAACG,IAAI,kCAAkC,EAAE,UAAU,CAAC;QAC7F,IAAI,CAAChL,MAAM,CAACiL,QAAQ,CAAC,CAAC,8CAA8C,CAAC,CAAC;MACxE,CAAC;MACDrI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1C,MAAM,CAAC0C,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;MACpC;KACD,CAAC;EACJ;EAEQjB,gBAAgBA,CAAA;IACtB,IAAI,CAAC1B,eAAe,CAACsC,YAAY,EAAE,CAACV,SAAS,CAAC;MAC5CW,IAAI,EAAGQ,QAAa,IAAI;QACtB,IAAI,CAACN,mBAAmB,CAACM,QAAQ,CAAC;MACpC,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1C,MAAM,CAAC0C,KAAK,CAACA,KAAK,EAAE,OAAO,CAAC;QACjC,IAAI,CAAChC,YAAY,GAAG,CAAC;UAAEiC,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAI,CAAE,CAAC;QACrD,IAAI,CAACuI,yBAAyB,EAAE;MAClC;KACD,CAAC;EACJ;EAEQ3I,mBAAmBA,CAACY,WAAgB;IAC1C,IAAI,CAACA,WAAW,IAAI,CAACyC,KAAK,CAACC,OAAO,CAAC1C,WAAW,CAAC,EAAE;MAC/C,IAAI,CAAC1C,YAAY,GAAG,CAAC;QAAEiC,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAE,CAAC;MACrD,IAAI,CAACuI,yBAAyB,EAAE;MAChC;IACF;IAEA,IAAI,CAACzK,YAAY,GAAG0C,WAAW,CAAC2C,GAAG,CAACtB,IAAI,KAAK;MAC3C9B,IAAI,EAAE8B,IAAI,CAAC9B,IAAI,IAAI8B,IAAI,CAAC2G,QAAQ,IAAI,kBAAkB;MACtDxI,IAAI,EAAE6B,IAAI,CAAC7B,IAAI,IAAI6B,IAAI,CAAC4G,QAAQ,IAAI5G,IAAI,CAAC9B,IAAI,EAAEQ,WAAW,EAAE,CAACmI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI;KACjF,CAAC,CAAC;IAEH,IAAI,IAAI,CAAC5K,YAAY,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC1B,YAAY,GAAG,CAAC;QAAEiC,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAE,CAAC;IACvD;IAEA,IAAI,CAACuI,yBAAyB,EAAE;EAClC;EAEQA,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAAClI,UAAU,IAAI,IAAI,CAACvC,YAAY,CAAC0B,MAAM,GAAG,CAAC,EAAE;MACnD,MAAMmJ,qBAAqB,GAAG,IAAI,CAACtI,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEpC,KAAK;MAC1E,IAAI,CAACyK,qBAAqB,IAAI,CAAC,IAAI,CAAC7K,YAAY,CAAC8D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7B,IAAI,KAAK2I,qBAAqB,CAAC,EAAE;QAClG,IAAI,CAACtI,UAAU,CAACS,UAAU,CAAC;UAAE4B,cAAc,EAAE,IAAI,CAAC5E,YAAY,CAAC,CAAC,CAAC,CAACkC;QAAI,CAAE,CAAC;QAEzE,MAAMqB,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;QACpE,IAAIe,cAAc,CAAC7B,MAAM,KAAK,CAAC,EAAE;UAC/B,MAAM+C,oBAAoB,GAAG,IAAI,CAACR,sBAAsB,CAAC,IAAI,CAACjE,YAAY,CAAC,CAAC,CAAC,CAAC;UAC9EuD,cAAc,CAACiB,IAAI,CAACC,oBAAoB,CAAC;UACzC;UACA,IAAI,CAACI,wBAAwB,CAAC,IAAI,CAACtC,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK,CAAC;QAC1E;MACF;IACF;EACF;EAEQiJ,oBAAoBA,CAAC3B,SAAgC;IAC3DN,MAAM,CAACC,IAAI,CAACK,SAAS,CAACzB,QAAQ,CAAC,CAAC1E,OAAO,CAAC+F,GAAG,IAAG;MAC5C,MAAMG,OAAO,GAAGC,SAAS,CAAClF,GAAG,CAAC8E,GAAG,CAAC;MAClC,IAAIG,OAAO,YAAYzJ,SAAS,IAAIyJ,OAAO,YAAY3J,SAAS,EAAE;QAChE,IAAI,CAACuL,oBAAoB,CAAC5B,OAAO,CAAC;MACpC,CAAC,MAAM;QACLA,OAAO,EAAEqD,aAAa,EAAE;MAC1B;IACF,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAACxF,SAAiB;IAC9B,MAAMD,KAAK,GAAG,IAAI,CAAC/C,UAAU,CAACC,GAAG,CAAC+C,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAED,KAAK,IAAIA,KAAK,CAACgE,OAAO,KAAKhE,KAAK,CAAC0F,KAAK,IAAI1F,KAAK,CAAC2F,OAAO,CAAC,CAAC;EACrE;EAEAC,oBAAoBA,CAAC3F,SAAiB;IACpC,MAAMD,KAAK,GAAG,IAAI,CAAC/C,UAAU,CAACC,GAAG,CAAC+C,SAAS,CAAC;IAC5C,IAAID,KAAK,IAAIA,KAAK,CAACkC,MAAM,KAAKlC,KAAK,CAAC0F,KAAK,IAAI1F,KAAK,CAAC2F,OAAO,CAAC,EAAE;MAC3D,IAAI3F,KAAK,CAACkC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAG,IAAI,CAAC2D,mBAAmB,CAAC5F,SAAS,CAAC,cAAc;MAC7D;IACF;IACA,OAAO,EAAE;EACX;EAEA6F,sBAAsBA,CAACtE,aAAqB,EAAEvB,SAAiB;IAC7D,MAAMhC,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,MAAM6I,aAAa,GAAG9H,cAAc,CAACoB,EAAE,CAACmC,aAAa,CAAc;IACnE,MAAMxB,KAAK,GAAG+F,aAAa,EAAE7I,GAAG,CAAC+C,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAED,KAAK,IAAIA,KAAK,CAACgE,OAAO,KAAKhE,KAAK,CAAC0F,KAAK,IAAI1F,KAAK,CAAC2F,OAAO,CAAC,CAAC;EACrE;EAEAK,4BAA4BA,CAACxE,aAAqB,EAAEvB,SAAiB;IACnE,MAAMhC,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,MAAM6I,aAAa,GAAG9H,cAAc,CAACoB,EAAE,CAACmC,aAAa,CAAc;IACnE,MAAMxB,KAAK,GAAG+F,aAAa,EAAE7I,GAAG,CAAC+C,SAAS,CAAC;IAC3C,IAAID,KAAK,IAAIA,KAAK,CAACkC,MAAM,KAAKlC,KAAK,CAAC0F,KAAK,IAAI1F,KAAK,CAAC2F,OAAO,CAAC,EAAE;MAC3D,IAAI3F,KAAK,CAACkC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAG,IAAI,CAAC2D,mBAAmB,CAAC5F,SAAS,CAAC,cAAc;MAC7D;IACF;IACA,OAAO,EAAE;EACX;EAEQmE,oBAAoBA,CAAC7D,SAAgB;IAC3C,IAAI,CAACA,SAAS,IAAIA,SAAS,CAACnE,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,KAAK;IACd;IAEA,MAAMuB,WAAW,GAAG,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK;IAC7D,OAAOyF,SAAS,CAACkD,IAAI,CAAChF,IAAI,IAAG;MAC3B,MAAMK,YAAY,GAAGnB,WAAW,KAAK,OAAO,GAAIc,IAAI,CAACO,SAAS,IAAI,EAAE,GAAKP,IAAI,CAACK,YAAY,IAAI,EAAG;MACjG,OAAOA,YAAY,CAACjD,QAAQ,CAAC,SAAS,CAAC;IACzC,CAAC,CAAC;EACJ;EAEQgK,mBAAmBA,CAAC5F,SAAiB;IAC3C,MAAMgG,UAAU,GAA8B;MAC5C,cAAc,EAAE,eAAe;MAC/B,aAAa,EAAE,cAAc;MAC7B,cAAc,EAAE,cAAc;MAC9B,cAAc,EAAE,eAAe;MAC/B,YAAY,EAAE,aAAa;MAC3B,eAAe,EAAE;KAClB;IACD,OAAOA,UAAU,CAAChG,SAAS,CAAC,IAAIA,SAAS;EAC3C;EAEAiG,cAAcA,CAAA;IACZ,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;IACzD,IAAID,mBAAmB,KAAK,CAAC,CAAC,EAAE;IAEhC,MAAMxI,WAAW,GAAG,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK;IAC7D,MAAM8F,eAAe,GAAI,IAAI,CAAC3D,UAAU,CAACC,GAAG,CAAC,WAAW,CAAe,CAACmC,EAAE,CAAC8G,mBAAmB,CAAC;IAE/F,IAAIxI,WAAW,KAAK,OAAO,EAAE;MAC3B;MACA,MAAMzB,MAAM,GAAG,IAAI,CAACjB,OAAO,CAACkL,mBAAmB,CAAC;MAChD,IAAIjK,MAAM,IAAIA,MAAM,CAACmK,IAAI,EAAE;QACzB,IAAI;UACF;UACAnK,MAAM,CAACoK,QAAQ,CAACC,UAAU,CAAC,SAAS,CAAC,CAACC,IAAI,EAAE;UAE5C;UACAtD,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEgD,mBAAmB,CAAC;UAE3E;QACF,CAAC,CAAC,OAAOzJ,KAAK,EAAE;UACdwG,OAAO,CAACxG,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC/D;MACF,CAAC,MAAM;QACLwG,OAAO,CAACuD,IAAI,CAAC,gCAAgC,EAAEN,mBAAmB,CAAC;QACnE;QACA,IAAI,CAACxC,2BAA2B,CAACwC,mBAAmB,CAAC;MACvD;IACF,CAAC,MAAM;MACL;MACA,MAAMhE,OAAO,GAAGvB,eAAe,CAAC1D,GAAG,CAAC,cAAc,CAAC;MACnD,MAAMwJ,YAAY,GAAGvE,OAAO,CAACrH,KAAK,IAAI,EAAE;MAExC,MAAM6L,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAwB;MACtF,IAAI,CAACF,eAAe,EAAE;MAEtB,MAAMG,cAAc,GAAGH,eAAe,CAACI,cAAc;MAErD,MAAMC,QAAQ,GAAGN,YAAY,CAACpB,SAAS,CAAC,CAAC,EAAEwB,cAAc,CAAC,GAC1C,SAAS,GACTJ,YAAY,CAACpB,SAAS,CAACqB,eAAe,CAACM,YAAY,IAAIH,cAAc,CAAC;MAEtF3E,OAAO,CAAC+E,QAAQ,CAACF,QAAQ,CAAC;MAC1B7E,OAAO,CAACjB,sBAAsB,EAAE;MAChC1B,UAAU,CAAC,MAAK;QACdmH,eAAe,CAACQ,KAAK,EAAE;QACvBR,eAAe,CAACS,iBAAiB,CAACN,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAG,CAAC,CAAC;MAC3E,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAV,sBAAsBA,CAAA;IACpB,MAAM9G,cAAc,GAAG,IAAI,CAACrC,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAACpC,KAAK;IAClE,MAAMyF,SAAS,GAAG,IAAI,CAACtD,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IAE/D,KAAK,IAAImK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9G,SAAS,CAACnE,MAAM,EAAEiL,CAAC,EAAE,EAAE;MACzC,MAAM5I,IAAI,GAAG8B,SAAS,CAAClB,EAAE,CAACgI,CAAC,CAAC,CAACnK,GAAG,CAAC,cAAc,CAAC,CAACpC,KAAK;MACtD,IAAI2D,IAAI,KAAKa,cAAc,EAAE;QAC3B,OAAO+H,CAAC;MACV;IACF;IACA,OAAO,CAAC,CAAC;EACX;EAEAC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACrK,UAAU,EAAEC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK,KAAK,KAAK;EAC7D;EAEAyM,sBAAsBA,CAAC/F,aAAqB;IAC1C,MAAM7D,WAAW,GAAG,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK;IAC7D,MAAM8F,eAAe,GAAI,IAAI,CAAC3D,UAAU,CAACC,GAAG,CAAC,WAAW,CAAe,CAACmC,EAAE,CAACmC,aAAa,CAAC;IAEzF,IAAI7D,WAAW,KAAK,OAAO,EAAE;MAC3B;MACA;MACA,MAAM6J,OAAO,GAAG5G,eAAe,CAAC1D,GAAG,CAAC,WAAW,CAAC,EAAEpC,KAAK,IAAI,EAAE;MAC7D,OAAO0M,OAAO,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,OAAO5G,eAAe,CAAC1D,GAAG,CAAC,cAAc,CAAC,EAAEpC,KAAK,IAAI,EAAE;IACzD;EACF;EAEA2M,uCAAuCA,CAACjG,aAAqB;IAC3D,MAAM7D,WAAW,GAAG,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK;IAC7D,MAAMmF,SAAS,GAAGtC,WAAW,KAAK,OAAO,GAAG,WAAW,GAAG,cAAc;IACxE,OAAO,IAAI,CAACmI,sBAAsB,CAACtE,aAAa,EAAEvB,SAAS,CAAC;EAC9D;EAEAyH,6CAA6CA,CAAClG,aAAqB;IACjE,MAAM7D,WAAW,GAAG,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK;IAC7D,MAAMmF,SAAS,GAAGtC,WAAW,KAAK,OAAO,GAAG,WAAW,GAAG,cAAc;IACxE,OAAO,IAAI,CAACqI,4BAA4B,CAACxE,aAAa,EAAEvB,SAAS,CAAC;EACpE;EAEA;EACA0D,2BAA2BA,CAAC5I,KAAa;IACvC,IAAI,CAAC,IAAI,CAACE,OAAO,CAACF,KAAK,CAAC,EAAE;MACxB,IAAI;QACF,IAAI,CAACE,OAAO,CAACF,KAAK,CAAC,GAAG,IAAIxB,MAAM,CAAC;UAC/BoO,OAAO,EAAE,IAAI;UACbC,iBAAiB,EAAE,IAAI;UACvBC,UAAU,EAAE;SACb,CAAC;QACF,IAAI,CAAC3M,YAAY,CAACH,KAAK,CAAC,GAAG,EAAE;QAC7BmI,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEpI,KAAK,CAAC;MAClE,CAAC,CAAC,OAAO2B,KAAK,EAAE;QACdwG,OAAO,CAACxG,KAAK,CAAC,wCAAwC,EAAE3B,KAAK,EAAE2B,KAAK,CAAC;QACrE;QACA,IAAI;UACF,IAAI,CAACzB,OAAO,CAACF,KAAK,CAAC,GAAG,IAAIxB,MAAM,EAAE;UAClC,IAAI,CAAC2B,YAAY,CAACH,KAAK,CAAC,GAAG,EAAE;UAC7BmI,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEpI,KAAK,CAAC;QACzE,CAAC,CAAC,OAAO+M,aAAa,EAAE;UACtB5E,OAAO,CAACxG,KAAK,CAAC,uDAAuD,EAAEoL,aAAa,CAAC;QACvF;MACF;IACF;EACF;EAEApI,gCAAgCA,CAAA;IAC9B,MAAM/B,WAAW,GAAG,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEpC,KAAK;IAC7D,IAAI6C,WAAW,KAAK,OAAO,EAAE;MAC3B,MAAMM,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;MACpE,KAAK,IAAImK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpJ,cAAc,CAAC7B,MAAM,EAAEiL,CAAC,EAAE,EAAE;QAC9C,IAAI;UACF,IAAI,CAAC1D,2BAA2B,CAAC0D,CAAC,CAAC;UACnC;UACA,MAAMtG,gBAAgB,GAAG9C,cAAc,CAACoB,EAAE,CAACgI,CAAC,CAAC,CAACnK,GAAG,CAAC,WAAW,CAAC;UAC9D,IAAI6D,gBAAgB,EAAEjG,KAAK,EAAE;YAC3B;YACA,IAAI,CAACI,YAAY,CAACmM,CAAC,CAAC,GAAG,IAAI,CAACpI,eAAe,CAAC8B,gBAAgB,CAACjG,KAAK,CAAC;YACnEoI,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkE,CAAC,CAAC;YACvDnE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpC,gBAAgB,CAACjG,KAAK,CAAC;YAC5DoI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACjI,YAAY,CAACmM,CAAC,CAAC,CAAC;UACvD;QACF,CAAC,CAAC,OAAO3K,KAAK,EAAE;UACdwG,OAAO,CAACxG,KAAK,CAAC,+CAA+C,EAAE2K,CAAC,EAAE3K,KAAK,CAAC;QAC1E;MACF;IACF;EACF;EAEAqL,qBAAqBA,CAAChN,KAAa;IACjC;IACA,IAAI,IAAI,CAACI,qBAAqB,EAAE;MAC9B+H,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE;IACF;IAEA,MAAMlF,cAAc,GAAG,IAAI,CAAChB,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,MAAM6D,gBAAgB,GAAG9C,cAAc,CAACoB,EAAE,CAACtE,KAAK,CAAC,CAACmC,GAAG,CAAC,WAAW,CAAC;IAClE,IAAI6D,gBAAgB,EAAE;MACpB;MACA,MAAMiH,gBAAgB,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC/M,YAAY,CAACH,KAAK,CAAC,IAAI,EAAE,CAAC;MAE7E;MACAmI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEpI,KAAK,CAAC;MACtDmI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACjI,YAAY,CAACH,KAAK,CAAC,CAAC;MAC1DmI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6E,gBAAgB,CAAC;MAEpDjH,gBAAgB,CAACmG,QAAQ,CAACc,gBAAgB,CAAC;MAC3CjH,gBAAgB,CAACG,sBAAsB,EAAE;IAC3C;EACF;EAEAgH,aAAaA,CAACnN,KAAa;IACzB,IAAI,IAAI,CAACE,OAAO,CAACF,KAAK,CAAC,EAAE;MACvB,IAAI,CAACE,OAAO,CAACF,KAAK,CAAC,CAACoB,OAAO,EAAE;MAC7B,IAAI,CAAClB,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI;MAC1B,IAAI,CAACG,YAAY,CAACH,KAAK,CAAC,GAAG,EAAE;IAC/B;EACF;EAEA2F,uBAAuBA,CAAC/C,WAAmB;IACzC,IAAIA,WAAW,KAAK,OAAO,EAAE;MAC3B;MACA6B,UAAU,CAAC,MAAK;QACd,IAAI,CAACE,gCAAgC,EAAE;MACzC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACL;MACA,IAAI,CAACzE,OAAO,CAACgB,OAAO,CAAC,CAACC,MAAM,EAAEnB,KAAK,KAAI;QACrC,IAAImB,MAAM,EAAE;UACV,IAAI,CAACgM,aAAa,CAACnN,KAAK,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ;EACF;EAEA;EACQoN,0BAA0BA,CAACX,OAAe;IAChD;IACA,OAAOA,OAAO,CACXpH,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAC1BA,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAC1BA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;EAC3B;EAEA;EACQ6H,eAAeA,CAACG,IAAY;IAClC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpBlF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiF,IAAI,CAAC;IAEnD;IACA,MAAMC,OAAO,GAAGzB,QAAQ,CAAC0B,aAAa,CAAC,KAAK,CAAC;IAC7CD,OAAO,CAACE,SAAS,GAAGH,IAAI;IAExB;IACA,IAAII,SAAS,GAAGH,OAAO,CAACI,WAAW,IAAIJ,OAAO,CAACK,SAAS,IAAI,EAAE;IAE9DxF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEqF,SAAS,CAAC;IAE/C;IACAA,SAAS,GAAG,IAAI,CAACL,0BAA0B,CAACK,SAAS,CAAC;IAEtDtF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqF,SAAS,CAAC;IAE9C,OAAOA,SAAS;EAClB;EAEA;EACQvJ,eAAeA,CAAC0J,IAAY;IAClC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpBzF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEwF,IAAI,CAAC;IAEnD;IACA,IAAIC,aAAa,GAAGD,IAAI,CACrBvI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;IAExB8C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEyF,aAAa,CAAC;IAElD;IACA,MAAMC,KAAK,GAAGD,aAAa,CAACE,KAAK,CAAC,IAAI,CAAC;IACvC,MAAMC,SAAS,GAAGF,KAAK,CAAC9I,GAAG,CAACiJ,IAAI,IAAG;MACjC,IAAIA,IAAI,CAAC7E,IAAI,EAAE,KAAK,EAAE,EAAE;QACtB,OAAO,aAAa,CAAC,CAAC;MACxB,CAAC,MAAM;QACL,OAAO,MAAM6E,IAAI,MAAM,CAAC,CAAC;MAC3B;IACF,CAAC,CAAC;IAEF,MAAMC,MAAM,GAAGF,SAAS,CAACG,IAAI,CAAC,EAAE,CAAC;IACjChG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8F,MAAM,CAAC;IAEzC,OAAOA,MAAM;EACf;;;;;;;cAv+BC3Q,SAAS;QAAA6Q,IAAA,GAAC,mBAAmB,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;MAAA;;;;AA5BtC3P,uBAAuB,GAAA4P,UAAA,EAjBnCjR,SAAS,CAAC;EACTkR,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7Q,WAAW,EACXC,mBAAmB,EACnBM,YAAY,EACZC,0BAA0B,EAC1BC,wBAAwB,EACxBC,yBAAyB,EACzBG,eAAe,CAChB;EACDiQ,OAAO,EAAE,CAAClR,gBAAgB,CAAC;EAC3BmR,SAAS,EAAE,CAACpQ,eAAe,CAAC;EAC5BmJ,QAAA,EAAAkH,oBAA+C;;CAEhD,CAAC,C,EACWlQ,uBAAuB,CAqgCnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}