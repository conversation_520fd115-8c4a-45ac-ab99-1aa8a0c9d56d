{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./execution-petition.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./execution-petition.component.css?ngResource\";\nimport { Component, Output, EventEmitter, Input } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { legalService } from '../legal.service';\nimport { legalConfigService } from '../legalconfig.service';\nlet ExecutionPetitionComponent = class ExecutionPetitionComponent {\n  constructor(toastr, legalService, legalConfigService) {\n    this.toastr = toastr;\n    this.legalService = legalService;\n    this.legalConfigService = legalConfigService;\n    this.viewDetails = new EventEmitter();\n    this.petition = {};\n    this.workflowStatusList = [];\n    this.loader = {\n      isSearching: false,\n      statusSearch: false\n    };\n    this.lastDate = new Date();\n    this.lastDate.setDate(this.lastDate.getDate() - 1);\n    this.nextDate = new Date();\n    this.nextDate.setDate(this.nextDate.getDate() + 1);\n  }\n  ngOnInit() {\n    // this.getWorkflowStatus()\n    this.setPetitionParams();\n  }\n  setPetitionParams() {\n    this.petition = {\n      \"epFilingDate\": \"\",\n      \"epCaseNumber\": \"\",\n      \"courtLocation\": \"\",\n      \"epAdvocateName\": \"\",\n      \"epAdvocateNumber\": \"\",\n      \"epAdvocateEmailId\": \"\",\n      \"epLastDateOfHearing\": \"\",\n      \"epNextDateOfHearing\": \"\",\n      \"epStatus\": \"\",\n      \"scheduleLoanClosingDate\": \"\",\n      \"scheduleLoanClosingAmount\": \"\",\n      \"epRemarks\": \"\"\n    };\n  }\n  getWorkflowStatus(data) {\n    this.loader.statusSearch = true;\n    let inputParams = {\n      \"casetype\": data\n    };\n    this.legalService.getworkflowStatusList(inputParams).subscribe(response => {\n      this.loader.statusSearch = false;\n      this.workflowStatusList = this.legalConfigService.generalKeySort(response, \"status\");\n    }, err => {\n      this.toastr.error(err);\n      this.loader.statusSearch = false;\n    });\n  }\n  caseStatusNoResults(event) {\n    this.caseNoResult = event;\n  }\n  caseStatusChangeLoading(event) {\n    this.caseStatustypeaheadLoading = event;\n  }\n  oncaseStatusSelect(event) {\n    this.petition[\"epStatus\"] = event.item.status;\n  }\n  addUpdatePetition() {\n    if (this.legalId) {\n      this.petition[\"legalId\"] = this.custId; // custom id will be here\n      this.loader.isSearching = true;\n      this.legalService.addUpdatePetition(this.petition).subscribe(response => {\n        this.toastr.success(\"Execution Petition successfully\");\n        this.loader.isSearching = false;\n        // this.setPetitionParams()\n        this.viewDetails.emit({\n          id: this.legalId\n        });\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSearching = false;\n      });\n    } else {\n      this.petition[\"legalId\"] = this.addLegalId;\n      this.loader.isSearching = true;\n      this.legalService.addUpdatePetition(this.petition).subscribe(response => {\n        this.toastr.success(\"Execution Petition successfully\");\n        this.loader.isSearching = false;\n        // this.setPetitionParams()\n        this.viewDetails.emit({\n          \"legalId\": response\n        });\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSearching = false;\n      });\n    }\n  }\n  sendData(data, custId) {\n    this.custId = custId;\n    this.legalId = data.id;\n    this.petition = data;\n    this.getWorkflowStatus(data.caseType);\n    if (data.epFilingDate) {\n      this.petition[\"epFilingDate\"] = new Date(data.epFilingDate);\n    }\n    if (data.epLastDateOfHearing) {\n      this.petition[\"epLastDateOfHearing\"] = new Date(data.epLastDateOfHearing);\n    }\n    if (data.epNextDateOfHearing) {\n      this.petition[\"epNextDateOfHearing\"] = new Date(data.epNextDateOfHearing);\n    }\n    if (data.scheduleLoanClosingDate) {\n      this.petition[\"scheduleLoanClosingDate\"] = new Date(data.scheduleLoanClosingDate);\n    }\n    if (data.epStatus) {\n      this.petition[\"epStatus\"] = data.epStatus;\n      // let event = {\n      //   item:[]\n      // }\n      // Object.assign(event.item,{status:data.epStatus})\n      // this.oncaseStatusSelect(event)\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: legalService\n    }, {\n      type: legalConfigService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      legalId: [{\n        type: Input\n      }],\n      addLegalId: [{\n        type: Input\n      }],\n      viewDetails: [{\n        type: Output,\n        args: [\"viewDetails\"]\n      }]\n    };\n  }\n};\nExecutionPetitionComponent = __decorate([Component({\n  selector: 'legal-execution-petition',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ExecutionPetitionComponent);\nexport { ExecutionPetitionComponent };", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "Input", "ToastrService", "legalService", "legalConfigService", "ExecutionPetitionComponent", "constructor", "toastr", "viewDetails", "petition", "workflowStatusList", "loader", "isSearching", "statusSearch", "lastDate", "Date", "setDate", "getDate", "nextDate", "ngOnInit", "setPetitionParams", "getWorkflowStatus", "data", "inputParams", "getworkflowStatusList", "subscribe", "response", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "error", "caseStatusNoResults", "event", "caseNoResult", "caseStatusChangeLoading", "caseStatustypeaheadLoading", "oncaseStatusSelect", "item", "status", "addUpdatePetition", "legalId", "custId", "success", "emit", "id", "addLegalId", "sendData", "caseType", "epFilingDate", "epLastDateOfHearing", "epNextDateOfHearing", "scheduleLoanClosingDate", "epStatus", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\execution-petition\\execution-petition.component.ts"], "sourcesContent": ["import { Component, OnInit,Output,EventEmitter,Input } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { legalService } from '../legal.service';\r\nimport { legalConfigService } from '../legalconfig.service';\r\nexport interface Loader{\r\n  isSearching: boolean;\r\n  statusSearch: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'legal-execution-petition',\r\n  templateUrl: './execution-petition.component.html',\r\n  styleUrls: ['./execution-petition.component.css']\r\n})\r\nexport class ExecutionPetitionComponent implements OnInit {\r\n  @Input() legalId: any;\r\n  @Input() addLegalId: any;\r\n  @Output(\"viewDetails\") viewDetails: EventEmitter<any> = new EventEmitter();\r\n  loader: Loader;\r\n  petition : any = {}\r\n  workflowStatusList = [];\r\n  custId:any\r\n  lastDate: any;\r\n  nextDate: any;\r\n  caseNoResult:any;\r\n  caseStatustypeaheadLoading:any;\r\n  constructor(public toastr: ToastrService,private legalService: legalService,\r\n    private legalConfigService: legalConfigService) {\r\n      this.loader = {\r\n        isSearching: false,\r\n        statusSearch: false\r\n      }\r\n      this.lastDate =new Date();\r\n      this.lastDate.setDate(this.lastDate.getDate()-1)\r\n      this.nextDate =new Date();\r\n      this.nextDate.setDate(this.nextDate.getDate()+1)\r\n      \r\n     }\r\n\r\n  ngOnInit() {\r\n    // this.getWorkflowStatus()\r\n    this.setPetitionParams()\r\n    \r\n  }\r\n\r\n  setPetitionParams(){\r\n    this.petition={\r\n      \"epFilingDate\":\"\",\r\n      \"epCaseNumber\":\"\",\r\n      \"courtLocation\":\"\",\r\n      \"epAdvocateName\":\"\",\r\n      \"epAdvocateNumber\":\"\",\r\n      \"epAdvocateEmailId\":\"\",   \r\n      \"epLastDateOfHearing\":\"\",\r\n      \"epNextDateOfHearing\":\"\",\r\n      \"epStatus\":\"\",\r\n      \"scheduleLoanClosingDate\":\"\",\r\n      \"scheduleLoanClosingAmount\":\"\",\r\n      \"epRemarks\":\"\"\r\n    }\r\n  }\r\n\r\n  getWorkflowStatus(data){\r\n    this.loader.statusSearch = true\r\n     let inputParams = {\r\n      \"casetype\": data  \r\n    }\r\n    this.legalService.getworkflowStatusList(inputParams).subscribe(response => { \r\n      this.loader.statusSearch = false\r\n      this.workflowStatusList= this.legalConfigService.generalKeySort(response,\"status\");\r\n    },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.statusSearch = false\r\n     });\r\n }\r\n\r\ncaseStatusNoResults(event: boolean): void {\r\n  this.caseNoResult = event;\r\n}\r\ncaseStatusChangeLoading(event: boolean): void {\r\n  this.caseStatustypeaheadLoading = event;\r\n}\r\n\r\noncaseStatusSelect(event){\r\n  this.petition[\"epStatus\"] = event.item.status\r\n}\r\n\r\naddUpdatePetition(){\r\n    if(this.legalId){\r\n      this.petition[\"legalId\"] = this.custId // custom id will be here\r\n      this.loader.isSearching = true\r\n      this.legalService.addUpdatePetition(this.petition).subscribe(response => { \r\n        this.toastr.success(\"Execution Petition successfully\")\r\n        this.loader.isSearching = false\r\n        // this.setPetitionParams()\r\n        this.viewDetails.emit({id: this.legalId})\r\n     },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching = false\r\n     });\r\n\r\n    }else{\r\n      this.petition[\"legalId\"] = this.addLegalId\r\n      this.loader.isSearching = true\r\n      this.legalService.addUpdatePetition(this.petition).subscribe(response => { \r\n        this.toastr.success(\"Execution Petition successfully\")\r\n        this.loader.isSearching = false\r\n        // this.setPetitionParams()\r\n        this.viewDetails.emit({\"legalId\": response})\r\n     },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching = false\r\n     });\r\n    }\r\n      \r\n  }\r\n\r\n  sendData(data,custId){\r\n    this.custId = custId\r\n    this.legalId=data.id\r\n    this.petition=data\r\n    this.getWorkflowStatus(data.caseType);\r\n    if(data.epFilingDate){\r\n      this.petition[\"epFilingDate\"]= new Date(data.epFilingDate)\r\n    }\r\n    if(data.epLastDateOfHearing){\r\n    this.petition[\"epLastDateOfHearing\"]= new Date(data.epLastDateOfHearing)      \r\n    }\r\n    if(data.epNextDateOfHearing){\r\n    this.petition[\"epNextDateOfHearing\"]= new Date(data.epNextDateOfHearing)      \r\n    }\r\n    if(data.scheduleLoanClosingDate){\r\n    this.petition[\"scheduleLoanClosingDate\"]= new Date(data.scheduleLoanClosingDate)      \r\n    }\r\n    if(data.epStatus){\r\n      this.petition[\"epStatus\"] = data.epStatus\r\n      // let event = {\r\n      //   item:[]\r\n      // }\r\n      // Object.assign(event.item,{status:data.epStatus})\r\n      // this.oncaseStatusSelect(event)\r\n    }\r\n\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAASC,MAAM,EAACC,YAAY,EAACC,KAAK,QAAQ,eAAe;AAC3E,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,wBAAwB;AAWpD,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAYrCC,YAAmBC,MAAqB,EAASJ,YAA0B,EACjEC,kBAAsC;IAD7B,KAAAG,MAAM,GAANA,MAAM;IAAwB,KAAAJ,YAAY,GAAZA,YAAY;IACnD,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAVL,KAAAI,WAAW,GAAsB,IAAIR,YAAY,EAAE;IAE1E,KAAAS,QAAQ,GAAS,EAAE;IACnB,KAAAC,kBAAkB,GAAG,EAAE;IAQnB,IAAI,CAACC,MAAM,GAAG;MACZC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;KACf;IACD,IAAI,CAACC,QAAQ,GAAE,IAAIC,IAAI,EAAE;IACzB,IAAI,CAACD,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACF,QAAQ,CAACG,OAAO,EAAE,GAAC,CAAC,CAAC;IAChD,IAAI,CAACC,QAAQ,GAAE,IAAIH,IAAI,EAAE;IACzB,IAAI,CAACG,QAAQ,CAACF,OAAO,CAAC,IAAI,CAACE,QAAQ,CAACD,OAAO,EAAE,GAAC,CAAC,CAAC;EAEjD;EAEHE,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;EAE1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACX,QAAQ,GAAC;MACZ,cAAc,EAAC,EAAE;MACjB,cAAc,EAAC,EAAE;MACjB,eAAe,EAAC,EAAE;MAClB,gBAAgB,EAAC,EAAE;MACnB,kBAAkB,EAAC,EAAE;MACrB,mBAAmB,EAAC,EAAE;MACtB,qBAAqB,EAAC,EAAE;MACxB,qBAAqB,EAAC,EAAE;MACxB,UAAU,EAAC,EAAE;MACb,yBAAyB,EAAC,EAAE;MAC5B,2BAA2B,EAAC,EAAE;MAC9B,WAAW,EAAC;KACb;EACH;EAEAY,iBAAiBA,CAACC,IAAI;IACpB,IAAI,CAACX,MAAM,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAIU,WAAW,GAAG;MACjB,UAAU,EAAED;KACb;IACD,IAAI,CAACnB,YAAY,CAACqB,qBAAqB,CAACD,WAAW,CAAC,CAACE,SAAS,CAACC,QAAQ,IAAG;MACxE,IAAI,CAACf,MAAM,CAACE,YAAY,GAAG,KAAK;MAChC,IAAI,CAACH,kBAAkB,GAAE,IAAI,CAACN,kBAAkB,CAACuB,cAAc,CAACD,QAAQ,EAAC,QAAQ,CAAC;IACpF,CAAC,EAACE,GAAG,IAAE;MACH,IAAI,CAACrB,MAAM,CAACsB,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAACjB,MAAM,CAACE,YAAY,GAAG,KAAK;IACnC,CAAC,CAAC;EACN;EAEDiB,mBAAmBA,CAACC,KAAc;IAChC,IAAI,CAACC,YAAY,GAAGD,KAAK;EAC3B;EACAE,uBAAuBA,CAACF,KAAc;IACpC,IAAI,CAACG,0BAA0B,GAAGH,KAAK;EACzC;EAEAI,kBAAkBA,CAACJ,KAAK;IACtB,IAAI,CAACtB,QAAQ,CAAC,UAAU,CAAC,GAAGsB,KAAK,CAACK,IAAI,CAACC,MAAM;EAC/C;EAEAC,iBAAiBA,CAAA;IACb,IAAG,IAAI,CAACC,OAAO,EAAC;MACd,IAAI,CAAC9B,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC+B,MAAM,EAAC;MACvC,IAAI,CAAC7B,MAAM,CAACC,WAAW,GAAG,IAAI;MAC9B,IAAI,CAACT,YAAY,CAACmC,iBAAiB,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAACgB,SAAS,CAACC,QAAQ,IAAG;QACtE,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,iCAAiC,CAAC;QACtD,IAAI,CAAC9B,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B;QACA,IAAI,CAACJ,WAAW,CAACkC,IAAI,CAAC;UAACC,EAAE,EAAE,IAAI,CAACJ;QAAO,CAAC,CAAC;MAC5C,CAAC,EAACX,GAAG,IAAE;QACJ,IAAI,CAACrB,MAAM,CAACsB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACjB,MAAM,CAACC,WAAW,GAAG,KAAK;MAClC,CAAC,CAAC;IAEH,CAAC,MAAI;MACH,IAAI,CAACH,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAACmC,UAAU;MAC1C,IAAI,CAACjC,MAAM,CAACC,WAAW,GAAG,IAAI;MAC9B,IAAI,CAACT,YAAY,CAACmC,iBAAiB,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAACgB,SAAS,CAACC,QAAQ,IAAG;QACtE,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,iCAAiC,CAAC;QACtD,IAAI,CAAC9B,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B;QACA,IAAI,CAACJ,WAAW,CAACkC,IAAI,CAAC;UAAC,SAAS,EAAEhB;QAAQ,CAAC,CAAC;MAC/C,CAAC,EAACE,GAAG,IAAE;QACJ,IAAI,CAACrB,MAAM,CAACsB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACjB,MAAM,CAACC,WAAW,GAAG,KAAK;MAClC,CAAC,CAAC;IACH;EAEF;EAEAiC,QAAQA,CAACvB,IAAI,EAACkB,MAAM;IAClB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,OAAO,GAACjB,IAAI,CAACqB,EAAE;IACpB,IAAI,CAAClC,QAAQ,GAACa,IAAI;IAClB,IAAI,CAACD,iBAAiB,CAACC,IAAI,CAACwB,QAAQ,CAAC;IACrC,IAAGxB,IAAI,CAACyB,YAAY,EAAC;MACnB,IAAI,CAACtC,QAAQ,CAAC,cAAc,CAAC,GAAE,IAAIM,IAAI,CAACO,IAAI,CAACyB,YAAY,CAAC;IAC5D;IACA,IAAGzB,IAAI,CAAC0B,mBAAmB,EAAC;MAC5B,IAAI,CAACvC,QAAQ,CAAC,qBAAqB,CAAC,GAAE,IAAIM,IAAI,CAACO,IAAI,CAAC0B,mBAAmB,CAAC;IACxE;IACA,IAAG1B,IAAI,CAAC2B,mBAAmB,EAAC;MAC5B,IAAI,CAACxC,QAAQ,CAAC,qBAAqB,CAAC,GAAE,IAAIM,IAAI,CAACO,IAAI,CAAC2B,mBAAmB,CAAC;IACxE;IACA,IAAG3B,IAAI,CAAC4B,uBAAuB,EAAC;MAChC,IAAI,CAACzC,QAAQ,CAAC,yBAAyB,CAAC,GAAE,IAAIM,IAAI,CAACO,IAAI,CAAC4B,uBAAuB,CAAC;IAChF;IACA,IAAG5B,IAAI,CAAC6B,QAAQ,EAAC;MACf,IAAI,CAAC1C,QAAQ,CAAC,UAAU,CAAC,GAAGa,IAAI,CAAC6B,QAAQ;MACzC;MACA;MACA;MACA;MACA;IACF;EAEF;;;;;;;;;;;;;cAhIClD;MAAK;;cACLA;MAAK;;cACLF,MAAM;QAAAqD,IAAA,GAAC,aAAa;MAAA;;;;AAHV/C,0BAA0B,GAAAgD,UAAA,EALtCvD,SAAS,CAAC;EACTwD,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAkD;;CAEnD,CAAC,C,EACWnD,0BAA0B,CAmItC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}