{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./budgeted-target-file-upload-status.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./budgeted-target-file-upload-status.component.css?ngResource\";\nexport class SearchControls {}\nconst statusOptions = ['Failed', 'Invalid File Format', 'Processed', 'Partially Processed', 'Uploaded'];\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { TargetService } from '../target.service';\nlet BudgetedTargetFileUploadStatusComponent = class BudgetedTargetFileUploadStatusComponent {\n  constructor(toastr, modalService, TargetService) {\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.TargetService = TargetService;\n    this.breadcrumbData = [{\n      label: \"Target\",\n      path: \"/target/budgeted-target-file-upload-status\"\n    }, {\n      label: \"Target File Upload Status\",\n      path: \"/target/budgeted-target-file-upload-status\"\n    }];\n    this.searchControls = new SearchControls();\n    this.statusOptions = statusOptions;\n    this.searchControl = false;\n    this.variableInit();\n    this.currentPage = 1;\n    this.itemsPerPage = 5;\n  }\n  variableInit() {\n    this.searchControls = {\n      fileName: '',\n      fileuploadDate: '',\n      status: '',\n      transactionId: ''\n    };\n    this.maxDate = new Date();\n    this.loader = {\n      isSearching: false\n    };\n  }\n  searchStatus() {\n    if (this.searchControls.fileName == '' && this.searchControls.status == '' && this.searchControls.transactionId == '' && this.searchControls.fileuploadDate == '') {\n      this.searchControl = false;\n      this.toastr.warning(\"Enter at least one filter value\");\n      return false;\n    }\n    this.loader.isSearching = true;\n    this.TargetService.gettargetFileUploadStatus(this.formatSearchParams()).subscribe(response => {\n      this.loader.isSearching = false;\n      if (response.length === 0) {\n        this.searchControl = false;\n        this.toastr.info('No results found!');\n        return false;\n      }\n      this.searchControl = true;\n      this.results = response;\n      this.currentRecords = this.fetchRecordsByPage(1);\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.loader.isSearching = false;\n    });\n  }\n  formatSearchParams() {\n    const searchParams = {};\n    searchParams['FileName'] = this.searchControls.fileName;\n    searchParams['FileuploadDate'] = this.searchControls.fileuploadDate;\n    searchParams['status'] = this.searchControls.status;\n    searchParams['TransactionId'] = this.searchControls.transactionId;\n    return searchParams;\n  }\n  download(item) {\n    // if (item.description == null || item.status == 'Invalid File Format') {\n    //   this.toastr.warning(\"No file to download\");\n    //   return false;\n    // }\n    let data = {\n      \"FileName\": item.fileName\n    };\n    this.TargetService.downloadFile(item.fileName).subscribe(data => {\n      const blob = new Blob([data.blob()], {\n        type: 'application/vnd.ms.excel'\n      });\n      const file = new File([blob], item.fileName, {\n        type: 'application/vnd.ms.excel'\n      });\n      //saveAs(file);\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  changeItemsPerPage() {\n    this.currentRecords = this.fetchRecordsByPage(1);\n  }\n  fetchRecordsByPage(page) {\n    const results = this.results;\n    return results.slice((page - 1) * this.itemsPerPage, this.itemsPerPage * page);\n  }\n  pageChange(event) {\n    this.currentRecords = this.fetchRecordsByPage(event.page);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: TargetService\n    }];\n  }\n};\nBudgetedTargetFileUploadStatusComponent = __decorate([Component({\n  selector: 'budgeted-target-file-upload-status',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], BudgetedTargetFileUploadStatusComponent);\nexport { BudgetedTargetFileUploadStatusComponent };", "map": {"version": 3, "names": ["SearchControls", "statusOptions", "Component", "ToastrService", "BsModalService", "TargetService", "BudgetedTargetFileUploadStatusComponent", "constructor", "toastr", "modalService", "breadcrumbData", "label", "path", "searchControls", "searchControl", "variableInit", "currentPage", "itemsPerPage", "fileName", "fileuploadDate", "status", "transactionId", "maxDate", "Date", "loader", "isSearching", "searchStatus", "warning", "gettargetFileUploadStatus", "formatSearchParams", "subscribe", "response", "length", "info", "results", "currentRecords", "fetchRecordsByPage", "err", "error", "searchParams", "download", "item", "data", "downloadFile", "blob", "Blob", "type", "file", "File", "changeItemsPerPage", "page", "slice", "pageChange", "event", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\target\\budgeted-target-file-upload-status\\budgeted-target-file-upload-status.component.ts"], "sourcesContent": ["export class SearchControls {\r\n\tfileName: string;\r\n\tfileuploadDate: string;\r\n\tstatus: string;\r\n\ttransactionId: string;\r\n}\r\nexport interface Loader {\r\n\tisSearching: boolean;\r\n}\r\n\r\nconst statusOptions=['Failed', 'Invalid File Format','Processed', 'Partially Processed', 'Uploaded'];\r\n\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\nimport { TargetService } from '../target.service';\r\n\r\n@Component({\r\n  selector: 'budgeted-target-file-upload-status',\r\n  templateUrl: './budgeted-target-file-upload-status.component.html',\r\n  styleUrls: ['./budgeted-target-file-upload-status.component.css']\r\n})\r\nexport class BudgetedTargetFileUploadStatusComponent{\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Target\", path: \"/target/budgeted-target-file-upload-status\" },\r\n\t\t{ label: \"Target File Upload Status\", path: \"/target/budgeted-target-file-upload-status\" },\r\n\t  ]\r\n  searchControls: SearchControls = new SearchControls();\r\n\tloader: Loader;\r\n\tmaxDate: Date;\r\n\tstatusList: Array<string>;\r\n  statusOptions = statusOptions;\r\n  currentPage: number;\r\n  itemsPerPage: number;\r\n  searchControl:boolean=false;\r\n\r\n\r\n  constructor(public toastr: ToastrService,\r\n\t\tprivate modalService: BsModalService,\r\n\t\tprivate TargetService: TargetService) {\r\n\t\tthis.variableInit();\r\n    this.currentPage = 1;\r\n  \tthis.itemsPerPage = 5;\r\n\t}\r\n\r\n\r\n     variableInit() {\r\n      this.searchControls = {\r\n        fileName: '',\r\n        fileuploadDate: '',\r\n        status: '',\r\n        transactionId: ''\r\n      }\r\n      this.maxDate = new Date();\r\n      this.loader = {\r\n        isSearching: false\r\n      }\r\n    }\r\n\r\nresults:any;\r\ncurrentRecords:any;\r\nsearchStatus() {\r\n      if (this.searchControls.fileName == '' && this.searchControls.status == ''\r\n        && this.searchControls.transactionId == '' && this.searchControls.fileuploadDate == '') {\r\n        this.searchControl=false;\r\n        this.toastr.warning(\"Enter at least one filter value\");\r\n\r\n        return false;\r\n      }\r\n      this.loader.isSearching = true\r\n      this.TargetService\r\n        .gettargetFileUploadStatus(this.formatSearchParams())\r\n        .subscribe(response => {\r\n          this.loader.isSearching = false\r\n          if (response.length === 0) {\r\n            this.searchControl=false;\r\n            this.toastr.info('No results found!');\r\n            return false\r\n          }\r\n          this.searchControl=true\r\n          this.results = response\r\n          this.currentRecords = this.fetchRecordsByPage(1);\r\n\r\n        }, err => {\r\n          this.toastr.error(err, \"Error!\")\r\n          this.loader.isSearching = false\r\n        })\r\n  }\r\n\r\n  formatSearchParams() {\r\n    const searchParams = {};\r\n    searchParams['FileName'] = this.searchControls.fileName;\r\n    searchParams['FileuploadDate'] = this.searchControls.fileuploadDate;\r\n    searchParams['status'] = this.searchControls.status;\r\n    searchParams['TransactionId'] = this.searchControls.transactionId;\r\n    return searchParams;\r\n  }\r\n\r\n  download(item) {\r\n    // if (item.description == null || item.status == 'Invalid File Format') {\r\n    //   this.toastr.warning(\"No file to download\");\r\n    //   return false;\r\n    // }\r\n    let data = {\r\n      \"FileName\": item.fileName\r\n    }\r\n    this.TargetService\r\n      .downloadFile(item.fileName)\r\n      .subscribe(data => {\r\n        const blob = new Blob([data.blob()], { type: 'application/vnd.ms.excel' });\r\n        const file = new File([blob], item.fileName, { type: 'application/vnd.ms.excel' });\r\n        //saveAs(file);\r\n      }, err => {\r\n        this.toastr.error(err, \"Error!\");\r\n      })\r\n  }\r\n\r\n  changeItemsPerPage(): void {\r\n    this.currentRecords = this.fetchRecordsByPage(1);\r\n  }\r\n\r\n  fetchRecordsByPage(page: number): any {\r\n    const results = this.results;\r\n    return results.slice((page - 1) * this.itemsPerPage, this.itemsPerPage * page);\r\n  }\r\n\r\n  pageChange(event: any): void {\r\n    this.currentRecords = this.fetchRecordsByPage(event.page);\r\n  }\r\n\r\n}\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;AAU3B,MAAMC,aAAa,GAAC,CAAC,QAAQ,EAAE,qBAAqB,EAAC,WAAW,EAAE,qBAAqB,EAAE,UAAU,CAAC;AAEpG,SAASC,SAAS,QAAgB,eAAe;AACjD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAoB,qBAAqB;AAChE,SAASC,aAAa,QAAQ,mBAAmB;AAO1C,IAAMC,uCAAuC,GAA7C,MAAMA,uCAAuC;EAelDC,YAAmBC,MAAqB,EAChCC,YAA4B,EAC5BJ,aAA4B;IAFjB,KAAAG,MAAM,GAANA,MAAM;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAJ,aAAa,GAAbA,aAAa;IAhBd,KAAAK,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAA4C,CAAE,EACvE;MAAED,KAAK,EAAE,2BAA2B;MAAEC,IAAI,EAAE;IAA4C,CAAE,CACxF;IACF,KAAAC,cAAc,GAAmB,IAAIb,cAAc,EAAE;IAIrD,KAAAC,aAAa,GAAGA,aAAa;IAG7B,KAAAa,aAAa,GAAS,KAAK;IAM3B,IAAI,CAACC,YAAY,EAAE;IACjB,IAAI,CAACC,WAAW,GAAG,CAAC;IACrB,IAAI,CAACC,YAAY,GAAG,CAAC;EACvB;EAGIF,YAAYA,CAAA;IACX,IAAI,CAACF,cAAc,GAAG;MACpBK,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAE,EAAE;MACVC,aAAa,EAAE;KAChB;IACD,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE;IACzB,IAAI,CAACC,MAAM,GAAG;MACZC,WAAW,EAAE;KACd;EACH;EAIJC,YAAYA,CAAA;IACN,IAAI,IAAI,CAACb,cAAc,CAACK,QAAQ,IAAI,EAAE,IAAI,IAAI,CAACL,cAAc,CAACO,MAAM,IAAI,EAAE,IACrE,IAAI,CAACP,cAAc,CAACQ,aAAa,IAAI,EAAE,IAAI,IAAI,CAACR,cAAc,CAACM,cAAc,IAAI,EAAE,EAAE;MACxF,IAAI,CAACL,aAAa,GAAC,KAAK;MACxB,IAAI,CAACN,MAAM,CAACmB,OAAO,CAAC,iCAAiC,CAAC;MAEtD,OAAO,KAAK;IACd;IACA,IAAI,CAACH,MAAM,CAACC,WAAW,GAAG,IAAI;IAC9B,IAAI,CAACpB,aAAa,CACfuB,yBAAyB,CAAC,IAAI,CAACC,kBAAkB,EAAE,CAAC,CACpDC,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAI,CAACP,MAAM,CAACC,WAAW,GAAG,KAAK;MAC/B,IAAIM,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;QACzB,IAAI,CAAClB,aAAa,GAAC,KAAK;QACxB,IAAI,CAACN,MAAM,CAACyB,IAAI,CAAC,mBAAmB,CAAC;QACrC,OAAO,KAAK;MACd;MACA,IAAI,CAACnB,aAAa,GAAC,IAAI;MACvB,IAAI,CAACoB,OAAO,GAAGH,QAAQ;MACvB,IAAI,CAACI,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;IAElD,CAAC,EAAEC,GAAG,IAAG;MACP,IAAI,CAAC7B,MAAM,CAAC8B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACb,MAAM,CAACC,WAAW,GAAG,KAAK;IACjC,CAAC,CAAC;EACR;EAEAI,kBAAkBA,CAAA;IAChB,MAAMU,YAAY,GAAG,EAAE;IACvBA,YAAY,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC1B,cAAc,CAACK,QAAQ;IACvDqB,YAAY,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC1B,cAAc,CAACM,cAAc;IACnEoB,YAAY,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC1B,cAAc,CAACO,MAAM;IACnDmB,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC1B,cAAc,CAACQ,aAAa;IACjE,OAAOkB,YAAY;EACrB;EAEAC,QAAQA,CAACC,IAAI;IACX;IACA;IACA;IACA;IACA,IAAIC,IAAI,GAAG;MACT,UAAU,EAAED,IAAI,CAACvB;KAClB;IACD,IAAI,CAACb,aAAa,CACfsC,YAAY,CAACF,IAAI,CAACvB,QAAQ,CAAC,CAC3BY,SAAS,CAACY,IAAI,IAAG;MAChB,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,IAAI,CAACE,IAAI,EAAE,CAAC,EAAE;QAAEE,IAAI,EAAE;MAA0B,CAAE,CAAC;MAC1E,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAEH,IAAI,CAACvB,QAAQ,EAAE;QAAE4B,IAAI,EAAE;MAA0B,CAAE,CAAC;MAClF;IACF,CAAC,EAAET,GAAG,IAAG;MACP,IAAI,CAAC7B,MAAM,CAAC8B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IAClC,CAAC,CAAC;EACN;EAEAY,kBAAkBA,CAAA;IAChB,IAAI,CAACd,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;EAClD;EAEAA,kBAAkBA,CAACc,IAAY;IAC7B,MAAMhB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,OAAOA,OAAO,CAACiB,KAAK,CAAC,CAACD,IAAI,GAAG,CAAC,IAAI,IAAI,CAACjC,YAAY,EAAE,IAAI,CAACA,YAAY,GAAGiC,IAAI,CAAC;EAChF;EAEAE,UAAUA,CAACC,KAAU;IACnB,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACiB,KAAK,CAACH,IAAI,CAAC;EAC3D;;;;;;;;;;;AA1GW5C,uCAAuC,GAAAgD,UAAA,EALnDpD,SAAS,CAAC;EACTqD,QAAQ,EAAE,oCAAoC;EAC9CC,QAAA,EAAAC,oBAAkE;;CAEnE,CAAC,C,EACWnD,uCAAuC,CA4GnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}