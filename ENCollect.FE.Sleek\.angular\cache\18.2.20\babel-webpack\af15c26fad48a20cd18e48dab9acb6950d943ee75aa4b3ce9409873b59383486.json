{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { RecieptCancellationApproveRejectComponent } from './reciept-cancellation-approve-reject.component';\ndescribe('RecieptCancellationApproveRejectComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [RecieptCancellationApproveRejectComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(RecieptCancellationApproveRejectComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "RecieptCancellationApproveRejectComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\payments\\v1\\reciept-cancellation-approve-reject\\reciept-cancellation-approve-reject.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\r\n\r\nimport { RecieptCancellationApproveRejectComponent } from './reciept-cancellation-approve-reject.component';\r\n\r\ndescribe('RecieptCancellationApproveRejectComponent', () => {\r\n  let component: RecieptCancellationApproveRejectComponent;\r\n  let fixture: ComponentFixture<RecieptCancellationApproveRejectComponent>;\r\n\r\n  beforeEach(waitForAsync(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ RecieptCancellationApproveRejectComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(RecieptCancellationApproveRejectComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,yCAAyC,QAAQ,iDAAiD;AAE3GC,QAAQ,CAAC,2CAA2C,EAAE,MAAK;EACzD,IAAIC,SAAoD;EACxD,IAAIC,OAAoE;EAExEC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,yCAAyC;KAC1D,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,yCAAyC,CAAC;IAC5EE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}