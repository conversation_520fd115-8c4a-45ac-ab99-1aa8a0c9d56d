import { Component, inject, TemplateRef, ViewChild, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NO_ERRORS_SCHEMA } from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { SharedModule } from "src/app/shared";
import { TemplateVarConfigDirective } from "src/app/shared/directives/template-var-config.directive";
import { TemplatePreviewDirective } from "src/app/shared/directives/template-preview.directive";
import { RestrictHtmlTagsDirective } from "src/app/shared/directives/restrict-html-tags.directive";
import { TemplateService } from "../template.service";
import { Editor, NgxEditorModule, Toolbar } from 'ngx-editor';

@Component({
  selector: "app-create-template",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    TemplateVarConfigDirective,
    TemplatePreviewDirective,
    RestrictHtmlTagsDirective,
    NgxEditorModule
  ],
  schemas: [NO_ERRORS_SCHEMA],
  providers: [TemplateService],
  templateUrl: "./create-template.component.html",
  styleUrl: "./create-template.component.scss",
})
export class CreateTemplateComponent implements OnInit, OnDestroy {
  private fb: FormBuilder = inject(FormBuilder);
  private modalService: BsModalService = inject(BsModalService);
  private route: ActivatedRoute = inject(ActivatedRoute);
  private router: Router = inject(Router);
  private templateService: TemplateService = inject(TemplateService);
  private toastr: ToastrService = inject(ToastrService);

  breadcrumbData = [
    { label: "Communication" },
    { label: "Create Communication Template" },
  ];

  isViewMode: boolean = false;
  isEditMode: boolean = false;
  templateId: string | null = null;
  isLoading: boolean = false;
  templateData: any = null;
  currentTemplateData: any = null;
  selectedViewLanguageIndex: number = 0;
  allLanguages: any[] = [];
  createForm!: FormGroup;
  variables: {name: string, id: string, code?: string}[] = [];
  isLoadingFields = false;
  selectedVariable: {value: string, index: number} = { value: null, index: -1 };
  selectedLanguage: string = null;
  mapVarModalRef!: BsModalRef;
  addLangModalRef!: BsModalRef;
  @ViewChild('templateVarConfig', { static: false }) templateVarConfig!: TemplateVarConfigDirective;

  // NGX Editor properties
  editors: Editor[] = [];
  htmlContents: string[] = [];
  isUpdatingFromMapping: boolean = false;
  toolbar: Toolbar = [
    ['bold', 'italic'],
    ['underline'],
    ['code', 'blockquote'],
    ['ordered_list', 'bullet_list'],
    [{ heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] }],
    ['link'],
    ['align_left', 'align_center', 'align_right', 'align_justify'],
  ];

  constructor() {
    this.buildCreateTemplateForm();
  }

  ngOnInit() {
    this.loadDatabaseFields();
    this.loadLanguageList();
    this.route.params.subscribe(params => {
      this.templateId = params['id'] || null;

      const url = this.router.url;
      if (url.includes('view-communication-template')) {
        this.isViewMode = true;
        this.isEditMode = false;
        this.updateBreadcrumb('View Communication Template');
      } else if (url.includes('edit-communication-template')) {
        this.isViewMode = false;
        this.isEditMode = true;
        this.updateBreadcrumb('Edit Communication Template');
      } else {
        this.isViewMode = false;
        this.isEditMode = false;
        this.updateBreadcrumb('Create Communication Template');
      }

      if (this.templateId && (this.isViewMode || this.isEditMode)) {
        this.loadTemplateData(this.templateId);
      }
    });
  }

  ngOnDestroy(): void {
    this.editors.forEach(editor => {
      if (editor) {
        editor.destroy();
      }
    });
  }

  private updateBreadcrumb(label: string) {
    this.breadcrumbData = [
      { label: "Communication" },
      { label: label },
    ];
  }

  private loadTemplateData(templateId: string) {
    this.isLoading = true;

    if (this.allLanguages.length === 0) {
      this.templateService.languageList().subscribe({
        next: (languageResponse: any) => {
          this.mapLanguageResponse(languageResponse);
          this.fetchAndPopulateTemplate(templateId);
        },
        error: () => {
          this.allLanguages = [{ name: "English", code: "en" }];
          this.fetchAndPopulateTemplate(templateId);
        }
      });
    } else {
      this.fetchAndPopulateTemplate(templateId);
    }
  }

  private fetchAndPopulateTemplate(templateId: string) {
    this.templateService.fetchTemplateById(templateId).subscribe({
      next: (response: any) => {
        this.templateData = response;
        this.populateFormWithTemplateData(response);
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.toastr.error(error, 'Error!');
      }
    });
  }

  get isSMS(): boolean {
  return this.createForm?.get('channelType')?.value?.toLowerCase() === 'sms';
}


  private populateFormWithTemplateData(apiResponse: any) {
    if (!apiResponse) return;

    const templateData = apiResponse.data || apiResponse;
    const templateDetails = templateData.communicationTemplateDetails || [];
    this.currentTemplateData = templateData;

    // Convert the boolean value to string "true" or "false" for radio buttons
    const allowAccessValue = templateData.isAvailableInAccountDetails === true ? "true" : "false";

    this.createForm.patchValue({
      channelType: templateData.templateType?.toLowerCase() || 'email',
      templateName: templateData.name || '',
      allowAccessFromAccount: allowAccessValue,
      entryPoint: templateData.entryPoint || '',
      recipientType: templateData.recipientType || '',
    });

    const languagesArray = this.createForm.get('languages') as FormArray;
    while (languagesArray.length !== 0) {
      languagesArray.removeAt(0);
    }

    if (templateDetails.length > 0) {
      templateDetails.forEach((detail: any) => {
        let languageCode = 'en';
        const languageName = detail.language || 'English';

        if (this.allLanguages && this.allLanguages.length > 0) {
          const foundLanguage = this.allLanguages.find(lang =>
            lang.name.toLowerCase() === languageName.toLowerCase()
          );
          if (foundLanguage) {
            languageCode = foundLanguage.code;
          }
        }

        const languageFormGroup = this.buildLanguageFormGroup({
          code: languageCode,
          name: languageName
        });

        languageFormGroup.patchValue({
          languageCode: languageCode,
          languageName: languageName,
          emailSubject: detail.subject || '',
          templateBody: this.templateData.templateType?.toLowerCase() !== 'email' ? detail.body || '' : '',
          emailBody: this.templateData.templateType?.toLowerCase() === 'email' ? detail.body || '' : '',
        });

        // Initialize editor content for email templates
        if (this.templateData.templateType?.toLowerCase() === 'email') {
          const index = languagesArray.length;
          // Convert plain text template to HTML for the editor
          this.htmlContents[index] = this.plainTextToHtml(detail.body || '');
        }
        languagesArray.push(languageFormGroup);
      });
    } else {
      const defaultLanguageGroup = this.buildLanguageFormGroup({
        code: 'en',
        name: 'English'
      });
      languagesArray.push(defaultLanguageGroup);
    }

    if (languagesArray.length > 0) {
      const firstLanguage = languagesArray.at(0)?.value;
      const activeLanguage = firstLanguage?.languageCode || 'en';
      this.createForm.patchValue({ activeLanguage });
    }

    // Update validation for all language form groups based on channel type
    this.updateLanguageValidation(this.createForm.get('channelType')?.value);

    setTimeout(() => {
      if (this.isViewMode) {
        this.createForm.disable();
      } else if (this.isEditMode) {
        this.createForm.get('channelType')?.disable();
        this.createForm.get('templateName')?.disable();
      }
    }, 200);

    if (this.isViewMode) {
      this.updateBreadcrumb('View Communication Template');
      this.selectedViewLanguageIndex = 0;
    } else if (this.isEditMode) {
      this.updateBreadcrumb('Edit Communication Template');
    }

    // Initialize editors for email templates after form is populated
    setTimeout(() => {
      if (this.templateData.templateType?.toLowerCase() === 'email') {
        this.initializeEditorsForAllLanguages();
        // Additional check to ensure editors are ready
        setTimeout(() => {
          this.ensureEditorsAreReady();
        }, 500);
      }
    }, 1000); // Increased delay to ensure DOM is ready
  }


  private loadDatabaseFields() {
    this.isLoadingFields = true;

    this.templateService.getFieldsList().subscribe({
      next: (response: any) => {
        this.mapFieldsToVariables(response);
        this.isLoadingFields = false;
      },
      error: () => {
        this.isLoadingFields = false;
        this.toastr.error('error', 'Error!');
        this.variables = [];
      }
    });
  }

  private mapFieldsToVariables(apiResponse: any[]) {
    if (!Array.isArray(apiResponse)) {
      this.variables = [];
      return;
    }

    this.variables = apiResponse.map(field => ({
      name: field.name || field.fieldName || 'Unknown Field',
      id: field.id || field.code || field.name?.toUpperCase().replace(/\s+/g, '_') || 'UNKNOWN',
      code: field.code || field.id || 'UNKNOWN_CODE'
    }));
  }

  buildCreateTemplateForm() {
    this.createForm = this.fb.group({
      channelType: ["email", [Validators.required]],
      templateName: [null, [Validators.required]],
      allowAccessFromAccount: ["true"],
      activeLanguage: ["en"],
      languages: this.fb.array([]),
       entryPoint: ['Account', [Validators.required]],
      recipientType: ['Customer', [Validators.required]],
    });

    this.createForm.get('channelType')?.valueChanges.subscribe(channelType => {
      this.updateLanguageValidation(channelType);
      this.handleChannelTypeChange(channelType);
    });

    this.updateLanguageValidation(this.createForm.get('channelType')?.value);
  }

  private updateLanguageValidation(channelType: string) {
    const languagesArray = this.createForm.get('languages') as FormArray;

    languagesArray.controls.forEach(languageControl => {
      const emailSubjectControl = languageControl.get('emailSubject');
      const templateBodyControl = languageControl.get('templateBody');
      const emailBodyControl = languageControl.get('emailBody');

      if (channelType === 'email') {
        emailSubjectControl?.setValidators([Validators.required]);
        emailBodyControl?.setValidators([Validators.required]);
        templateBodyControl?.clearValidators();
      } else {
        emailSubjectControl?.clearValidators();
        emailBodyControl?.clearValidators();
        templateBodyControl?.setValidators([Validators.required]);
      }

      emailSubjectControl?.updateValueAndValidity();
      templateBodyControl?.updateValueAndValidity();
      emailBodyControl?.updateValueAndValidity();
    });
  }

  buildLanguagesFormArray(data?: any[]) {
    const formArray = new FormArray([]);

    if (data && data.length > 0) {
      data.forEach((o) => {
        formArray.push(this.buildLanguageFormGroup(o));
      });
    } else if (this.allLanguages && this.allLanguages.length > 0) {
      formArray.push(this.buildLanguageFormGroup(this.allLanguages[0]));
    }

    return formArray;
  }

  buildLanguageFormGroup(data?: any) {
    return this.fb.group({
      languageCode: data?.code,
      languageName: data?.name,
      emailSubject: [null],
      templateBody: [null], // Validators will be set by updateLanguageValidation
      emailBody: [null], // Validators will be set by updateLanguageValidation
    });
  }

  get fValue(): any {
    return this.createForm.value;
  }

  shouldShowLanguageFields(languageIndex: number): boolean {
    if (this.isViewMode) {
      return languageIndex === 0;
    }
    const activeLanguage = this.fValue?.activeLanguage;
    const currentLanguage = this.fValue?.languages?.[languageIndex];
    return activeLanguage === currentLanguage?.languageCode;
  }

  selectViewLanguage(index: number): void {
    this.selectedViewLanguageIndex = index;
  }

  getSelectedLanguageDetail(): any {
    if (this.isViewMode && this.currentTemplateData?.communicationTemplateDetails?.length > 0) {
      return this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex] ||
             this.currentTemplateData.communicationTemplateDetails[0];
    }
    return null;
  }

  getFormValidationErrors() {
    let formErrors: any = {};

    Object.keys(this.createForm.controls).forEach(key => {
      const controlErrors = this.createForm.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }
    });

    const languagesArray = this.createForm.get('languages') as FormArray;
    if (languagesArray) {
      languagesArray.controls.forEach((control, index) => {
        const formGroup = control as FormGroup;
        Object.keys(formGroup.controls).forEach(fieldKey => {
          const fieldControl = formGroup.get(fieldKey);
          if (fieldControl?.errors) {
            if (!formErrors.languages) formErrors.languages = {};
            if (!formErrors.languages[index]) formErrors.languages[index] = {};
            formErrors.languages[index][fieldKey] = fieldControl.errors;
          }
        });
      });
    }

    return formErrors;
  }

  openMapVariableModal(event: {index: number, value: string}, template: TemplateRef<any>) {
    this.selectedVariable = event;
    this.mapVarModalRef = this.modalService.show(template, {
      animated: true,
    })
  }

  assignVariable() {
    this.mapVarModalRef.hide();
    if (this.selectedVariable?.value) {
      this.templateVarConfig.onUpdateVariable(this.selectedVariable);
      this.selectedVariable = { value: null, index: -1 };
    }
  }

  updateTemplateValue(template: string, index: number) {
    const languageControl = (this.createForm.get('languages') as FormArray).at(index);
    const channelType = this.createForm.get('channelType')?.value;

    console.log('updateTemplateValue called with:', { template, index, channelType });

    if (channelType === 'email') {
      // Update the form control with the plain text template (already in correct format)
      languageControl.patchValue({ emailBody: template });
      languageControl.get('emailBody')?.updateValueAndValidity();

      // Temporarily disable the content change handler to avoid circular updates
      this.isUpdatingFromMapping = true;

      // Update the editor content directly with the plain text converted to HTML
      const htmlContent = this.plainTextToHtml(template);
      this.htmlContents[index] = htmlContent;

      console.log('Template value updated for email at index:', index);
      console.log('Plain text template:', template);
      console.log('HTML content for editor:', htmlContent);

      // Re-enable the content change handler after a short delay
      setTimeout(() => {
        this.isUpdatingFromMapping = false;
      }, 200);
    } else {
      languageControl.patchValue({ templateBody: template });
      languageControl.get('templateBody')?.updateValueAndValidity();
    }
    this.createForm.updateValueAndValidity();
  }

  openAddLangModal(template: TemplateRef<any>) {
    this.addLangModalRef = this.modalService.show(template, { animated: true });
  }

  addLanguage() {
    this.addLangModalRef?.hide();
    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);

    const languagesArray = this.createForm.get('languages') as FormArray;
    const isLanguageAlreadySelected = languagesArray.controls.some(control =>
      control.get('languageCode')?.value === this.selectedLanguage
    );

    if (isLanguageAlreadySelected) {
      this.toastr.error('The same language is already selected.', 'Error!');
      return;
    }

    const langFormGroup = this.buildLanguageFormGroup(language);
    languagesArray.push(langFormGroup);
    this.updateLanguageValidation(this.createForm.get('channelType')?.value);
    this.createForm.updateValueAndValidity();

    // Initialize editor for new language if channel type is email
    if (this.createForm.get('channelType')?.value === 'email') {
      this.initializeEditorForLanguage(languagesArray.length - 1);
    }
  }

  removeLanguage(index: number) {
    const languagesArray = this.createForm.get('languages') as FormArray;

    if (languagesArray.length <= 1) {
      return;
    }

    const removedLanguage = languagesArray.at(index).value;
    languagesArray.removeAt(index);

    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {
      const firstLanguage = languagesArray.at(0)?.value;
      if (firstLanguage) {
        this.createForm.patchValue({ activeLanguage: firstLanguage.languageCode });
      }
    }

    this.createForm.updateValueAndValidity();
  }

  createTemplate() {
    this.markFormGroupTouched(this.createForm);

    if (this.createForm.invalid) {
      this.toastr.error('Please fill all required fields.', 'Error');
      return;
    }

    const formValue = this.createForm.value;
    const hasTemplateBody = formValue.channelType === 'email'
      ? formValue.languages?.some((lang: any) => lang.emailBody && lang.emailBody.trim())
      : formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());

    if (!hasTemplateBody) {
      this.toastr.error('Please enter template body content.', 'Error');
      return;
    }

    if (this.hasUnmappedVariables(formValue.languages)) {
      this.toastr.error('Please map all variables and continue creating template.', 'Error');
      return;
    }

    if (formValue.channelType === 'email') {
      const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());
      if (!hasEmailSubject) {
        this.toastr.error('Please enter email subject for all languages.', 'Error');
        return;
      }
    }

    const getTemplateType = (channelType: string): string => {
      switch (channelType.toLowerCase()) {
        case 'email': return 'Email';
        case 'sms': return 'SMS';
        case 'letter': return 'Letter';
        default: return 'Email';
      }
    };

    const getLanguageName = (languageCode: string): string => {
      const language = this.allLanguages.find(lang => lang.code === languageCode);
      return language ? language.name : 'English';
    };

    const communicationTemplateDetails = formValue.languages.map((lang: any) => {
      let bodyContent = '';

      if (formValue.channelType === 'email') {
        // For email templates, ensure we send plain text (already converted in form control)
        bodyContent = lang.emailBody || "";
      } else {
        bodyContent = lang.templateBody || "";
      }

      const detail: any = {
        "Language": getLanguageName(lang.languageCode),
        "body": bodyContent
      };

      if (formValue.channelType === 'email') {
        detail.Subject = lang.emailSubject || "";
      }

      return detail;
    });

    const json = {
      "templateType": getTemplateType(formValue.channelType),
      "Name": formValue.templateName,
      "isAvailableInAccountDetails": formValue.allowAccessFromAccount === 'true',
      "CommunicationTemplateDetails": communicationTemplateDetails,
      "entryPoint": formValue.entryPoint,
      "recipientType": formValue.recipientType
    };

    this.templateService.saveCommunicationTemplate(json).subscribe({
      next: () => {
        this.toastr.success(`The Template "${json.Name}" has been created successfully.`, "Success!");
        this.router.navigate(['communication/search-communication-templates']);
      },
      error: (error) => {
        this.toastr.error(error, "Error!");
      }
    });
  }

  updateTemplate() {
    this.markFormGroupTouched(this.createForm);

    if (this.createForm.invalid) {
      this.toastr.error('Please fill in all required fields.', 'Error');
      return;
    }

    const formValue = this.createForm.getRawValue();
    const hasTemplateBody = formValue.channelType === 'email'
      ? formValue.languages?.some((lang: any) => lang.emailBody && lang.emailBody.trim())
      : formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());

    if (!hasTemplateBody) {
      this.toastr.error('Please enter template body content.', 'Error');
      return;
    }

    if (this.hasUnmappedVariables(formValue.languages)) {
      this.toastr.error('Please map all variables and continue creating template.', 'Error');
      return;
    }

    if (!formValue.channelType) {
      this.toastr.error('Please select a channel type.', 'Error');
      return;
    }

    if (formValue.channelType === 'email') {
      const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());
      if (!hasEmailSubject) {
        this.toastr.error('Please enter email subject for all languages.', 'Error');
        return;
      }
    }

    const getTemplateType = (channelType: string): string => {
      if (!channelType || typeof channelType !== 'string') {
        return 'Email';
      }
      switch (channelType.toLowerCase()) {
        case 'email': return 'Email';
        case 'sms': return 'SMS';
        case 'letter': return 'Letter';
        default: return 'Email';
      }
    };

    const getLanguageName = (languageCode: string): string => {
      if (!languageCode) return 'English';
      const language = this.allLanguages.find(lang => lang.code === languageCode);
      return language ? language.name : 'English';
    };

    const communicationTemplateDetails = formValue.languages?.map((lang: any) => {
      let bodyContent = '';

      if (formValue.channelType === 'email') {
        // For email templates, ensure we send plain text (already converted in form control)
        bodyContent = lang.emailBody || "";
      } else {
        bodyContent = lang.templateBody || "";
      }

      const detail: any = {
        "Language": getLanguageName(lang.languageCode),
        "body": bodyContent
      };

      if (formValue.channelType === 'email') {
        detail.Subject = lang.emailSubject || "";
      }

      return detail;
    }) || [];

    const json = {
      "id": this.templateId,
      "templateType": getTemplateType(formValue.channelType),
      "Name": formValue.templateName || "",
      "IsAvailableInAccountDetails": formValue.allowAccessFromAccount === 'true',
      "CommunicationTemplateDetails": communicationTemplateDetails,
      "entryPoint": formValue.entryPoint,
      "recipientType": formValue.recipientType,
    };

    this.templateService.updateCcmTemplate(json).subscribe({
      next: () => {
        this.toastr.success(`The Template "${json.Name}" has been updated successfully.`, "Success!");
        this.router.navigate(['communication/search-communication-templates']);
      },
      error: (error) => {
        this.toastr.error(error, "Error!");
      }
    });
  }

  private loadLanguageList() {
    this.templateService.languageList().subscribe({
      next: (response: any) => {
        this.mapLanguageResponse(response);
      },
      error: (error) => {
        this.toastr.error(error, 'Error');
        this.allLanguages = [{ name: "English", code: "en" }];
        this.initializeDefaultLanguage();
      }
    });
  }

  private mapLanguageResponse(apiResponse: any) {
    if (!apiResponse || !Array.isArray(apiResponse)) {
      this.allLanguages = [{ name: "English", code: "en" }];
      this.initializeDefaultLanguage();
      return;
    }

    this.allLanguages = apiResponse.map(lang => ({
      name: lang.name || lang.itemName || 'Unknown Language',
      code: lang.code || lang.itemCode || lang.name?.toLowerCase().substring(0, 2) || 'en'
    }));

    if (this.allLanguages.length === 0) {
      this.allLanguages = [{ name: "English", code: "en" }];
    }

    this.initializeDefaultLanguage();
  }

  private initializeDefaultLanguage() {
    if (this.createForm && this.allLanguages.length > 0) {
      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;
      if (!currentActiveLanguage || !this.allLanguages.find(lang => lang.code === currentActiveLanguage)) {
        this.createForm.patchValue({ activeLanguage: this.allLanguages[0].code });

        const languagesArray = this.createForm.get('languages') as FormArray;
        if (languagesArray.length === 0) {
          const defaultLanguageGroup = this.buildLanguageFormGroup(this.allLanguages[0]);
          languagesArray.push(defaultLanguageGroup);
          // Update validation for the newly added language group
          this.updateLanguageValidation(this.createForm.get('channelType')?.value);
        }
      }
    }
  }

  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.createForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldErrorMessage(fieldName: string): string {
    const field = this.createForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
    }
    return '';
  }

  isLanguageFieldInvalid(languageIndex: number, fieldName: string): boolean {
    const languagesArray = this.createForm.get('languages') as FormArray;
    const languageGroup = languagesArray.at(languageIndex) as FormGroup;
    const field = languageGroup?.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getLanguageFieldErrorMessage(languageIndex: number, fieldName: string): string {
    const languagesArray = this.createForm.get('languages') as FormArray;
    const languageGroup = languagesArray.at(languageIndex) as FormGroup;
    const field = languageGroup?.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
    }
    return '';
  }

  private hasUnmappedVariables(languages: any[]): boolean {
    if (!languages || languages.length === 0) {
      return false;
    }

    const channelType = this.createForm.get('channelType')?.value;
    return languages.some(lang => {
      const templateBody = channelType === 'email' ? (lang.emailBody || '') : (lang.templateBody || '');
      return templateBody.includes('<<Var>>');
    });
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'templateName': 'Template Name',
      'channelType': 'Channel Type',
      'emailSubject': 'Subject Line',
      'templateBody': 'Template Body',
      'entryPoint': 'Entry Point',
      'recipientType': 'Recipient Type'
    };
    return fieldNames[fieldName] || fieldName;
  }

  insertVariable() {
    const activeLanguageIndex = this.getActiveLanguageIndex();
    if (activeLanguageIndex === -1) {
      console.warn('No active language index found');
      return;
    }

    const channelType = this.createForm.get('channelType')?.value || this.templateData?.templateType?.toLowerCase();
    const languageControl = (this.createForm.get('languages') as FormArray).at(activeLanguageIndex);

    console.log('Inserting variable for channel type:', channelType, 'at index:', activeLanguageIndex);

    if (channelType === 'email') {
      // For email templates using ngx-editor
      const editor = this.editors[activeLanguageIndex];
      if (editor && editor.view) {
        try {
          // Insert the variable as plain text to preserve the angle brackets
          editor.commands.insertText('<<Var>>').exec();

          // Debug: Log to console to verify insertion
          console.log('Variable inserted into editor at index:', activeLanguageIndex);

          // The editor content will be automatically synced via ngModel binding
        } catch (error) {
          console.error('Error inserting variable into editor:', error);
        }
      } else {
        console.warn('Editor not available at index:', activeLanguageIndex);
        // Fallback: Initialize editor if not available and try again
        this.initializeEditorForLanguage(activeLanguageIndex);
        setTimeout(() => {
          const retryEditor = this.editors[activeLanguageIndex];
          if (retryEditor && retryEditor.view) {
            try {
              retryEditor.commands.insertText('<<Var>>').exec();
              console.log('Variable inserted into editor after retry at index:', activeLanguageIndex);
            } catch (error) {
              console.error('Error inserting variable into editor after retry:', error);
              // Final fallback: add to form control directly
              this.fallbackInsertVariableToEmailBody(activeLanguageIndex);
            }
          } else {
            // Final fallback: add to form control directly
            this.fallbackInsertVariableToEmailBody(activeLanguageIndex);
          }
        }, 500);
      }
    } else {
      // For SMS/Letter templates using textarea
      const control = languageControl.get('templateBody');
      const currentValue = control?.value || '';

      const textareaElement = document.getElementById('templateBody') as HTMLTextAreaElement;
      if (!textareaElement) {
        console.warn('Textarea element not found, adding variable to form control directly');
        control?.setValue(currentValue + '<<Var>>');
        control?.updateValueAndValidity();
        return;
      }

      const cursorPosition = textareaElement.selectionStart;

      const newValue = currentValue.substring(0, cursorPosition) +
                      '<<Var>>' +
                      currentValue.substring(textareaElement.selectionEnd || cursorPosition);

      control?.setValue(newValue);
      control?.updateValueAndValidity();
      setTimeout(() => {
        textareaElement.focus();
        textareaElement.setSelectionRange(cursorPosition + 7, cursorPosition + 7);
      }, 0);
    }
  }

  private fallbackInsertVariableToEmailBody(languageIndex: number): void {
    const languagesArray = this.createForm.get('languages') as FormArray;
    const emailBodyControl = languagesArray.at(languageIndex).get('emailBody');
    const currentValue = emailBodyControl?.value || '';

    emailBodyControl?.setValue(currentValue + '<<Var>>');
    emailBodyControl?.updateValueAndValidity();

    // Update the HTML content as well
    this.htmlContents[languageIndex] = this.plainTextToHtml(emailBodyControl?.value || '');

    console.log('Variable added to email body via fallback method at index:', languageIndex);
  }

  private ensureEditorsAreReady(): void {
    const channelType = this.createForm.get('channelType')?.value || this.templateData?.templateType?.toLowerCase();
    if (channelType === 'email') {
      const languagesArray = this.createForm.get('languages') as FormArray;
      for (let i = 0; i < languagesArray.length; i++) {
        if (!this.editors[i] || !this.editors[i].view) {
          console.log('Re-initializing editor for index:', i);
          this.initializeEditorForLanguage(i);

          // Set content if available
          const emailBodyControl = languagesArray.at(i).get('emailBody');
          if (emailBodyControl?.value && !this.htmlContents[i]) {
            this.htmlContents[i] = this.plainTextToHtml(emailBodyControl.value);
          }
        }
      }
    }
  }

  getActiveLanguageIndex(): number {
    const activeLanguage = this.createForm.get('activeLanguage').value;
    const languages = this.createForm.get('languages') as FormArray;

    for (let i = 0; i < languages.length; i++) {
      const lang = languages.at(i).get('languageCode').value;
      if (lang === activeLanguage) {
        return i;
      }
    }
    return -1;
  }

  shouldRestrictHtmlTags(): boolean {
    return this.createForm?.get('channelType')?.value !== 'sms';
  }

  getCurrentTemplateBody(languageIndex: number): string {
    const channelType = this.createForm.get('channelType')?.value;
    const languageControl = (this.createForm.get('languages') as FormArray).at(languageIndex);

    if (channelType === 'email') {
      // For email templates, return the plain text content from form control
      // This ensures template variable mapping and preview work correctly
      const content = languageControl.get('emailBody')?.value || '';
      return content; // Already converted to plain text in onEditorContentChange
    } else {
      return languageControl.get('templateBody')?.value || '';
    }
  }

  isLanguageFieldInvalidForCurrentChannel(languageIndex: number): boolean {
    const channelType = this.createForm.get('channelType')?.value;
    const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';
    return this.isLanguageFieldInvalid(languageIndex, fieldName);
  }

  getLanguageFieldErrorMessageForCurrentChannel(languageIndex: number): string {
    const channelType = this.createForm.get('channelType')?.value;
    const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';
    return this.getLanguageFieldErrorMessage(languageIndex, fieldName);
  }

  // NGX Editor Methods
  initializeEditorForLanguage(index: number): void {
    if (!this.editors[index]) {
      try {
        this.editors[index] = new Editor({
          history: true,
          keyboardShortcuts: true,
          inputRules: true,
        });
        this.htmlContents[index] = '';
        console.log('Editor initialized successfully for index:', index);
      } catch (error) {
        console.error('Failed to initialize editor for index:', index, error);
        // Fallback: try with minimal configuration
        try {
          this.editors[index] = new Editor();
          this.htmlContents[index] = '';
          console.log('Editor initialized with minimal config for index:', index);
        } catch (fallbackError) {
          console.error('Failed to initialize editor even with minimal config:', fallbackError);
        }
      }
    }
  }

  initializeEditorsForAllLanguages(): void {
    const channelType = this.createForm.get('channelType')?.value || this.templateData?.templateType?.toLowerCase();
    console.log('Initializing editors for channel type:', channelType);

    if (channelType === 'email') {
      const languagesArray = this.createForm.get('languages') as FormArray;
      console.log('Languages array length:', languagesArray.length);

      for (let i = 0; i < languagesArray.length; i++) {
        try {
          this.initializeEditorForLanguage(i);
          // Set initial content from form control - convert plain text to HTML
          const emailBodyControl = languagesArray.at(i).get('emailBody');
          if (emailBodyControl?.value) {
            // Convert plain text template to HTML for the editor
            this.htmlContents[i] = this.plainTextToHtml(emailBodyControl.value);
            console.log('Initialized editor content for index:', i);
            console.log('Plain text from form:', emailBodyControl.value);
            console.log('HTML for editor:', this.htmlContents[i]);
          } else {
            // Initialize with empty content
            this.htmlContents[i] = '';
            console.log('Initialized empty editor content for index:', i);
          }
        } catch (error) {
          console.error('Error initializing editor for language index:', i, error);
        }
      }
    }
  }

  onEditorContentChange(index: number): void {
    // Skip processing if we're updating from template mapping to avoid circular updates
    if (this.isUpdatingFromMapping) {
      console.log('Skipping editor content change - updating from mapping');
      return;
    }

    const languagesArray = this.createForm.get('languages') as FormArray;
    const emailBodyControl = languagesArray.at(index).get('emailBody');
    if (emailBodyControl) {
      // Convert HTML content to plain text for template processing
      const plainTextContent = this.htmlToPlainText(this.htmlContents[index] || '');

      // Debug: Log content changes
      console.log('Editor content changed at index:', index);
      console.log('Raw HTML content:', this.htmlContents[index]);
      console.log('Plain text content:', plainTextContent);

      emailBodyControl.setValue(plainTextContent);
      emailBodyControl.updateValueAndValidity();
    }
  }

  destroyEditor(index: number): void {
    if (this.editors[index]) {
      this.editors[index].destroy();
      this.editors[index] = null;
      this.htmlContents[index] = '';
    }
  }

  handleChannelTypeChange(channelType: string): void {
    if (channelType === 'email') {
      // Initialize editors for email templates with a longer delay to ensure DOM is ready
      setTimeout(() => {
        this.initializeEditorsForAllLanguages();
      }, 500);
    } else {
      // Destroy editors for non-email templates
      this.editors.forEach((editor, index) => {
        if (editor) {
          this.destroyEditor(index);
        }
      });
    }
  }

  // Helper method to ensure template variables are properly formatted
  private normalizeTemplateVariables(content: string): string {
    // Convert HTML entities back to angle brackets for template processing
    return content
      .replace(/&lt;&lt;/g, '<<')
      .replace(/&gt;&gt;/g, '>>')
      .replace(/&amp;/g, '&');
  }

  // Helper method to convert HTML content to plain text for template processing
  private htmlToPlainText(html: string): string {
    if (!html) return '';

    console.log('Converting HTML to plain text:', html);

    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get plain text content
    let plainText = tempDiv.textContent || tempDiv.innerText || '';

    console.log('Extracted plain text:', plainText);

    // Normalize template variables
    plainText = this.normalizeTemplateVariables(plainText);

    console.log('After normalization:', plainText);

    return plainText;
  }

  // Helper method to convert plain text back to HTML for ngx-editor
  private plainTextToHtml(text: string): string {
    if (!text) return '';

    console.log('Converting plain text to HTML:', text);

    // First, escape any existing HTML entities to prevent double-encoding
    let processedText = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    console.log('After HTML escaping:', processedText);

    // Split text into lines and process each line
    const lines = processedText.split('\n');
    const htmlLines = lines.map(line => {
      if (line.trim() === '') {
        return '<p><br></p>'; // Empty line
      } else {
        return `<p>${line}</p>`; // Wrap in paragraph
      }
    });

    const result = htmlLines.join('');
    console.log('Final HTML result:', result);

    return result;
  }

}
