{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./bulk-initiate.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./bulk-initiate.component.css?ngResource\";\nexport class UploadControls {}\nimport { Component, ViewChild } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { JwtService } from '../../../authentication/jwt.service';\nimport { repoConfigService } from '../../legal-custom-config.service';\nimport { repoService } from '../../legal-custom.service';\nimport { Router } from '@angular/router';\nlet BulkInitiateComponent = class BulkInitiateComponent {\n  constructor(jwtService, toastr, modalService, legalService, legalConfigService, router) {\n    this.jwtService = jwtService;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.legalService = legalService;\n    this.legalConfigService = legalConfigService;\n    this.router = router;\n    this.breadcrumbData = [{\n      label: \"Legal Management\",\n      path: \"/encollect/legal-custom/bulkupload-initiate\"\n    }, {\n      label: \"Upload Bulk Initiate Case\",\n      path: \"/encollect/legal-custom/bulkupload-initiate\"\n    }];\n    this.uploadControls = new UploadControls();\n    this.serverBusy = false;\n    this.dataToCsv = [];\n  }\n  ngOnInit() {\n    // Set the user name\n    this.currentUserData = this.jwtService.getUser();\n    this.varInit();\n    //  master Call\n  }\n  varInit() {\n    this.results = [];\n    this.uploadControls = {\n      template: '',\n      allocationType: false,\n      fileName: ''\n    };\n    this.loader = {\n      isDownload: false,\n      isSubmit: false,\n      product: false,\n      subproduct: false\n    };\n  }\n  fileUploadConfirmation(event, confirmation) {\n    this.fileList = event.target.files;\n    var file_extension = this.fileList[0].name.split('.').pop();\n    this.uploadControls.fileName = this.fileList[0].name;\n    if (file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension != \"XLS\") {\n      this.fileList = [];\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.info('You can only upload the file with extension xls or xlsx');\n      return;\n    } else {\n      let config = {\n        ignoreBackdropClick: true\n      };\n      this.modalRef = this.modalService.show(confirmation, config);\n    }\n  }\n  fIleUpload() {\n    const file = this.fileList[0];\n    const fd = new FormData();\n    fd.append('file', file);\n    this.modalRef?.hide();\n    this.serverBusy = true;\n    this.legalService.uploadFile(fd).subscribe(data => {\n      this.initateLegalUpload(data);\n    }, err => {\n      this.fileUploader.nativeElement.value = null;\n      this.serverBusy = false;\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  initateLegalUpload(data) {\n    let inputParams = {\n      \"FileName\": data[\"fileName\"]\n    };\n    this.legalService.initateLegalUpload(inputParams).subscribe(data => {\n      this.fileUploader.nativeElement.value = null;\n      setTimeout(() => {\n        this.toastr.success(\"File Uploaded Successfully. Transaction ID : \" + data.transactionId);\n        this.fileList = [];\n        this.uploadControls = new UploadControls();\n        this.serverBusy = false;\n        this.router.navigateByUrl('/', {\n          skipLocationChange: true\n        }).then(() => this.router.navigate(['encollect/legal-custom/bulkupload-initiate-status']));\n      }, 3000);\n    }, err => {\n      this.serverBusy = false;\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: JwtService\n    }, {\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: repoService\n    }, {\n      type: repoConfigService\n    }, {\n      type: Router\n    }];\n  }\n  static {\n    this.propDecorators = {\n      fileUploader: [{\n        type: ViewChild,\n        args: ['fileUploader']\n      }]\n    };\n  }\n};\nBulkInitiateComponent = __decorate([Component({\n  selector: 'app-bulk-initiate',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], BulkInitiateComponent);\nexport { BulkInitiateComponent };", "map": {"version": 3, "names": ["UploadControls", "Component", "ViewChild", "ToastrService", "BsModalService", "JwtService", "repoConfigService", "repoService", "Router", "BulkInitiateComponent", "constructor", "jwtService", "toastr", "modalService", "legalService", "legalConfigService", "router", "breadcrumbData", "label", "path", "uploadControls", "serverBusy", "dataToCsv", "ngOnInit", "currentUserData", "getUser", "varInit", "results", "template", "allocationType", "fileName", "loader", "isDownload", "isSubmit", "product", "subproduct", "fileUploadConfirmation", "event", "confirmation", "fileList", "target", "files", "file_extension", "name", "split", "pop", "fileUploader", "nativeElement", "value", "info", "config", "ignoreBackdropClick", "modalRef", "show", "fIleUpload", "file", "fd", "FormData", "append", "hide", "uploadFile", "subscribe", "data", "initateLegalUpload", "err", "error", "inputParams", "setTimeout", "success", "transactionId", "navigateByUrl", "skipLocationChange", "then", "navigate", "args", "__decorate", "selector", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal-custom\\bulk-initiate\\bulk-initiate\\bulk-initiate.component.ts"], "sourcesContent": ["\r\nexport class UploadControls {\r\n\ttemplate: string;\r\n\tallocationType: boolean;\r\n\tfileName: string;\r\n}\r\nexport interface Loader {\r\n\tisDownload: boolean;\r\n\tisSubmit: boolean;\r\n\tproduct: boolean;\r\n\tsubproduct: boolean;\r\n}\r\nimport { Component, OnInit, ViewChild, Output, TemplateRef, ElementRef } from '@angular/core';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\nimport { JwtService } from '../../../authentication/jwt.service';\r\nimport { repoConfigService } from '../../legal-custom-config.service';\r\nimport { repoService } from '../../legal-custom.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n\tselector: 'app-bulk-initiate',\r\n\ttemplateUrl: './bulk-initiate.component.html',\r\n\tstyleUrls: ['./bulk-initiate.component.css']\r\n})\r\nexport class BulkInitiateComponent implements OnInit {\r\n\tpublic breadcrumbData = [\r\n\t\t{ label: \"Legal Management\", path: \"/encollect/legal-custom/bulkupload-initiate\" },\r\n\t\t{ label: \"Upload Bulk Initiate Case\", path: \"/encollect/legal-custom/bulkupload-initiate\" },\r\n\t  ];\r\n\t@ViewChild('fileUploader') fileUploader: ElementRef;\r\n\r\n\tuploadControls: UploadControls = new UploadControls();\r\n\tloader: Loader;\r\n\tcurrentUserData: any;\r\n\r\n\tresults: Array<object>;\r\n\tfileList: any;\r\n\tmodalRef: BsModalRef;\r\n\tattachedFile: any;\r\n\tserverBusy = false\r\n\tdataToCsv = [];\r\n\ttotalRecordCount: any;\r\n\tconstructor(private jwtService: JwtService,\r\n\t\tpublic toastr: ToastrService,\r\n\t\tprivate modalService: BsModalService,\r\n\t\tprivate legalService: repoService,\r\n\t\tprivate legalConfigService: repoConfigService,\r\n\t\tprivate router: Router,\r\n\t) { }\r\n\r\n\tngOnInit() {\r\n\t\t// Set the user name\r\n\t\tthis.currentUserData = this.jwtService.getUser()\r\n\t\tthis.varInit()\r\n\t\t//  master Call\r\n\t}\r\n\r\n\tvarInit() {\r\n\t\tthis.results = [];\r\n\t\tthis.uploadControls = {\r\n\t\t\ttemplate: '',\r\n\t\t\tallocationType: false,\r\n\t\t\tfileName: ''\r\n\t\t}\r\n\t\tthis.loader = {\r\n\t\t\tisDownload: false,\r\n\t\t\tisSubmit: false,\r\n\t\t\tproduct: false,\r\n\t\t\tsubproduct: false,\r\n\t\t}\r\n\r\n\t}\r\n\r\n\tfileUploadConfirmation(event, confirmation: TemplateRef<any>) {\r\n\t\tthis.fileList = event.target.files;\r\n\t\tvar file_extension = this.fileList[0].name.split('.').pop();\r\n\t\tthis.uploadControls.fileName = this.fileList[0].name\r\n\t\tif (file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension != \"XLS\") {\r\n\t\t\tthis.fileList = []\r\n\t\t\tthis.fileUploader.nativeElement.value = null;\r\n\t\t\tthis.toastr.info('You can only upload the file with extension xls or xlsx');\r\n\t\t\treturn;\r\n\t\t} else {\r\n\t\t\tlet config = {\r\n\t\t\t\tignoreBackdropClick: true,\r\n\t\t\t};\r\n\t\t\tthis.modalRef = this.modalService.show(confirmation, config);\r\n\t\t}\r\n\t}\r\n\r\n\tfIleUpload() {\r\n\t\tconst file: File = this.fileList[0];\r\n\t\tconst fd: FormData = new FormData();\r\n\t\tfd.append('file', file);\r\n\t\tthis.modalRef?.hide()\r\n\t\tthis.serverBusy = true\r\n\t\tthis.legalService.uploadFile(fd).subscribe(data => {\r\n\t\t\tthis.initateLegalUpload(data);\r\n\t\t}, err => {\r\n\t\t\tthis.fileUploader.nativeElement.value = null;\r\n\t\t\tthis.serverBusy = false\r\n\t\t\tthis.toastr.error(err, \"Error!\")\r\n\t\t});\r\n\t}\r\n\tinitateLegalUpload(data) {\r\n\t\tlet inputParams = {\r\n\t\t\t\"FileName\": data[\"fileName\"],\r\n\r\n\t\t}\r\n\t\tthis.legalService.initateLegalUpload(inputParams)\r\n\t\t\t.subscribe(data => {\r\n\t\t\t\tthis.fileUploader.nativeElement.value = null;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.toastr.success(\"File Uploaded Successfully. Transaction ID : \" + data.transactionId);\r\n\t\t\t\t\tthis.fileList = []\r\n\t\t\t\t\tthis.uploadControls = new UploadControls();\r\n\t\t\t\t\tthis.serverBusy = false\r\n\t\t\t\t\tthis.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>\r\n\t\t\t\t\t\tthis.router.navigate(['encollect/legal-custom/bulkupload-initiate-status']));\r\n\t\t\t\t}, 3000);\r\n\t\t\t}, err => {\r\n\t\t\t\tthis.serverBusy = false\r\n\t\t\t\tthis.fileUploader.nativeElement.value = null;\r\n\t\t\t\tthis.toastr.error(err, \"Error!\");\r\n\t\t\t});\r\n\t}\r\n}\r\n\r\n\r\n"], "mappings": ";;;AACA,OAAM,MAAOA,cAAc;AAW3B,SAASC,SAAS,EAAUC,SAAS,QAAyC,eAAe;AAE7F,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAoB,qBAAqB;AAEhE,SAASC,UAAU,QAAQ,qCAAqC;AAChE,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,MAAM,QAAQ,iBAAiB;AAOjC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAkBjCC,YAAoBC,UAAsB,EAClCC,MAAqB,EACpBC,YAA4B,EAC5BC,YAAyB,EACzBC,kBAAqC,EACrCC,MAAc;IALH,KAAAL,UAAU,GAAVA,UAAU;IACtB,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAtBR,KAAAC,cAAc,GAAG,CACvB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAA6C,CAAE,EAClF;MAAED,KAAK,EAAE,2BAA2B;MAAEC,IAAI,EAAE;IAA6C,CAAE,CACzF;IAGH,KAAAC,cAAc,GAAmB,IAAIpB,cAAc,EAAE;IAQrD,KAAAqB,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,EAAE;EAQV;EAEJC,QAAQA,CAAA;IACP;IACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACb,UAAU,CAACc,OAAO,EAAE;IAChD,IAAI,CAACC,OAAO,EAAE;IACd;EACD;EAEAA,OAAOA,CAAA;IACN,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACP,cAAc,GAAG;MACrBQ,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,KAAK;MACrBC,QAAQ,EAAE;KACV;IACD,IAAI,CAACC,MAAM,GAAG;MACbC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;KACZ;EAEF;EAEAC,sBAAsBA,CAACC,KAAK,EAAEC,YAA8B;IAC3D,IAAI,CAACC,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK;IAClC,IAAIC,cAAc,GAAG,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAC3D,IAAI,CAACzB,cAAc,CAACU,QAAQ,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI;IACpD,IAAID,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,KAAK,IAAIA,cAAc,IAAI,KAAK,EAAE;MAC/G,IAAI,CAACH,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACO,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAACpC,MAAM,CAACqC,IAAI,CAAC,yDAAyD,CAAC;MAC3E;IACD,CAAC,MAAM;MACN,IAAIC,MAAM,GAAG;QACZC,mBAAmB,EAAE;OACrB;MACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACvC,YAAY,CAACwC,IAAI,CAACf,YAAY,EAAEY,MAAM,CAAC;IAC7D;EACD;EAEAI,UAAUA,CAAA;IACT,MAAMC,IAAI,GAAS,IAAI,CAAChB,QAAQ,CAAC,CAAC,CAAC;IACnC,MAAMiB,EAAE,GAAa,IAAIC,QAAQ,EAAE;IACnCD,EAAE,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IACvB,IAAI,CAACH,QAAQ,EAAEO,IAAI,EAAE;IACrB,IAAI,CAACtC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACP,YAAY,CAAC8C,UAAU,CAACJ,EAAE,CAAC,CAACK,SAAS,CAACC,IAAI,IAAG;MACjD,IAAI,CAACC,kBAAkB,CAACD,IAAI,CAAC;IAC9B,CAAC,EAAEE,GAAG,IAAG;MACR,IAAI,CAAClB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAAC3B,UAAU,GAAG,KAAK;MACvB,IAAI,CAACT,MAAM,CAACqD,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IACjC,CAAC,CAAC;EACH;EACAD,kBAAkBA,CAACD,IAAI;IACtB,IAAII,WAAW,GAAG;MACjB,UAAU,EAAEJ,IAAI,CAAC,UAAU;KAE3B;IACD,IAAI,CAAChD,YAAY,CAACiD,kBAAkB,CAACG,WAAW,CAAC,CAC/CL,SAAS,CAACC,IAAI,IAAG;MACjB,IAAI,CAAChB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5CmB,UAAU,CAAC,MAAK;QACf,IAAI,CAACvD,MAAM,CAACwD,OAAO,CAAC,+CAA+C,GAAGN,IAAI,CAACO,aAAa,CAAC;QACzF,IAAI,CAAC9B,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACnB,cAAc,GAAG,IAAIpB,cAAc,EAAE;QAC1C,IAAI,CAACqB,UAAU,GAAG,KAAK;QACvB,IAAI,CAACL,MAAM,CAACsD,aAAa,CAAC,GAAG,EAAE;UAAEC,kBAAkB,EAAE;QAAI,CAAE,CAAC,CAACC,IAAI,CAAC,MACjE,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,mDAAmD,CAAC,CAAC,CAAC;MAC9E,CAAC,EAAE,IAAI,CAAC;IACT,CAAC,EAAET,GAAG,IAAG;MACR,IAAI,CAAC3C,UAAU,GAAG,KAAK;MACvB,IAAI,CAACyB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAACpC,MAAM,CAACqD,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IACjC,CAAC,CAAC;EACJ;;;;;;;;;;;;;;;;;;;cAhGC9D,SAAS;QAAAwE,IAAA,GAAC,cAAc;MAAA;;;;AALbjE,qBAAqB,GAAAkE,UAAA,EALjC1E,SAAS,CAAC;EACV2E,QAAQ,EAAE,mBAAmB;EAC7BhD,QAAA,EAAAiD,oBAA6C;;CAE7C,CAAC,C,EACWpE,qBAAqB,CAsGjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}