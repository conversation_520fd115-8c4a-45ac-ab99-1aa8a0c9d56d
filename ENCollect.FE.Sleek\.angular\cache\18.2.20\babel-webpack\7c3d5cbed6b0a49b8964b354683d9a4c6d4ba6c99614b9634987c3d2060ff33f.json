{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable, EventEmitter } from '@angular/core';\nimport { ApiService } from '../shared/services/api.service';\nimport * as moment from 'moment/moment';\nlet repoConfigService = class repoConfigService {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.leadUpdate = new EventEmitter();\n    this.selectedResults = new EventEmitter();\n  }\n  onleadUpdate(item) {\n    this.leadUpdate.emit(item);\n  }\n  // Internal FIlters\n  filterArrayByState(arr, val) {\n    var cityList = [];\n    for (var i = 0; i < arr.length; i++) {\n      if (arr[i].STATE === val) {\n        cityList.push(arr[i]);\n      }\n    }\n    return cityList;\n  }\n  generalFilterByKey(arr, val, key) {\n    var cityList = [];\n    for (var i = 0; i < arr.length; i++) {\n      if (arr[i][key] === val) {\n        cityList.push(arr[i]);\n      }\n    }\n    return cityList;\n  }\n  generalKeySort(array, key) {\n    return array.sort(function (a, b) {\n      var x = a[key];\n      var y = b[key];\n      return x < y ? -1 : x > y ? 1 : 0;\n    });\n  }\n  cityNameSort(a, b) {\n    const nameA = a.CITY.toUpperCase();\n    const nameB = b.CITY.toUpperCase();\n    let comparison = 0;\n    if (nameA > nameB) {\n      comparison = 1;\n    } else if (nameA < nameB) {\n      comparison = -1;\n    }\n    return comparison;\n  }\n  removeDuplicates(myArr, prop) {\n    return myArr.filter((obj, pos, arr) => {\n      return arr.map(mapObj => mapObj[prop]).indexOf(obj[prop]) === pos;\n    });\n  }\n  convertToCSV(objArray) {\n    var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;\n    var str = '';\n    var row = \"\";\n    for (var index in objArray[0]) {\n      //Now convert each value to string and comma-separated\n      row += index + ',';\n    }\n    row = row.slice(0, -1);\n    //append Label row with line break\n    str += row + '\\r\\n';\n    for (var i = 0; i < array.length; i++) {\n      var line = '';\n      for (var index in array[i]) {\n        if (line != '') line += ',';\n        line += array[i][index];\n      }\n      str += line + '\\r\\n';\n    }\n    return str;\n  }\n  findObjectByKey(arr, key, value) {\n    var results = arr.find(obj => obj[key] == value);\n    return results;\n  }\n  getByValue(arr, value) {\n    for (var i = 0, iLen = arr.length; i < iLen; i++) {\n      if (arr[i].id == value) {\n        return arr[i];\n      }\n      ;\n    }\n  }\n  convertDate(data) {\n    if (data['date']) {\n      var dateFormate = moment.utc(data['date']['month'].toString() + '/' + data['date']['day'].toString() + '/' + data['date']['year'].toString());\n      return dateFormate;\n    } else {\n      data = new Date(data);\n      let getDate = data.getDate();\n      let getMonth = data.getMonth();\n      let getFullYear = data.getFullYear();\n      var dateFormate = moment.utc(getDate.toString() + '/' + getMonth.toString() + '/' + getFullYear.toString());\n      return dateFormate;\n    }\n  }\n  legalAllocationStatusList() {\n    return ['Failed', 'Invalid File Format', 'Partially Processed', 'Processed', 'Uploaded'];\n  }\n  getTemplateData(caseType) {\n    // [{\"id\":\"Lt1\",\"name\":\"Arbitration\"},{\"id\":\"Lt2\",\"name\":\"Section9\"},{\"id\":\"Lt3\",\"name\":\"Section138\"},{\"id\":\"Lt4\",\"name\":\"ExecutionProceedings\"},{\"id\":\"Lt5\",\"name\":\"AgainstCases\"},{\"id\":\"Lt6\",\"name\":\"LRNNotice\"},{\"id\":\"Lt7\",\"name\":\"DemandNotice\"},]\n    const arbitrationLevel1 = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"caseType\",\n        \"name\": \"Case Type\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case Type\",\n          \"key\": \"caseType\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"customerAccountNo\",\n        \"name\": \"Loan No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan No.\",\n          \"key\": \"customerAccountNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"firstName\",\n        \"name\": \"Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Name\",\n          \"key\": \"firstName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"branch\",\n        \"name\": \"Branch Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Branch Name\",\n          \"key\": \"branch\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"assetMake\",\n        \"name\": \"Vehicle Make\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Vehicle Make\",\n          \"key\": \"assetMake\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"rcBookNo\",\n        \"name\": \"Registration No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Registration No.\",\n          \"key\": \"rcBookNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"engineNo\",\n        \"name\": \"Engine No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Engine No.\",\n          \"key\": \"engineNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"chassisNo\",\n        \"name\": \"Chassis No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Chassis No.\",\n          \"key\": \"chassisNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"pos\",\n        \"name\": \"POS\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"POS\",\n          \"key\": \"pos\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAmount\",\n        \"name\": \"Loan Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Amount\",\n          \"key\": \"loanAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"currentStatus\",\n        \"name\": \"Current Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Current Status\",\n          \"key\": \"currentStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"sector17OrderDate\",\n        \"name\": \"Sector-17 Order Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Sector-17 Order Date\",\n          \"key\": \"sector17OrderDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"receiverName\",\n        \"name\": \"Receiver Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Receiver Name\",\n          \"key\": \"receiverName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"^[A-Za-z -]+$\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"dateOfAward\",\n        \"name\": \"Date of Award\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Date of Award\",\n          \"key\": \"dateOfAward\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"amountOfAward\",\n        \"name\": \"Amount of Award\",\n        \"type\": \"currencyField\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Amount of Award\",\n          \"key\": \"amountOfAward\",\n          \"independentTo\": null,\n          \"type\": \"currencyField\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"arbitratorName\",\n        \"name\": \"Arbitrator Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Arbitrator Name\",\n          \"key\": \"arbitratorName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"[A-Za-z ]{1,50}\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalAgencyName\",\n        \"name\": \"Legal Agency Name\",\n        \"type\": \"typeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Legal Agency Name\",\n          \"key\": \"legalAgencyName\",\n          \"independentTo\": null,\n          \"type\": \"typeahead\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"options\": [{\n            \"key\": \"key\",\n            \"value\": \"value\"\n          }],\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"advocateName\",\n        \"name\": \"Advocate Name\",\n        \"type\": \"advocatetypeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Advocate Name\",\n          \"key\": \"advocateName\",\n          \"independentTo\": null,\n          \"type\": \"advocatetypeahead\",\n          \"required\": false,\n          \"options\": [],\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"foreclosureAmount\",\n        \"name\": \"Foreclosure Amount\",\n        \"type\": \"currencyField\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Foreclosure Amount\",\n          \"key\": \"foreclosureAmount\",\n          \"independentTo\": null,\n          \"type\": \"currencyField\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"totalLossAmount\",\n        \"name\": \"Total Loss Amount\",\n        \"type\": \"currencyField\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Total Loss Amount\",\n          \"key\": \"totalLossAmount\",\n          \"independentTo\": null,\n          \"type\": \"currencyField\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"arbitrationRefNo\",\n        \"name\": \"ARB Ref No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"ARB Ref No.\",\n          \"key\": \"arbitrationRefNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"liveClosed\",\n        \"name\": \"Live/Closed\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Live/Closed\",\n          \"key\": \"liveClosed\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"updatedStatus\",\n        \"name\": \"Updated Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Updated Status\",\n          \"key\": \"updatedStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false,\n          \"onKeypress\": function (model, options, _this, event) {\n            // DO SEARCH (WORKING!)\n            console.log(event);\n          }\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"allocationDate\",\n        \"name\": \"Allocation Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Allocation Date\",\n          \"key\": \"allocationDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"lastDate\",\n        \"name\": \"Last Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Last Date\",\n          \"key\": \"lastDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"nextDate\",\n        \"name\": \"Next Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Next Date\",\n          \"key\": \"nextDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"zone\",\n        \"name\": \"Zone\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Zone\",\n          \"key\": \"zone\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }\n      // Table of Hearing Date\n      // Hearing Remarks\n      ]\n    }];\n    const sec9Level1 = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"caseType\",\n        \"name\": \"Case Type\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case Type\",\n          \"key\": \"caseType\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"customerAccountNo\",\n        \"name\": \"Agreement No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Agreement No.\",\n          \"key\": \"customerAccountNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"firstName\",\n        \"name\": \"Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Name\",\n          \"key\": \"firstName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"rcBookNo\",\n        \"name\": \"Registration No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Registration No.\",\n          \"key\": \"rcBookNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"engineNo\",\n        \"name\": \"Engine No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Engine No.\",\n          \"key\": \"engineNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"chassisNo\",\n        \"name\": \"Chassis No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Chassis No.\",\n          \"key\": \"chassisNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"pos\",\n        \"name\": \"POS\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"POS\",\n          \"key\": \"pos\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAmount\",\n        \"name\": \"Loan Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Amount\",\n          \"key\": \"loanAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"branch\",\n        \"name\": \"Branch\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Branch\",\n          \"key\": \"branch\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"assetMake\",\n        \"name\": \"Vehicle Make\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Vehicle Make\",\n          \"key\": \"assetMake\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"allocationDate\",\n        \"name\": \"Date of Allocation\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Date of Allocation\",\n          \"key\": \"allocationDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"caseNo\",\n        \"name\": \"Case No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case No.\",\n          \"key\": \"caseNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"zone\",\n        \"name\": \"Zone\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Zone\",\n          \"key\": \"zone\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"lastDate\",\n        \"name\": \"Last Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Last Date\",\n          \"key\": \"lastDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"nextDate\",\n        \"name\": \"Next Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Next Date\",\n          \"key\": \"nextDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"currentStatus\",\n        \"name\": \"Current Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Current Status\",\n          \"key\": \"currentStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"dateOfRO\",\n        \"name\": \"Date of RO\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Date of RO\",\n          \"key\": \"dateOfRO\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"orderValidity\",\n        \"name\": \"Order Validity\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Order Validity\",\n          \"key\": \"orderValidity\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"courtLocation\",\n        \"name\": \"Court/Location\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Court/Location\",\n          \"key\": \"courtLocation\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"judgeName\",\n        \"name\": \"Judge Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Judge Name\",\n          \"key\": \"judgeName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"nameOfReceiver\",\n        \"name\": \"Name of Receiver\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Name of Receiver\",\n          \"key\": \"nameOfReceiver\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalAgencyName\",\n        \"name\": \"Legal Agency Name\",\n        \"type\": \"typeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Legal Agency Name\",\n          \"key\": \"legalAgencyName\",\n          \"independentTo\": null,\n          \"type\": \"typeahead\",\n          \"options\": [{\n            \"key\": \"key\",\n            \"value\": \"value\"\n          }],\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"advocateName\",\n        \"name\": \"Advocate Name\",\n        \"type\": \"advocatetypeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Advocate Name\",\n          \"key\": \"advocateName\",\n          \"independentTo\": null,\n          \"type\": \"advocatetypeahead\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"liveClosed\",\n        \"name\": \"Live/Closed\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Live/Closed\",\n          \"key\": \"liveClosed\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"updatedStatus\",\n        \"name\": \"Updated Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Updated Status\",\n          \"key\": \"updatedStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }\n      // Table of Hearing Date\n      // Hearing Remarks\n      ]\n    }];\n    const sec138Level1 = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"caseType\",\n        \"name\": \"Case Type\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case Type\",\n          \"key\": \"caseType\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"customerAccountNo\",\n        \"name\": \"Agreement No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Agreement No.\",\n          \"key\": \"customerAccountNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"firstName\",\n        \"name\": \"Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Name\",\n          \"key\": \"firstName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"assetMake\",\n        \"name\": \"Vehicle Make\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Vehicle Make\",\n          \"key\": \"assetMake\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"rcBookNo\",\n        \"name\": \"Registration No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Registration No.\",\n          \"key\": \"rcBookNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"engineNo\",\n        \"name\": \"Engine No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Engine No.\",\n          \"key\": \"engineNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"chassisNo\",\n        \"name\": \"Chassis No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Chassis No.\",\n          \"key\": \"chassisNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"pos\",\n        \"name\": \"POS\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"POS\",\n          \"key\": \"pos\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAmount\",\n        \"name\": \"Loan Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Amount\",\n          \"key\": \"loanAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"branch\",\n        \"name\": \"Branch\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Branch\",\n          \"key\": \"branch\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"updatedStatus\",\n        \"name\": \"Updated Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Updated Status\",\n          \"key\": \"updatedStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"caseNo\",\n        \"name\": \"Case No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case No.\",\n          \"key\": \"caseNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"zone\",\n        \"name\": \"Zone\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Zone\",\n          \"key\": \"zone\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"lastDate\",\n        \"name\": \"Last Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Last Date\",\n          \"key\": \"lastDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"nextDate\",\n        \"name\": \"Next Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Next Date\",\n          \"key\": \"nextDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"currentStatus\",\n        \"name\": \"Current Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Current Status\",\n          \"key\": \"currentStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"courtLocation\",\n        \"name\": \"Court/Location\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Court/Location\",\n          \"key\": \"courtLocation\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"judgeName\",\n        \"name\": \"Judge Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Judge Name\",\n          \"key\": \"judgeName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalAgencyName\",\n        \"name\": \"Legal Agency Name\",\n        \"type\": \"typeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Legal Agency Name\",\n          \"key\": \"legalAgencyName\",\n          \"independentTo\": null,\n          \"type\": \"typeahead\",\n          \"options\": [{\n            \"key\": \"key\",\n            \"value\": \"value\"\n          }],\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"advocateName\",\n        \"name\": \"Advocate Name\",\n        \"type\": \"advocatetypeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Advocate Name\",\n          \"key\": \"advocateName\",\n          \"independentTo\": null,\n          \"type\": \"advocatetypeahead\",\n          \"required\": false,\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"chequeAmount\",\n        \"name\": \"Cheque Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Cheque Amount\",\n          \"key\": \"chequeAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"allocationDate\",\n        \"name\": \"Date of Allocation\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Date of Allocation\",\n          \"key\": \"allocationDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"liveClosed\",\n        \"name\": \"Live/Closed\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Live/Closed\",\n          \"key\": \"liveClosed\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"foreclosureAmount\",\n        \"name\": \"Foreclosure Amount\",\n        \"type\": \"currencyField\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Foreclosure Amount\",\n          \"key\": \"foreclosureAmount\",\n          \"independentTo\": null,\n          \"type\": \"currencyField\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"totalLossAmount\",\n        \"name\": \"Total Loss Amount\",\n        \"type\": \"currencyField\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Total Loss Amount\",\n          \"key\": \"totalLossAmount\",\n          \"independentTo\": null,\n          \"type\": \"currencyField\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }\n      // Table of Hearing Date\n      // Hearing Remarks\n      ]\n    }];\n    const executionLevel1 = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"caseType\",\n        \"name\": \"Case Type\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case Type\",\n          \"key\": \"caseType\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"customerAccountNo\",\n        \"name\": \"Agreement No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Agreement No.\",\n          \"key\": \"customerAccountNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"firstName\",\n        \"name\": \"Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Name\",\n          \"key\": \"firstName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"assetMake\",\n        \"name\": \"Vehicle Make\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Vehicle Make\",\n          \"key\": \"assetMake\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"rcBookNo\",\n        \"name\": \"Registration No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Registration No.\",\n          \"key\": \"rcBookNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"engineNo\",\n        \"name\": \"Engine No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Engine No.\",\n          \"key\": \"engineNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"chassisNo\",\n        \"name\": \"Chassis No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Chassis No.\",\n          \"key\": \"chassisNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"pos\",\n        \"name\": \"POS\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"POS\",\n          \"key\": \"pos\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAmount\",\n        \"name\": \"Loan Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Amount\",\n          \"key\": \"loanAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"branch\",\n        \"name\": \"Branch\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Branch\",\n          \"key\": \"branch\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"zone\",\n        \"name\": \"Zone\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Zone\",\n          \"key\": \"zone\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"lastDate\",\n        \"name\": \"Last Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Last Date\",\n          \"key\": \"lastDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"nextDate\",\n        \"name\": \"Next Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Next Date\",\n          \"key\": \"nextDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"currentStatus\",\n        \"name\": \"Current Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Current Status\",\n          \"key\": \"currentStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"courtLocation\",\n        \"name\": \"Court/Location\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Court/Location\",\n          \"key\": \"courtLocation\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"judgeName\",\n        \"name\": \"Judge Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Judge Name\",\n          \"key\": \"judgeName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"awardDated\",\n        \"name\": \"Award Dated\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Award Dated\",\n          \"key\": \"awardDated\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalAgencyName\",\n        \"name\": \"Legal Agency Name\",\n        \"type\": \"typeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Legal Agency Name\",\n          \"key\": \"legalAgencyName\",\n          \"independentTo\": null,\n          \"type\": \"typeahead\",\n          \"options\": [{\n            \"key\": \"key\",\n            \"value\": \"value\"\n          }],\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"advocateName\",\n        \"name\": \"Advocate Name\",\n        \"type\": \"advocatetypeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Advocate Name\",\n          \"key\": \"advocateName\",\n          \"independentTo\": null,\n          \"type\": \"advocatetypeahead\",\n          \"required\": false,\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"executionAmount\",\n        \"name\": \"Execution Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Execution Amount\",\n          \"key\": \"executionAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"foreclosureAmount\",\n        \"name\": \"Foreclosure Amount\",\n        \"type\": \"currencyField\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Foreclosure Amount\",\n          \"key\": \"foreclosureAmount\",\n          \"independentTo\": null,\n          \"type\": \"currencyField\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"totalLossAmount\",\n        \"name\": \"Total Loss Amount\",\n        \"type\": \"currencyField\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Total Loss Amount\",\n          \"key\": \"totalLossAmount\",\n          \"independentTo\": null,\n          \"type\": \"currencyField\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"allocationDate\",\n        \"name\": \"Date of Allocation\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Date of Allocation\",\n          \"key\": \"allocationDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"liveClosed\",\n        \"name\": \"Live/Closed\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Live/Closed\",\n          \"key\": \"liveClosed\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"updatedStatus\",\n        \"name\": \"Updated Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Updated Status\",\n          \"key\": \"updatedStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"caseNo\",\n        \"name\": \"Case No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case No.\",\n          \"key\": \"caseNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }\n      // Table of Hearing Date\n      // Hearing Remarks\n      ]\n    }];\n    const againstCasesLevel1 = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"caseType\",\n        \"name\": \"Case Type\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case Type\",\n          \"key\": \"caseType\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"customerAccountNo\",\n        \"name\": \"Agreement No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Agreement No.\",\n          \"key\": \"customerAccountNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"firstName\",\n        \"name\": \"Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Name\",\n          \"key\": \"firstName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"assetMake\",\n        \"name\": \"Vehicle Make\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Vehicle Make\",\n          \"key\": \"assetMake\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"rcBookNo\",\n        \"name\": \"Registration No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Registration No.\",\n          \"key\": \"rcBookNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"engineNo\",\n        \"name\": \"Engine No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Engine No.\",\n          \"key\": \"engineNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"chassisNo\",\n        \"name\": \"Chassis No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Chassis No.\",\n          \"key\": \"chassisNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"pos\",\n        \"name\": \"POS\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"POS\",\n          \"key\": \"pos\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAmount\",\n        \"name\": \"Loan Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Amount\",\n          \"key\": \"loanAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"branch\",\n        \"name\": \"Branch\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Branch\",\n          \"key\": \"branch\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"fillingDate\",\n        \"name\": \"Filling Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Filling Date\",\n          \"key\": \"fillingDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"zone\",\n        \"name\": \"Zone\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Zone\",\n          \"key\": \"zone\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"liveClosed\",\n        \"name\": \"Live/Closed\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Live/Closed\",\n          \"key\": \"liveClosed\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"updatedStatus\",\n        \"name\": \"Updated Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Updated Status\",\n          \"key\": \"updatedStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"caseNo\",\n        \"name\": \"Case No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case No.\",\n          \"key\": \"caseNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"natureOfCase\",\n        \"name\": \"Nature of Case\",\n        \"type\": \"natureCaseSelect\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Nature of Case\",\n          \"key\": \"natureOfCase\",\n          \"independentTo\": null,\n          \"type\": \"natureCaseSelect\",\n          \"options\": [{\n            \"key\": \"key\",\n            \"value\": \"value\"\n          }],\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"lastDate\",\n        \"name\": \"Last Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Last Date\",\n          \"key\": \"lastDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"nextDate\",\n        \"name\": \"Next Date\",\n        \"type\": \"datepicker\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Next Date\",\n          \"key\": \"nextDate\",\n          \"independentTo\": null,\n          \"type\": \"datepicker\",\n          \"required\": false,\n          \"options\": [],\n          \"noFutureDate\": \"true\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"currentStatus\",\n        \"name\": \"Current Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Current Status\",\n          \"key\": \"currentStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"courtLocation\",\n        \"name\": \"Court/Location\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Court/Location\",\n          \"key\": \"courtLocation\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"judgeName\",\n        \"name\": \"Judge Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Judge Name\",\n          \"key\": \"judgeName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalAgencyName\",\n        \"name\": \"Legal Agency Name\",\n        \"type\": \"typeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Legal Agency Name\",\n          \"key\": \"legalAgencyName\",\n          \"independentTo\": null,\n          \"type\": \"typeahead\",\n          \"options\": [{\n            \"key\": \"key\",\n            \"value\": \"value\"\n          }],\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"advocateName\",\n        \"name\": \"Advocate Name\",\n        \"type\": \"advocatetypeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Advocate Name\",\n          \"key\": \"advocateName\",\n          \"independentTo\": null,\n          \"type\": \"advocatetypeahead\",\n          \"required\": false,\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"contingentLiability\",\n        \"name\": \"ContingentLiability\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"ContingentLiability\",\n          \"key\": \"contingentLiability\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }\n      // Table of Hearing Date\n      // Hearing Remarks\n      ]\n    }];\n    const LRNDemandNoticeLevel1 = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"caseType\",\n        \"name\": \"Case Type\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case Type\",\n          \"key\": \"caseType\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"customerAccountNo\",\n        \"name\": \"Agreement No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Agreement No.\",\n          \"key\": \"customerAccountNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"firstName\",\n        \"name\": \"Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Name\",\n          \"key\": \"firstName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"assetMake\",\n        \"name\": \"Vehicle Make\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Vehicle Make\",\n          \"key\": \"assetMake\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"rcBookNo\",\n        \"name\": \"Registration No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Registration No.\",\n          \"key\": \"rcBookNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"engineNo\",\n        \"name\": \"Engine No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Engine No.\",\n          \"key\": \"engineNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"chassisNo\",\n        \"name\": \"Chassis No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Chassis No.\",\n          \"key\": \"chassisNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"pos\",\n        \"name\": \"POS\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"POS\",\n          \"key\": \"pos\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAmount\",\n        \"name\": \"Loan Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Amount\",\n          \"key\": \"loanAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"branch\",\n        \"name\": \"Branch\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Branch\",\n          \"key\": \"branch\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalAgencyName\",\n        \"name\": \"Legal Agency Name\",\n        \"type\": \"typeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Legal Agency Name\",\n          \"key\": \"legalAgencyName\",\n          \"independentTo\": null,\n          \"type\": \"typeahead\",\n          \"options\": [{\n            \"key\": \"key\",\n            \"value\": \"value\"\n          }],\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"advocateName\",\n        \"name\": \"Advocate Name\",\n        \"type\": \"advocatetypeahead\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Advocate Name\",\n          \"key\": \"advocateName\",\n          \"independentTo\": null,\n          \"type\": \"advocatetypeahead\",\n          \"required\": false,\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }]\n    }];\n    const commonLevel2 = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"level2date\",\n        \"name\": \"Case Date\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Case Date\",\n          \"key\": \"level2date\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"region\",\n        \"name\": \"Region\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Region\",\n          \"key\": \"region\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"openingDPD\",\n        \"name\": \"Opening DPD\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Opening DPD\",\n          \"key\": \"openingDPD\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"openingBucket\",\n        \"name\": \"Opening BKT\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Opening BKT\",\n          \"key\": \"openingBucket\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"ageing\",\n        \"name\": \"Ageing\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Ageing\",\n          \"key\": \"ageing\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAgreementId\",\n        \"name\": \"Loan Agreement No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Agreement No.\",\n          \"key\": \"loanAgreementId\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"pattern\": \"\",\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"customerName\",\n        \"name\": \"Customer Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Customer Name\",\n          \"key\": \"customerName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"currentApplicationStatus\",\n        \"name\": \"Current Staus of Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Current Staus of Status\",\n          \"key\": \"currentApplicationStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"finalRemarks\",\n        \"name\": \"Final Remarks\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Final Remarks\",\n          \"key\": \"finalRemarks\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalFee\",\n        \"name\": \"Legal Fee\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Legal Fee\",\n          \"key\": \"legalFee\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"openingRepoStock\",\n        \"name\": \"Opening Repo Stock\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Opening Repo Stock\",\n          \"key\": \"openingRepoStock\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": false\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"branch\",\n        \"name\": \"Branch\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Branch\",\n          \"key\": \"branch\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"assetMake\",\n        \"name\": \"Asset Make\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Asset Make\",\n          \"key\": \"assetMake\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanAmount\",\n        \"name\": \"Loan Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Amount\",\n          \"key\": \"loanAmount\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"bucket\",\n        \"name\": \"Bucket\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Bucket\",\n          \"key\": \"bucket\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"billedInstallement\",\n        \"name\": \"Billed Installement\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Billed Installement\",\n          \"key\": \"billedInstallement\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"unbilledInstallement\",\n        \"name\": \"Unbilled Installement\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Unbilled Installement\",\n          \"key\": \"unbilledInstallement\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"tenure\",\n        \"name\": \"Tenure\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Tenure\",\n          \"key\": \"tenure\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"dueDate\",\n        \"name\": \"Due Date\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Due Date\",\n          \"key\": \"dueDate\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"zone\",\n        \"name\": \"Zone\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Zone\",\n          \"key\": \"zone\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"rcm\",\n        \"name\": \"RCM\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"RCM\",\n          \"key\": \"rcm\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"collectionManager\",\n        \"name\": \"Collection Manager\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Collection Manager\",\n          \"key\": \"collectionManager\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"collectionManagerW\",\n        \"name\": \"Collection Manager W\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Collection Manager W\",\n          \"key\": \"collectionManagerW\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"accOpeningStatus\",\n        \"name\": \"A/C Opening Status\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"A/C Opening Status\",\n          \"key\": \"accOpeningStatus\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"appId\",\n        \"name\": \"Application Id\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Application Id\",\n          \"key\": \"appId\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanBookingDate\",\n        \"name\": \"Loan Booking Date\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Booking Date\",\n          \"key\": \"loanBookingDate\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"loanMaturityDate\",\n        \"name\": \"Loan Maturity Date\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Loan Maturity Date\",\n          \"key\": \"loanMaturityDate\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"firstEmiDate\",\n        \"name\": \"First EMI Due Date\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"First EMI Due Date\",\n          \"key\": \"firstEmiDate\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"disbursalAmt\",\n        \"name\": \"Disbursal Amount\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Disbursal Amount\",\n          \"key\": \"disbursalAmt\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"resAdd\",\n        \"name\": \"Resedential Add.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Resedential Add.\",\n          \"key\": \"resAdd\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"resNo\",\n        \"name\": \"Resedential No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Resedential No.\",\n          \"key\": \"resNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"offAdd\",\n        \"name\": \"Office Add.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Office Add.\",\n          \"key\": \"offAdd\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"officeNo\",\n        \"name\": \"Office No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Office No.\",\n          \"key\": \"officeNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"permAdd\",\n        \"name\": \"Permanent Add.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Permanent Add.\",\n          \"key\": \"permAdd\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"permNo\",\n        \"name\": \"Permanent No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Permanent No.\",\n          \"key\": \"permNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"coappName\",\n        \"name\": \"Co-App-Name\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Co-App-Name\",\n          \"key\": \"coappName\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"coappresAdd\",\n        \"name\": \"Co-App-Res Add.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Co-App-Res Add.\",\n          \"key\": \"coappresAdd\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"coappresNo\",\n        \"name\": \"Co-App-Res No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Co-App-Res No.\",\n          \"key\": \"coappresNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"coappoffAdd\",\n        \"name\": \"Co-App-Office Add.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Co-App-Office Add.\",\n          \"key\": \"coappoffAdd\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"coappoffNo\",\n        \"name\": \"Co-App-Office No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Co-App-Office No.\",\n          \"key\": \"coappoffNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"coapppermAdd\",\n        \"name\": \"Co-App-Perm Add.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Co-App-Perm Add.\",\n          \"key\": \"coapppermAdd\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"coapppermNo\",\n        \"name\": \"Co-App-Perm No.\",\n        \"type\": \"input\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Co-App-Perm No.\",\n          \"key\": \"coapppermNo\",\n          \"independentTo\": null,\n          \"type\": \"input\",\n          \"required\": false,\n          \"disabled\": true\n        },\n        \"validators\": {\n          \"validation\": []\n        }\n      }]\n    }];\n    const commonUploadDocument = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"LoanAgreement\",\n        \"mediaName\": \"LoanAgreement\",\n        \"name\": \"Loan Agreement\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Loan Agreement\",\n          \"key\": \"LoanAgreement\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"appform\",\n        \"mediaName\": \"appform\",\n        \"name\": \"Application Form\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Application Form\",\n          \"key\": \"appform\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"hypotecationProof\",\n        \"mediaName\": \"hypotecationProof\",\n        \"name\": \"Hypothecation Proof\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Hypothecation Proof\",\n          \"key\": \"hypotecationProof\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"stOfAccount\",\n        \"mediaName\": \"stOfAccount\",\n        \"name\": \"Statetment of Account\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Statetment of Account\",\n          \"key\": \"stOfAccount\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"foreclosureStatement\",\n        \"mediaName\": \"foreclosureStatement\",\n        \"name\": \"Foreclosure Statement\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Foreclosure Statement\",\n          \"key\": \"foreclosureStatement\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"deviationApprovals\",\n        \"mediaName\": \"deviationApprovals\",\n        \"name\": \"Approvals\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Approvals\",\n          \"key\": \"deviationApprovals\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"uploadLRN\",\n        \"mediaName\": \"uploadLRN\",\n        \"name\": \"Upload Latest LRN\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Upload Latest LRN\",\n          \"key\": \"uploadLRN\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"uploadDemandNotice\",\n        \"mediaName\": \"uploadDemandNotice\",\n        \"name\": \"Upload Latest Demand Notice\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Upload Latest Demand Notice\",\n          \"key\": \"uploadDemandNotice\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"LRN1\",\n        \"mediaName\": \"LRN1\",\n        \"name\": \"LRN 1\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Generate LRN 1\",\n          \"key\": \"LRN1\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"LRN2\",\n        \"mediaName\": \"LRN2\",\n        \"name\": \"LRN 2\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"LRN 2\",\n          \"key\": \"LRN2\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"LRN3\",\n        \"mediaName\": \"LRN3\",\n        \"name\": \"LRN 3\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"LRN 3\",\n          \"key\": \"LRN3\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"LRN4\",\n        \"mediaName\": \"LRN4\",\n        \"name\": \"LRN 4\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"LRN 4\",\n          \"key\": \"LRN4\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"LRN5\",\n        \"mediaName\": \"LRN5\",\n        \"name\": \"LRN 5\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"LRN 5\",\n          \"key\": \"LRN5\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"LRN6\",\n        \"mediaName\": \"LRN6\",\n        \"name\": \"LRN 6\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"LRN 6\",\n          \"key\": \"LRN6\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"DemandNotice1\",\n        \"mediaName\": \"DemandNotice1\",\n        \"name\": \"Demand Notice 1\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Demand Notice 1\",\n          \"key\": \"DemandNotice1\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"DemandNotice2\",\n        \"mediaName\": \"DemandNotice2\",\n        \"name\": \" Demand Notice 2\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Demand Notice 2\",\n          \"key\": \"DemandNotice2\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"DemandNotice3\",\n        \"mediaName\": \"DemandNotice3\",\n        \"name\": \"Demand Notice 3\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Demand Notice 3\",\n          \"key\": \"DemandNotice3\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"DemandNotice4\",\n        \"mediaName\": \"DemandNotice4\",\n        \"name\": \"Demand Notice 4\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Demand Notice 4\",\n          \"key\": \"DemandNotice4\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"DemandNotice5\",\n        \"mediaName\": \"DemandNotice5\",\n        \"name\": \"Demand Notice 5\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Demand Notice 5\",\n          \"key\": \"DemandNotice5\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"DemandNotice6\",\n        \"mediaName\": \"DemandNotice6\",\n        \"name\": \"Demand Notice 6\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Demand Notice 6\",\n          \"key\": \"DemandNotice6\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalDoc1\",\n        \"mediaName\": \"legalDoc1\",\n        \"name\": \"Legal Document 1\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Legal Document 1\",\n          \"key\": \"legalDoc1\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalDoc2\",\n        \"mediaName\": \"legalDoc2\",\n        \"name\": \"Legal Document 2\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Legal Document 2\",\n          \"key\": \"legalDoc2\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalDoc3\",\n        \"mediaName\": \"legalDoc3\",\n        \"name\": \"Legal Document 3\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Legal Document 3\",\n          \"key\": \"legalDoc3\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalDoc4\",\n        \"mediaName\": \"legalDoc4\",\n        \"name\": \"Legal Document 4\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Legal Document 4\",\n          \"key\": \"legalDoc4\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"legalDoc5\",\n        \"mediaName\": \"legalDoc5\",\n        \"name\": \"Legal Document 5\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"Legal Document 5\",\n          \"key\": \"legalDoc5\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"DeviationDocument\",\n        \"mediaName\": \"DeviationDocument\",\n        \"name\": \"DeviationDocument\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"disabled\": false,\n        \"templateOptions\": {\n          \"label\": \"DeviationDocument\",\n          \"key\": \"DeviationDocument\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }]\n    }];\n    const arbitrationUploadDocument = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"sec17Order\",\n        \"mediaName\": \"sec17Order\",\n        \"name\": \"Section 17 Order\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Section 17 Order\",\n          \"key\": \"sec17Order\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }, {\n        \"className\": \"col-md-6\",\n        \"key\": \"awardCopy\",\n        \"mediaName\": \"awardCopy\",\n        \"name\": \"Award Copy\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Award Copy\",\n          \"key\": \"awardCopy\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }]\n    }];\n    const sec9UploadDocument = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"sec9Order\",\n        \"mediaName\": \"sec9Order\",\n        \"name\": \"Section 9 Order\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Section 9 Order\",\n          \"key\": \"sec9Order\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }]\n    }];\n    const againstCasesUploadDocument = [{\n      \"fieldGroupClassName\": \"row\",\n      \"fieldGroup\": [{\n        \"className\": \"col-md-6\",\n        \"key\": \"finalOrder\",\n        \"mediaName\": \"finalOrder\",\n        \"name\": \"Final Order\",\n        \"type\": \"file\",\n        \"defaultValue\": \"\",\n        \"templateOptions\": {\n          \"label\": \"Final Order\",\n          \"key\": \"finalOrder\",\n          \"type\": \"file\",\n          \"required\": false,\n          \"disabled\": false\n        }\n      }]\n    }];\n    const obj = [{\n      \"levelName\": \"Level 1\",\n      \"level\": [{\n        \"name\": \"Level 1\",\n        \"active\": true,\n        \"fields\": []\n      }]\n    }, {\n      \"levelName\": \"Level 2\",\n      \"level\": [{\n        \"name\": \"Level 2\",\n        \"active\": true,\n        //changed to true by default shud be false Vinay\n        \"fields\": []\n      }]\n    }, {\n      \"levelName\": \"Upload Document\",\n      \"level\": [{\n        \"name\": \"Upload Document\",\n        \"active\": false,\n        \"fields\": [{\n          \"fieldGroupClassName\": \"row\",\n          \"fieldGroup\": [\n            // {\n            //   \"className\": \"col-md-6\",\n            //   \"key\": \"generateLRN\",\n            //   \"name\": \"Generate LRN\",\n            //   \"type\": \"file\",\n            //   \"defaultValue\": \"\",\n            //   \"disabled\": false,\n            //   \"templateOptions\": {\n            //     \"label\": \"Generate LRN\",\n            //     \"key\": \"generateLRN\",\n            //     \"type\": \"file\",\n            //     \"required\": false,\n            //     \"disabled\": false\n            //   }\n            // },\n            // {\n            //   \"className\": \"col-md-6\",\n            //   \"key\": \"generatedemandNotice\",\n            //   \"name\": \"Generate Demand Notice\",\n            //   \"type\": \"file\",\n            //   \"defaultValue\": \"\",\n            //   \"disabled\": false,\n            //   \"templateOptions\": {\n            //     \"label\": \"Generate Demand Notice\",\n            //     \"key\": \"generatedemandNotice\",\n            //     \"type\": \"file\",\n            //     \"required\": false,\n            //     \"disabled\": false\n            //   }\n            // },\n          ]\n        }]\n      }]\n    }];\n    // For Level 1\n    if (caseType == 'Arbitration') {\n      obj[0].level[0].fields = arbitrationLevel1;\n    }\n    if (caseType == 'Section9') {\n      obj[0].level[0].fields = sec9Level1;\n    }\n    if (caseType == 'Section138') {\n      obj[0].level[0].fields = sec138Level1;\n    }\n    if (caseType == 'ExecutionProceedings') {\n      obj[0].level[0].fields = executionLevel1;\n    }\n    if (caseType == 'AgainstCases') {\n      obj[0].level[0].fields = againstCasesLevel1;\n    }\n    if (caseType == 'LRNNotice' || caseType == 'DemandNotice') {\n      obj[0].level[0].fields = LRNDemandNoticeLevel1;\n    }\n    // For Level 2\n    obj[1].level[0].fields = commonLevel2;\n    // For Upload Document\n    obj[2].level[0].fields = commonUploadDocument;\n    const docLength = commonUploadDocument[0].fieldGroup.length;\n    if (caseType == 'Arbitration') {\n      const fields = arbitrationUploadDocument[0].fieldGroup;\n      fields.forEach(f => obj[2].level[0].fields[0].fieldGroup.splice(docLength - 5, 0, f));\n    }\n    if (caseType == 'Section9') {\n      const fields = sec9UploadDocument[0].fieldGroup;\n      fields.forEach(f => obj[2].level[0].fields[0].fieldGroup.splice(docLength - 5, 0, f));\n    }\n    if (caseType == 'AgainstCases') {\n      const fields = againstCasesUploadDocument[0].fieldGroup;\n      fields.forEach(f => obj[2].level[0].fields[0].fieldGroup.splice(docLength - 5, 0, f));\n    }\n    return obj;\n  }\n  noticeDoc(val) {\n    let objArray = [\"NoticeSentToBorrower\", \"NoticeSentToCo-Borrower\", \"NoticeSentToGuarantor\"];\n    let findVal = objArray.find(v => v.toLowerCase() == val.toLowerCase());\n    return findVal;\n  }\n  recallDoc(val) {\n    let objArray = [\"RecallNoticeSent\", \"RecallNoticeSentToGuarantor\"];\n    let findVal = objArray.find(v => v.toLowerCase() == val.toLowerCase());\n    return findVal;\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ApiService\n    }];\n  }\n};\nrepoConfigService = __decorate([Injectable({\n  providedIn: 'root'\n})], repoConfigService);\nexport { repoConfigService };", "map": {"version": 3, "names": ["Injectable", "EventEmitter", "ApiService", "moment", "repoConfigService", "constructor", "apiService", "leadUpdate", "selectedResults", "onleadUpdate", "item", "emit", "filterArrayByState", "arr", "val", "cityList", "i", "length", "STATE", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "sort", "a", "b", "x", "y", "cityNameSort", "nameA", "CITY", "toUpperCase", "nameB", "comparison", "removeDuplicates", "myArr", "prop", "filter", "obj", "pos", "map", "mapObj", "indexOf", "convertToCSV", "obj<PERSON><PERSON>y", "JSON", "parse", "str", "row", "index", "slice", "line", "findObjectByKey", "value", "results", "find", "getByValue", "iLen", "id", "convertDate", "data", "dateFormate", "utc", "toString", "Date", "getDate", "getMonth", "getFullYear", "legalAllocationStatusList", "getTemplateData", "caseType", "arbitrationLevel1", "onKeypress", "model", "options", "_this", "event", "console", "log", "sec9Level1", "sec138Level1", "executionLevel1", "againstCasesLevel1", "LRNDemandNoticeLevel1", "commonLevel2", "commonUploadDocument", "arbitrationUploadDocument", "sec9UploadDocument", "againstCasesUploadDocument", "level", "fields", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldGroup", "for<PERSON>ach", "f", "splice", "noticeDoc", "findVal", "v", "toLowerCase", "recallDoc", "__decorate", "providedIn"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal-custom\\legal-custom-config.service.ts"], "sourcesContent": ["import { Injectable, EventEmitter } from '@angular/core';\r\nimport { ApiService } from '../shared/services/api.service';\r\nimport * as moment from 'moment/moment';\r\n\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class repoConfigService {\r\n  leadUpdate = new EventEmitter();\r\n  selectedResults: EventEmitter<any>;\r\n  constructor(private apiService: ApiService) {\r\n    this.selectedResults = new EventEmitter<any>();\r\n  }\r\n\r\n  onleadUpdate(item: any) {\r\n    this.leadUpdate.emit(item);\r\n  }\r\n  // Internal FIlters\r\n  filterArrayByState(arr, val) {\r\n    var cityList = []\r\n    for (var i = 0; i < arr.length; i++) {\r\n      if (arr[i].STATE === val) {\r\n        cityList.push(arr[i]);\r\n      }\r\n    }\r\n    return cityList\r\n  }\r\n  generalFilterByKey(arr, val, key) {\r\n    var cityList = []\r\n    for (var i = 0; i < arr.length; i++) {\r\n      if (arr[i][key] === val) {\r\n        cityList.push(arr[i]);\r\n      }\r\n    }\r\n    return cityList\r\n  }\r\n  generalKeySort(array, key) {\r\n    return array.sort(function (a, b) {\r\n      var x = a[key]; var y = b[key];\r\n      return ((x < y) ? -1 : ((x > y) ? 1 : 0));\r\n    });\r\n  }\r\n  cityNameSort(a, b) {\r\n    const nameA = a.CITY.toUpperCase();\r\n    const nameB = b.CITY.toUpperCase();\r\n\r\n    let comparison = 0;\r\n    if (nameA > nameB) {\r\n      comparison = 1;\r\n    } else if (nameA < nameB) {\r\n      comparison = -1;\r\n    }\r\n    return comparison;\r\n  }\r\n  removeDuplicates(myArr, prop) {\r\n    return myArr.filter((obj, pos, arr) => {\r\n      return arr.map(mapObj =>\r\n        mapObj[prop]).indexOf(obj[prop]) === pos;\r\n    });\r\n  }\r\n  convertToCSV(objArray) {\r\n    var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;\r\n    var str = '';\r\n    var row = \"\";\r\n\r\n    for (var index in objArray[0]) {\r\n      //Now convert each value to string and comma-separated\r\n      row += index + ',';\r\n    }\r\n    row = row.slice(0, -1);\r\n    //append Label row with line break\r\n    str += row + '\\r\\n';\r\n\r\n    for (var i = 0; i < array.length; i++) {\r\n      var line = '';\r\n      for (var index in array[i]) {\r\n        if (line != '') line += ','\r\n\r\n        line += array[i][index];\r\n      }\r\n      str += line + '\\r\\n';\r\n    }\r\n    return str;\r\n  }\r\n  findObjectByKey(arr, key, value) {\r\n    var results = arr.find(obj => obj[key] == value)\r\n    return results\r\n  }\r\n  getByValue(arr, value) {\r\n    for (var i = 0, iLen = arr.length; i < iLen; i++) {\r\n      if (arr[i].id == value) {\r\n        return arr[i]\r\n      };\r\n    }\r\n  }\r\n  convertDate(data) {\r\n    if (data['date']) {\r\n      var dateFormate = moment.utc(data['date']['month'].toString() + '/' + data['date']['day'].toString() + '/' + data['date']['year'].toString());\r\n      return dateFormate;\r\n    } else {\r\n      data = new Date(data)\r\n      let getDate = data.getDate()\r\n      let getMonth = data.getMonth()\r\n      let getFullYear = data.getFullYear()\r\n      var dateFormate = moment.utc(getDate.toString() + '/' + getMonth.toString() + '/' + getFullYear.toString());\r\n      return dateFormate;\r\n    }\r\n  }\r\n\r\n  legalAllocationStatusList() {\r\n    return ['Failed', 'Invalid File Format', 'Partially Processed', 'Processed', 'Uploaded']\r\n  }\r\n\r\n  getTemplateData(caseType: string) {\r\n\r\n    // [{\"id\":\"Lt1\",\"name\":\"Arbitration\"},{\"id\":\"Lt2\",\"name\":\"Section9\"},{\"id\":\"Lt3\",\"name\":\"Section138\"},{\"id\":\"Lt4\",\"name\":\"ExecutionProceedings\"},{\"id\":\"Lt5\",\"name\":\"AgainstCases\"},{\"id\":\"Lt6\",\"name\":\"LRNNotice\"},{\"id\":\"Lt7\",\"name\":\"DemandNotice\"},]\r\n\r\n    const arbitrationLevel1 = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseType\",\r\n            \"name\": \"Case Type\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case Type\",\r\n              \"key\": \"caseType\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"customerAccountNo\",\r\n            \"name\": \"Loan No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan No.\",\r\n              \"key\": \"customerAccountNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"firstName\",\r\n            \"name\": \"Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Name\",\r\n              \"key\": \"firstName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"branch\",\r\n            \"name\": \"Branch Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Branch Name\",\r\n              \"key\": \"branch\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"assetMake\",\r\n            \"name\": \"Vehicle Make\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Vehicle Make\",\r\n              \"key\": \"assetMake\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"rcBookNo\",\r\n            \"name\": \"Registration No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Registration No.\",\r\n              \"key\": \"rcBookNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"engineNo\",\r\n            \"name\": \"Engine No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Engine No.\",\r\n              \"key\": \"engineNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"chassisNo\",\r\n            \"name\": \"Chassis No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Chassis No.\",\r\n              \"key\": \"chassisNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"pos\",\r\n            \"name\": \"POS\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"POS\",\r\n              \"key\": \"pos\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAmount\",\r\n            \"name\": \"Loan Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Amount\",\r\n              \"key\": \"loanAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"currentStatus\",\r\n            \"name\": \"Current Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Current Status\",\r\n              \"key\": \"currentStatus\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"sector17OrderDate\",\r\n            \"name\": \"Sector-17 Order Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Sector-17 Order Date\",\r\n              \"key\": \"sector17OrderDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"receiverName\",\r\n            \"name\": \"Receiver Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Receiver Name\",\r\n              \"key\": \"receiverName\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"^[A-Za-z -]+$\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"dateOfAward\",\r\n            \"name\": \"Date of Award\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Date of Award\",\r\n              \"key\": \"dateOfAward\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"amountOfAward\",\r\n            \"name\": \"Amount of Award\",\r\n            \"type\": \"currencyField\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Amount of Award\",\r\n              \"key\": \"amountOfAward\",\r\n              \"independentTo\": null,\r\n            \"type\": \"currencyField\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"arbitratorName\",\r\n            \"name\": \"Arbitrator Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Arbitrator Name\",\r\n              \"key\": \"arbitratorName\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"[A-Za-z ]{1,50}\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalAgencyName\",\r\n            \"name\": \"Legal Agency Name\",\r\n            \"type\": \"typeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Agency Name\",\r\n              \"key\": \"legalAgencyName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"typeahead\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"options\": [\r\n                        {\r\n                          \"key\": \"key\",\r\n                          \"value\": \"value\"\r\n                        }\r\n                      ],\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"advocateName\",\r\n            \"name\": \"Advocate Name\",\r\n            \"type\": \"advocatetypeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Advocate Name\",\r\n              \"key\": \"advocateName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"advocatetypeahead\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"foreclosureAmount\",\r\n            \"name\": \"Foreclosure Amount\",\r\n            \"type\": \"currencyField\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Foreclosure Amount\",\r\n              \"key\": \"foreclosureAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"currencyField\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"totalLossAmount\",\r\n            \"name\": \"Total Loss Amount\",\r\n            \"type\": \"currencyField\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Total Loss Amount\",\r\n              \"key\": \"totalLossAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"currencyField\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"arbitrationRefNo\",\r\n            \"name\": \"ARB Ref No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"ARB Ref No.\",\r\n              \"key\": \"arbitrationRefNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"liveClosed\",\r\n            \"name\": \"Live/Closed\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Live/Closed\",\r\n              \"key\": \"liveClosed\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"updatedStatus\",\r\n            \"name\": \"Updated Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Updated Status\",\r\n              \"key\": \"updatedStatus\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false,\r\n              \"onKeypress\": function(model, options, _this, event) {\r\n                // DO SEARCH (WORKING!)\r\n                console.log(event);\r\n               }\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"allocationDate\",\r\n            \"name\": \"Allocation Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Allocation Date\",\r\n              \"key\": \"allocationDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"lastDate\",\r\n            \"name\": \"Last Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Last Date\",\r\n              \"key\": \"lastDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"nextDate\",\r\n            \"name\": \"Next Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Next Date\",\r\n              \"key\": \"nextDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"zone\",\r\n            \"name\": \"Zone\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Zone\",\r\n              \"key\": \"zone\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          // Table of Hearing Date\r\n          // Hearing Remarks\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const sec9Level1 = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseType\",\r\n            \"name\": \"Case Type\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case Type\",\r\n              \"key\": \"caseType\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"customerAccountNo\",\r\n            \"name\": \"Agreement No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Agreement No.\",\r\n              \"key\": \"customerAccountNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"firstName\",\r\n            \"name\": \"Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Name\",\r\n              \"key\": \"firstName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"rcBookNo\",\r\n            \"name\": \"Registration No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Registration No.\",\r\n              \"key\": \"rcBookNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"engineNo\",\r\n            \"name\": \"Engine No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Engine No.\",\r\n              \"key\": \"engineNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"chassisNo\",\r\n            \"name\": \"Chassis No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Chassis No.\",\r\n              \"key\": \"chassisNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"pos\",\r\n            \"name\": \"POS\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"POS\",\r\n              \"key\": \"pos\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAmount\",\r\n            \"name\": \"Loan Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Amount\",\r\n              \"key\": \"loanAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"branch\",\r\n            \"name\": \"Branch\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Branch\",\r\n              \"key\": \"branch\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"assetMake\",\r\n            \"name\": \"Vehicle Make\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Vehicle Make\",\r\n              \"key\": \"assetMake\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"allocationDate\",\r\n            \"name\": \"Date of Allocation\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Date of Allocation\",\r\n              \"key\": \"allocationDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseNo\",\r\n            \"name\": \"Case No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case No.\",\r\n              \"key\": \"caseNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"zone\",\r\n            \"name\": \"Zone\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Zone\",\r\n              \"key\": \"zone\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"lastDate\",\r\n            \"name\": \"Last Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Last Date\",\r\n              \"key\": \"lastDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"nextDate\",\r\n            \"name\": \"Next Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Next Date\",\r\n              \"key\": \"nextDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"currentStatus\",\r\n            \"name\": \"Current Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Current Status\",\r\n              \"key\": \"currentStatus\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"dateOfRO\",\r\n            \"name\": \"Date of RO\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Date of RO\",\r\n              \"key\": \"dateOfRO\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"orderValidity\",\r\n            \"name\": \"Order Validity\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Order Validity\",\r\n              \"key\": \"orderValidity\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"courtLocation\",\r\n            \"name\": \"Court/Location\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Court/Location\",\r\n              \"key\": \"courtLocation\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"judgeName\",\r\n            \"name\": \"Judge Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Judge Name\",\r\n              \"key\": \"judgeName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"nameOfReceiver\",\r\n            \"name\": \"Name of Receiver\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Name of Receiver\",\r\n              \"key\": \"nameOfReceiver\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalAgencyName\",\r\n            \"name\": \"Legal Agency Name\",\r\n            \"type\": \"typeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Agency Name\",\r\n              \"key\": \"legalAgencyName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"typeahead\",\r\n              \"options\": [\r\n                        {\r\n                          \"key\": \"key\",\r\n                          \"value\": \"value\"\r\n                        }\r\n                      ],\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"advocateName\",\r\n            \"name\": \"Advocate Name\",\r\n            \"type\": \"advocatetypeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Advocate Name\",\r\n              \"key\": \"advocateName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"advocatetypeahead\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"liveClosed\",\r\n            \"name\": \"Live/Closed\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Live/Closed\",\r\n              \"key\": \"liveClosed\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"updatedStatus\",\r\n            \"name\": \"Updated Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Updated Status\",\r\n              \"key\": \"updatedStatus\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          // Table of Hearing Date\r\n          // Hearing Remarks\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const sec138Level1 = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseType\",\r\n            \"name\": \"Case Type\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case Type\",\r\n              \"key\": \"caseType\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"customerAccountNo\",\r\n            \"name\": \"Agreement No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Agreement No.\",\r\n              \"key\": \"customerAccountNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"firstName\",\r\n            \"name\": \"Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Name\",\r\n              \"key\": \"firstName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"assetMake\",\r\n            \"name\": \"Vehicle Make\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Vehicle Make\",\r\n              \"key\": \"assetMake\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"rcBookNo\",\r\n            \"name\": \"Registration No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Registration No.\",\r\n              \"key\": \"rcBookNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"engineNo\",\r\n            \"name\": \"Engine No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Engine No.\",\r\n              \"key\": \"engineNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"chassisNo\",\r\n            \"name\": \"Chassis No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Chassis No.\",\r\n              \"key\": \"chassisNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"pos\",\r\n            \"name\": \"POS\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"POS\",\r\n              \"key\": \"pos\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAmount\",\r\n            \"name\": \"Loan Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Amount\",\r\n              \"key\": \"loanAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"branch\",\r\n            \"name\": \"Branch\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Branch\",\r\n              \"key\": \"branch\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"updatedStatus\",\r\n            \"name\": \"Updated Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Updated Status\",\r\n              \"key\": \"updatedStatus\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseNo\",\r\n            \"name\": \"Case No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case No.\",\r\n              \"key\": \"caseNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"zone\",\r\n            \"name\": \"Zone\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Zone\",\r\n              \"key\": \"zone\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"lastDate\",\r\n            \"name\": \"Last Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Last Date\",\r\n              \"key\": \"lastDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"nextDate\",\r\n            \"name\": \"Next Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Next Date\",\r\n              \"key\": \"nextDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"currentStatus\",\r\n            \"name\": \"Current Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Current Status\",\r\n              \"key\": \"currentStatus\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"courtLocation\",\r\n            \"name\": \"Court/Location\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Court/Location\",\r\n              \"key\": \"courtLocation\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"judgeName\",\r\n            \"name\": \"Judge Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Judge Name\",\r\n              \"key\": \"judgeName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalAgencyName\",\r\n            \"name\": \"Legal Agency Name\",\r\n            \"type\": \"typeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Agency Name\",\r\n              \"key\": \"legalAgencyName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"typeahead\",\r\n              \"options\": [\r\n                        {\r\n                          \"key\": \"key\",\r\n                          \"value\": \"value\"\r\n                        }\r\n                      ],\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"advocateName\",\r\n            \"name\": \"Advocate Name\",\r\n            \"type\": \"advocatetypeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Advocate Name\",\r\n              \"key\": \"advocateName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"advocatetypeahead\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"chequeAmount\",\r\n            \"name\": \"Cheque Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Cheque Amount\",\r\n              \"key\": \"chequeAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"allocationDate\",\r\n            \"name\": \"Date of Allocation\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Date of Allocation\",\r\n              \"key\": \"allocationDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"liveClosed\",\r\n            \"name\": \"Live/Closed\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Live/Closed\",\r\n              \"key\": \"liveClosed\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"foreclosureAmount\",\r\n            \"name\": \"Foreclosure Amount\",\r\n            \"type\": \"currencyField\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Foreclosure Amount\",\r\n              \"key\": \"foreclosureAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"currencyField\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"totalLossAmount\",\r\n            \"name\": \"Total Loss Amount\",\r\n            \"type\": \"currencyField\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Total Loss Amount\",\r\n              \"key\": \"totalLossAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"currencyField\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          // Table of Hearing Date\r\n          // Hearing Remarks\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const executionLevel1 = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseType\",\r\n            \"name\": \"Case Type\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case Type\",\r\n              \"key\": \"caseType\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"customerAccountNo\",\r\n            \"name\": \"Agreement No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Agreement No.\",\r\n              \"key\": \"customerAccountNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"firstName\",\r\n            \"name\": \"Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Name\",\r\n              \"key\": \"firstName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"assetMake\",\r\n            \"name\": \"Vehicle Make\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Vehicle Make\",\r\n              \"key\": \"assetMake\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"rcBookNo\",\r\n            \"name\": \"Registration No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Registration No.\",\r\n              \"key\": \"rcBookNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"engineNo\",\r\n            \"name\": \"Engine No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Engine No.\",\r\n              \"key\": \"engineNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"chassisNo\",\r\n            \"name\": \"Chassis No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Chassis No.\",\r\n              \"key\": \"chassisNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"pos\",\r\n            \"name\": \"POS\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"POS\",\r\n              \"key\": \"pos\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAmount\",\r\n            \"name\": \"Loan Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Amount\",\r\n              \"key\": \"loanAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"branch\",\r\n            \"name\": \"Branch\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Branch\",\r\n              \"key\": \"branch\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"zone\",\r\n            \"name\": \"Zone\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Zone\",\r\n              \"key\": \"zone\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"lastDate\",\r\n            \"name\": \"Last Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Last Date\",\r\n              \"key\": \"lastDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"nextDate\",\r\n            \"name\": \"Next Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Next Date\",\r\n              \"key\": \"nextDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"currentStatus\",\r\n            \"name\": \"Current Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Current Status\",\r\n              \"key\": \"currentStatus\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"courtLocation\",\r\n            \"name\": \"Court/Location\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Court/Location\",\r\n              \"key\": \"courtLocation\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"judgeName\",\r\n            \"name\": \"Judge Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Judge Name\",\r\n              \"key\": \"judgeName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"awardDated\",\r\n            \"name\": \"Award Dated\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Award Dated\",\r\n              \"key\": \"awardDated\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalAgencyName\",\r\n            \"name\": \"Legal Agency Name\",\r\n            \"type\": \"typeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Agency Name\",\r\n              \"key\": \"legalAgencyName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"typeahead\",\r\n              \"options\": [\r\n                        {\r\n                          \"key\": \"key\",\r\n                          \"value\": \"value\"\r\n                        }\r\n                      ],\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"advocateName\",\r\n            \"name\": \"Advocate Name\",\r\n            \"type\": \"advocatetypeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Advocate Name\",\r\n              \"key\": \"advocateName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"advocatetypeahead\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"executionAmount\",\r\n            \"name\": \"Execution Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Execution Amount\",\r\n              \"key\": \"executionAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"foreclosureAmount\",\r\n            \"name\": \"Foreclosure Amount\",\r\n            \"type\": \"currencyField\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Foreclosure Amount\",\r\n              \"key\": \"foreclosureAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"currencyField\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"totalLossAmount\",\r\n            \"name\": \"Total Loss Amount\",\r\n            \"type\": \"currencyField\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Total Loss Amount\",\r\n              \"key\": \"totalLossAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"currencyField\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"allocationDate\",\r\n            \"name\": \"Date of Allocation\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Date of Allocation\",\r\n              \"key\": \"allocationDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"liveClosed\",\r\n            \"name\": \"Live/Closed\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Live/Closed\",\r\n              \"key\": \"liveClosed\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"updatedStatus\",\r\n            \"name\": \"Updated Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Updated Status\",\r\n              \"key\": \"updatedStatus\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseNo\",\r\n            \"name\": \"Case No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case No.\",\r\n              \"key\": \"caseNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          // Table of Hearing Date\r\n          // Hearing Remarks\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const againstCasesLevel1 = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseType\",\r\n            \"name\": \"Case Type\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case Type\",\r\n              \"key\": \"caseType\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"customerAccountNo\",\r\n            \"name\": \"Agreement No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Agreement No.\",\r\n              \"key\": \"customerAccountNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"firstName\",\r\n            \"name\": \"Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Name\",\r\n              \"key\": \"firstName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"assetMake\",\r\n            \"name\": \"Vehicle Make\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Vehicle Make\",\r\n              \"key\": \"assetMake\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"rcBookNo\",\r\n            \"name\": \"Registration No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Registration No.\",\r\n              \"key\": \"rcBookNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"engineNo\",\r\n            \"name\": \"Engine No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Engine No.\",\r\n              \"key\": \"engineNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"chassisNo\",\r\n            \"name\": \"Chassis No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Chassis No.\",\r\n              \"key\": \"chassisNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"pos\",\r\n            \"name\": \"POS\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"POS\",\r\n              \"key\": \"pos\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAmount\",\r\n            \"name\": \"Loan Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Amount\",\r\n              \"key\": \"loanAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"branch\",\r\n            \"name\": \"Branch\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Branch\",\r\n              \"key\": \"branch\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"fillingDate\",\r\n            \"name\": \"Filling Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Filling Date\",\r\n              \"key\": \"fillingDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"zone\",\r\n            \"name\": \"Zone\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Zone\",\r\n              \"key\": \"zone\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"liveClosed\",\r\n            \"name\": \"Live/Closed\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Live/Closed\",\r\n              \"key\": \"liveClosed\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"updatedStatus\",\r\n            \"name\": \"Updated Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Updated Status\",\r\n              \"key\": \"updatedStatus\",\r\n              \"independentTo\": null,\r\n            \"type\": \"input\",\r\n            \"required\": false,\r\n            \"pattern\": \"\",\r\n            \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseNo\",\r\n            \"name\": \"Case No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case No.\",\r\n              \"key\": \"caseNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"natureOfCase\",\r\n            \"name\": \"Nature of Case\",\r\n            \"type\": \"natureCaseSelect\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Nature of Case\",\r\n              \"key\": \"natureOfCase\",\r\n              \"independentTo\": null,\r\n              \"type\": \"natureCaseSelect\",\r\n              \"options\": [\r\n                        {\r\n                          \"key\": \"key\",\r\n                          \"value\": \"value\"\r\n                        }\r\n               ],\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          }, {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"lastDate\",\r\n            \"name\": \"Last Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Last Date\",\r\n              \"key\": \"lastDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"nextDate\",\r\n            \"name\": \"Next Date\",\r\n            \"type\": \"datepicker\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Next Date\",\r\n              \"key\": \"nextDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"datepicker\",\r\n              \"required\": false,\r\n              \"options\": [],\r\n              \"noFutureDate\": \"true\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"currentStatus\",\r\n            \"name\": \"Current Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Current Status\",\r\n              \"key\": \"currentStatus\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"courtLocation\",\r\n            \"name\": \"Court/Location\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Court/Location\",\r\n              \"key\": \"courtLocation\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"judgeName\",\r\n            \"name\": \"Judge Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Judge Name\",\r\n              \"key\": \"judgeName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalAgencyName\",\r\n            \"name\": \"Legal Agency Name\",\r\n            \"type\": \"typeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Agency Name\",\r\n              \"key\": \"legalAgencyName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"typeahead\",\r\n              \"options\": [\r\n                        {\r\n                          \"key\": \"key\",\r\n                          \"value\": \"value\"\r\n                        }\r\n               ],\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"advocateName\",\r\n            \"name\": \"Advocate Name\",\r\n            \"type\": \"advocatetypeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Advocate Name\",\r\n              \"key\": \"advocateName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"advocatetypeahead\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"contingentLiability\",\r\n            \"name\": \"ContingentLiability\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"ContingentLiability\",\r\n              \"key\": \"contingentLiability\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          // Table of Hearing Date\r\n          // Hearing Remarks\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const LRNDemandNoticeLevel1 = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"caseType\",\r\n            \"name\": \"Case Type\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case Type\",\r\n              \"key\": \"caseType\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"customerAccountNo\",\r\n            \"name\": \"Agreement No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Agreement No.\",\r\n              \"key\": \"customerAccountNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"firstName\",\r\n            \"name\": \"Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Name\",\r\n              \"key\": \"firstName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"assetMake\",\r\n            \"name\": \"Vehicle Make\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Vehicle Make\",\r\n              \"key\": \"assetMake\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"rcBookNo\",\r\n            \"name\": \"Registration No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Registration No.\",\r\n              \"key\": \"rcBookNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"engineNo\",\r\n            \"name\": \"Engine No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Engine No.\",\r\n              \"key\": \"engineNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"chassisNo\",\r\n            \"name\": \"Chassis No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Chassis No.\",\r\n              \"key\": \"chassisNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"pos\",\r\n            \"name\": \"POS\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"POS\",\r\n              \"key\": \"pos\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAmount\",\r\n            \"name\": \"Loan Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Amount\",\r\n              \"key\": \"loanAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"branch\",\r\n            \"name\": \"Branch\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Branch\",\r\n              \"key\": \"branch\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalAgencyName\",\r\n            \"name\": \"Legal Agency Name\",\r\n            \"type\": \"typeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Agency Name\",\r\n              \"key\": \"legalAgencyName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"typeahead\",\r\n              \"options\": [\r\n                        {\r\n                          \"key\": \"key\",\r\n                          \"value\": \"value\"\r\n                        }\r\n                      ],\r\n              \"required\": false,\r\n              \"pattern\": \"\",\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"advocateName\",\r\n            \"name\": \"Advocate Name\",\r\n            \"type\": \"advocatetypeahead\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Advocate Name\",\r\n              \"key\": \"advocateName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"advocatetypeahead\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const commonLevel2 = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"level2date\",\r\n            \"name\": \"Case Date\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Case Date\",\r\n              \"key\": \"level2date\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"region\",\r\n            \"name\": \"Region\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Region\",\r\n              \"key\": \"region\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"openingDPD\",\r\n            \"name\": \"Opening DPD\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Opening DPD\",\r\n              \"key\": \"openingDPD\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"openingBucket\",\r\n            \"name\": \"Opening BKT\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Opening BKT\",\r\n              \"key\": \"openingBucket\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"ageing\",\r\n            \"name\": \"Ageing\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Ageing\",\r\n              \"key\": \"ageing\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAgreementId\",\r\n            \"name\": \"Loan Agreement No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Agreement No.\",\r\n              \"key\": \"loanAgreementId\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"pattern\":\"\",\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"customerName\",\r\n            \"name\": \"Customer Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Customer Name\",\r\n              \"key\": \"customerName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"currentApplicationStatus\",\r\n            \"name\": \"Current Staus of Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Current Staus of Status\",\r\n              \"key\": \"currentApplicationStatus\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"finalRemarks\",\r\n            \"name\": \"Final Remarks\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Final Remarks\",\r\n              \"key\": \"finalRemarks\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalFee\",\r\n            \"name\": \"Legal Fee\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Fee\",\r\n              \"key\": \"legalFee\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"openingRepoStock\",\r\n            \"name\": \"Opening Repo Stock\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Opening Repo Stock\",\r\n              \"key\": \"openingRepoStock\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"branch\",\r\n            \"name\": \"Branch\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Branch\",\r\n              \"key\": \"branch\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"assetMake\",\r\n            \"name\": \"Asset Make\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Asset Make\",\r\n              \"key\": \"assetMake\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanAmount\",\r\n            \"name\": \"Loan Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Amount\",\r\n              \"key\": \"loanAmount\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"bucket\",\r\n            \"name\": \"Bucket\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Bucket\",\r\n              \"key\": \"bucket\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"billedInstallement\",\r\n            \"name\": \"Billed Installement\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Billed Installement\",\r\n              \"key\": \"billedInstallement\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"unbilledInstallement\",\r\n            \"name\": \"Unbilled Installement\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Unbilled Installement\",\r\n              \"key\": \"unbilledInstallement\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"tenure\",\r\n            \"name\": \"Tenure\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Tenure\",\r\n              \"key\": \"tenure\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"dueDate\",\r\n            \"name\": \"Due Date\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Due Date\",\r\n              \"key\": \"dueDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"zone\",\r\n            \"name\": \"Zone\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Zone\",\r\n              \"key\": \"zone\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"rcm\",\r\n            \"name\": \"RCM\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"RCM\",\r\n              \"key\": \"rcm\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"collectionManager\",\r\n            \"name\": \"Collection Manager\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Collection Manager\",\r\n              \"key\": \"collectionManager\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"collectionManagerW\",\r\n            \"name\": \"Collection Manager W\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Collection Manager W\",\r\n              \"key\": \"collectionManagerW\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"accOpeningStatus\",\r\n            \"name\": \"A/C Opening Status\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"A/C Opening Status\",\r\n              \"key\": \"accOpeningStatus\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"appId\",\r\n            \"name\": \"Application Id\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Application Id\",\r\n              \"key\": \"appId\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanBookingDate\",\r\n            \"name\": \"Loan Booking Date\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Booking Date\",\r\n              \"key\": \"loanBookingDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"loanMaturityDate\",\r\n            \"name\": \"Loan Maturity Date\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Maturity Date\",\r\n              \"key\": \"loanMaturityDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"firstEmiDate\",\r\n            \"name\": \"First EMI Due Date\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"First EMI Due Date\",\r\n              \"key\": \"firstEmiDate\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"disbursalAmt\",\r\n            \"name\": \"Disbursal Amount\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Disbursal Amount\",\r\n              \"key\": \"disbursalAmt\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"resAdd\",\r\n            \"name\": \"Resedential Add.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Resedential Add.\",\r\n              \"key\": \"resAdd\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"resNo\",\r\n            \"name\": \"Resedential No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Resedential No.\",\r\n              \"key\": \"resNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"offAdd\",\r\n            \"name\": \"Office Add.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Office Add.\",\r\n              \"key\": \"offAdd\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"officeNo\",\r\n            \"name\": \"Office No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Office No.\",\r\n              \"key\": \"officeNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"permAdd\",\r\n            \"name\": \"Permanent Add.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Permanent Add.\",\r\n              \"key\": \"permAdd\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"permNo\",\r\n            \"name\": \"Permanent No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Permanent No.\",\r\n              \"key\": \"permNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"coappName\",\r\n            \"name\": \"Co-App-Name\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Co-App-Name\",\r\n              \"key\": \"coappName\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"coappresAdd\",\r\n            \"name\": \"Co-App-Res Add.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Co-App-Res Add.\",\r\n              \"key\": \"coappresAdd\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"coappresNo\",\r\n            \"name\": \"Co-App-Res No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Co-App-Res No.\",\r\n              \"key\": \"coappresNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"coappoffAdd\",\r\n            \"name\": \"Co-App-Office Add.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Co-App-Office Add.\",\r\n              \"key\": \"coappoffAdd\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"coappoffNo\",\r\n            \"name\": \"Co-App-Office No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Co-App-Office No.\",\r\n              \"key\": \"coappoffNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"coapppermAdd\",\r\n            \"name\": \"Co-App-Perm Add.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Co-App-Perm Add.\",\r\n              \"key\": \"coapppermAdd\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"coapppermNo\",\r\n            \"name\": \"Co-App-Perm No.\",\r\n            \"type\": \"input\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Co-App-Perm No.\",\r\n              \"key\": \"coapppermNo\",\r\n              \"independentTo\": null,\r\n              \"type\": \"input\",\r\n              \"required\": false,\r\n\r\n              \"disabled\": true\r\n            },\r\n            \"validators\": {\r\n              \"validation\": []\r\n            }\r\n          },\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const commonUploadDocument = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"LoanAgreement\",\r\n            \"mediaName\": \"LoanAgreement\",\r\n            \"name\": \"Loan Agreement\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Loan Agreement\",\r\n              \"key\": \"LoanAgreement\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"appform\",\r\n            \"mediaName\": \"appform\",\r\n            \"name\": \"Application Form\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Application Form\",\r\n              \"key\": \"appform\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"hypotecationProof\",\r\n            \"mediaName\": \"hypotecationProof\",\r\n            \"name\": \"Hypothecation Proof\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Hypothecation Proof\",\r\n              \"key\": \"hypotecationProof\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"stOfAccount\",\r\n              \"mediaName\": \"stOfAccount\",\r\n            \"name\": \"Statetment of Account\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Statetment of Account\",\r\n              \"key\": \"stOfAccount\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"foreclosureStatement\",\r\n            \"mediaName\": \"foreclosureStatement\",\r\n            \"name\": \"Foreclosure Statement\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Foreclosure Statement\",\r\n              \"key\": \"foreclosureStatement\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"deviationApprovals\",\r\n            \"mediaName\": \"deviationApprovals\",\r\n            \"name\": \"Approvals\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Approvals\",\r\n              \"key\": \"deviationApprovals\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"uploadLRN\",\r\n            \"mediaName\": \"uploadLRN\",\r\n            \"name\": \"Upload Latest LRN\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Upload Latest LRN\",\r\n              \"key\": \"uploadLRN\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"uploadDemandNotice\",\r\n            \"mediaName\": \"uploadDemandNotice\",\r\n            \"name\": \"Upload Latest Demand Notice\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Upload Latest Demand Notice\",\r\n              \"key\": \"uploadDemandNotice\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"LRN1\",\r\n            \"mediaName\": \"LRN1\",\r\n            \"name\": \"LRN 1\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Generate LRN 1\",\r\n              \"key\": \"LRN1\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },{\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"LRN2\",\r\n            \"mediaName\": \"LRN2\",\r\n            \"name\": \"LRN 2\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"LRN 2\",\r\n              \"key\": \"LRN2\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },{\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"LRN3\",\r\n            \"mediaName\": \"LRN3\",\r\n            \"name\": \"LRN 3\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"LRN 3\",\r\n              \"key\": \"LRN3\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },{\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"LRN4\",\r\n            \"mediaName\": \"LRN4\",\r\n            \"name\": \"LRN 4\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"LRN 4\",\r\n              \"key\": \"LRN4\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },{\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"LRN5\",\r\n            \"mediaName\": \"LRN5\",\r\n            \"name\": \"LRN 5\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"LRN 5\",\r\n              \"key\": \"LRN5\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },{\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"LRN6\",\r\n            \"mediaName\": \"LRN6\",\r\n            \"name\": \"LRN 6\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"LRN 6\",\r\n              \"key\": \"LRN6\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"DemandNotice1\",\r\n            \"mediaName\": \"DemandNotice1\",\r\n            \"name\": \"Demand Notice 1\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Demand Notice 1\",\r\n              \"key\": \"DemandNotice1\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },         {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"DemandNotice2\",\r\n            \"mediaName\": \"DemandNotice2\",\r\n            \"name\": \" Demand Notice 2\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Demand Notice 2\",\r\n              \"key\": \"DemandNotice2\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },         {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"DemandNotice3\",\r\n            \"mediaName\": \"DemandNotice3\",\r\n            \"name\": \"Demand Notice 3\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Demand Notice 3\",\r\n              \"key\": \"DemandNotice3\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },         {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"DemandNotice4\",\r\n            \"mediaName\": \"DemandNotice4\",\r\n            \"name\": \"Demand Notice 4\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Demand Notice 4\",\r\n              \"key\": \"DemandNotice4\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },         {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"DemandNotice5\",\r\n            \"mediaName\": \"DemandNotice5\",\r\n            \"name\": \"Demand Notice 5\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Demand Notice 5\",\r\n              \"key\": \"DemandNotice5\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },         {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"DemandNotice6\",\r\n            \"mediaName\": \"DemandNotice6\",\r\n            \"name\": \"Demand Notice 6\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Demand Notice 6\",\r\n              \"key\": \"DemandNotice6\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalDoc1\",\r\n            \"mediaName\": \"legalDoc1\",\r\n            \"name\": \"Legal Document 1\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Document 1\",\r\n              \"key\": \"legalDoc1\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalDoc2\",\r\n            \"mediaName\": \"legalDoc2\",\r\n            \"name\": \"Legal Document 2\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Document 2\",\r\n              \"key\": \"legalDoc2\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalDoc3\",\r\n             \"mediaName\": \"legalDoc3\",\r\n            \"name\": \"Legal Document 3\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Document 3\",\r\n              \"key\": \"legalDoc3\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalDoc4\",\r\n            \"mediaName\": \"legalDoc4\",\r\n            \"name\": \"Legal Document 4\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Document 4\",\r\n              \"key\": \"legalDoc4\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"legalDoc5\",\r\n            \"mediaName\": \"legalDoc5\",\r\n            \"name\": \"Legal Document 5\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"disabled\": false,\r\n            \"templateOptions\": {\r\n              \"label\": \"Legal Document 5\",\r\n              \"key\": \"legalDoc5\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n      {\r\n        \"className\": \"col-md-6\",\r\n      \"key\": \"DeviationDocument\",\r\n      \"mediaName\": \"DeviationDocument\",\r\n      \"name\": \"DeviationDocument\",\r\n        \"type\": \"file\",\r\n        \"defaultValue\": \"\",\r\n        \"disabled\": false,\r\n        \"templateOptions\": {\r\n      \"label\": \"DeviationDocument\",\r\n      \"key\": \"DeviationDocument\",\r\n          \"type\": \"file\",\r\n          \"required\": false,\r\n          \"disabled\": false\r\n        }\r\n      },\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const arbitrationUploadDocument = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"sec17Order\",\r\n            \"mediaName\": \"sec17Order\",\r\n            \"name\": \"Section 17 Order\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Section 17 Order\",\r\n              \"key\": \"sec17Order\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"awardCopy\",\r\n            \"mediaName\": \"awardCopy\",\r\n            \"name\": \"Award Copy\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Award Copy\",\r\n              \"key\": \"awardCopy\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const sec9UploadDocument = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"sec9Order\",\r\n            \"mediaName\": \"sec9Order\",\r\n            \"name\": \"Section 9 Order\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Section 9 Order\",\r\n              \"key\": \"sec9Order\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const againstCasesUploadDocument = [\r\n      {\r\n        \"fieldGroupClassName\": \"row\",\r\n        \"fieldGroup\": [\r\n          {\r\n            \"className\": \"col-md-6\",\r\n            \"key\": \"finalOrder\",\r\n            \"mediaName\": \"finalOrder\",\r\n            \"name\": \"Final Order\",\r\n            \"type\": \"file\",\r\n            \"defaultValue\": \"\",\r\n            \"templateOptions\": {\r\n              \"label\": \"Final Order\",\r\n              \"key\": \"finalOrder\",\r\n              \"type\": \"file\",\r\n              \"required\": false,\r\n              \"disabled\": false\r\n            }\r\n          },\r\n        ]\r\n      }\r\n    ];\r\n\r\n    const obj = [\r\n      {\r\n        \"levelName\": \"Level 1\",\r\n        \"level\": [\r\n          {\r\n            \"name\": \"Level 1\",\r\n            \"active\": true,\r\n            \"fields\": []\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        \"levelName\": \"Level 2\",\r\n        \"level\": [\r\n          {\r\n            \"name\": \"Level 2\",\r\n            \"active\": true,//changed to true by default shud be false Vinay\r\n            \"fields\": []\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        \"levelName\": \"Upload Document\",\r\n        \"level\": [\r\n          {\r\n            \"name\": \"Upload Document\",\r\n            \"active\": false,\r\n            \"fields\": [\r\n              {\r\n                \"fieldGroupClassName\": \"row\",\r\n                \"fieldGroup\": [\r\n                  // {\r\n                  //   \"className\": \"col-md-6\",\r\n                  //   \"key\": \"generateLRN\",\r\n                  //   \"name\": \"Generate LRN\",\r\n                  //   \"type\": \"file\",\r\n                  //   \"defaultValue\": \"\",\r\n                  //   \"disabled\": false,\r\n                  //   \"templateOptions\": {\r\n                  //     \"label\": \"Generate LRN\",\r\n                  //     \"key\": \"generateLRN\",\r\n                  //     \"type\": \"file\",\r\n                  //     \"required\": false,\r\n                  //     \"disabled\": false\r\n                  //   }\r\n                  // },\r\n                  // {\r\n                  //   \"className\": \"col-md-6\",\r\n                  //   \"key\": \"generatedemandNotice\",\r\n                  //   \"name\": \"Generate Demand Notice\",\r\n                  //   \"type\": \"file\",\r\n                  //   \"defaultValue\": \"\",\r\n                  //   \"disabled\": false,\r\n                  //   \"templateOptions\": {\r\n                  //     \"label\": \"Generate Demand Notice\",\r\n                  //     \"key\": \"generatedemandNotice\",\r\n                  //     \"type\": \"file\",\r\n                  //     \"required\": false,\r\n                  //     \"disabled\": false\r\n                  //   }\r\n                  // },\r\n                ]\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n\r\n    // For Level 1\r\n    if (caseType == 'Arbitration') {\r\n      obj[0].level[0].fields = arbitrationLevel1;\r\n    }\r\n    if (caseType == 'Section9') {\r\n      obj[0].level[0].fields = sec9Level1;\r\n    }\r\n    if (caseType == 'Section138') {\r\n      obj[0].level[0].fields = sec138Level1;\r\n    }\r\n    if (caseType == 'ExecutionProceedings') {\r\n      obj[0].level[0].fields = executionLevel1;\r\n    }\r\n    if (caseType == 'AgainstCases') {\r\n      obj[0].level[0].fields = againstCasesLevel1;\r\n    }\r\n    if (caseType == 'LRNNotice' || caseType == 'DemandNotice') {\r\n      obj[0].level[0].fields = LRNDemandNoticeLevel1;\r\n    }\r\n\r\n    // For Level 2\r\n    obj[1].level[0].fields = commonLevel2;\r\n\r\n    // For Upload Document\r\n    obj[2].level[0].fields = commonUploadDocument;\r\n    const docLength = commonUploadDocument[0].fieldGroup.length;\r\n    if (caseType == 'Arbitration') {\r\n      const fields = arbitrationUploadDocument[0].fieldGroup;\r\n      fields.forEach(f => obj[2].level[0].fields[0].fieldGroup.splice(docLength - 5, 0, f));\r\n    }\r\n    if (caseType == 'Section9') {\r\n      const fields = sec9UploadDocument[0].fieldGroup;\r\n      fields.forEach(f => obj[2].level[0].fields[0].fieldGroup.splice(docLength - 5, 0, f));\r\n    }\r\n    if (caseType == 'AgainstCases') {\r\n      const fields = againstCasesUploadDocument[0].fieldGroup;\r\n      fields.forEach(f => obj[2].level[0].fields[0].fieldGroup.splice(docLength - 5, 0, f));\r\n    }\r\n    return obj\r\n  }\r\n\r\n\r\n  noticeDoc(val){\r\n    let objArray = [\"NoticeSentToBorrower\",\"NoticeSentToCo-Borrower\",\"NoticeSentToGuarantor\"]\r\n    let findVal = objArray.find(v=>(v).toLowerCase()==(val).toLowerCase())\r\n    return findVal\r\n  }\r\n\r\n  recallDoc(val){\r\n    let objArray = [\"RecallNoticeSent\",\"RecallNoticeSentToGuarantor\"]\r\n    let findVal = objArray.find(v=>(v).toLowerCase()==(val).toLowerCase())\r\n    return findVal\r\n  }\r\n\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,YAAY,QAAQ,eAAe;AACxD,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAO,KAAKC,MAAM,MAAM,eAAe;AAMhC,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAG5BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAF9B,KAAAC,UAAU,GAAG,IAAIN,YAAY,EAAE;IAG7B,IAAI,CAACO,eAAe,GAAG,IAAIP,YAAY,EAAO;EAChD;EAEAQ,YAAYA,CAACC,IAAS;IACpB,IAAI,CAACH,UAAU,CAACI,IAAI,CAACD,IAAI,CAAC;EAC5B;EACA;EACAE,kBAAkBA,CAACC,GAAG,EAAEC,GAAG;IACzB,IAAIC,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,IAAIH,GAAG,CAACG,CAAC,CAAC,CAACE,KAAK,KAAKJ,GAAG,EAAE;QACxBC,QAAQ,CAACI,IAAI,CAACN,GAAG,CAACG,CAAC,CAAC,CAAC;MACvB;IACF;IACA,OAAOD,QAAQ;EACjB;EACAK,kBAAkBA,CAACP,GAAG,EAAEC,GAAG,EAAEO,GAAG;IAC9B,IAAIN,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,IAAIH,GAAG,CAACG,CAAC,CAAC,CAACK,GAAG,CAAC,KAAKP,GAAG,EAAE;QACvBC,QAAQ,CAACI,IAAI,CAACN,GAAG,CAACG,CAAC,CAAC,CAAC;MACvB;IACF;IACA,OAAOD,QAAQ;EACjB;EACAO,cAAcA,CAACC,KAAK,EAAEF,GAAG;IACvB,OAAOE,KAAK,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC;MAC9B,IAAIC,CAAC,GAAGF,CAAC,CAACJ,GAAG,CAAC;MAAE,IAAIO,CAAC,GAAGF,CAAC,CAACL,GAAG,CAAC;MAC9B,OAASM,CAAC,GAAGC,CAAC,GAAI,CAAC,CAAC,GAAKD,CAAC,GAAGC,CAAC,GAAI,CAAC,GAAG,CAAE;IAC1C,CAAC,CAAC;EACJ;EACAC,YAAYA,CAACJ,CAAC,EAAEC,CAAC;IACf,MAAMI,KAAK,GAAGL,CAAC,CAACM,IAAI,CAACC,WAAW,EAAE;IAClC,MAAMC,KAAK,GAAGP,CAAC,CAACK,IAAI,CAACC,WAAW,EAAE;IAElC,IAAIE,UAAU,GAAG,CAAC;IAClB,IAAIJ,KAAK,GAAGG,KAAK,EAAE;MACjBC,UAAU,GAAG,CAAC;IAChB,CAAC,MAAM,IAAIJ,KAAK,GAAGG,KAAK,EAAE;MACxBC,UAAU,GAAG,CAAC,CAAC;IACjB;IACA,OAAOA,UAAU;EACnB;EACAC,gBAAgBA,CAACC,KAAK,EAAEC,IAAI;IAC1B,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,EAAE3B,GAAG,KAAI;MACpC,OAAOA,GAAG,CAAC4B,GAAG,CAACC,MAAM,IACnBA,MAAM,CAACL,IAAI,CAAC,CAAC,CAACM,OAAO,CAACJ,GAAG,CAACF,IAAI,CAAC,CAAC,KAAKG,GAAG;IAC5C,CAAC,CAAC;EACJ;EACAI,YAAYA,CAACC,QAAQ;IACnB,IAAItB,KAAK,GAAG,OAAOsB,QAAQ,IAAI,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,GAAGA,QAAQ;IACzE,IAAIG,GAAG,GAAG,EAAE;IACZ,IAAIC,GAAG,GAAG,EAAE;IAEZ,KAAK,IAAIC,KAAK,IAAIL,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC7B;MACAI,GAAG,IAAIC,KAAK,GAAG,GAAG;IACpB;IACAD,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB;IACAH,GAAG,IAAIC,GAAG,GAAG,MAAM;IAEnB,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,KAAK,CAACN,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIoC,IAAI,GAAG,EAAE;MACb,KAAK,IAAIF,KAAK,IAAI3B,KAAK,CAACP,CAAC,CAAC,EAAE;QAC1B,IAAIoC,IAAI,IAAI,EAAE,EAAEA,IAAI,IAAI,GAAG;QAE3BA,IAAI,IAAI7B,KAAK,CAACP,CAAC,CAAC,CAACkC,KAAK,CAAC;MACzB;MACAF,GAAG,IAAII,IAAI,GAAG,MAAM;IACtB;IACA,OAAOJ,GAAG;EACZ;EACAK,eAAeA,CAACxC,GAAG,EAAEQ,GAAG,EAAEiC,KAAK;IAC7B,IAAIC,OAAO,GAAG1C,GAAG,CAAC2C,IAAI,CAACjB,GAAG,IAAIA,GAAG,CAAClB,GAAG,CAAC,IAAIiC,KAAK,CAAC;IAChD,OAAOC,OAAO;EAChB;EACAE,UAAUA,CAAC5C,GAAG,EAAEyC,KAAK;IACnB,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAE0C,IAAI,GAAG7C,GAAG,CAACI,MAAM,EAAED,CAAC,GAAG0C,IAAI,EAAE1C,CAAC,EAAE,EAAE;MAChD,IAAIH,GAAG,CAACG,CAAC,CAAC,CAAC2C,EAAE,IAAIL,KAAK,EAAE;QACtB,OAAOzC,GAAG,CAACG,CAAC,CAAC;MACf;MAAC;IACH;EACF;EACA4C,WAAWA,CAACC,IAAI;IACd,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAE;MAChB,IAAIC,WAAW,GAAG3D,MAAM,CAAC4D,GAAG,CAACF,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAACG,QAAQ,EAAE,GAAG,GAAG,GAAGH,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAACG,QAAQ,EAAE,GAAG,GAAG,GAAGH,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAACG,QAAQ,EAAE,CAAC;MAC7I,OAAOF,WAAW;IACpB,CAAC,MAAM;MACLD,IAAI,GAAG,IAAII,IAAI,CAACJ,IAAI,CAAC;MACrB,IAAIK,OAAO,GAAGL,IAAI,CAACK,OAAO,EAAE;MAC5B,IAAIC,QAAQ,GAAGN,IAAI,CAACM,QAAQ,EAAE;MAC9B,IAAIC,WAAW,GAAGP,IAAI,CAACO,WAAW,EAAE;MACpC,IAAIN,WAAW,GAAG3D,MAAM,CAAC4D,GAAG,CAACG,OAAO,CAACF,QAAQ,EAAE,GAAG,GAAG,GAAGG,QAAQ,CAACH,QAAQ,EAAE,GAAG,GAAG,GAAGI,WAAW,CAACJ,QAAQ,EAAE,CAAC;MAC3G,OAAOF,WAAW;IACpB;EACF;EAEAO,yBAAyBA,CAAA;IACvB,OAAO,CAAC,QAAQ,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,EAAE,UAAU,CAAC;EAC1F;EAEAC,eAAeA,CAACC,QAAgB;IAE9B;IAEA,MAAMC,iBAAiB,GAAG,CACxB;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,UAAU;UACnB,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,cAAc;UACvB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,KAAK;UACd,KAAK,EAAE,KAAK;UACZ,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,sBAAsB;QAC9B,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,sBAAsB;UAC/B,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,eAAe;UAC1B,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,aAAa;QACpB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,aAAa;UACpB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,eAAe;QACvB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,eAAe;UACvB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,gBAAgB;QACvB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,gBAAgB;UACvB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,iBAAiB;UAC5B,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,WAAW;QACnB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,WAAW;UACnB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,SAAS,EAAE,CACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;WACV,CACF;UACT,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,mBAAmB;QAC3B,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,mBAAmB;UAC3B,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,eAAe;QACvB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,eAAe;UACvB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,eAAe;QACvB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,eAAe;UACvB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,kBAAkB;QACzB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,kBAAkB;UACzB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE,KAAK;UACjB,YAAY,EAAE,SAAAC,CAASC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK;YACjD;YACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;UACnB;SACF;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,gBAAgB;QACvB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,gBAAgB;UACvB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,MAAM;UACb,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;;MAGlB;MACA;MAAA;KAEH,CACF;IAED,MAAMG,UAAU,GAAG,CACjB;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,KAAK;UACd,KAAK,EAAE,KAAK;UACZ,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,cAAc;UACvB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,gBAAgB;QACvB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,gBAAgB;UACvB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,UAAU;UACnB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,MAAM;UACb,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,gBAAgB;QACvB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,gBAAgB;UACvB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,WAAW;QACnB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,WAAW;UACnB,SAAS,EAAE,CACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;WACV,CACF;UACT,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,mBAAmB;QAC3B,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,mBAAmB;UAC3B,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;;MAGlB;MACA;MAAA;KAEH,CACF;IAED,MAAMC,YAAY,GAAG,CACnB;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,cAAc;UACvB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,KAAK;UACd,KAAK,EAAE,KAAK;UACZ,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,UAAU;UACnB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,MAAM;UACb,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,WAAW;QACnB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,WAAW;UACnB,SAAS,EAAE,CACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;WACV,CACF;UACT,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,mBAAmB;QAC3B,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,mBAAmB;UAC3B,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,gBAAgB;QACvB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,gBAAgB;UACvB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,eAAe;QACvB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,eAAe;UACvB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,eAAe;QACvB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,eAAe;UACvB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;;MAGlB;MACA;MAAA;KAEH,CACF;IAED,MAAMC,eAAe,GAAG,CACtB;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,cAAc;UACvB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,KAAK;UACd,KAAK,EAAE,KAAK;UACZ,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,MAAM;UACb,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,WAAW;QACnB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,WAAW;UACnB,SAAS,EAAE,CACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;WACV,CACF;UACT,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,mBAAmB;QAC3B,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,mBAAmB;UAC3B,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,eAAe;QACvB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,eAAe;UACvB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,eAAe;QACvB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,eAAe;UACvB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,gBAAgB;QACvB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,gBAAgB;UACvB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,UAAU;UACnB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;;MAGlB;MACA;MAAA;KAEH,CACF;IAED,MAAMC,kBAAkB,GAAG,CACzB;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,cAAc;UACvB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,KAAK;UACd,KAAK,EAAE,KAAK;UACZ,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,aAAa;QACpB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,cAAc;UACvB,KAAK,EAAE,aAAa;UACpB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,MAAM;UACb,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACvB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACX;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,UAAU;UACnB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,kBAAkB;QAC1B,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,kBAAkB;UAC1B,SAAS,EAAE,CACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;WACV,CACT;UACF,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EAAE;QACD,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,YAAY;UACpB,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,cAAc,EAAE,MAAM;UACtB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,WAAW;QACnB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,WAAW;UACnB,SAAS,EAAE,CACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;WACV,CACT;UACF,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,mBAAmB;QAC3B,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,mBAAmB;UAC3B,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,qBAAqB;QAC5B,MAAM,EAAE,qBAAqB;QAC7B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,qBAAqB;UAC9B,KAAK,EAAE,qBAAqB;UAC5B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;;MAGlB;MACA;MAAA;KAEH,CACF;IAED,MAAMC,qBAAqB,GAAG,CAC5B;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,cAAc;QACtB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,cAAc;UACvB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,KAAK;UACd,KAAK,EAAE,KAAK;UACZ,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,WAAW;QACnB,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,WAAW;UACnB,SAAS,EAAE,CACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;WACV,CACF;UACT,UAAU,EAAE,KAAK;UACjB,SAAS,EAAE,EAAE;UACb,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,mBAAmB;QAC3B,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,mBAAmB;UAC3B,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB;KAEJ,CACF;IAED,MAAMC,YAAY,GAAG,CACnB;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,eAAe;UACtB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,SAAS,EAAC,EAAE;UACZ,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,0BAA0B;QACjC,MAAM,EAAE,yBAAyB;QACjC,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,yBAAyB;UAClC,KAAK,EAAE,0BAA0B;UACjC,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,kBAAkB;QACzB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,kBAAkB;UACzB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,oBAAoB;QAC3B,MAAM,EAAE,qBAAqB;QAC7B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,qBAAqB;UAC9B,KAAK,EAAE,oBAAoB;UAC3B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,sBAAsB;QAC7B,MAAM,EAAE,uBAAuB;QAC/B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,uBAAuB;UAChC,KAAK,EAAE,sBAAsB;UAC7B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,QAAQ;UACjB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,SAAS;QAChB,MAAM,EAAE,UAAU;QAClB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,UAAU;UACnB,KAAK,EAAE,SAAS;UAChB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,MAAM;UACf,KAAK,EAAE,MAAM;UACb,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,KAAK;UACd,KAAK,EAAE,KAAK;UACZ,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,mBAAmB;UAC1B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,oBAAoB;QAC3B,MAAM,EAAE,sBAAsB;QAC9B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,sBAAsB;UAC/B,KAAK,EAAE,oBAAoB;UAC3B,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,kBAAkB;QACzB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,kBAAkB;UACzB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,OAAO;UACd,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,iBAAiB;UACxB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,kBAAkB;QACzB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,kBAAkB;UACzB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,OAAO;UACd,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,UAAU;UACjB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,SAAS;QAChB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,SAAS;UAChB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,eAAe;QACvB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,eAAe;UACxB,KAAK,EAAE,QAAQ;UACf,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,WAAW;UAClB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,aAAa;QACpB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,aAAa;UACpB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,aAAa;QACpB,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,oBAAoB;UAC7B,KAAK,EAAE,aAAa;UACpB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,YAAY;UACnB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,cAAc;UACrB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,aAAa;QACpB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,OAAO;QACf,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,aAAa;UACpB,eAAe,EAAE,IAAI;UACrB,MAAM,EAAE,OAAO;UACf,UAAU,EAAE,KAAK;UAEjB,UAAU,EAAE;SACb;QACD,YAAY,EAAE;UACZ,YAAY,EAAE;;OAEjB;KAEJ,CACF;IAED,MAAMC,oBAAoB,GAAG,CAC3B;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,gBAAgB;QACxB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,eAAe;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,SAAS;QACtB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,SAAS;UAChB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,mBAAmB;QAC1B,WAAW,EAAE,mBAAmB;QAChC,MAAM,EAAE,qBAAqB;QAC7B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,qBAAqB;UAC9B,KAAK,EAAE,mBAAmB;UAC1B,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,aAAa;QAClB,WAAW,EAAE,aAAa;QAC5B,MAAM,EAAE,uBAAuB;QAC/B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,uBAAuB;UAChC,KAAK,EAAE,aAAa;UACpB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,sBAAsB;QAC7B,WAAW,EAAE,sBAAsB;QACnC,MAAM,EAAE,uBAAuB;QAC/B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,uBAAuB;UAChC,KAAK,EAAE,sBAAsB;UAC7B,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,oBAAoB;QACjC,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,WAAW;UACpB,KAAK,EAAE,oBAAoB;UAC3B,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE,mBAAmB;QAC3B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,oBAAoB;QACjC,MAAM,EAAE,6BAA6B;QACrC,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,6BAA6B;UACtC,KAAK,EAAE,oBAAoB;UAC3B,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,WAAW,EAAE,MAAM;QACnB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,gBAAgB;UACzB,KAAK,EAAE,MAAM;UACb,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAC;QACA,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,WAAW,EAAE,MAAM;QACnB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,MAAM;UACb,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAC;QACA,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,WAAW,EAAE,MAAM;QACnB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,MAAM;UACb,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAC;QACA,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,WAAW,EAAE,MAAM;QACnB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,MAAM;UACb,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAC;QACA,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,WAAW,EAAE,MAAM;QACnB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,MAAM;UACb,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAC;QACA,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,MAAM;QACb,WAAW,EAAE,MAAM;QACnB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,OAAO;UAChB,KAAK,EAAE,MAAM;UACb,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,eAAe;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAU;QACT,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,eAAe;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAU;QACT,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,eAAe;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAU;QACT,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,eAAe;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAU;QACT,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,eAAe;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EAAU;QACT,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,eAAe;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QACjB,WAAW,EAAE,WAAW;QACzB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACL;QACE,WAAW,EAAE,UAAU;QACzB,KAAK,EAAE,mBAAmB;QAC1B,WAAW,EAAE,mBAAmB;QAChC,MAAM,EAAE,mBAAmB;QACzB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,KAAK;QACjB,iBAAiB,EAAE;UACrB,OAAO,EAAE,mBAAmB;UAC5B,KAAK,EAAE,mBAAmB;UACtB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf;KAEA,CACF;IAED,MAAMC,yBAAyB,GAAG,CAChC;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,WAAW,EAAE,YAAY;QACzB,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,kBAAkB;UAC3B,KAAK,EAAE,YAAY;UACnB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf,EACD;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE,YAAY;QACpB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,YAAY;UACrB,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf;KAEJ,CACF;IAED,MAAMC,kBAAkB,GAAG,CACzB;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,WAAW;QACxB,MAAM,EAAE,iBAAiB;QACzB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,iBAAiB;UAC1B,KAAK,EAAE,WAAW;UAClB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf;KAEJ,CACF;IAED,MAAMC,0BAA0B,GAAG,CACjC;MACE,qBAAqB,EAAE,KAAK;MAC5B,YAAY,EAAE,CACZ;QACE,WAAW,EAAE,UAAU;QACvB,KAAK,EAAE,YAAY;QACnB,WAAW,EAAE,YAAY;QACzB,MAAM,EAAE,aAAa;QACrB,MAAM,EAAE,MAAM;QACd,cAAc,EAAE,EAAE;QAClB,iBAAiB,EAAE;UACjB,OAAO,EAAE,aAAa;UACtB,KAAK,EAAE,YAAY;UACnB,MAAM,EAAE,MAAM;UACd,UAAU,EAAE,KAAK;UACjB,UAAU,EAAE;;OAEf;KAEJ,CACF;IAED,MAAMlD,GAAG,GAAG,CACV;MACE,WAAW,EAAE,SAAS;MACtB,OAAO,EAAE,CACP;QACE,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE;OACX;KAEJ,EACD;MACE,WAAW,EAAE,SAAS;MACtB,OAAO,EAAE,CACP;QACE,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE,IAAI;QAAC;QACf,QAAQ,EAAE;OACX;KAEJ,EACD;MACE,WAAW,EAAE,iBAAiB;MAC9B,OAAO,EAAE,CACP;QACE,MAAM,EAAE,iBAAiB;QACzB,QAAQ,EAAE,KAAK;QACf,QAAQ,EAAE,CACR;UACE,qBAAqB,EAAE,KAAK;UAC5B,YAAY,EAAE;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UAAA;SAEH;OAEJ;KAEJ,CACF;IAED;IACA,IAAIgC,QAAQ,IAAI,aAAa,EAAE;MAC7BhC,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGnB,iBAAiB;IAC5C;IACA,IAAID,QAAQ,IAAI,UAAU,EAAE;MAC1BhC,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGX,UAAU;IACrC;IACA,IAAIT,QAAQ,IAAI,YAAY,EAAE;MAC5BhC,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGV,YAAY;IACvC;IACA,IAAIV,QAAQ,IAAI,sBAAsB,EAAE;MACtChC,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGT,eAAe;IAC1C;IACA,IAAIX,QAAQ,IAAI,cAAc,EAAE;MAC9BhC,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGR,kBAAkB;IAC7C;IACA,IAAIZ,QAAQ,IAAI,WAAW,IAAIA,QAAQ,IAAI,cAAc,EAAE;MACzDhC,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGP,qBAAqB;IAChD;IAEA;IACA7C,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGN,YAAY;IAErC;IACA9C,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAGL,oBAAoB;IAC7C,MAAMM,SAAS,GAAGN,oBAAoB,CAAC,CAAC,CAAC,CAACO,UAAU,CAAC5E,MAAM;IAC3D,IAAIsD,QAAQ,IAAI,aAAa,EAAE;MAC7B,MAAMoB,MAAM,GAAGJ,yBAAyB,CAAC,CAAC,CAAC,CAACM,UAAU;MACtDF,MAAM,CAACG,OAAO,CAACC,CAAC,IAAIxD,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACE,UAAU,CAACG,MAAM,CAACJ,SAAS,GAAG,CAAC,EAAE,CAAC,EAAEG,CAAC,CAAC,CAAC;IACvF;IACA,IAAIxB,QAAQ,IAAI,UAAU,EAAE;MAC1B,MAAMoB,MAAM,GAAGH,kBAAkB,CAAC,CAAC,CAAC,CAACK,UAAU;MAC/CF,MAAM,CAACG,OAAO,CAACC,CAAC,IAAIxD,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACE,UAAU,CAACG,MAAM,CAACJ,SAAS,GAAG,CAAC,EAAE,CAAC,EAAEG,CAAC,CAAC,CAAC;IACvF;IACA,IAAIxB,QAAQ,IAAI,cAAc,EAAE;MAC9B,MAAMoB,MAAM,GAAGF,0BAA0B,CAAC,CAAC,CAAC,CAACI,UAAU;MACvDF,MAAM,CAACG,OAAO,CAACC,CAAC,IAAIxD,GAAG,CAAC,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACE,UAAU,CAACG,MAAM,CAACJ,SAAS,GAAG,CAAC,EAAE,CAAC,EAAEG,CAAC,CAAC,CAAC;IACvF;IACA,OAAOxD,GAAG;EACZ;EAGA0D,SAASA,CAACnF,GAAG;IACX,IAAI+B,QAAQ,GAAG,CAAC,sBAAsB,EAAC,yBAAyB,EAAC,uBAAuB,CAAC;IACzF,IAAIqD,OAAO,GAAGrD,QAAQ,CAACW,IAAI,CAAC2C,CAAC,IAAGA,CAAC,CAAEC,WAAW,EAAE,IAAGtF,GAAG,CAAEsF,WAAW,EAAE,CAAC;IACtE,OAAOF,OAAO;EAChB;EAEAG,SAASA,CAACvF,GAAG;IACX,IAAI+B,QAAQ,GAAG,CAAC,kBAAkB,EAAC,6BAA6B,CAAC;IACjE,IAAIqD,OAAO,GAAGrD,QAAQ,CAACW,IAAI,CAAC2C,CAAC,IAAGA,CAAC,CAAEC,WAAW,EAAE,IAAGtF,GAAG,CAAEsF,WAAW,EAAE,CAAC;IACtE,OAAOF,OAAO;EAChB;;;;;;;AArrIW9F,iBAAiB,GAAAkG,UAAA,EAH7BtG,UAAU,CAAC;EACVuG,UAAU,EAAE;CACb,CAAC,C,EACWnG,iBAAiB,CAwrI7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}