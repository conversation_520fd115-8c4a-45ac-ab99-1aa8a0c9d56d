{"ast": null, "code": "/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, createPlatformFactory, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { ɵplatformCoreDynamic, ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS } from '@angular/platform-browser-dynamic';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport { ɵgetDOM, DOCUMENT } from '@angular/common';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  insertRootElement(rootElId) {\n    this.removeAllRootElementsImpl();\n    const rootElement = ɵgetDOM().getDefaultDocument().createElement('div');\n    rootElement.setAttribute('id', rootElId);\n    this._doc.body.appendChild(rootElement);\n  }\n  removeAllRootElements() {\n    // Check whether the `DOCUMENT` instance retrieved from DI contains\n    // the necessary function to complete the cleanup. In tests that don't\n    // interact with DOM, the `DOCUMENT` might be mocked and some functions\n    // might be missing. For such tests, DOM cleanup is not required and\n    // we skip the logic if there are missing functions.\n    if (typeof this._doc.querySelectorAll === 'function') {\n      this.removeAllRootElementsImpl();\n    }\n  }\n  removeAllRootElementsImpl() {\n    const oldRoots = this._doc.querySelectorAll('[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      ɵgetDOM().remove(oldRoots[i]);\n    }\n  }\n  static {\n    this.ɵfac = function DOMTestComponentRenderer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DOMTestComponentRenderer)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DOMTestComponentRenderer,\n      factory: DOMTestComponentRenderer.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DOMTestComponentRenderer, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Platform for dynamic tests\n *\n * @publicApi\n */\nconst platformCoreDynamicTesting = createPlatformFactory(ɵplatformCoreDynamic, 'coreDynamicTesting', []);\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformCoreDynamicTesting, 'browserDynamicTesting', ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {\n  static {\n    this.ɵfac = function BrowserDynamicTestingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BrowserDynamicTestingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserDynamicTestingModule,\n      exports: [BrowserTestingModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: TestComponentRenderer,\n        useClass: DOMTestComponentRenderer\n      }],\n      imports: [BrowserTestingModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserDynamicTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserTestingModule],\n      providers: [{\n        provide: TestComponentRenderer,\n        useClass: DOMTestComponentRenderer\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting, DOMTestComponentRenderer as ɵDOMTestComponentRenderer, platformCoreDynamicTesting as ɵplatformCoreDynamicTesting };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "createPlatformFactory", "NgModule", "TestComponent<PERSON><PERSON><PERSON>", "ɵplatformCoreDynamic", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "BrowserTestingModule", "ɵgetDOM", "DOCUMENT", "DOMTestComponentRenderer", "constructor", "_doc", "insertRootElement", "rootElId", "removeAllRootElementsImpl", "rootElement", "getDefaultDocument", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "removeAllRootElements", "querySelectorAll", "oldRoots", "i", "length", "remove", "ɵfac", "DOMTestComponentRenderer_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "platformCoreDynamicTesting", "platformBrowserDynamicTesting", "BrowserDynamicTestingModule", "BrowserDynamicTestingModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector", "providers", "provide", "useClass", "imports", "ɵDOMTestComponentRenderer", "ɵplatformCoreDynamicTesting"], "sources": ["D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@angular/platform-browser-dynamic/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, createPlatformFactory, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { ɵplatformCoreDynamic, ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS } from '@angular/platform-browser-dynamic';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport { ɵgetDOM, DOCUMENT } from '@angular/common';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    insertRootElement(rootElId) {\n        this.removeAllRootElementsImpl();\n        const rootElement = ɵgetDOM().getDefaultDocument().createElement('div');\n        rootElement.setAttribute('id', rootElId);\n        this._doc.body.appendChild(rootElement);\n    }\n    removeAllRootElements() {\n        // Check whether the `DOCUMENT` instance retrieved from DI contains\n        // the necessary function to complete the cleanup. In tests that don't\n        // interact with DOM, the `DOCUMENT` might be mocked and some functions\n        // might be missing. For such tests, DOM cleanup is not required and\n        // we skip the logic if there are missing functions.\n        if (typeof this._doc.querySelectorAll === 'function') {\n            this.removeAllRootElementsImpl();\n        }\n    }\n    removeAllRootElementsImpl() {\n        const oldRoots = this._doc.querySelectorAll('[id^=root]');\n        for (let i = 0; i < oldRoots.length; i++) {\n            ɵgetDOM().remove(oldRoots[i]);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DOMTestComponentRenderer, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DOMTestComponentRenderer }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DOMTestComponentRenderer, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Platform for dynamic tests\n *\n * @publicApi\n */\nconst platformCoreDynamicTesting = createPlatformFactory(ɵplatformCoreDynamic, 'coreDynamicTesting', []);\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformCoreDynamicTesting, 'browserDynamicTesting', ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserDynamicTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserDynamicTestingModule, exports: [BrowserTestingModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserDynamicTestingModule, providers: [{ provide: TestComponentRenderer, useClass: DOMTestComponentRenderer }], imports: [BrowserTestingModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserDynamicTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserTestingModule],\n                    providers: [{ provide: TestComponentRenderer, useClass: DOMTestComponentRenderer }],\n                }]\n        }] });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting, DOMTestComponentRenderer as ɵDOMTestComponentRenderer, platformCoreDynamicTesting as ɵplatformCoreDynamicTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,QAAQ,QAAQ,eAAe;AACnF,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,oBAAoB,EAAEC,4CAA4C,QAAQ,mCAAmC;AACtH,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,OAAO,EAAEC,QAAQ,QAAQ,iBAAiB;;AAEnD;AACA;AACA;AACA,MAAMC,wBAAwB,SAASN,qBAAqB,CAAC;EACzDO,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAC,iBAAiBA,CAACC,QAAQ,EAAE;IACxB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,MAAMC,WAAW,GAAGR,OAAO,CAAC,CAAC,CAACS,kBAAkB,CAAC,CAAC,CAACC,aAAa,CAAC,KAAK,CAAC;IACvEF,WAAW,CAACG,YAAY,CAAC,IAAI,EAAEL,QAAQ,CAAC;IACxC,IAAI,CAACF,IAAI,CAACQ,IAAI,CAACC,WAAW,CAACL,WAAW,CAAC;EAC3C;EACAM,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACV,IAAI,CAACW,gBAAgB,KAAK,UAAU,EAAE;MAClD,IAAI,CAACR,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACAA,yBAAyBA,CAAA,EAAG;IACxB,MAAMS,QAAQ,GAAG,IAAI,CAACZ,IAAI,CAACW,gBAAgB,CAAC,YAAY,CAAC;IACzD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCjB,OAAO,CAAC,CAAC,CAACmB,MAAM,CAACH,QAAQ,CAACC,CAAC,CAAC,CAAC;IACjC;EACJ;EACA;IAAS,IAAI,CAACG,IAAI,YAAAC,iCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAyFpB,wBAAwB,EAAlCX,EAAE,CAAAgC,QAAA,CAAkDtB,QAAQ;IAAA,CAA6C;EAAE;EAC5M;IAAS,IAAI,CAACuB,KAAK,kBAD8EjC,EAAE,CAAAkC,kBAAA;MAAAC,KAAA,EACYxB,wBAAwB;MAAAyB,OAAA,EAAxBzB,wBAAwB,CAAAkB;IAAA,EAAG;EAAE;AAChJ;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAHqGrC,EAAE,CAAAsC,iBAAA,CAGX3B,wBAAwB,EAAc,CAAC;IACvH4B,IAAI,EAAEtC;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsC,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAErC,MAAM;MACZwC,IAAI,EAAE,CAAChC,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA,MAAMiC,0BAA0B,GAAGxC,qBAAqB,CAACG,oBAAoB,EAAE,oBAAoB,EAAE,EAAE,CAAC;;AAExG;AACA;AACA;AACA,MAAMsC,6BAA6B,GAAGzC,qBAAqB,CAACwC,0BAA0B,EAAE,uBAAuB,EAAEpC,4CAA4C,CAAC;AAC9J;AACA;AACA;AACA;AACA;AACA,MAAMsC,2BAA2B,CAAC;EAC9B;IAAS,IAAI,CAAChB,IAAI,YAAAiB,oCAAAf,iBAAA;MAAA,YAAAA,iBAAA,IAAyFc,2BAA2B;IAAA,CAAkD;EAAE;EAC1L;IAAS,IAAI,CAACE,IAAI,kBA5B+E/C,EAAE,CAAAgD,gBAAA;MAAAT,IAAA,EA4BSM,2BAA2B;MAAAI,OAAA,GAAYzC,oBAAoB;IAAA,EAAI;EAAE;EAC7K;IAAS,IAAI,CAAC0C,IAAI,kBA7B+ElD,EAAE,CAAAmD,gBAAA;MAAAC,SAAA,EA6BiD,CAAC;QAAEC,OAAO,EAAEhD,qBAAqB;QAAEiD,QAAQ,EAAE3C;MAAyB,CAAC,CAAC;MAAA4C,OAAA,GAAY/C,oBAAoB;IAAA,EAAI;EAAE;AACtQ;AACA;EAAA,QAAA6B,SAAA,oBAAAA,SAAA,KA/BqGrC,EAAE,CAAAsC,iBAAA,CA+BXO,2BAA2B,EAAc,CAAC;IAC1HN,IAAI,EAAEnC,QAAQ;IACdsC,IAAI,EAAE,CAAC;MACCO,OAAO,EAAE,CAACzC,oBAAoB,CAAC;MAC/B4C,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEhD,qBAAqB;QAAEiD,QAAQ,EAAE3C;MAAyB,CAAC;IACtF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASkC,2BAA2B,EAAED,6BAA6B,EAAEjC,wBAAwB,IAAI6C,yBAAyB,EAAEb,0BAA0B,IAAIc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}