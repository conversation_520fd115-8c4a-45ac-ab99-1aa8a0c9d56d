{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./case-filing.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./case-filing.component.css?ngResource\";\nimport { Component, Output, EventEmitter, Input } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { legalService } from '../legal.service';\nimport { legalConfigService } from '../legalconfig.service';\nlet CaseFilingComponent = class CaseFilingComponent {\n  constructor(toastr, legalService, legalConfigService) {\n    this.toastr = toastr;\n    this.legalService = legalService;\n    this.legalConfigService = legalConfigService;\n    this.viewDetails = new EventEmitter();\n    this.caseFiling = {};\n    this.workflowStatusList = [];\n    this.loader = {\n      isSearching: false,\n      statusSearch: false\n    };\n    this.lastDate = new Date();\n    this.lastDate.setDate(this.lastDate.getDate() - 1);\n    this.nextDate = new Date();\n    this.nextDate.setDate(this.nextDate.getDate() + 1);\n  }\n  ngOnInit() {\n    // this.getWorkflowStatus()\n    this.setCaseFilingParams();\n  }\n  setCaseFilingParams() {\n    this.caseFiling = {\n      \"mandateNumber\": \"\",\n      \"mandateAmount\": \"\",\n      \"issuingBank\": \"\",\n      \"bankAccountNumber\": \"\",\n      \"presentationDate\": \"\",\n      \"bounceReferenceNo\": \"\",\n      \"bounceDate\": \"\",\n      \"bounceReason\": \"\",\n      \"advocateName\": \"\",\n      \"advocateEmailId\": \"\",\n      \"advocateContactNumber\": \"\",\n      \"caseFilingDate\": \"\",\n      \"courtLocation\": \"\",\n      \"courtName\": \"\",\n      \"lastDateOfHearing\": \"\",\n      \"nextDateOfHearing\": \"\",\n      \"caseStatus\": \"\"\n    };\n  }\n  getWorkflowStatus(data) {\n    this.loader.statusSearch = true;\n    let inputParams = {\n      \"casetype\": data\n    };\n    this.legalService.getworkflowStatusList(inputParams).subscribe(response => {\n      this.loader.statusSearch = false;\n      this.workflowStatusList = this.legalConfigService.generalKeySort(response, \"status\");\n    }, err => {\n      this.toastr.error(err);\n      this.loader.statusSearch = false;\n    });\n  }\n  caseStatusNoResults(event) {\n    this.caseNoResult = event;\n  }\n  caseStatusChangeLoading(event) {\n    this.caseStatustypeaheadLoading = event;\n  }\n  oncaseStatusSelect(event) {\n    this.caseFiling[\"caseStatus\"] = event.item.status;\n  }\n  addUpdateCaseFiling() {\n    if (this.legalId) {\n      this.caseFiling[\"legalId\"] = this.custId; // custom id will be here\n      this.loader.isSearching = true;\n      this.legalService.addUpdateCaseFiling(this.caseFiling).subscribe(response => {\n        this.toastr.success(\"Case Filing successfully\");\n        this.loader.isSearching = false;\n        this.viewDetails.emit({\n          id: this.legalId\n        });\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSearching = false;\n      });\n    } else {\n      this.caseFiling[\"legalId\"] = this.addLegalId;\n      this.loader.isSearching = true;\n      this.legalService.addUpdateCaseFiling(this.caseFiling).subscribe(response => {\n        this.toastr.success(\"Case Filing successfully\");\n        this.loader.isSearching = false;\n        // this.setCaseFilingParams()\n        this.viewDetails.emit({\n          \"legalId\": response\n        });\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSearching = false;\n      });\n    }\n  }\n  sendData(data, custId) {\n    console.log(data);\n    this.custId = custId;\n    this.legalId = data.id;\n    this.caseFiling = data;\n    this.getWorkflowStatus(data.caseType);\n    if (data.bouncingDate) {\n      this.caseFiling[\"bounceDate\"] = new Date(data.bouncingDate);\n    }\n    this.caseFiling[\"bounceReason\"] = data.bouncingReason;\n    this.caseFiling[\"advocateName\"] = data.epAdvocateName;\n    this.caseFiling[\"advocateEmailId\"] = data.epAdvocateEmailId;\n    if (data.caseFilingDate) {\n      this.caseFiling[\"caseFilingDate\"] = new Date(data.caseFilingDate);\n    }\n    if (data.presentationDate) {\n      this.caseFiling[\"presentationDate\"] = new Date(data.presentationDate);\n    }\n    if (data.caseStatus) {\n      this.caseFiling[\"caseStatus\"] = data.caseStatus;\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: legalService\n    }, {\n      type: legalConfigService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      legalId: [{\n        type: Input\n      }],\n      addLegalId: [{\n        type: Input\n      }],\n      viewDetails: [{\n        type: Output,\n        args: [\"viewDetails\"]\n      }]\n    };\n  }\n};\nCaseFilingComponent = __decorate([Component({\n  selector: 'legal-case-filing',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CaseFilingComponent);\nexport { CaseFilingComponent };", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "Input", "ToastrService", "legalService", "legalConfigService", "CaseFilingComponent", "constructor", "toastr", "viewDetails", "caseFiling", "workflowStatusList", "loader", "isSearching", "statusSearch", "lastDate", "Date", "setDate", "getDate", "nextDate", "ngOnInit", "setCaseFilingParams", "getWorkflowStatus", "data", "inputParams", "getworkflowStatusList", "subscribe", "response", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "error", "caseStatusNoResults", "event", "caseNoResult", "caseStatusChangeLoading", "caseStatustypeaheadLoading", "oncaseStatusSelect", "item", "status", "addUpdateCaseFiling", "legalId", "custId", "success", "emit", "id", "addLegalId", "sendData", "console", "log", "caseType", "bouncingDate", "bouncingReason", "epAdvocateName", "epAdvocateEmailId", "caseFilingDate", "presentationDate", "caseStatus", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\case-filing\\case-filing.component.ts"], "sourcesContent": ["import { Component, OnInit, Output,EventEmitter,Input } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { legalService } from '../legal.service';\r\nimport { legalConfigService } from '../legalconfig.service';\r\nexport interface Loader{\r\n  isSearching: boolean;\r\n  statusSearch: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'legal-case-filing',\r\n  templateUrl: './case-filing.component.html',\r\n  styleUrls: ['./case-filing.component.css']\r\n})\r\nexport class CaseFilingComponent implements OnInit {\r\n  @Input() legalId: any;\r\n  @Input() addLegalId: any;\r\n  @Output(\"viewDetails\") viewDetails: EventEmitter<any> = new EventEmitter();\r\n  loader: Loader;\r\n  caseFiling : any = {}\r\n  workflowStatusList = [];\r\n  custId:any\r\n  lastDate: any;\r\n  nextDate: any;\r\n  caseNoResult:any;\r\n  caseStatustypeaheadLoading:any;\r\n  constructor(public toastr: ToastrService,private legalService: legalService,\r\n    private legalConfigService: legalConfigService) {\r\n      this.loader = {\r\n        isSearching: false,\r\n        statusSearch: false\r\n      }\r\n      this.lastDate =new Date();\r\n     this.lastDate.setDate(this.lastDate.getDate()-1)\r\n     this.nextDate =new Date();\r\n     this.nextDate.setDate(this.nextDate.getDate()+1)\r\n  }\r\n\r\n  ngOnInit() {\r\n    // this.getWorkflowStatus()\r\n    this.setCaseFilingParams()\r\n  }\r\n\r\n  setCaseFilingParams(){\r\n    this.caseFiling={\r\n      \"mandateNumber\":\"\",\r\n      \"mandateAmount\":\"\",\r\n      \"issuingBank\":\"\",\r\n      \"bankAccountNumber\":\"\",\r\n      \"presentationDate\":\"\",\r\n      \"bounceReferenceNo\":\"\",\r\n      \"bounceDate\":\"\",\r\n      \"bounceReason\":\"\",\r\n      \"advocateName\":\"\",\r\n      \"advocateEmailId\":\"\",\r\n      \"advocateContactNumber\":\"\",\r\n      \"caseFilingDate\":\"\",\r\n      \"courtLocation\":\"\",\r\n      \"courtName\":\"\",\r\n      \"lastDateOfHearing\":\"\",\r\n      \"nextDateOfHearing\":\"\",\r\n      \"caseStatus\":\"\"\r\n    }\r\n  }\r\n\r\n  getWorkflowStatus(data){\r\n    this.loader.statusSearch = true\r\n     let inputParams = {\r\n      \"casetype\": data  \r\n    }\r\n    this.legalService.getworkflowStatusList(inputParams).subscribe(response => { \r\n      this.loader.statusSearch = false\r\n      this.workflowStatusList= this.legalConfigService.generalKeySort(response,\"status\");\r\n    },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.statusSearch = false\r\n     });\r\n }\r\n\r\ncaseStatusNoResults(event: boolean): void {\r\n  this.caseNoResult = event;\r\n}\r\n\r\ncaseStatusChangeLoading(event: boolean): void {\r\n  this.caseStatustypeaheadLoading = event;\r\n}\r\n\r\noncaseStatusSelect(event){\r\n  this.caseFiling[\"caseStatus\"] = event.item.status\r\n}\r\n\r\naddUpdateCaseFiling(){\r\n    if(this.legalId){\r\n      this.caseFiling[\"legalId\"] = this.custId // custom id will be here\r\n      this.loader.isSearching = true\r\n      this.legalService.addUpdateCaseFiling(this.caseFiling).subscribe(response => { \r\n        this.toastr.success(\"Case Filing successfully\")\r\n        this.loader.isSearching = false\r\n        this.viewDetails.emit({id: this.legalId})\r\n     },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching = false\r\n     });\r\n\r\n    }else{\r\n      this.caseFiling[\"legalId\"] = this.addLegalId\r\n      this.loader.isSearching = true\r\n      this.legalService.addUpdateCaseFiling(this.caseFiling).subscribe(response => { \r\n        this.toastr.success(\"Case Filing successfully\")\r\n        this.loader.isSearching = false\r\n        // this.setCaseFilingParams()\r\n        this.viewDetails.emit({\"legalId\": response})\r\n     },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching = false\r\n     });\r\n    }\r\n  }\r\n\r\n  sendData(data,custId){\r\n    console.log(data)\r\n    this.custId = custId\r\n    this.legalId=data.id\r\n    this.caseFiling=data\r\n    this.getWorkflowStatus(data.caseType);\r\n    if(data.bouncingDate){\r\n      this.caseFiling[\"bounceDate\"] = new Date(data.bouncingDate)\r\n    }\r\n    this.caseFiling[\"bounceReason\"] = data.bouncingReason\r\n    this.caseFiling[\"advocateName\"] = data.epAdvocateName\r\n    this.caseFiling[\"advocateEmailId\"] = data.epAdvocateEmailId\r\n    if(data.caseFilingDate){\r\n      this.caseFiling[\"caseFilingDate\"] = new Date(data.caseFilingDate)\r\n    }\r\n    if(data.presentationDate){\r\n      this.caseFiling[\"presentationDate\"] = new Date(data.presentationDate) \r\n    }\r\n    if(data.caseStatus){\r\n      this.caseFiling[\"caseStatus\"] = data.caseStatus\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAUC,MAAM,EAACC,YAAY,EAACC,KAAK,QAAQ,eAAe;AAC5E,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,wBAAwB;AAWpD,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAY9BC,YAAmBC,MAAqB,EAASJ,YAA0B,EACjEC,kBAAsC;IAD7B,KAAAG,MAAM,GAANA,MAAM;IAAwB,KAAAJ,YAAY,GAAZA,YAAY;IACnD,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAVL,KAAAI,WAAW,GAAsB,IAAIR,YAAY,EAAE;IAE1E,KAAAS,UAAU,GAAS,EAAE;IACrB,KAAAC,kBAAkB,GAAG,EAAE;IAQnB,IAAI,CAACC,MAAM,GAAG;MACZC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;KACf;IACD,IAAI,CAACC,QAAQ,GAAE,IAAIC,IAAI,EAAE;IAC1B,IAAI,CAACD,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACF,QAAQ,CAACG,OAAO,EAAE,GAAC,CAAC,CAAC;IAChD,IAAI,CAACC,QAAQ,GAAE,IAAIH,IAAI,EAAE;IACzB,IAAI,CAACG,QAAQ,CAACF,OAAO,CAAC,IAAI,CAACE,QAAQ,CAACD,OAAO,EAAE,GAAC,CAAC,CAAC;EACnD;EAEAE,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,CAACX,UAAU,GAAC;MACd,eAAe,EAAC,EAAE;MAClB,eAAe,EAAC,EAAE;MAClB,aAAa,EAAC,EAAE;MAChB,mBAAmB,EAAC,EAAE;MACtB,kBAAkB,EAAC,EAAE;MACrB,mBAAmB,EAAC,EAAE;MACtB,YAAY,EAAC,EAAE;MACf,cAAc,EAAC,EAAE;MACjB,cAAc,EAAC,EAAE;MACjB,iBAAiB,EAAC,EAAE;MACpB,uBAAuB,EAAC,EAAE;MAC1B,gBAAgB,EAAC,EAAE;MACnB,eAAe,EAAC,EAAE;MAClB,WAAW,EAAC,EAAE;MACd,mBAAmB,EAAC,EAAE;MACtB,mBAAmB,EAAC,EAAE;MACtB,YAAY,EAAC;KACd;EACH;EAEAY,iBAAiBA,CAACC,IAAI;IACpB,IAAI,CAACX,MAAM,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAIU,WAAW,GAAG;MACjB,UAAU,EAAED;KACb;IACD,IAAI,CAACnB,YAAY,CAACqB,qBAAqB,CAACD,WAAW,CAAC,CAACE,SAAS,CAACC,QAAQ,IAAG;MACxE,IAAI,CAACf,MAAM,CAACE,YAAY,GAAG,KAAK;MAChC,IAAI,CAACH,kBAAkB,GAAE,IAAI,CAACN,kBAAkB,CAACuB,cAAc,CAACD,QAAQ,EAAC,QAAQ,CAAC;IACpF,CAAC,EAACE,GAAG,IAAE;MACH,IAAI,CAACrB,MAAM,CAACsB,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAACjB,MAAM,CAACE,YAAY,GAAG,KAAK;IACnC,CAAC,CAAC;EACN;EAEDiB,mBAAmBA,CAACC,KAAc;IAChC,IAAI,CAACC,YAAY,GAAGD,KAAK;EAC3B;EAEAE,uBAAuBA,CAACF,KAAc;IACpC,IAAI,CAACG,0BAA0B,GAAGH,KAAK;EACzC;EAEAI,kBAAkBA,CAACJ,KAAK;IACtB,IAAI,CAACtB,UAAU,CAAC,YAAY,CAAC,GAAGsB,KAAK,CAACK,IAAI,CAACC,MAAM;EACnD;EAEAC,mBAAmBA,CAAA;IACf,IAAG,IAAI,CAACC,OAAO,EAAC;MACd,IAAI,CAAC9B,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC+B,MAAM,EAAC;MACzC,IAAI,CAAC7B,MAAM,CAACC,WAAW,GAAG,IAAI;MAC9B,IAAI,CAACT,YAAY,CAACmC,mBAAmB,CAAC,IAAI,CAAC7B,UAAU,CAAC,CAACgB,SAAS,CAACC,QAAQ,IAAG;QAC1E,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,0BAA0B,CAAC;QAC/C,IAAI,CAAC9B,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B,IAAI,CAACJ,WAAW,CAACkC,IAAI,CAAC;UAACC,EAAE,EAAE,IAAI,CAACJ;QAAO,CAAC,CAAC;MAC5C,CAAC,EAACX,GAAG,IAAE;QACJ,IAAI,CAACrB,MAAM,CAACsB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACjB,MAAM,CAACC,WAAW,GAAG,KAAK;MAClC,CAAC,CAAC;IAEH,CAAC,MAAI;MACH,IAAI,CAACH,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAACmC,UAAU;MAC5C,IAAI,CAACjC,MAAM,CAACC,WAAW,GAAG,IAAI;MAC9B,IAAI,CAACT,YAAY,CAACmC,mBAAmB,CAAC,IAAI,CAAC7B,UAAU,CAAC,CAACgB,SAAS,CAACC,QAAQ,IAAG;QAC1E,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,0BAA0B,CAAC;QAC/C,IAAI,CAAC9B,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B;QACA,IAAI,CAACJ,WAAW,CAACkC,IAAI,CAAC;UAAC,SAAS,EAAEhB;QAAQ,CAAC,CAAC;MAC/C,CAAC,EAACE,GAAG,IAAE;QACJ,IAAI,CAACrB,MAAM,CAACsB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACjB,MAAM,CAACC,WAAW,GAAG,KAAK;MAClC,CAAC,CAAC;IACH;EACF;EAEAiC,QAAQA,CAACvB,IAAI,EAACkB,MAAM;IAClBM,OAAO,CAACC,GAAG,CAACzB,IAAI,CAAC;IACjB,IAAI,CAACkB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,OAAO,GAACjB,IAAI,CAACqB,EAAE;IACpB,IAAI,CAAClC,UAAU,GAACa,IAAI;IACpB,IAAI,CAACD,iBAAiB,CAACC,IAAI,CAAC0B,QAAQ,CAAC;IACrC,IAAG1B,IAAI,CAAC2B,YAAY,EAAC;MACnB,IAAI,CAACxC,UAAU,CAAC,YAAY,CAAC,GAAG,IAAIM,IAAI,CAACO,IAAI,CAAC2B,YAAY,CAAC;IAC7D;IACA,IAAI,CAACxC,UAAU,CAAC,cAAc,CAAC,GAAGa,IAAI,CAAC4B,cAAc;IACrD,IAAI,CAACzC,UAAU,CAAC,cAAc,CAAC,GAAGa,IAAI,CAAC6B,cAAc;IACrD,IAAI,CAAC1C,UAAU,CAAC,iBAAiB,CAAC,GAAGa,IAAI,CAAC8B,iBAAiB;IAC3D,IAAG9B,IAAI,CAAC+B,cAAc,EAAC;MACrB,IAAI,CAAC5C,UAAU,CAAC,gBAAgB,CAAC,GAAG,IAAIM,IAAI,CAACO,IAAI,CAAC+B,cAAc,CAAC;IACnE;IACA,IAAG/B,IAAI,CAACgC,gBAAgB,EAAC;MACvB,IAAI,CAAC7C,UAAU,CAAC,kBAAkB,CAAC,GAAG,IAAIM,IAAI,CAACO,IAAI,CAACgC,gBAAgB,CAAC;IACvE;IACA,IAAGhC,IAAI,CAACiC,UAAU,EAAC;MACjB,IAAI,CAAC9C,UAAU,CAAC,YAAY,CAAC,GAAGa,IAAI,CAACiC,UAAU;IACjD;EACF;;;;;;;;;;;;;cA7HCtD;MAAK;;cACLA;MAAK;;cACLF,MAAM;QAAAyD,IAAA,GAAC,aAAa;MAAA;;;;AAHVnD,mBAAmB,GAAAoD,UAAA,EAL/B3D,SAAS,CAAC;EACT4D,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA2C;;CAE5C,CAAC,C,EACWvD,mBAAmB,CAgI/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}