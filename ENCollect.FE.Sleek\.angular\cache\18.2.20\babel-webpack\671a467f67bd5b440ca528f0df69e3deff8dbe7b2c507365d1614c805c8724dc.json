{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./settings-page.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./settings-page.component.css?ngResource\";\nimport { Component } from '@angular/core';\nlet SettingsPageComponent = class SettingsPageComponent {\n  constructor() {\n    this.breadcrumbData = [{\n      label: \"Reports\",\n      path: \"/settings/main-settings\"\n    }, {\n      label: \"Settings\",\n      path: \"/settings/main-settings\"\n    }];\n  }\n  ngOnInit() {}\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nSettingsPageComponent = __decorate([Component({\n  selector: 'app-settings-page',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SettingsPageComponent);\nexport { SettingsPageComponent };", "map": {"version": 3, "names": ["Component", "SettingsPageComponent", "constructor", "breadcrumbData", "label", "path", "ngOnInit", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\common-config\\settings-page\\settings-page.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-settings-page',\r\n  templateUrl: './settings-page.component.html',\r\n  styleUrls: ['./settings-page.component.css']\r\n})\r\nexport class SettingsPageComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Reports\", path: \"/settings/main-settings\" },\r\n\t\t{ label: \"Settings\", path: \"/settings/main-settings\" },\r\n\t  ]\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AAO1C,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAMhCC,YAAA;IALO,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAyB,CAAE,EACrD;MAAED,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAyB,CAAE,CACpD;EAEc;EAEhBC,QAAQA,CAAA,GACR;;;;;AATWL,qBAAqB,GAAAM,UAAA,EALjCP,SAAS,CAAC;EACTQ,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA6C;;CAE9C,CAAC,C,EACWT,qBAAqB,CAWjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}