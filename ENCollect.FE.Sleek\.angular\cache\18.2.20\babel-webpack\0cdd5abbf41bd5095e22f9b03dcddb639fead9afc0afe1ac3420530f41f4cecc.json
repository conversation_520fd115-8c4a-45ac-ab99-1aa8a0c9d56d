{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable, EventEmitter } from '@angular/core';\nimport { ApiService } from '../shared/services/api.service';\nimport * as moment from 'moment/moment';\nlet legalConfigService = class legalConfigService {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.selectedResults = new EventEmitter();\n  }\n  // Internal FIlters\n  filterArrayByState(arr, val) {\n    var cityList = [];\n    for (var i = 0; i < arr.length; i++) {\n      if (arr[i].STATE === val) {\n        cityList.push(arr[i]);\n      }\n    }\n    return cityList;\n  }\n  generalFilterByKey(arr, val, key) {\n    var cityList = [];\n    for (var i = 0; i < arr.length; i++) {\n      if (arr[i][key] === val) {\n        cityList.push(arr[i]);\n      }\n    }\n    return cityList;\n  }\n  generalKeySort(array, key) {\n    return array.sort(function (a, b) {\n      var x = a[key];\n      var y = b[key];\n      return x < y ? -1 : x > y ? 1 : 0;\n    });\n  }\n  cityNameSort(a, b) {\n    const nameA = a.CITY.toUpperCase();\n    const nameB = b.CITY.toUpperCase();\n    let comparison = 0;\n    if (nameA > nameB) {\n      comparison = 1;\n    } else if (nameA < nameB) {\n      comparison = -1;\n    }\n    return comparison;\n  }\n  removeDuplicates(myArr, prop) {\n    return myArr.filter((obj, pos, arr) => {\n      return arr.map(mapObj => mapObj[prop]).indexOf(obj[prop]) === pos;\n    });\n  }\n  convertToCSV(objArray) {\n    var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;\n    var str = '';\n    var row = \"\";\n    for (var index in objArray[0]) {\n      //Now convert each value to string and comma-separated\n      row += index + ',';\n    }\n    row = row.slice(0, -1);\n    //append Label row with line break\n    str += row + '\\r\\n';\n    for (var i = 0; i < array.length; i++) {\n      var line = '';\n      for (var index in array[i]) {\n        if (line != '') line += ',';\n        line += array[i][index];\n      }\n      str += line + '\\r\\n';\n    }\n    return str;\n  }\n  findObjectByKey(arr, key, value) {\n    var results = arr.find(obj => obj[key] == value);\n    return results;\n  }\n  getByValue(arr, value) {\n    for (var i = 0, iLen = arr.length; i < iLen; i++) {\n      if (arr[i].id == value) {\n        return arr[i];\n      }\n      ;\n    }\n  }\n  convertDate(data) {\n    if (data['date']) {\n      var dateFormate = moment.utc(data['date']['month'].toString() + '/' + data['date']['day'].toString() + '/' + data['date']['year'].toString());\n      return dateFormate;\n    } else {\n      data = new Date(data);\n      let getDate = data.getDate();\n      let getMonth = data.getMonth();\n      let getFullYear = data.getFullYear();\n      var dateFormate = moment.utc(getDate.toString() + '/' + getMonth.toString() + '/' + getFullYear.toString());\n      return dateFormate;\n    }\n  }\n  legalAllocationStatusList() {\n    return ['Failed', 'Invalid File Format', 'Partially Processed', 'Processed', 'Uploaded'];\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ApiService\n    }];\n  }\n};\nlegalConfigService = __decorate([Injectable({\n  providedIn: 'root'\n})], legalConfigService);\nexport { legalConfigService };", "map": {"version": 3, "names": ["Injectable", "EventEmitter", "ApiService", "moment", "legalConfigService", "constructor", "apiService", "selectedResults", "filterArrayByState", "arr", "val", "cityList", "i", "length", "STATE", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "sort", "a", "b", "x", "y", "cityNameSort", "nameA", "CITY", "toUpperCase", "nameB", "comparison", "removeDuplicates", "myArr", "prop", "filter", "obj", "pos", "map", "mapObj", "indexOf", "convertToCSV", "obj<PERSON><PERSON>y", "JSON", "parse", "str", "row", "index", "slice", "line", "findObjectByKey", "value", "results", "find", "getByValue", "iLen", "id", "convertDate", "data", "dateFormate", "utc", "toString", "Date", "getDate", "getMonth", "getFullYear", "legalAllocationStatusList", "__decorate", "providedIn"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\legalconfig.service.ts"], "sourcesContent": ["import { Injectable,EventEmitter } from '@angular/core';\r\nimport { ApiService } from '../shared/services/api.service';\r\nimport * as moment from 'moment/moment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class legalConfigService {\r\n  selectedResults: EventEmitter<any>;\r\n constructor(private apiService: ApiService) {\r\n    this.selectedResults = new EventEmitter<any>();\r\n  }\r\n  // Internal FIlters\r\n  filterArrayByState(arr,val){\r\n    var cityList = []\r\n      for (var i = 0; i < arr.length ; i++) {\r\n          if (arr[i].STATE === val) {\r\n              cityList.push(arr[i]);\r\n          }\r\n      }\r\n    return cityList\r\n  }\r\n  generalFilterByKey(arr,val,key){\r\n      var cityList = []\r\n      for (var i = 0; i < arr.length ; i++) {\r\n          if (arr[i][key] === val) {\r\n              cityList.push(arr[i]);\r\n          }\r\n      }\r\n    return cityList\r\n  }\r\n  generalKeySort(array,key){\r\n     return array.sort(function(a, b) {\r\n        var x = a[key]; var y = b[key];\r\n        return ((x < y) ? -1 : ((x > y) ? 1 : 0));\r\n    });\r\n  }\r\n  cityNameSort(a, b) {\r\n    const nameA = a.CITY.toUpperCase();\r\n    const nameB = b.CITY.toUpperCase();\r\n\r\n    let comparison = 0;\r\n    if (nameA > nameB) {\r\n      comparison = 1;\r\n    } else if (nameA < nameB) {\r\n      comparison = -1;\r\n    }\r\n    return comparison;\r\n  }\r\n  removeDuplicates(myArr, prop) {\r\n    return myArr.filter((obj, pos, arr) => {\r\n      return arr.map(mapObj =>\r\n      mapObj[prop]).indexOf(obj[prop]) === pos;\r\n      });\r\n  }\r\n  convertToCSV(objArray) {\r\n        var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;\r\n        var str = '';\r\n        var row = \"\";\r\n\r\n        for (var index in objArray[0]) {\r\n            //Now convert each value to string and comma-separated\r\n            row += index + ',';\r\n        }\r\n        row = row.slice(0, -1);\r\n        //append Label row with line break\r\n        str += row + '\\r\\n';\r\n\r\n        for (var i = 0; i < array.length; i++) {\r\n            var line = '';\r\n            for (var index in array[i]) {\r\n                if (line != '') line += ','\r\n\r\n                line += array[i][index];\r\n            }\r\n            str += line + '\\r\\n';\r\n        }\r\n        return str;\r\n  }\r\n  findObjectByKey(arr,key,value){\r\n    var results = arr.find(obj=> obj[key]==value)\r\n    return results\r\n  }\r\n  getByValue(arr, value) {\r\n    for (var i=0, iLen=arr.length; i<iLen; i++) {\r\n      if (arr[i].id == value){\r\n         return arr[i]\r\n      };\r\n    }\r\n  }\r\n  convertDate(data){\r\n    if(data['date']){\r\n  \t\tvar dateFormate = moment.utc(data['date']['month'].toString() + '/' + data['date']['day'].toString() + '/' + data['date']['year'].toString());\r\n  \t\treturn dateFormate;\r\n    } else {\r\n      data = new Date(data)\r\n      let getDate = data.getDate()\r\n      let getMonth = data.getMonth()\r\n      let getFullYear = data.getFullYear()\r\n      var dateFormate = moment.utc(getDate.toString() + '/' + getMonth.toString() + '/' + getFullYear.toString());\r\n      return dateFormate;\r\n    }\r\n  }\r\n\r\n  legalAllocationStatusList(){\r\n    return ['Failed', 'Invalid File Format', 'Partially Processed', 'Processed', 'Uploaded']\r\n }\r\n\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,EAACC,YAAY,QAAQ,eAAe;AACvD,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAO,KAAKC,MAAM,MAAM,eAAe;AAKhC,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAE9BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAC3B,IAAI,CAACC,eAAe,GAAG,IAAIN,YAAY,EAAO;EAChD;EACA;EACAO,kBAAkBA,CAACC,GAAG,EAACC,GAAG;IACxB,IAAIC,QAAQ,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAGD,CAAC,EAAE,EAAE;MAClC,IAAIH,GAAG,CAACG,CAAC,CAAC,CAACE,KAAK,KAAKJ,GAAG,EAAE;QACtBC,QAAQ,CAACI,IAAI,CAACN,GAAG,CAACG,CAAC,CAAC,CAAC;MACzB;IACJ;IACF,OAAOD,QAAQ;EACjB;EACAK,kBAAkBA,CAACP,GAAG,EAACC,GAAG,EAACO,GAAG;IAC1B,IAAIN,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAGD,CAAC,EAAE,EAAE;MAClC,IAAIH,GAAG,CAACG,CAAC,CAAC,CAACK,GAAG,CAAC,KAAKP,GAAG,EAAE;QACrBC,QAAQ,CAACI,IAAI,CAACN,GAAG,CAACG,CAAC,CAAC,CAAC;MACzB;IACJ;IACF,OAAOD,QAAQ;EACjB;EACAO,cAAcA,CAACC,KAAK,EAACF,GAAG;IACrB,OAAOE,KAAK,CAACC,IAAI,CAAC,UAASC,CAAC,EAAEC,CAAC;MAC5B,IAAIC,CAAC,GAAGF,CAAC,CAACJ,GAAG,CAAC;MAAE,IAAIO,CAAC,GAAGF,CAAC,CAACL,GAAG,CAAC;MAC9B,OAASM,CAAC,GAAGC,CAAC,GAAI,CAAC,CAAC,GAAKD,CAAC,GAAGC,CAAC,GAAI,CAAC,GAAG,CAAE;IAC5C,CAAC,CAAC;EACJ;EACAC,YAAYA,CAACJ,CAAC,EAAEC,CAAC;IACf,MAAMI,KAAK,GAAGL,CAAC,CAACM,IAAI,CAACC,WAAW,EAAE;IAClC,MAAMC,KAAK,GAAGP,CAAC,CAACK,IAAI,CAACC,WAAW,EAAE;IAElC,IAAIE,UAAU,GAAG,CAAC;IAClB,IAAIJ,KAAK,GAAGG,KAAK,EAAE;MACjBC,UAAU,GAAG,CAAC;IAChB,CAAC,MAAM,IAAIJ,KAAK,GAAGG,KAAK,EAAE;MACxBC,UAAU,GAAG,CAAC,CAAC;IACjB;IACA,OAAOA,UAAU;EACnB;EACAC,gBAAgBA,CAACC,KAAK,EAAEC,IAAI;IAC1B,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,EAAE3B,GAAG,KAAI;MACpC,OAAOA,GAAG,CAAC4B,GAAG,CAACC,MAAM,IACrBA,MAAM,CAACL,IAAI,CAAC,CAAC,CAACM,OAAO,CAACJ,GAAG,CAACF,IAAI,CAAC,CAAC,KAAKG,GAAG;IACxC,CAAC,CAAC;EACN;EACAI,YAAYA,CAACC,QAAQ;IACf,IAAItB,KAAK,GAAG,OAAOsB,QAAQ,IAAI,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,GAAGA,QAAQ;IACzE,IAAIG,GAAG,GAAG,EAAE;IACZ,IAAIC,GAAG,GAAG,EAAE;IAEZ,KAAK,IAAIC,KAAK,IAAIL,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC3B;MACAI,GAAG,IAAIC,KAAK,GAAG,GAAG;IACtB;IACAD,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB;IACAH,GAAG,IAAIC,GAAG,GAAG,MAAM;IAEnB,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,KAAK,CAACN,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,IAAIoC,IAAI,GAAG,EAAE;MACb,KAAK,IAAIF,KAAK,IAAI3B,KAAK,CAACP,CAAC,CAAC,EAAE;QACxB,IAAIoC,IAAI,IAAI,EAAE,EAAEA,IAAI,IAAI,GAAG;QAE3BA,IAAI,IAAI7B,KAAK,CAACP,CAAC,CAAC,CAACkC,KAAK,CAAC;MAC3B;MACAF,GAAG,IAAII,IAAI,GAAG,MAAM;IACxB;IACA,OAAOJ,GAAG;EAChB;EACAK,eAAeA,CAACxC,GAAG,EAACQ,GAAG,EAACiC,KAAK;IAC3B,IAAIC,OAAO,GAAG1C,GAAG,CAAC2C,IAAI,CAACjB,GAAG,IAAGA,GAAG,CAAClB,GAAG,CAAC,IAAEiC,KAAK,CAAC;IAC7C,OAAOC,OAAO;EAChB;EACAE,UAAUA,CAAC5C,GAAG,EAAEyC,KAAK;IACnB,KAAK,IAAItC,CAAC,GAAC,CAAC,EAAE0C,IAAI,GAAC7C,GAAG,CAACI,MAAM,EAAED,CAAC,GAAC0C,IAAI,EAAE1C,CAAC,EAAE,EAAE;MAC1C,IAAIH,GAAG,CAACG,CAAC,CAAC,CAAC2C,EAAE,IAAIL,KAAK,EAAC;QACpB,OAAOzC,GAAG,CAACG,CAAC,CAAC;MAChB;MAAC;IACH;EACF;EACA4C,WAAWA,CAACC,IAAI;IACd,IAAGA,IAAI,CAAC,MAAM,CAAC,EAAC;MAChB,IAAIC,WAAW,GAAGvD,MAAM,CAACwD,GAAG,CAACF,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAACG,QAAQ,EAAE,GAAG,GAAG,GAAGH,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAACG,QAAQ,EAAE,GAAG,GAAG,GAAGH,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAACG,QAAQ,EAAE,CAAC;MAC7I,OAAOF,WAAW;IAClB,CAAC,MAAM;MACLD,IAAI,GAAG,IAAII,IAAI,CAACJ,IAAI,CAAC;MACrB,IAAIK,OAAO,GAAGL,IAAI,CAACK,OAAO,EAAE;MAC5B,IAAIC,QAAQ,GAAGN,IAAI,CAACM,QAAQ,EAAE;MAC9B,IAAIC,WAAW,GAAGP,IAAI,CAACO,WAAW,EAAE;MACpC,IAAIN,WAAW,GAAGvD,MAAM,CAACwD,GAAG,CAACG,OAAO,CAACF,QAAQ,EAAE,GAAG,GAAG,GAAGG,QAAQ,CAACH,QAAQ,EAAE,GAAG,GAAG,GAAGI,WAAW,CAACJ,QAAQ,EAAE,CAAC;MAC3G,OAAOF,WAAW;IACpB;EACF;EAEAO,yBAAyBA,CAAA;IACvB,OAAO,CAAC,QAAQ,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,EAAE,UAAU,CAAC;EAC3F;;;;;;;AAnGY7D,kBAAkB,GAAA8D,UAAA,EAH9BlE,UAAU,CAAC;EACVmE,UAAU,EAAE;CACb,CAAC,C,EACW/D,kBAAkB,CAsG9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}