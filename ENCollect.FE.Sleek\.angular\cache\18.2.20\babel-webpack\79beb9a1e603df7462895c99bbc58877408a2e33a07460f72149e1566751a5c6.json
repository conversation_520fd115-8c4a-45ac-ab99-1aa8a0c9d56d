{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-repo.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-repo.component.css?ngResource\";\nimport { Component } from '@angular/core';\nlet CreateRepoComponent = class CreateRepoComponent {\n  constructor() {\n    this.breadcrumbData = [{\n      label: \"Repossession Management\",\n      path: \"/encollect/repossession/create-repossession\"\n    }, {\n      label: \"Add Repossession\",\n      path: \"/encollect/repossession/create-repossession\"\n    }];\n  }\n  ngOnInit() {}\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nCreateRepoComponent = __decorate([Component({\n  selector: 'app-create-repo',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateRepoComponent);\nexport { CreateRepoComponent };", "map": {"version": 3, "names": ["Component", "CreateRepoComponent", "constructor", "breadcrumbData", "label", "path", "ngOnInit", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\repo\\create-repo\\create-repo.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-create-repo',\r\n  templateUrl: './create-repo.component.html',\r\n  styleUrls: ['./create-repo.component.css']\r\n})\r\nexport class CreateRepoComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Repossession Management\", path: \"/encollect/repossession/create-repossession\" },\r\n\t\t{ label: \"Add Repossession\", path: \"/encollect/repossession/create-repossession\" },\r\n\t  ];\r\n  constructor() { }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AAO1C,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAK9BC,YAAA;IAJO,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,yBAAyB;MAAEC,IAAI,EAAE;IAA6C,CAAE,EACzF;MAAED,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAA6C,CAAE,CAChF;EACc;EAEhBC,QAAQA,CAAA,GACR;;;;;AARWL,mBAAmB,GAAAM,UAAA,EAL/BP,SAAS,CAAC;EACTQ,QAAQ,EAAE,iBAAiB;EAC3BC,QAAA,EAAAC,oBAA2C;;CAE5C,CAAC,C,EACWT,mBAAmB,CAU/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}