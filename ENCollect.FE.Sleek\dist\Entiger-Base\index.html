<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <title>ENCollect</title>
    <base href="/web/"/>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="favicon.ico"/>
    <link rel="manifest" href="manifest.json"/>
    <meta name="theme-color" content="#1976d2"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.13.0/css/all.min.css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.13.0/css/v4-shims.min.css" rel="stylesheet"/>
    <link rel="manifest" href="manifest.webmanifest"/>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/>
    <style>/* cyrillic-ext */
@font-face {
  font-family: 'Onest';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/onest/v8/gNMKW3F-SZuj7xmS-HY6EQ.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Onest';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/onest/v8/gNMKW3F-SZuj7xmb-HY6EQ.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Onest';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/onest/v8/gNMKW3F-SZuj7xmR-HY6EQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Onest';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/onest/v8/gNMKW3F-SZuj7xmf-HY.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

    <!--<script type="text/javascript" language="javascript" src="https://emergeapp5.ameyoemerge.in:8443/ameyowebaccess/toolbar/js/ameyo-integration-v4_10.js"></script>
  <script type="text/javascript" language="javascript" src="./assets/js/ameyo-integration-custom1.js"></script>-->
  <link rel="stylesheet" href="styles.ccab1248a5f86f0b.css"></head>
  <body>
    <app-root></app-root>
    <app-redirect></app-redirect>
  <script src="runtime.bf47822b08fac645.js" type="module"></script><script src="polyfills.0b76944a17b607da.js" type="module"></script><script src="scripts.1ac6d0d02f230370.js" defer=""></script><script src="vendor.f8ef7f2d7710a6fc.js" type="module"></script><script src="main.51c5285b2c71e548.js" type="module"></script></body>
</html>
