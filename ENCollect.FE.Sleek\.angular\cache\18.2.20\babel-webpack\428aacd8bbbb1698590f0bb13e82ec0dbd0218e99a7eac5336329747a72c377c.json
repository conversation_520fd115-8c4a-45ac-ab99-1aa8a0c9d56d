{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./download-all-accounts.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./download-all-accounts.component.css?ngResource\";\nimport { Component } from \"@angular/core\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { repoService } from \"../../legal-custom.service\";\nlet DownloadAllAccountsComponent = class DownloadAllAccountsComponent {\n  constructor(legalService, toastr) {\n    this.legalService = legalService;\n    this.toastr = toastr;\n    this.batchFileList = [];\n    this.itemsPerPage = 5;\n    this.currentPage = 0;\n    this.totalCount = 0;\n    this.results = [];\n    this.currentRecords = [];\n    this.skip = 0;\n  }\n  ngOnInit() {\n    this.getBatchFileList();\n  }\n  generateBatchFile(data) {\n    this.loader = true;\n    this.legalService.generateBatchFile(data).subscribe(res => {\n      this.loader = false;\n      this.getBatchFileList();\n    }, err => {\n      this.loader = false;\n    });\n  }\n  getBatchFileList() {\n    this.loader = true;\n    let data = {\n      take: this.itemsPerPage,\n      skip: this.skip == 0 ? this.skip : this.skip - 1\n    };\n    this.legalService.getBatchFileList(data).subscribe(res => {\n      this.loader = false;\n      if (res && res.totalCount) {\n        this.batchFileList = res.result || [];\n      } else {\n        this.batchFileList = [];\n      }\n    }, err => {\n      this.loader = false;\n      this.batchFileList = [];\n    });\n  }\n  downloadBatchFile(fileName) {\n    this.loader = true;\n    this.legalService.downloadBatchFile(fileName).subscribe(res => {\n      if (res.length === 0) {\n        this.loader = false;\n        this.toastr.warning(\"No results found!\");\n      } else {\n        var imagetype = \"zip\";\n        var mediaType = \"application/zip\";\n        var a = document.createElement(\"a\");\n        a.setAttribute(\"style\", \"display:none;\");\n        document.body.appendChild(a);\n        var blob = new Blob([res], {\n          type: mediaType\n        });\n        var url = window.URL.createObjectURL(blob);\n        a.href = url;\n        a.download = \"trailgap.zip\";\n        a.click();\n        this.loader = false;\n      }\n    }, err => {\n      this.loader = false;\n    });\n  }\n  // Page Change\n  pageChange(e) {\n    this.currentPage = e.page;\n    this.itemsPerPage = e.itemsPerPage;\n    this.currentRecords = this.getBatchFileList();\n  }\n  // Change Items Per Page\n  changeItemsPerPage() {\n    this.currentPage = 1;\n    this.currentRecords = this.getBatchFileList();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: repoService\n    }, {\n      type: ToastrService\n    }];\n  }\n};\nDownloadAllAccountsComponent = __decorate([Component({\n  selector: \"app-download-all-accounts\",\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DownloadAllAccountsComponent);\nexport { DownloadAllAccountsComponent };", "map": {"version": 3, "names": ["Component", "ToastrService", "repoService", "DownloadAllAccountsComponent", "constructor", "legalService", "toastr", "batchFileList", "itemsPerPage", "currentPage", "totalCount", "results", "currentRecords", "skip", "ngOnInit", "getBatchFileList", "generateBatchFile", "data", "loader", "subscribe", "res", "err", "take", "result", "downloadBatchFile", "fileName", "length", "warning", "imagetype", "mediaType", "a", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "href", "download", "click", "pageChange", "e", "page", "changeItemsPerPage", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal-custom\\common\\download-all-accounts\\download-all-accounts.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { repoService } from \"../../legal-custom.service\";\r\nimport { PageChangedEvent } from \"ngx-bootstrap/pagination\";\r\n\r\n@Component({\r\n  selector: \"app-download-all-accounts\",\r\n  templateUrl: \"./download-all-accounts.component.html\",\r\n  styleUrls: [\"./download-all-accounts.component.css\"],\r\n})\r\nexport class DownloadAllAccountsComponent implements OnInit {\r\n  public loader: boolean;\r\n  public batchFileList: any[] = [];\r\n  public itemsPerPage: number = 5;\r\n  public currentPage: number = 0;\r\n  public totalCount: number = 0;\r\n  public results: any = [];\r\n  public currentRecords: any = [];\r\n  skip: any = 0;\r\n  constructor(\r\n    private legalService: repoService,\r\n    public toastr: ToastrService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.getBatchFileList();\r\n  }\r\n\r\n  generateBatchFile(data) {\r\n    this.loader = true;\r\n    this.legalService.generateBatchFile(data).subscribe(\r\n      (res: any) => {\r\n        this.loader = false;\r\n        this.getBatchFileList();\r\n      },\r\n      (err: any) => {\r\n        this.loader = false;\r\n      }\r\n    );\r\n  }\r\n\r\n  getBatchFileList() {\r\n    this.loader = true;\r\n    let data = {\r\n      take: this.itemsPerPage,\r\n      skip: this.skip == 0 ? this.skip : this.skip - 1,\r\n    };\r\n    this.legalService.getBatchFileList(data).subscribe(\r\n      (res: any) => {\r\n        this.loader = false;\r\n        if (res && res.totalCount) {\r\n          this.batchFileList = res.result || [];\r\n        } else {\r\n          this.batchFileList = [];\r\n        }\r\n      },\r\n      (err: any) => {\r\n        this.loader = false;\r\n        this.batchFileList = [];\r\n      }\r\n    );\r\n  }\r\n\r\n  downloadBatchFile(fileName) {\r\n    this.loader = true;\r\n    this.legalService.downloadBatchFile(fileName).subscribe(\r\n      (res: any) => {\r\n        if (res.length === 0) {\r\n          this.loader = false;\r\n          this.toastr.warning(\"No results found!\");\r\n        } else {\r\n          var imagetype = \"zip\";\r\n          var mediaType = \"application/zip\";\r\n          var a = document.createElement(\"a\");\r\n          a.setAttribute(\"style\", \"display:none;\");\r\n          document.body.appendChild(a);\r\n          var blob = new Blob([res], { type: mediaType });\r\n          var url = window.URL.createObjectURL(blob);\r\n          a.href = url;\r\n          a.download = \"trailgap.zip\";\r\n          a.click();\r\n          this.loader = false;\r\n        }\r\n      },\r\n      (err: any) => {\r\n        this.loader = false;\r\n      }\r\n    );\r\n  }\r\n\r\n  // Page Change\r\n  pageChange(e: PageChangedEvent) {\r\n    this.currentPage = e.page;\r\n    this.itemsPerPage = e.itemsPerPage;\r\n    this.currentRecords = this.getBatchFileList();\r\n  }\r\n\r\n  // Change Items Per Page\r\n  changeItemsPerPage(): void {\r\n    this.currentPage = 1;\r\n    this.currentRecords = this.getBatchFileList();\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,WAAW,QAAQ,4BAA4B;AAQjD,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA4B;EASvCC,YACUC,YAAyB,EAC1BC,MAAqB;IADpB,KAAAD,YAAY,GAAZA,YAAY;IACb,KAAAC,MAAM,GAANA,MAAM;IATR,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,OAAO,GAAQ,EAAE;IACjB,KAAAC,cAAc,GAAQ,EAAE;IAC/B,KAAAC,IAAI,GAAQ,CAAC;EAIT;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,iBAAiBA,CAACC,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACb,YAAY,CAACW,iBAAiB,CAACC,IAAI,CAAC,CAACE,SAAS,CAChDC,GAAQ,IAAI;MACX,IAAI,CAACF,MAAM,GAAG,KAAK;MACnB,IAAI,CAACH,gBAAgB,EAAE;IACzB,CAAC,EACAM,GAAQ,IAAI;MACX,IAAI,CAACH,MAAM,GAAG,KAAK;IACrB,CAAC,CACF;EACH;EAEAH,gBAAgBA,CAAA;IACd,IAAI,CAACG,MAAM,GAAG,IAAI;IAClB,IAAID,IAAI,GAAG;MACTK,IAAI,EAAE,IAAI,CAACd,YAAY;MACvBK,IAAI,EAAE,IAAI,CAACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG;KAChD;IACD,IAAI,CAACR,YAAY,CAACU,gBAAgB,CAACE,IAAI,CAAC,CAACE,SAAS,CAC/CC,GAAQ,IAAI;MACX,IAAI,CAACF,MAAM,GAAG,KAAK;MACnB,IAAIE,GAAG,IAAIA,GAAG,CAACV,UAAU,EAAE;QACzB,IAAI,CAACH,aAAa,GAAGa,GAAG,CAACG,MAAM,IAAI,EAAE;MACvC,CAAC,MAAM;QACL,IAAI,CAAChB,aAAa,GAAG,EAAE;MACzB;IACF,CAAC,EACAc,GAAQ,IAAI;MACX,IAAI,CAACH,MAAM,GAAG,KAAK;MACnB,IAAI,CAACX,aAAa,GAAG,EAAE;IACzB,CAAC,CACF;EACH;EAEAiB,iBAAiBA,CAACC,QAAQ;IACxB,IAAI,CAACP,MAAM,GAAG,IAAI;IAClB,IAAI,CAACb,YAAY,CAACmB,iBAAiB,CAACC,QAAQ,CAAC,CAACN,SAAS,CACpDC,GAAQ,IAAI;MACX,IAAIA,GAAG,CAACM,MAAM,KAAK,CAAC,EAAE;QACpB,IAAI,CAACR,MAAM,GAAG,KAAK;QACnB,IAAI,CAACZ,MAAM,CAACqB,OAAO,CAAC,mBAAmB,CAAC;MAC1C,CAAC,MAAM;QACL,IAAIC,SAAS,GAAG,KAAK;QACrB,IAAIC,SAAS,GAAG,iBAAiB;QACjC,IAAIC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACnCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACxCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,CAAC,CAAC;QAC5B,IAAIM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACjB,GAAG,CAAC,EAAE;UAAEkB,IAAI,EAAET;QAAS,CAAE,CAAC;QAC/C,IAAIU,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QAC1CN,CAAC,CAACa,IAAI,GAAGJ,GAAG;QACZT,CAAC,CAACc,QAAQ,GAAG,cAAc;QAC3Bd,CAAC,CAACe,KAAK,EAAE;QACT,IAAI,CAAC3B,MAAM,GAAG,KAAK;MACrB;IACF,CAAC,EACAG,GAAQ,IAAI;MACX,IAAI,CAACH,MAAM,GAAG,KAAK;IACrB,CAAC,CACF;EACH;EAEA;EACA4B,UAAUA,CAACC,CAAmB;IAC5B,IAAI,CAACtC,WAAW,GAAGsC,CAAC,CAACC,IAAI;IACzB,IAAI,CAACxC,YAAY,GAAGuC,CAAC,CAACvC,YAAY;IAClC,IAAI,CAACI,cAAc,GAAG,IAAI,CAACG,gBAAgB,EAAE;EAC/C;EAEA;EACAkC,kBAAkBA,CAAA;IAChB,IAAI,CAACxC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACG,cAAc,GAAG,IAAI,CAACG,gBAAgB,EAAE;EAC/C;;;;;;;;;AA3FWZ,4BAA4B,GAAA+C,UAAA,EALxClD,SAAS,CAAC;EACTmD,QAAQ,EAAE,2BAA2B;EACrCC,QAAA,EAAAC,oBAAqD;;CAEtD,CAAC,C,EACWlD,4BAA4B,CA4FxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}