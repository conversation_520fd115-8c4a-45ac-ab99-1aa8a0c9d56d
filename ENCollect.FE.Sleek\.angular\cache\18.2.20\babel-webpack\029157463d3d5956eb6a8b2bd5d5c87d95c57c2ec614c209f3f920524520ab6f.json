{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nimport { ApiService } from \"../shared/services/api.service\";\nlet TargetService = class TargetService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  uploadFile(formData) {\n    const options = {};\n    return this.apiService.post(\"api/UploadFile\", formData, options);\n  }\n  budgetTargetUpload(data) {\n    return this.apiService.post(\"api/mvp/targets/upload/budget/batch\", data);\n  }\n  gettargetFileUploadStatus(data) {\n    return this.apiService.post(\"api/mvp/targets/upload/budget/batch/status\", data);\n  }\n  downloadFile(data) {\n    return this.apiService.getRawImage(\"api/mvp/getimage/\" + data);\n  }\n  getbudgetedTargets(data) {\n    return this.apiService.post(\"api/mvp/targets/budget/search\", data);\n  }\n  deleteBudgetedTarget(data) {\n    return this.apiService.post(\"api/mvp/budget/target/delete/\" + data);\n  }\n  getAgencyList() {\n    return this.apiService.get(\"api/mvp/agencylist\");\n  }\n  getBucketList() {\n    return this.apiService.get(\"api/mvp/bucketmaster\");\n  }\n  fetchAgencyTypes() {\n    return this.apiService.get(\"api/mvp/agencytypes\");\n  }\n  targetEmail(data) {\n    return this.apiService.post(\"api/mvp/agency/target/email/\" + data);\n  }\n  getZones() {\n    return this.apiService.get(\"api/mvp/zones\");\n  }\n  getregions(data) {\n    return this.apiService.get(\"api/mvp/regions/\" + data);\n  }\n  getProductGroupList() {\n    return this.apiService.get(\"api/mvp/get/primaryCategoryItems?categoryMasterId=ProductGroup\");\n  }\n  getProductListByPG(data) {\n    return this.apiService.get(\"api/mvp/get/secondaryCategoryItemByParentId?parentId=\" + data);\n  }\n  getSubproductListByProduct(data) {\n    return this.apiService.get(\"api/mvp/get/secondaryCategoryItemByParentId?parentId=\" + data);\n  }\n  searchTargets(data) {\n    return this.apiService.post(\"api/mvp/agency/targets/list/search\", data);\n  }\n  targetPrint(data) {\n    return this.apiService.get(\"api/mvp/agency/target/print/\" + data);\n  }\n  viewTarget(data) {\n    return this.apiService.get(\"api/mvp/agency/target/edit/\" + data);\n  }\n  targetUpdate(data) {\n    return this.apiService.post(\"api/mvp/agency/target/update\", data);\n  }\n  searchTargetInfo(data) {\n    return this.apiService.post(\"api/mvp/budget/targets/list/search\", data);\n  }\n  createTarget(data) {\n    return this.apiService.post(\"api/mvp/agency/target/create\", data);\n  }\n  updateTarget(data) {\n    return this.apiService.post(\"api/mvp/agency/target/update\", data);\n  }\n  removeDuplicates(myArr, prop) {\n    return myArr.filter((obj, pos, arr) => {\n      return arr.map(mapObj => mapObj[prop]).indexOf(obj[prop]) === pos;\n    });\n  }\n  generalFilterByKey(arr, val, key) {\n    var cityList = [];\n    for (var i = 0; i < arr.length; i++) {\n      if (arr[i][key] === val) {\n        cityList.push(arr[i]);\n      }\n    }\n    return cityList;\n  }\n  generalKeySort(array, key) {\n    return array.sort(function (a, b) {\n      var x = a[key];\n      var y = b[key];\n      return x < y ? -1 : x > y ? 1 : 0;\n    });\n  }\n  regionAllocation(data) {\n    return this.apiService.post(\"api/mvp/Master/Region\", data);\n  }\n  stateAllocation(data) {\n    return this.apiService.post(\"api/mvp/Master/State\", data);\n  }\n  cityAllocation(data) {\n    return this.apiService.post(\"api/mvp/Master/City\", data);\n  }\n  getMasterCountry() {\n    return this.apiService.get(\"api/mvp/Master/Country\");\n  }\n  downloadAllocatedAcc() {\n    return this.apiService.get(\"api/mvp/accounts/allocations/target/download\");\n  }\n  convertToCSV(objArray) {\n    var array = typeof objArray != \"object\" ? JSON.parse(objArray) : objArray;\n    var str = \"\";\n    var row = \"\";\n    for (var index in objArray[0]) {\n      //Now convert each value to string and comma-separated\n      row += index + \",\";\n    }\n    row = row.slice(0, -1);\n    //append Label row with line break\n    str += row + \"\\r\\n\";\n    for (var i = 0; i < array.length; i++) {\n      var line = \"\";\n      for (var index in array[i]) {\n        if (line != \"\") line += \",\";\n        line += array[i][index];\n      }\n      str += line + \"\\r\\n\";\n    }\n    return str;\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ApiService\n    }];\n  }\n};\nTargetService = __decorate([Injectable({\n  providedIn: \"root\"\n})], TargetService);\nexport { TargetService };", "map": {"version": 3, "names": ["Injectable", "ApiService", "TargetService", "constructor", "apiService", "uploadFile", "formData", "options", "post", "budgetTargetUpload", "data", "gettargetFileUploadStatus", "downloadFile", "getRawImage", "getbudgetedTargets", "deleteBudgetedTarget", "getAgencyList", "get", "getBucketList", "fetchAgencyTypes", "targetEmail", "getZones", "getregions", "getProductGroupList", "getProductListByPG", "getSubproductListByProduct", "searchTargets", "targetPrint", "viewTarget", "targetUpdate", "searchTargetInfo", "createTarget", "updateTarget", "removeDuplicates", "myArr", "prop", "filter", "obj", "pos", "arr", "map", "mapObj", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "val", "key", "cityList", "i", "length", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "sort", "a", "b", "x", "y", "regionAllocation", "stateAllocation", "cityAllocation", "getMasterCountry", "downloadAllocatedAcc", "convertToCSV", "obj<PERSON><PERSON>y", "JSON", "parse", "str", "row", "index", "slice", "line", "__decorate", "providedIn"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\target\\target.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ApiService } from \"../shared/services/api.service\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class TargetService {\r\n  constructor(private apiService: ApiService) {}\r\n\r\n  uploadFile(formData: FormData) {\r\n    const options = {};\r\n    return this.apiService.post(\"api/UploadFile\", formData, options);\r\n  }\r\n\r\n  budgetTargetUpload(data) {\r\n    return this.apiService.post(\"api/mvp/targets/upload/budget/batch\", data);\r\n  }\r\n\r\n  gettargetFileUploadStatus(data) {\r\n    return this.apiService.post(\r\n      \"api/mvp/targets/upload/budget/batch/status\",\r\n      data\r\n    );\r\n  }\r\n\r\n  downloadFile(data) {\r\n    return this.apiService.getRawImage(\"api/mvp/getimage/\" + data);\r\n  }\r\n\r\n  getbudgetedTargets(data) {\r\n    return this.apiService.post(\"api/mvp/targets/budget/search\", data);\r\n  }\r\n\r\n  deleteBudgetedTarget(data) {\r\n    return this.apiService.post(\"api/mvp/budget/target/delete/\" + data);\r\n  }\r\n\r\n  getAgencyList() {\r\n    return this.apiService.get(\"api/mvp/agencylist\");\r\n  }\r\n\r\n  getBucketList() {\r\n    return this.apiService.get(\"api/mvp/bucketmaster\");\r\n  }\r\n\r\n  fetchAgencyTypes() {\r\n    return this.apiService.get(\"api/mvp/agencytypes\");\r\n  }\r\n\r\n  targetEmail(data) {\r\n    return this.apiService.post(\"api/mvp/agency/target/email/\" + data);\r\n  }\r\n\r\n  getZones() {\r\n    return this.apiService.get(\"api/mvp/zones\");\r\n  }\r\n\r\n  getregions(data) {\r\n    return this.apiService.get(\"api/mvp/regions/\" + data);\r\n  }\r\n\r\n  getProductGroupList() {\r\n    return this.apiService.get(\r\n      \"api/mvp/get/primaryCategoryItems?categoryMasterId=ProductGroup\"\r\n    );\r\n  }\r\n\r\n  getProductListByPG(data) {\r\n    return this.apiService.get(\r\n      \"api/mvp/get/secondaryCategoryItemByParentId?parentId=\" + data\r\n    );\r\n  }\r\n\r\n  getSubproductListByProduct(data) {\r\n    return this.apiService.get(\r\n      \"api/mvp/get/secondaryCategoryItemByParentId?parentId=\" + data\r\n    );\r\n  }\r\n\r\n  searchTargets(data) {\r\n    return this.apiService.post(\"api/mvp/agency/targets/list/search\", data);\r\n  }\r\n\r\n  targetPrint(data) {\r\n    return this.apiService.get(\"api/mvp/agency/target/print/\" + data);\r\n  }\r\n\r\n  viewTarget(data) {\r\n    return this.apiService.get(\"api/mvp/agency/target/edit/\" + data);\r\n  }\r\n\r\n  targetUpdate(data) {\r\n    return this.apiService.post(\"api/mvp/agency/target/update\", data);\r\n  }\r\n\r\n  searchTargetInfo(data) {\r\n    return this.apiService.post(\"api/mvp/budget/targets/list/search\", data);\r\n  }\r\n\r\n  createTarget(data) {\r\n    return this.apiService.post(\"api/mvp/agency/target/create\", data);\r\n  }\r\n\r\n  updateTarget(data) {\r\n    return this.apiService.post(\"api/mvp/agency/target/update\", data);\r\n  }\r\n\r\n  removeDuplicates(myArr, prop) {\r\n    return myArr.filter((obj, pos, arr) => {\r\n      return arr.map((mapObj) => mapObj[prop]).indexOf(obj[prop]) === pos;\r\n    });\r\n  }\r\n\r\n  generalFilterByKey(arr, val, key) {\r\n    var cityList = [];\r\n    for (var i = 0; i < arr.length; i++) {\r\n      if (arr[i][key] === val) {\r\n        cityList.push(arr[i]);\r\n      }\r\n    }\r\n    return cityList;\r\n  }\r\n\r\n  generalKeySort(array, key) {\r\n    return array.sort(function (a, b) {\r\n      var x = a[key];\r\n      var y = b[key];\r\n      return x < y ? -1 : x > y ? 1 : 0;\r\n    });\r\n  }\r\n\r\n  regionAllocation(data) {\r\n    return this.apiService.post(\"api/mvp/Master/Region\", data);\r\n  }\r\n\r\n  stateAllocation(data) {\r\n    return this.apiService.post(\"api/mvp/Master/State\", data);\r\n  }\r\n\r\n  cityAllocation(data) {\r\n    return this.apiService.post(\"api/mvp/Master/City\", data);\r\n  }\r\n\r\n  getMasterCountry() {\r\n    return this.apiService.get(\"api/mvp/Master/Country\");\r\n  }\r\n\r\n  downloadAllocatedAcc() {\r\n    return this.apiService.get(\"api/mvp/accounts/allocations/target/download\");\r\n  }\r\n\r\n  convertToCSV(objArray) {\r\n    var array = typeof objArray != \"object\" ? JSON.parse(objArray) : objArray;\r\n    var str = \"\";\r\n    var row = \"\";\r\n\r\n    for (var index in objArray[0]) {\r\n      //Now convert each value to string and comma-separated\r\n      row += index + \",\";\r\n    }\r\n    row = row.slice(0, -1);\r\n    //append Label row with line break\r\n    str += row + \"\\r\\n\";\r\n\r\n    for (var i = 0; i < array.length; i++) {\r\n      var line = \"\";\r\n      for (var index in array[i]) {\r\n        if (line != \"\") line += \",\";\r\n\r\n        line += array[i][index];\r\n      }\r\n      str += line + \"\\r\\n\";\r\n    }\r\n    return str;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,gCAAgC;AAKpD,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EACxBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAe;EAE7CC,UAAUA,CAACC,QAAkB;IAC3B,MAAMC,OAAO,GAAG,EAAE;IAClB,OAAO,IAAI,CAACH,UAAU,CAACI,IAAI,CAAC,gBAAgB,EAAEF,QAAQ,EAAEC,OAAO,CAAC;EAClE;EAEAE,kBAAkBA,CAACC,IAAI;IACrB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,qCAAqC,EAAEE,IAAI,CAAC;EAC1E;EAEAC,yBAAyBA,CAACD,IAAI;IAC5B,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CACzB,4CAA4C,EAC5CE,IAAI,CACL;EACH;EAEAE,YAAYA,CAACF,IAAI;IACf,OAAO,IAAI,CAACN,UAAU,CAACS,WAAW,CAAC,mBAAmB,GAAGH,IAAI,CAAC;EAChE;EAEAI,kBAAkBA,CAACJ,IAAI;IACrB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,+BAA+B,EAAEE,IAAI,CAAC;EACpE;EAEAK,oBAAoBA,CAACL,IAAI;IACvB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,+BAA+B,GAAGE,IAAI,CAAC;EACrE;EAEAM,aAAaA,CAAA;IACX,OAAO,IAAI,CAACZ,UAAU,CAACa,GAAG,CAAC,oBAAoB,CAAC;EAClD;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACd,UAAU,CAACa,GAAG,CAAC,sBAAsB,CAAC;EACpD;EAEAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACf,UAAU,CAACa,GAAG,CAAC,qBAAqB,CAAC;EACnD;EAEAG,WAAWA,CAACV,IAAI;IACd,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,8BAA8B,GAAGE,IAAI,CAAC;EACpE;EAEAW,QAAQA,CAAA;IACN,OAAO,IAAI,CAACjB,UAAU,CAACa,GAAG,CAAC,eAAe,CAAC;EAC7C;EAEAK,UAAUA,CAACZ,IAAI;IACb,OAAO,IAAI,CAACN,UAAU,CAACa,GAAG,CAAC,kBAAkB,GAAGP,IAAI,CAAC;EACvD;EAEAa,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACnB,UAAU,CAACa,GAAG,CACxB,gEAAgE,CACjE;EACH;EAEAO,kBAAkBA,CAACd,IAAI;IACrB,OAAO,IAAI,CAACN,UAAU,CAACa,GAAG,CACxB,uDAAuD,GAAGP,IAAI,CAC/D;EACH;EAEAe,0BAA0BA,CAACf,IAAI;IAC7B,OAAO,IAAI,CAACN,UAAU,CAACa,GAAG,CACxB,uDAAuD,GAAGP,IAAI,CAC/D;EACH;EAEAgB,aAAaA,CAAChB,IAAI;IAChB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,oCAAoC,EAAEE,IAAI,CAAC;EACzE;EAEAiB,WAAWA,CAACjB,IAAI;IACd,OAAO,IAAI,CAACN,UAAU,CAACa,GAAG,CAAC,8BAA8B,GAAGP,IAAI,CAAC;EACnE;EAEAkB,UAAUA,CAAClB,IAAI;IACb,OAAO,IAAI,CAACN,UAAU,CAACa,GAAG,CAAC,6BAA6B,GAAGP,IAAI,CAAC;EAClE;EAEAmB,YAAYA,CAACnB,IAAI;IACf,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,8BAA8B,EAAEE,IAAI,CAAC;EACnE;EAEAoB,gBAAgBA,CAACpB,IAAI;IACnB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,oCAAoC,EAAEE,IAAI,CAAC;EACzE;EAEAqB,YAAYA,CAACrB,IAAI;IACf,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,8BAA8B,EAAEE,IAAI,CAAC;EACnE;EAEAsB,YAAYA,CAACtB,IAAI;IACf,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,8BAA8B,EAAEE,IAAI,CAAC;EACnE;EAEAuB,gBAAgBA,CAACC,KAAK,EAAEC,IAAI;IAC1B,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,KAAI;MACpC,OAAOA,GAAG,CAACC,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACN,IAAI,CAAC,CAAC,CAACO,OAAO,CAACL,GAAG,CAACF,IAAI,CAAC,CAAC,KAAKG,GAAG;IACrE,CAAC,CAAC;EACJ;EAEAK,kBAAkBA,CAACJ,GAAG,EAAEK,GAAG,EAAEC,GAAG;IAC9B,IAAIC,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,IAAIR,GAAG,CAACQ,CAAC,CAAC,CAACF,GAAG,CAAC,KAAKD,GAAG,EAAE;QACvBE,QAAQ,CAACG,IAAI,CAACV,GAAG,CAACQ,CAAC,CAAC,CAAC;MACvB;IACF;IACA,OAAOD,QAAQ;EACjB;EAEAI,cAAcA,CAACC,KAAK,EAAEN,GAAG;IACvB,OAAOM,KAAK,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC;MAC9B,IAAIC,CAAC,GAAGF,CAAC,CAACR,GAAG,CAAC;MACd,IAAIW,CAAC,GAAGF,CAAC,CAACT,GAAG,CAAC;MACd,OAAOU,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAC/C,IAAI;IACnB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,uBAAuB,EAAEE,IAAI,CAAC;EAC5D;EAEAgD,eAAeA,CAAChD,IAAI;IAClB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,sBAAsB,EAAEE,IAAI,CAAC;EAC3D;EAEAiD,cAAcA,CAACjD,IAAI;IACjB,OAAO,IAAI,CAACN,UAAU,CAACI,IAAI,CAAC,qBAAqB,EAAEE,IAAI,CAAC;EAC1D;EAEAkD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACxD,UAAU,CAACa,GAAG,CAAC,wBAAwB,CAAC;EACtD;EAEA4C,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACzD,UAAU,CAACa,GAAG,CAAC,8CAA8C,CAAC;EAC5E;EAEA6C,YAAYA,CAACC,QAAQ;IACnB,IAAIZ,KAAK,GAAG,OAAOY,QAAQ,IAAI,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,GAAGA,QAAQ;IACzE,IAAIG,GAAG,GAAG,EAAE;IACZ,IAAIC,GAAG,GAAG,EAAE;IAEZ,KAAK,IAAIC,KAAK,IAAIL,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC7B;MACAI,GAAG,IAAIC,KAAK,GAAG,GAAG;IACpB;IACAD,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB;IACAH,GAAG,IAAIC,GAAG,GAAG,MAAM;IAEnB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIuB,IAAI,GAAG,EAAE;MACb,KAAK,IAAIF,KAAK,IAAIjB,KAAK,CAACJ,CAAC,CAAC,EAAE;QAC1B,IAAIuB,IAAI,IAAI,EAAE,EAAEA,IAAI,IAAI,GAAG;QAE3BA,IAAI,IAAInB,KAAK,CAACJ,CAAC,CAAC,CAACqB,KAAK,CAAC;MACzB;MACAF,GAAG,IAAII,IAAI,GAAG,MAAM;IACtB;IACA,OAAOJ,GAAG;EACZ;;;;;;;AAxKWhE,aAAa,GAAAqE,UAAA,EAHzBvE,UAAU,CAAC;EACVwE,UAAU,EAAE;CACb,CAAC,C,EACWtE,aAAa,CAyKzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}