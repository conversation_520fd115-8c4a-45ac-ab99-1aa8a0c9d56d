{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { CreditCardDetailsComponent } from './credit-card-details.component';\ndescribe('CreditCardDetailsComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      declarations: [CreditCardDetailsComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(CreditCardDetailsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "CreditCardDetailsComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\dashboard\\v1\\credit-card-details\\credit-card-details.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { CreditCardDetailsComponent } from './credit-card-details.component';\r\n\r\ndescribe('CreditCardDetailsComponent', () => {\r\n  let component: CreditCardDetailsComponent;\r\n  let fixture: ComponentFixture<CreditCardDetailsComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      declarations: [ CreditCardDetailsComponent ]\r\n    })\r\n    .compileComponents();\r\n  });\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(CreditCardDetailsComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,0BAA0B,QAAQ,iCAAiC;AAE5EC,QAAQ,CAAC,4BAA4B,EAAE,MAAK;EAC1C,IAAIC,SAAqC;EACzC,IAAIC,OAAqD;EAEzDC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,YAAY,EAAE,CAAEP,0BAA0B;KAC3C,CAAC,CACDQ,iBAAiB,EAAE;EACtB,CAAC,EAAC;EAEFJ,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,0BAA0B,CAAC;IAC7DE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}