{"ast": null, "code": "import { inject } from \"@angular/core\";\nimport { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from \"@angular/forms\";\nimport { ActivatedRoute, Router } from \"@angular/router\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { SharedModule } from \"src/app/shared\";\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\nimport { TemplatePreviewDirective } from \"src/app/shared/directives/template-preview.directive\";\nimport { TemplateService } from \"../template.service\";\nimport { Editor, NgxEditorModule } from 'ngx-editor';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ngx-bootstrap/buttons\";\nimport * as i4 from \"@ng-select/ng-select\";\nimport * as i5 from \"ngx-bootstrap/tooltip\";\nimport * as i6 from \"angular-svg-icon\";\nimport * as i7 from \"../../shared/components/breadcrumb/breadcrumb.component\";\nimport * as i8 from \"../../shared/directives/restrict-html-tags.directive\";\nimport * as i9 from \"ngx-editor\";\nconst _c0 = [\"templateVarConfig\"];\nconst _forTrack0 = ($index, $item) => $item.language;\nconst _forTrack1 = ($index, $item) => $item.languageCode;\nconst _c1 = () => ({\n  standalone: true\n});\nconst _c2 = () => [\"/communication/search-communication-templates\"];\nfunction CreateTemplateComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" View Communication Template \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Edit Communication Template \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Create Communication Template \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵelement(1, \"svg-icon\", 10);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateTemplateComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 11)(2, \"div\", 12)(3, \"span\", 13);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p\", 14);\n    i0.ɵɵtext(6, \"Loading template data...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldErrorMessage(\"templateName\"), \" \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"select\", 28)(1, \"option\", 23);\n    i0.ɵɵtext(2, \"Select Entry Point\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"option\", 37);\n    i0.ɵɵtext(4, \"Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 37);\n    i0.ɵɵtext(6, \"User\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"selected\", ctx_r0.currentTemplateData.entryPoint === ctx_r0.Account);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"selected\", ctx_r0.currentTemplateData.entryPoint === ctx_r0.User);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_28_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldErrorMessage(\"entryPoint\"));\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"select\", 38)(1, \"option\", 23);\n    i0.ɵɵtext(2, \"Select Entry Point\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"option\", 39);\n    i0.ɵɵtext(4, \"Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 40);\n    i0.ɵɵtext(6, \"User\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CreateTemplateComponent_Conditional_9_Conditional_28_Conditional_7_Template, 2, 1, \"div\", 41);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isViewMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵconditional(ctx_r0.isFieldInvalid(\"entryPoint\") ? 7 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"select\", 29)(1, \"option\", 23);\n    i0.ɵɵtext(2, \"Select Recipient Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"option\", 37);\n    i0.ɵɵtext(4, \"Customer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 37);\n    i0.ɵɵtext(6, \"Agent\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"selected\", ctx_r0.currentTemplateData.recipientType === ctx_r0.Customer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"selected\", ctx_r0.currentTemplateData.recipientType === ctx_r0.Agent);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_35_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldErrorMessage(\"recipientType\"));\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"select\", 42)(1, \"option\", 23);\n    i0.ɵɵtext(2, \"Select Recipient Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"option\", 43);\n    i0.ɵɵtext(4, \"Customer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 44);\n    i0.ɵɵtext(6, \"Agent\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CreateTemplateComponent_Conditional_9_Conditional_35_Conditional_7_Template, 2, 1, \"div\", 41);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isViewMode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵconditional(ctx_r0.isFieldInvalid(\"recipientType\") ? 7 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_36_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\");\n    i0.ɵɵelement(2, \"input\", 47);\n    i0.ɵɵtext(3, \" Yes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵelement(5, \"input\", 48);\n    i0.ɵɵtext(6, \" No \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r0.currentTemplateData.isAvailableInAccountDetails === true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r0.currentTemplateData.isAvailableInAccountDetails === false);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_36_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"label\");\n    i0.ɵɵelement(2, \"input\", 50);\n    i0.ɵɵtext(3, \" Yes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵelement(5, \"input\", 51);\n    i0.ɵɵtext(6, \" No \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r0.isViewMode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isViewMode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isViewMode);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 18)(2, \"label\", 19);\n    i0.ɵɵtext(3, \"Allow Access from Account Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CreateTemplateComponent_Conditional_9_Conditional_36_Conditional_4_Template, 7, 2, \"div\", 45)(5, CreateTemplateComponent_Conditional_9_Conditional_36_Conditional_5_Template, 7, 4, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵconditional(ctx_r0.isViewMode && ctx_r0.currentTemplateData ? 4 : 5);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_42_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_Conditional_9_Conditional_42_For_2_Template_button_click_1_listener() {\n      const ɵ$index_194_r3 = i0.ɵɵrestoreView(_r2).$index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectViewLanguage(ɵ$index_194_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r4 = ctx.$implicit;\n    const ɵ$index_194_r3 = ctx.$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r0.selectedViewLanguageIndex === ɵ$index_194_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", detail_r4.language, \" \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵrepeaterCreate(1, CreateTemplateComponent_Conditional_9_Conditional_42_For_2_Template, 3, 3, \"div\", 52, _forTrack0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.currentTemplateData.communicationTemplateDetails);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_43_For_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_Conditional_9_Conditional_43_For_2_Conditional_3_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ɵ$index_203_r6 = i0.ɵɵnextContext().$index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      ctx_r0.removeLanguage(ɵ$index_203_r6);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(1, \" \\u00D7 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lang_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate1(\"title\", \"Remove \", lang_r7 == null ? null : lang_r7.languageName, \"\");\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_43_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"button\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, CreateTemplateComponent_Conditional_9_Conditional_43_For_2_Conditional_3_Template, 2, 2, \"span\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const lang_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"template-language-\", lang_r7 == null ? null : lang_r7.languageCode, \"\");\n    i0.ɵɵproperty(\"btnRadio\", lang_r7 == null ? null : lang_r7.languageCode);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", lang_r7 == null ? null : lang_r7.languageName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((ctx_r0.fValue == null ? null : ctx_r0.fValue.languages == null ? null : ctx_r0.fValue.languages.length) > 1 && !ctx_r0.isViewMode ? 3 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵrepeaterCreate(1, CreateTemplateComponent_Conditional_9_Conditional_43_For_2_Template, 4, 5, \"div\", 52, _forTrack1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.fValue == null ? null : ctx_r0.fValue.languages);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_Conditional_9_Conditional_44_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      const addLangModal_r9 = i0.ɵɵreference(13);\n      return i0.ɵɵresetView(ctx_r0.openAddLangModal(addLangModal_r9));\n    });\n    i0.ɵɵelement(1, \"svg-icon\", 58);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Add Language\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 18)(2, \"label\", 60);\n    i0.ɵɵtext(3, \"Upload Header\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 61);\n    i0.ɵɵlistener(\"change\", function CreateTemplateComponent_Conditional_9_Conditional_45_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onHeaderUpload($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 59)(6, \"div\", 18)(7, \"label\", 60);\n    i0.ɵɵtext(8, \"Upload Footer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 61);\n    i0.ɵɵlistener(\"change\", function CreateTemplateComponent_Conditional_9_Conditional_45_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onFooterUpload($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_46_Conditional_0_Template(rf, ctx) {}\nfunction CreateTemplateComponent_Conditional_9_Conditional_46_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 18)(2, \"label\", 19);\n    i0.ɵɵtext(3, \"Subject Line\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ((tmp_5_0 = ctx_r0.getSelectedLanguageDetail()) == null ? null : tmp_5_0.subject) || \"\");\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CreateTemplateComponent_Conditional_9_Conditional_46_Conditional_0_Template, 0, 0)(1, CreateTemplateComponent_Conditional_9_Conditional_46_Conditional_1_Template, 5, 1, \"div\", 30);\n    i0.ɵɵelementStart(2, \"div\", 30)(3, \"div\", 18)(4, \"label\", 19);\n    i0.ɵɵtext(5, \"Template Body\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"textarea\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 30)(8, \"div\", 18)(9, \"label\", 60);\n    i0.ɵɵtext(10, \"Template Preview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    let tmp_7_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(ctx_r0.currentTemplateData.communicationTemplateDetails.length > 1 ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"email\" ? 1 : -1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ((tmp_6_0 = ctx_r0.getSelectedLanguageDetail()) == null ? null : tmp_6_0.body) || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"template\", ((tmp_7_0 = ctx_r0.getSelectedLanguageDetail()) == null ? null : tmp_7_0.body) || \"\")(\"variables\", ctx_r0.variables);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_0_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(3).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getLanguageFieldErrorMessage(ɵ$index_271_r12, \"emailSubject\"), \" \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 18)(2, \"label\", 19);\n    i0.ɵɵtext(3, \"Subject Line\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 72);\n    i0.ɵɵtemplate(5, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_0_Conditional_5_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(2).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r0.isLanguageFieldInvalid(ɵ$index_271_r12, \"emailSubject\"));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isLanguageFieldInvalid(ɵ$index_271_r12, \"emailSubject\") ? 5 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ngx-editor-menu\", 74);\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(3).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"editor\", ctx_r0.editors[ɵ$index_271_r12] || ctx_r0.defaultEditor)(\"toolbar\", ctx_r0.toolbar);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-editor\", 80);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_2_Template_ngx_editor_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ɵ$index_271_r12 = i0.ɵɵnextContext(3).$index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.htmlContents[ɵ$index_271_r12], $event) || (ctx_r0.htmlContents[ɵ$index_271_r12] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_2_Template_ngx_editor_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ɵ$index_271_r12 = i0.ɵɵnextContext(3).$index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onEditorContentChange(ɵ$index_271_r12));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(3).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"editor\", ctx_r0.editors[ɵ$index_271_r12]);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.htmlContents[ɵ$index_271_r12]);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(5, _c1))(\"placeholder\", \"Enter Email Template Content\")(\"disabled\", false);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"p\");\n    i0.ɵɵtext(2, \"Initializing editor...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(3).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getLanguageFieldErrorMessage(ɵ$index_271_r12, \"emailBody\"), \" \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_1_Template, 1, 2, \"ngx-editor-menu\", 74)(2, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_2_Template, 1, 6, \"ngx-editor\", 75)(3, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_3_Template, 3, 0, \"div\", 76);\n    i0.ɵɵelementStart(4, \"div\", 77)(5, \"small\", 78);\n    i0.ɵɵelement(6, \"i\", 79);\n    i0.ɵɵtext(7, \" Email templates support rich text formatting. Use the toolbar for styling. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Conditional_8_Template, 2, 1, \"div\", 21);\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(2).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.editors[ɵ$index_271_r12] || ctx_r0.defaultEditor ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.editors[ɵ$index_271_r12] ? 2 : 3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵconditional(ctx_r0.isLanguageFieldInvalid(ɵ$index_271_r12, \"emailBody\") ? 8 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_12_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(3).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getLanguageFieldErrorMessage(ɵ$index_271_r12, \"templateBody\"), \" \");\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_12_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵelement(1, \"i\", 83);\n    i0.ɵɵelementStart(2, \"i\");\n    i0.ɵɵtext(3, \"SMS templates: Copy, paste and delete operations only allowed.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"textarea\", 81);\n    i0.ɵɵtemplate(1, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_12_Conditional_1_Template, 2, 1, \"div\", 21)(2, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_12_Conditional_2_Template, 4, 0, \"div\", 82);\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(2).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r0.isLanguageFieldInvalid(ɵ$index_271_r12, \"templateBody\"));\n    i0.ɵɵproperty(\"allowCopyPasteOnly\", (ctx_r0.fValue == null ? null : ctx_r0.fValue.channelType) === \"sms\" || ctx_r0.isEditMode && (ctx_r0.currentTemplateData == null ? null : ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"sms\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isLanguageFieldInvalid(ɵ$index_271_r12, \"templateBody\") ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((ctx_r0.fValue == null ? null : ctx_r0.fValue.channelType) === \"sms\" || ctx_r0.isEditMode && (ctx_r0.currentTemplateData == null ? null : ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"sms\" ? 2 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 18)(2, \"div\", 31)(3, \"label\", 19);\n    i0.ɵɵtext(4, \"Template Variable Mapping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 84)(6, \"label\", 85);\n    i0.ɵɵtext(7, \"Unmapped Variable\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"label\", 86);\n    i0.ɵɵtext(9, \"Mapped Variable\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 87, 2);\n    i0.ɵɵlistener(\"selectVar\", function CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_13_Template_div_selectVar_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext(5);\n      const mapVariableModal_r15 = i0.ɵɵreference(11);\n      return i0.ɵɵresetView(ctx_r0.openMapVariableModal($event, mapVariableModal_r15));\n    })(\"change\", function CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_13_Template_div_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ɵ$index_271_r12 = i0.ɵɵnextContext(2).$index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.updateTemplateValue($event, ɵ$index_271_r12));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 30)(13, \"div\", 18)(14, \"div\", 31)(15, \"label\", 19);\n    i0.ɵɵtext(16, \"Message Preview Using Template\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext(2).$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"template\", ctx_r0.getCurrentTemplateBody(ɵ$index_271_r12));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"template\", ctx_r0.getCurrentTemplateBody(ɵ$index_271_r12))(\"variables\", ctx_r0.variables);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_0_Template, 6, 3, \"div\", 30);\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 18)(3, \"div\", 67)(4, \"label\", 19);\n    i0.ɵɵtext(5, \"Template Body\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 60);\n    i0.ɵɵelement(7, \"i\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 69)(9, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.insertVariable());\n    });\n    i0.ɵɵelement(10, \"svg-icon\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_11_Template, 9, 3)(12, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_12_Template, 3, 5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Conditional_13_Template, 18, 3);\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = i0.ɵɵnextContext().$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional((ctx_r0.fValue == null ? null : ctx_r0.fValue.channelType) === \"email\" || ctx_r0.isEditMode && (ctx_r0.currentTemplateData == null ? null : ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"email\" ? 0 : -1);\n    i0.ɵɵadvance(11);\n    i0.ɵɵconditional((ctx_r0.fValue == null ? null : ctx_r0.fValue.channelType) === \"email\" || ctx_r0.isEditMode && (ctx_r0.currentTemplateData == null ? null : ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"email\" ? 11 : 12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.getCurrentTemplateBody(ɵ$index_271_r12) && !ctx_r0.isViewMode ? 13 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 65)(1, 66);\n    i0.ɵɵtemplate(2, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Conditional_2_Template, 14, 3);\n    i0.ɵɵelementContainerEnd()();\n  }\n  if (rf & 2) {\n    const ɵ$index_271_r12 = ctx.$index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"formArrayName\", \"languages\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", ɵ$index_271_r12);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.shouldShowLanguageFields(ɵ$index_271_r12) ? 2 : -1);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, CreateTemplateComponent_Conditional_9_Conditional_47_For_1_Template, 3, 3, \"ng-container\", 65, _forTrack1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵrepeater(ctx_r0.fValue == null ? null : ctx_r0.fValue.languages);\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵtext(1, \"Back to Search\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_Conditional_9_Conditional_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.updateTemplate());\n    });\n    i0.ɵɵtext(1, \"Update Template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 36);\n    i0.ɵɵtext(3, \"Cancel\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Conditional_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_Conditional_9_Conditional_51_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.createTemplate());\n    });\n    i0.ɵɵtext(1, \"Create Template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 36);\n    i0.ɵɵtext(3, \"Cancel\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction CreateTemplateComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 15)(2, \"div\", 16)(3, \"div\", 17)(4, \"div\", 18)(5, \"label\", 19);\n    i0.ɵɵtext(6, \"Template Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 20);\n    i0.ɵɵtemplate(8, CreateTemplateComponent_Conditional_9_Conditional_8_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 17)(10, \"div\", 18)(11, \"label\", 19);\n    i0.ɵɵtext(12, \"Channel Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"select\", 22)(14, \"option\", 23);\n    i0.ɵɵtext(15, \"Select Channel Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 24);\n    i0.ɵɵtext(17, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 25);\n    i0.ɵɵtext(19, \"SMS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 26);\n    i0.ɵɵtext(21, \"Letter\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 17)(23, \"div\", 27)(24, \"div\", 18)(25, \"label\", 19);\n    i0.ɵɵtext(26, \"Entry Point\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, CreateTemplateComponent_Conditional_9_Conditional_27_Template, 7, 2, \"select\", 28)(28, CreateTemplateComponent_Conditional_9_Conditional_28_Template, 8, 2);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 17)(30, \"div\", 27)(31, \"div\", 18)(32, \"label\", 19);\n    i0.ɵɵtext(33, \"Recipient Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, CreateTemplateComponent_Conditional_9_Conditional_34_Template, 7, 2, \"select\", 29)(35, CreateTemplateComponent_Conditional_9_Conditional_35_Template, 8, 2);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(36, CreateTemplateComponent_Conditional_9_Conditional_36_Template, 6, 1, \"div\", 30);\n    i0.ɵɵelementStart(37, \"div\", 30)(38, \"div\", 18)(39, \"label\", 19);\n    i0.ɵɵtext(40, \"Template Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 31);\n    i0.ɵɵtemplate(42, CreateTemplateComponent_Conditional_9_Conditional_42_Template, 3, 0, \"div\", 32)(43, CreateTemplateComponent_Conditional_9_Conditional_43_Template, 3, 0, \"div\", 33)(44, CreateTemplateComponent_Conditional_9_Conditional_44_Template, 4, 0, \"button\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(45, CreateTemplateComponent_Conditional_9_Conditional_45_Template, 10, 0)(46, CreateTemplateComponent_Conditional_9_Conditional_46_Template, 12, 5)(47, CreateTemplateComponent_Conditional_9_Conditional_47_Template, 2, 0);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 35);\n    i0.ɵɵtemplate(49, CreateTemplateComponent_Conditional_9_Conditional_49_Template, 2, 2, \"button\", 36)(50, CreateTemplateComponent_Conditional_9_Conditional_50_Template, 4, 2)(51, CreateTemplateComponent_Conditional_9_Conditional_51_Template, 4, 2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_14_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.createForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r0.isFieldInvalid(\"templateName\"))(\"form-control-readonly\", ctx_r0.isEditMode || ctx_r0.isViewMode);\n    i0.ɵɵproperty(\"readonly\", ctx_r0.isEditMode || ctx_r0.isViewMode);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isFieldInvalid(\"templateName\") ? 8 : -1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isEditMode || ctx_r0.isViewMode);\n    i0.ɵɵadvance(14);\n    i0.ɵɵconditional(ctx_r0.isViewMode && ctx_r0.currentTemplateData ? 27 : 28);\n    i0.ɵɵadvance(7);\n    i0.ɵɵconditional(ctx_r0.isViewMode && ctx_r0.currentTemplateData ? 34 : 35);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.isViewMode && ctx_r0.currentTemplateData && ((ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"email\" || (ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"sms\") || ctx_r0.isEditMode && ctx_r0.currentTemplateData && ((ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"email\" || (ctx_r0.currentTemplateData.templateType == null ? null : ctx_r0.currentTemplateData.templateType.toLowerCase()) === \"sms\") || !ctx_r0.isViewMode && !ctx_r0.isEditMode && ((ctx_r0.fValue == null ? null : ctx_r0.fValue.channelType) === \"email\" || (ctx_r0.fValue == null ? null : ctx_r0.fValue.channelType) === \"sms\") ? 36 : -1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵconditional(ctx_r0.isViewMode && (ctx_r0.currentTemplateData == null ? null : ctx_r0.currentTemplateData.communicationTemplateDetails == null ? null : ctx_r0.currentTemplateData.communicationTemplateDetails.length) > 0 ? 42 : 43);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(!ctx_r0.isViewMode ? 44 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((ctx_r0.createForm == null ? null : (tmp_14_0 = ctx_r0.createForm.get(\"channelType\")) == null ? null : tmp_14_0.value) === \"letter\" ? 45 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r0.isViewMode && (ctx_r0.currentTemplateData == null ? null : ctx_r0.currentTemplateData.communicationTemplateDetails == null ? null : ctx_r0.currentTemplateData.communicationTemplateDetails.length) > 0 ? 46 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r0.isViewMode ? 47 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r0.isViewMode ? 49 : ctx_r0.isEditMode ? 50 : 51);\n  }\n}\nfunction CreateTemplateComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"h4\", 90);\n    i0.ɵɵtext(2, \"Template Variable Mapping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"img\", 91);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_ng_template_10_Template_img_click_3_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.mapVarModalRef == null ? null : ctx_r0.mapVarModalRef.hide());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"div\", 18)(6, \"label\", 19);\n    i0.ɵɵtext(7, \"Mapping Fields\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ng-select\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateTemplateComponent_ng_template_10_Template_ng_select_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedVariable.value, $event) || (ctx_r0.selectedVariable.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 94)(10, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_ng_template_10_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.assignVariable());\n    });\n    i0.ɵɵtext(11, \" Map \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"items\", ctx_r0.variables);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectedVariable.value);\n    i0.ɵɵproperty(\"clearable\", false)(\"searchable\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r0.selectedVariable == null ? null : ctx_r0.selectedVariable.value));\n  }\n}\nfunction CreateTemplateComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"h4\", 90);\n    i0.ɵɵtext(2, \"Add New Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"img\", 91);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_ng_template_12_Template_img_click_3_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.addLangModalRef == null ? null : ctx_r0.addLangModalRef.hide());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"div\", 18)(6, \"label\", 19);\n    i0.ɵɵtext(7, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ng-select\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateTemplateComponent_ng_template_12_Template_ng_select_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedLanguage, $event) || (ctx_r0.selectedLanguage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 94)(10, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function CreateTemplateComponent_ng_template_12_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.addLanguage());\n    });\n    i0.ɵɵtext(11, \" Add Language \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"items\", ctx_r0.allLanguages);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.selectedLanguage);\n    i0.ɵɵproperty(\"clearable\", false)(\"searchable\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.selectedLanguage);\n  }\n}\nexport let CreateTemplateComponent = /*#__PURE__*/(() => {\n  class CreateTemplateComponent {\n    constructor() {\n      this.fb = inject(FormBuilder);\n      this.modalService = inject(BsModalService);\n      this.route = inject(ActivatedRoute);\n      this.router = inject(Router);\n      this.templateService = inject(TemplateService);\n      this.toastr = inject(ToastrService);\n      this.breadcrumbData = [{\n        label: \"Communication\"\n      }, {\n        label: \"Create Communication Template\"\n      }];\n      this.isViewMode = false;\n      this.isEditMode = false;\n      this.templateId = null;\n      this.isLoading = false;\n      this.templateData = null;\n      this.currentTemplateData = null;\n      this.selectedViewLanguageIndex = 0;\n      this.allLanguages = [];\n      this.variables = [];\n      this.isLoadingFields = false;\n      this.selectedVariable = {\n        value: null,\n        index: -1\n      };\n      this.selectedLanguage = null;\n      // NGX Editor properties\n      this.editors = [];\n      this.htmlContents = [];\n      this.defaultEditor = null; // Default editor for toolbar display\n      this.isUpdatingFromMapping = false;\n      this.toolbar = [['bold', 'italic'], ['underline'], ['code', 'blockquote'], ['ordered_list', 'bullet_list'], [{\n        heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']\n      }], ['link'], ['align_left', 'align_center', 'align_right', 'align_justify']];\n      this.buildCreateTemplateForm();\n      this.initializeDefaultEditor();\n    }\n    ngOnInit() {\n      this.loadDatabaseFields();\n      this.loadLanguageList();\n      this.route.params.subscribe(params => {\n        this.templateId = params['id'] || null;\n        const url = this.router.url;\n        if (url.includes('view-communication-template')) {\n          this.isViewMode = true;\n          this.isEditMode = false;\n          this.updateBreadcrumb('View Communication Template');\n        } else if (url.includes('edit-communication-template')) {\n          this.isViewMode = false;\n          this.isEditMode = true;\n          this.updateBreadcrumb('Edit Communication Template');\n        } else {\n          this.isViewMode = false;\n          this.isEditMode = false;\n          this.updateBreadcrumb('Create Communication Template');\n        }\n        if (this.templateId && (this.isViewMode || this.isEditMode)) {\n          this.loadTemplateData(this.templateId);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.editors.forEach(editor => {\n        if (editor) {\n          editor.destroy();\n        }\n      });\n      // Destroy the default editor\n      if (this.defaultEditor) {\n        this.defaultEditor.destroy();\n      }\n    }\n    initializeDefaultEditor() {\n      try {\n        this.defaultEditor = new Editor({\n          history: true,\n          keyboardShortcuts: true,\n          inputRules: true\n        });\n        console.log('Default editor initialized successfully');\n      } catch (error) {\n        console.error('Failed to initialize default editor:', error);\n        // Fallback: try with minimal configuration\n        try {\n          this.defaultEditor = new Editor();\n          console.log('Default editor initialized with minimal config');\n        } catch (fallbackError) {\n          console.error('Failed to initialize default editor even with minimal config:', fallbackError);\n          this.defaultEditor = null;\n        }\n      }\n    }\n    updateBreadcrumb(label) {\n      this.breadcrumbData = [{\n        label: \"Communication\"\n      }, {\n        label: label\n      }];\n    }\n    loadTemplateData(templateId) {\n      this.isLoading = true;\n      if (this.allLanguages.length === 0) {\n        this.templateService.languageList().subscribe({\n          next: languageResponse => {\n            this.mapLanguageResponse(languageResponse);\n            this.fetchAndPopulateTemplate(templateId);\n          },\n          error: () => {\n            this.allLanguages = [{\n              name: \"English\",\n              code: \"en\"\n            }];\n            this.fetchAndPopulateTemplate(templateId);\n          }\n        });\n      } else {\n        this.fetchAndPopulateTemplate(templateId);\n      }\n    }\n    fetchAndPopulateTemplate(templateId) {\n      this.templateService.fetchTemplateById(templateId).subscribe({\n        next: response => {\n          this.templateData = response;\n          this.populateFormWithTemplateData(response);\n          this.isLoading = false;\n        },\n        error: error => {\n          this.isLoading = false;\n          this.toastr.error(error, 'Error!');\n        }\n      });\n    }\n    get isSMS() {\n      return this.createForm?.get('channelType')?.value?.toLowerCase() === 'sms';\n    }\n    populateFormWithTemplateData(apiResponse) {\n      if (!apiResponse) return;\n      const templateData = apiResponse.data || apiResponse;\n      const templateDetails = templateData.communicationTemplateDetails || [];\n      this.currentTemplateData = templateData;\n      // Convert the boolean value to string \"true\" or \"false\" for radio buttons\n      const allowAccessValue = templateData.isAvailableInAccountDetails === true ? \"true\" : \"false\";\n      this.createForm.patchValue({\n        channelType: templateData.templateType?.toLowerCase() || 'email',\n        templateName: templateData.name || '',\n        allowAccessFromAccount: allowAccessValue,\n        entryPoint: templateData.entryPoint || '',\n        recipientType: templateData.recipientType || ''\n      });\n      const languagesArray = this.createForm.get('languages');\n      while (languagesArray.length !== 0) {\n        languagesArray.removeAt(0);\n      }\n      if (templateDetails.length > 0) {\n        templateDetails.forEach(detail => {\n          let languageCode = 'en';\n          const languageName = detail.language || 'English';\n          if (this.allLanguages && this.allLanguages.length > 0) {\n            const foundLanguage = this.allLanguages.find(lang => lang.name.toLowerCase() === languageName.toLowerCase());\n            if (foundLanguage) {\n              languageCode = foundLanguage.code;\n            }\n          }\n          const languageFormGroup = this.buildLanguageFormGroup({\n            code: languageCode,\n            name: languageName\n          });\n          languageFormGroup.patchValue({\n            languageCode: languageCode,\n            languageName: languageName,\n            emailSubject: detail.subject || '',\n            templateBody: this.templateData.templateType?.toLowerCase() !== 'email' ? detail.body || '' : '',\n            emailBody: this.templateData.templateType?.toLowerCase() === 'email' ? detail.body || '' : ''\n          });\n          // Initialize editor content for email templates\n          if (this.templateData.templateType?.toLowerCase() === 'email') {\n            const index = languagesArray.length;\n            // Convert plain text template to HTML for the editor\n            this.htmlContents[index] = this.plainTextToHtml(detail.body || '');\n          }\n          languagesArray.push(languageFormGroup);\n        });\n      } else {\n        const defaultLanguageGroup = this.buildLanguageFormGroup({\n          code: 'en',\n          name: 'English'\n        });\n        languagesArray.push(defaultLanguageGroup);\n      }\n      if (languagesArray.length > 0) {\n        const firstLanguage = languagesArray.at(0)?.value;\n        const activeLanguage = firstLanguage?.languageCode || 'en';\n        this.createForm.patchValue({\n          activeLanguage\n        });\n      }\n      // Update validation for all language form groups based on channel type\n      this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n      setTimeout(() => {\n        if (this.isViewMode) {\n          this.createForm.disable();\n        } else if (this.isEditMode) {\n          this.createForm.get('channelType')?.disable();\n          this.createForm.get('templateName')?.disable();\n        }\n      }, 200);\n      if (this.isViewMode) {\n        this.updateBreadcrumb('View Communication Template');\n        this.selectedViewLanguageIndex = 0;\n      } else if (this.isEditMode) {\n        this.updateBreadcrumb('Edit Communication Template');\n      }\n      // Initialize editors for email templates after form is populated\n      if (this.templateData.templateType?.toLowerCase() === 'email') {\n        // Initialize immediately\n        this.initializeEditorsForAllLanguages();\n        // Additional checks with shorter delays\n        setTimeout(() => {\n          this.initializeEditorsForAllLanguages();\n          this.ensureEditorsAreReady();\n        }, 100);\n        setTimeout(() => {\n          this.ensureEditorsAreReady();\n        }, 300);\n      }\n    }\n    loadDatabaseFields() {\n      this.isLoadingFields = true;\n      this.templateService.getFieldsList().subscribe({\n        next: response => {\n          this.mapFieldsToVariables(response);\n          this.isLoadingFields = false;\n        },\n        error: () => {\n          this.isLoadingFields = false;\n          this.toastr.error('error', 'Error!');\n          this.variables = [];\n        }\n      });\n    }\n    mapFieldsToVariables(apiResponse) {\n      if (!Array.isArray(apiResponse)) {\n        this.variables = [];\n        return;\n      }\n      this.variables = apiResponse.map(field => ({\n        name: field.name || field.fieldName || 'Unknown Field',\n        id: field.id || field.code || field.name?.toUpperCase().replace(/\\s+/g, '_') || 'UNKNOWN',\n        code: field.code || field.id || 'UNKNOWN_CODE'\n      }));\n    }\n    buildCreateTemplateForm() {\n      this.createForm = this.fb.group({\n        channelType: [\"email\", [Validators.required]],\n        templateName: [null, [Validators.required]],\n        allowAccessFromAccount: [\"true\"],\n        activeLanguage: [\"en\"],\n        languages: this.fb.array([]),\n        entryPoint: ['Account', [Validators.required]],\n        recipientType: ['Customer', [Validators.required]]\n      });\n      this.createForm.get('channelType')?.valueChanges.subscribe(channelType => {\n        this.updateLanguageValidation(channelType);\n        this.handleChannelTypeChange(channelType);\n      });\n      this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n    }\n    updateLanguageValidation(channelType) {\n      const languagesArray = this.createForm.get('languages');\n      languagesArray.controls.forEach(languageControl => {\n        const emailSubjectControl = languageControl.get('emailSubject');\n        const templateBodyControl = languageControl.get('templateBody');\n        const emailBodyControl = languageControl.get('emailBody');\n        if (channelType === 'email') {\n          emailSubjectControl?.setValidators([Validators.required]);\n          emailBodyControl?.setValidators([Validators.required]);\n          templateBodyControl?.clearValidators();\n        } else {\n          emailSubjectControl?.clearValidators();\n          emailBodyControl?.clearValidators();\n          templateBodyControl?.setValidators([Validators.required]);\n        }\n        emailSubjectControl?.updateValueAndValidity();\n        templateBodyControl?.updateValueAndValidity();\n        emailBodyControl?.updateValueAndValidity();\n      });\n    }\n    buildLanguagesFormArray(data) {\n      const formArray = new FormArray([]);\n      if (data && data.length > 0) {\n        data.forEach(o => {\n          formArray.push(this.buildLanguageFormGroup(o));\n        });\n      } else if (this.allLanguages && this.allLanguages.length > 0) {\n        formArray.push(this.buildLanguageFormGroup(this.allLanguages[0]));\n      }\n      return formArray;\n    }\n    buildLanguageFormGroup(data) {\n      return this.fb.group({\n        languageCode: data?.code,\n        languageName: data?.name,\n        emailSubject: [null],\n        templateBody: [null],\n        // Validators will be set by updateLanguageValidation\n        emailBody: [null] // Validators will be set by updateLanguageValidation\n      });\n    }\n    get fValue() {\n      return this.createForm.value;\n    }\n    shouldShowLanguageFields(languageIndex) {\n      if (this.isViewMode) {\n        return languageIndex === 0;\n      }\n      const activeLanguage = this.fValue?.activeLanguage;\n      const currentLanguage = this.fValue?.languages?.[languageIndex];\n      return activeLanguage === currentLanguage?.languageCode;\n    }\n    selectViewLanguage(index) {\n      this.selectedViewLanguageIndex = index;\n    }\n    getSelectedLanguageDetail() {\n      if (this.isViewMode && this.currentTemplateData?.communicationTemplateDetails?.length > 0) {\n        return this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex] || this.currentTemplateData.communicationTemplateDetails[0];\n      }\n      return null;\n    }\n    getFormValidationErrors() {\n      let formErrors = {};\n      Object.keys(this.createForm.controls).forEach(key => {\n        const controlErrors = this.createForm.get(key)?.errors;\n        if (controlErrors) {\n          formErrors[key] = controlErrors;\n        }\n      });\n      const languagesArray = this.createForm.get('languages');\n      if (languagesArray) {\n        languagesArray.controls.forEach((control, index) => {\n          const formGroup = control;\n          Object.keys(formGroup.controls).forEach(fieldKey => {\n            const fieldControl = formGroup.get(fieldKey);\n            if (fieldControl?.errors) {\n              if (!formErrors.languages) formErrors.languages = {};\n              if (!formErrors.languages[index]) formErrors.languages[index] = {};\n              formErrors.languages[index][fieldKey] = fieldControl.errors;\n            }\n          });\n        });\n      }\n      return formErrors;\n    }\n    openMapVariableModal(event, template) {\n      this.selectedVariable = event;\n      this.mapVarModalRef = this.modalService.show(template, {\n        animated: true\n      });\n    }\n    assignVariable() {\n      this.mapVarModalRef.hide();\n      if (this.selectedVariable?.value) {\n        this.templateVarConfig.onUpdateVariable(this.selectedVariable);\n        this.selectedVariable = {\n          value: null,\n          index: -1\n        };\n      }\n    }\n    updateTemplateValue(template, index) {\n      const languageControl = this.createForm.get('languages').at(index);\n      const channelType = this.createForm.get('channelType')?.value;\n      console.log('updateTemplateValue called with:', {\n        template,\n        index,\n        channelType\n      });\n      if (channelType === 'email') {\n        // Update the form control with the plain text template (already in correct format)\n        languageControl.patchValue({\n          emailBody: template\n        });\n        languageControl.get('emailBody')?.updateValueAndValidity();\n        // Temporarily disable the content change handler to avoid circular updates\n        this.isUpdatingFromMapping = true;\n        // Update the editor content directly with the plain text converted to HTML\n        const htmlContent = this.plainTextToHtml(template);\n        this.htmlContents[index] = htmlContent;\n        console.log('Template value updated for email at index:', index);\n        console.log('Plain text template:', template);\n        console.log('HTML content for editor:', htmlContent);\n        // Re-enable the content change handler after a short delay\n        setTimeout(() => {\n          this.isUpdatingFromMapping = false;\n        }, 200);\n      } else {\n        languageControl.patchValue({\n          templateBody: template\n        });\n        languageControl.get('templateBody')?.updateValueAndValidity();\n      }\n      this.createForm.updateValueAndValidity();\n    }\n    openAddLangModal(template) {\n      this.addLangModalRef = this.modalService.show(template, {\n        animated: true\n      });\n    }\n    addLanguage() {\n      this.addLangModalRef?.hide();\n      const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\n      const languagesArray = this.createForm.get('languages');\n      const isLanguageAlreadySelected = languagesArray.controls.some(control => control.get('languageCode')?.value === this.selectedLanguage);\n      if (isLanguageAlreadySelected) {\n        this.toastr.error('The same language is already selected.', 'Error!');\n        return;\n      }\n      const langFormGroup = this.buildLanguageFormGroup(language);\n      languagesArray.push(langFormGroup);\n      this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n      this.createForm.updateValueAndValidity();\n      // Initialize editor for new language if channel type is email\n      if (this.createForm.get('channelType')?.value === 'email') {\n        this.initializeEditorForLanguage(languagesArray.length - 1);\n      }\n    }\n    removeLanguage(index) {\n      const languagesArray = this.createForm.get('languages');\n      if (languagesArray.length <= 1) {\n        return;\n      }\n      const removedLanguage = languagesArray.at(index).value;\n      languagesArray.removeAt(index);\n      if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {\n        const firstLanguage = languagesArray.at(0)?.value;\n        if (firstLanguage) {\n          this.createForm.patchValue({\n            activeLanguage: firstLanguage.languageCode\n          });\n        }\n      }\n      this.createForm.updateValueAndValidity();\n    }\n    createTemplate() {\n      this.markFormGroupTouched(this.createForm);\n      if (this.createForm.invalid) {\n        this.toastr.error('Please fill all required fields.', 'Error');\n        return;\n      }\n      const formValue = this.createForm.value;\n      const hasTemplateBody = formValue.channelType === 'email' ? formValue.languages?.some(lang => lang.emailBody && lang.emailBody.trim()) : formValue.languages?.some(lang => lang.templateBody && lang.templateBody.trim());\n      if (!hasTemplateBody) {\n        this.toastr.error('Please enter template body content.', 'Error');\n        return;\n      }\n      if (this.hasUnmappedVariables(formValue.languages)) {\n        this.toastr.error('Please map all variables and continue creating template.', 'Error');\n        return;\n      }\n      if (formValue.channelType === 'email') {\n        const hasEmailSubject = formValue.languages?.every(lang => lang.emailSubject && lang.emailSubject.trim());\n        if (!hasEmailSubject) {\n          this.toastr.error('Please enter email subject for all languages.', 'Error');\n          return;\n        }\n      }\n      const getTemplateType = channelType => {\n        switch (channelType.toLowerCase()) {\n          case 'email':\n            return 'Email';\n          case 'sms':\n            return 'SMS';\n          case 'letter':\n            return 'Letter';\n          default:\n            return 'Email';\n        }\n      };\n      const getLanguageName = languageCode => {\n        const language = this.allLanguages.find(lang => lang.code === languageCode);\n        return language ? language.name : 'English';\n      };\n      const communicationTemplateDetails = formValue.languages.map(lang => {\n        let bodyContent = '';\n        if (formValue.channelType === 'email') {\n          // For email templates, ensure we send plain text (already converted in form control)\n          bodyContent = lang.emailBody || \"\";\n        } else {\n          bodyContent = lang.templateBody || \"\";\n        }\n        const detail = {\n          \"Language\": getLanguageName(lang.languageCode),\n          \"body\": bodyContent\n        };\n        if (formValue.channelType === 'email') {\n          detail.Subject = lang.emailSubject || \"\";\n        }\n        return detail;\n      });\n      const json = {\n        \"templateType\": getTemplateType(formValue.channelType),\n        \"Name\": formValue.templateName,\n        \"isAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n        \"CommunicationTemplateDetails\": communicationTemplateDetails,\n        \"entryPoint\": formValue.entryPoint,\n        \"recipientType\": formValue.recipientType\n      };\n      this.templateService.saveCommunicationTemplate(json).subscribe({\n        next: () => {\n          this.toastr.success(`The Template \"${json.Name}\" has been created successfully.`, \"Success!\");\n          this.router.navigate(['communication/search-communication-templates']);\n        },\n        error: error => {\n          this.toastr.error(error, \"Error!\");\n        }\n      });\n    }\n    updateTemplate() {\n      this.markFormGroupTouched(this.createForm);\n      if (this.createForm.invalid) {\n        this.toastr.error('Please fill in all required fields.', 'Error');\n        return;\n      }\n      const formValue = this.createForm.getRawValue();\n      const hasTemplateBody = formValue.channelType === 'email' ? formValue.languages?.some(lang => lang.emailBody && lang.emailBody.trim()) : formValue.languages?.some(lang => lang.templateBody && lang.templateBody.trim());\n      if (!hasTemplateBody) {\n        this.toastr.error('Please enter template body content.', 'Error');\n        return;\n      }\n      if (this.hasUnmappedVariables(formValue.languages)) {\n        this.toastr.error('Please map all variables and continue creating template.', 'Error');\n        return;\n      }\n      if (!formValue.channelType) {\n        this.toastr.error('Please select a channel type.', 'Error');\n        return;\n      }\n      if (formValue.channelType === 'email') {\n        const hasEmailSubject = formValue.languages?.every(lang => lang.emailSubject && lang.emailSubject.trim());\n        if (!hasEmailSubject) {\n          this.toastr.error('Please enter email subject for all languages.', 'Error');\n          return;\n        }\n      }\n      const getTemplateType = channelType => {\n        if (!channelType || typeof channelType !== 'string') {\n          return 'Email';\n        }\n        switch (channelType.toLowerCase()) {\n          case 'email':\n            return 'Email';\n          case 'sms':\n            return 'SMS';\n          case 'letter':\n            return 'Letter';\n          default:\n            return 'Email';\n        }\n      };\n      const getLanguageName = languageCode => {\n        if (!languageCode) return 'English';\n        const language = this.allLanguages.find(lang => lang.code === languageCode);\n        return language ? language.name : 'English';\n      };\n      const communicationTemplateDetails = formValue.languages?.map(lang => {\n        let bodyContent = '';\n        if (formValue.channelType === 'email') {\n          // For email templates, ensure we send plain text (already converted in form control)\n          bodyContent = lang.emailBody || \"\";\n        } else {\n          bodyContent = lang.templateBody || \"\";\n        }\n        const detail = {\n          \"Language\": getLanguageName(lang.languageCode),\n          \"body\": bodyContent\n        };\n        if (formValue.channelType === 'email') {\n          detail.Subject = lang.emailSubject || \"\";\n        }\n        return detail;\n      }) || [];\n      const json = {\n        \"id\": this.templateId,\n        \"templateType\": getTemplateType(formValue.channelType),\n        \"Name\": formValue.templateName || \"\",\n        \"IsAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n        \"CommunicationTemplateDetails\": communicationTemplateDetails,\n        \"entryPoint\": formValue.entryPoint,\n        \"recipientType\": formValue.recipientType\n      };\n      this.templateService.updateCcmTemplate(json).subscribe({\n        next: () => {\n          this.toastr.success(`The Template \"${json.Name}\" has been updated successfully.`, \"Success!\");\n          this.router.navigate(['communication/search-communication-templates']);\n        },\n        error: error => {\n          this.toastr.error(error, \"Error!\");\n        }\n      });\n    }\n    loadLanguageList() {\n      this.templateService.languageList().subscribe({\n        next: response => {\n          this.mapLanguageResponse(response);\n        },\n        error: error => {\n          this.toastr.error(error, 'Error');\n          this.allLanguages = [{\n            name: \"English\",\n            code: \"en\"\n          }];\n          this.initializeDefaultLanguage();\n        }\n      });\n    }\n    mapLanguageResponse(apiResponse) {\n      if (!apiResponse || !Array.isArray(apiResponse)) {\n        this.allLanguages = [{\n          name: \"English\",\n          code: \"en\"\n        }];\n        this.initializeDefaultLanguage();\n        return;\n      }\n      this.allLanguages = apiResponse.map(lang => ({\n        name: lang.name || lang.itemName || 'Unknown Language',\n        code: lang.code || lang.itemCode || lang.name?.toLowerCase().substring(0, 2) || 'en'\n      }));\n      if (this.allLanguages.length === 0) {\n        this.allLanguages = [{\n          name: \"English\",\n          code: \"en\"\n        }];\n      }\n      this.initializeDefaultLanguage();\n    }\n    initializeDefaultLanguage() {\n      if (this.createForm && this.allLanguages.length > 0) {\n        const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;\n        if (!currentActiveLanguage || !this.allLanguages.find(lang => lang.code === currentActiveLanguage)) {\n          this.createForm.patchValue({\n            activeLanguage: this.allLanguages[0].code\n          });\n          const languagesArray = this.createForm.get('languages');\n          if (languagesArray.length === 0) {\n            const defaultLanguageGroup = this.buildLanguageFormGroup(this.allLanguages[0]);\n            languagesArray.push(defaultLanguageGroup);\n            // Update validation for the newly added language group\n            this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n          }\n        }\n      }\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        if (control instanceof FormGroup || control instanceof FormArray) {\n          this.markFormGroupTouched(control);\n        } else {\n          control?.markAsTouched();\n        }\n      });\n    }\n    isFieldInvalid(fieldName) {\n      const field = this.createForm.get(fieldName);\n      return !!(field && field.invalid && (field.dirty || field.touched));\n    }\n    getFieldErrorMessage(fieldName) {\n      const field = this.createForm.get(fieldName);\n      if (field && field.errors && (field.dirty || field.touched)) {\n        if (field.errors['required']) {\n          return `${this.getFieldDisplayName(fieldName)} is required`;\n        }\n      }\n      return '';\n    }\n    isLanguageFieldInvalid(languageIndex, fieldName) {\n      const languagesArray = this.createForm.get('languages');\n      const languageGroup = languagesArray.at(languageIndex);\n      const field = languageGroup?.get(fieldName);\n      return !!(field && field.invalid && (field.dirty || field.touched));\n    }\n    getLanguageFieldErrorMessage(languageIndex, fieldName) {\n      const languagesArray = this.createForm.get('languages');\n      const languageGroup = languagesArray.at(languageIndex);\n      const field = languageGroup?.get(fieldName);\n      if (field && field.errors && (field.dirty || field.touched)) {\n        if (field.errors['required']) {\n          return `${this.getFieldDisplayName(fieldName)} is required`;\n        }\n      }\n      return '';\n    }\n    hasUnmappedVariables(languages) {\n      if (!languages || languages.length === 0) {\n        return false;\n      }\n      const channelType = this.createForm.get('channelType')?.value;\n      return languages.some(lang => {\n        const templateBody = channelType === 'email' ? lang.emailBody || '' : lang.templateBody || '';\n        return templateBody.includes('<<Var>>');\n      });\n    }\n    getFieldDisplayName(fieldName) {\n      const fieldNames = {\n        'templateName': 'Template Name',\n        'channelType': 'Channel Type',\n        'emailSubject': 'Subject Line',\n        'templateBody': 'Template Body',\n        'entryPoint': 'Entry Point',\n        'recipientType': 'Recipient Type'\n      };\n      return fieldNames[fieldName] || fieldName;\n    }\n    insertVariable() {\n      const activeLanguageIndex = this.getActiveLanguageIndex();\n      if (activeLanguageIndex === -1) {\n        console.warn('No active language index found');\n        return;\n      }\n      const channelType = this.createForm.get('channelType')?.value || this.templateData?.templateType?.toLowerCase();\n      const languageControl = this.createForm.get('languages').at(activeLanguageIndex);\n      console.log('Inserting variable for channel type:', channelType, 'at index:', activeLanguageIndex);\n      if (channelType === 'email') {\n        // For email templates using ngx-editor\n        const editor = this.editors[activeLanguageIndex];\n        if (editor && editor.view) {\n          try {\n            // Insert the variable as plain text to preserve the angle brackets\n            editor.commands.insertText('<<Var>>').exec();\n            // Debug: Log to console to verify insertion\n            console.log('Variable inserted into editor at index:', activeLanguageIndex);\n            // The editor content will be automatically synced via ngModel binding\n          } catch (error) {\n            console.error('Error inserting variable into editor:', error);\n          }\n        } else {\n          console.warn('Editor not available at index:', activeLanguageIndex);\n          // Fallback: Initialize editor if not available and try again\n          this.initializeEditorForLanguage(activeLanguageIndex);\n          setTimeout(() => {\n            const retryEditor = this.editors[activeLanguageIndex];\n            if (retryEditor && retryEditor.view) {\n              try {\n                retryEditor.commands.insertText('<<Var>>').exec();\n                console.log('Variable inserted into editor after retry at index:', activeLanguageIndex);\n              } catch (error) {\n                console.error('Error inserting variable into editor after retry:', error);\n                // Final fallback: add to form control directly\n                this.fallbackInsertVariableToEmailBody(activeLanguageIndex);\n              }\n            } else {\n              // Final fallback: add to form control directly\n              this.fallbackInsertVariableToEmailBody(activeLanguageIndex);\n            }\n          }, 500);\n        }\n      } else {\n        // For SMS/Letter templates using textarea\n        const control = languageControl.get('templateBody');\n        const currentValue = control?.value || '';\n        const textareaElement = document.getElementById('templateBody');\n        if (!textareaElement) {\n          console.warn('Textarea element not found, adding variable to form control directly');\n          control?.setValue(currentValue + '<<Var>>');\n          control?.updateValueAndValidity();\n          return;\n        }\n        const cursorPosition = textareaElement.selectionStart;\n        const newValue = currentValue.substring(0, cursorPosition) + '<<Var>>' + currentValue.substring(textareaElement.selectionEnd || cursorPosition);\n        control?.setValue(newValue);\n        control?.updateValueAndValidity();\n        setTimeout(() => {\n          textareaElement.focus();\n          textareaElement.setSelectionRange(cursorPosition + 7, cursorPosition + 7);\n        }, 0);\n      }\n    }\n    fallbackInsertVariableToEmailBody(languageIndex) {\n      const languagesArray = this.createForm.get('languages');\n      const emailBodyControl = languagesArray.at(languageIndex).get('emailBody');\n      const currentValue = emailBodyControl?.value || '';\n      emailBodyControl?.setValue(currentValue + '<<Var>>');\n      emailBodyControl?.updateValueAndValidity();\n      // Update the HTML content as well\n      this.htmlContents[languageIndex] = this.plainTextToHtml(emailBodyControl?.value || '');\n      console.log('Variable added to email body via fallback method at index:', languageIndex);\n    }\n    ensureEditorsAreReady() {\n      const channelType = this.createForm.get('channelType')?.value || this.templateData?.templateType?.toLowerCase();\n      if (channelType === 'email') {\n        const languagesArray = this.createForm.get('languages');\n        for (let i = 0; i < languagesArray.length; i++) {\n          if (!this.editors[i] || !this.editors[i].view) {\n            console.log('Re-initializing editor for index:', i);\n            this.initializeEditorForLanguage(i);\n            // Set content if available\n            const emailBodyControl = languagesArray.at(i).get('emailBody');\n            if (emailBodyControl?.value && !this.htmlContents[i]) {\n              this.htmlContents[i] = this.plainTextToHtml(emailBodyControl.value);\n            }\n          }\n        }\n      }\n    }\n    getActiveLanguageIndex() {\n      const activeLanguage = this.createForm.get('activeLanguage').value;\n      const languages = this.createForm.get('languages');\n      for (let i = 0; i < languages.length; i++) {\n        const lang = languages.at(i).get('languageCode').value;\n        if (lang === activeLanguage) {\n          return i;\n        }\n      }\n      return -1;\n    }\n    shouldRestrictHtmlTags() {\n      return this.createForm?.get('channelType')?.value !== 'sms';\n    }\n    getCurrentTemplateBody(languageIndex) {\n      const channelType = this.createForm.get('channelType')?.value;\n      const languageControl = this.createForm.get('languages').at(languageIndex);\n      if (channelType === 'email') {\n        // For email templates, return the plain text content from form control\n        // This ensures template variable mapping and preview work correctly\n        const content = languageControl.get('emailBody')?.value || '';\n        return content; // Already converted to plain text in onEditorContentChange\n      } else {\n        return languageControl.get('templateBody')?.value || '';\n      }\n    }\n    isLanguageFieldInvalidForCurrentChannel(languageIndex) {\n      const channelType = this.createForm.get('channelType')?.value;\n      const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';\n      return this.isLanguageFieldInvalid(languageIndex, fieldName);\n    }\n    getLanguageFieldErrorMessageForCurrentChannel(languageIndex) {\n      const channelType = this.createForm.get('channelType')?.value;\n      const fieldName = channelType === 'email' ? 'emailBody' : 'templateBody';\n      return this.getLanguageFieldErrorMessage(languageIndex, fieldName);\n    }\n    // NGX Editor Methods\n    initializeEditorForLanguage(index) {\n      if (!this.editors[index]) {\n        try {\n          this.editors[index] = new Editor({\n            history: true,\n            keyboardShortcuts: true,\n            inputRules: true\n          });\n          this.htmlContents[index] = '';\n          console.log('Editor initialized successfully for index:', index);\n        } catch (error) {\n          console.error('Failed to initialize editor for index:', index, error);\n          // Fallback: try with minimal configuration\n          try {\n            this.editors[index] = new Editor();\n            this.htmlContents[index] = '';\n            console.log('Editor initialized with minimal config for index:', index);\n          } catch (fallbackError) {\n            console.error('Failed to initialize editor even with minimal config:', fallbackError);\n          }\n        }\n      }\n    }\n    initializeEditorsForAllLanguages() {\n      const channelType = this.createForm.get('channelType')?.value || this.templateData?.templateType?.toLowerCase();\n      console.log('Initializing editors for channel type:', channelType);\n      if (channelType === 'email') {\n        const languagesArray = this.createForm.get('languages');\n        console.log('Languages array length:', languagesArray.length);\n        for (let i = 0; i < languagesArray.length; i++) {\n          try {\n            this.initializeEditorForLanguage(i);\n            // Set initial content from form control - convert plain text to HTML\n            const emailBodyControl = languagesArray.at(i).get('emailBody');\n            if (emailBodyControl?.value) {\n              // Convert plain text template to HTML for the editor\n              this.htmlContents[i] = this.plainTextToHtml(emailBodyControl.value);\n              console.log('Initialized editor content for index:', i);\n              console.log('Plain text from form:', emailBodyControl.value);\n              console.log('HTML for editor:', this.htmlContents[i]);\n            } else {\n              // Initialize with empty content\n              this.htmlContents[i] = '';\n              console.log('Initialized empty editor content for index:', i);\n            }\n          } catch (error) {\n            console.error('Error initializing editor for language index:', i, error);\n          }\n        }\n      }\n    }\n    onEditorContentChange(index) {\n      // Skip processing if we're updating from template mapping to avoid circular updates\n      if (this.isUpdatingFromMapping) {\n        console.log('Skipping editor content change - updating from mapping');\n        return;\n      }\n      const languagesArray = this.createForm.get('languages');\n      const emailBodyControl = languagesArray.at(index).get('emailBody');\n      if (emailBodyControl) {\n        // Convert HTML content to plain text for template processing\n        const plainTextContent = this.htmlToPlainText(this.htmlContents[index] || '');\n        // Debug: Log content changes\n        console.log('Editor content changed at index:', index);\n        console.log('Raw HTML content:', this.htmlContents[index]);\n        console.log('Plain text content:', plainTextContent);\n        emailBodyControl.setValue(plainTextContent);\n        emailBodyControl.updateValueAndValidity();\n      }\n    }\n    destroyEditor(index) {\n      if (this.editors[index]) {\n        this.editors[index].destroy();\n        this.editors[index] = null;\n        this.htmlContents[index] = '';\n      }\n    }\n    handleChannelTypeChange(channelType) {\n      if (channelType === 'email') {\n        // Ensure default editor is ready\n        if (!this.defaultEditor) {\n          this.initializeDefaultEditor();\n        }\n        // Initialize editors for email templates immediately, then with a short delay as backup\n        this.initializeEditorsForAllLanguages();\n        setTimeout(() => {\n          this.initializeEditorsForAllLanguages();\n        }, 100);\n      } else {\n        // Destroy editors for non-email templates\n        this.editors.forEach((editor, index) => {\n          if (editor) {\n            this.destroyEditor(index);\n          }\n        });\n      }\n    }\n    // Helper method to ensure template variables are properly formatted\n    normalizeTemplateVariables(content) {\n      // Convert HTML entities back to angle brackets for template processing\n      return content.replace(/&lt;&lt;/g, '<<').replace(/&gt;&gt;/g, '>>').replace(/&amp;/g, '&');\n    }\n    // Helper method to convert HTML content to plain text for template processing\n    htmlToPlainText(html) {\n      if (!html) return '';\n      console.log('Converting HTML to plain text:', html);\n      // Create a temporary div element to parse HTML\n      const tempDiv = document.createElement('div');\n      tempDiv.innerHTML = html;\n      // Get plain text content\n      let plainText = tempDiv.textContent || tempDiv.innerText || '';\n      console.log('Extracted plain text:', plainText);\n      // Normalize template variables\n      plainText = this.normalizeTemplateVariables(plainText);\n      console.log('After normalization:', plainText);\n      return plainText;\n    }\n    // Helper method to convert plain text back to HTML for ngx-editor\n    plainTextToHtml(text) {\n      if (!text) return '';\n      console.log('Converting plain text to HTML:', text);\n      // First, escape any existing HTML entities to prevent double-encoding\n      let processedText = text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n      console.log('After HTML escaping:', processedText);\n      // Split text into lines and process each line\n      const lines = processedText.split('\\n');\n      const htmlLines = lines.map(line => {\n        if (line.trim() === '') {\n          return '<p><br></p>'; // Empty line\n        } else {\n          return `<p>${line}</p>`; // Wrap in paragraph\n        }\n      });\n      const result = htmlLines.join('');\n      console.log('Final HTML result:', result);\n      return result;\n    }\n    static {\n      this.ɵfac = function CreateTemplateComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CreateTemplateComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CreateTemplateComponent,\n        selectors: [[\"app-create-template\"]],\n        viewQuery: function CreateTemplateComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateVarConfig = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([TemplateService]), i0.ɵɵStandaloneFeature],\n        decls: 14,\n        vars: 4,\n        consts: [[\"mapVariableModal\", \"\"], [\"addLangModal\", \"\"], [\"templateVarConfig\", \"appTemplateVarConfig\"], [1, \"inner-layout-container\"], [3, \"data\"], [1, \"d-flex\", \"align-items-center\", \"mb-4\"], [1, \"title\", \"mb-0\"], [\"tooltip\", \"Design templates for different communication channels with variable\\n        support and multilingual capabilities\", \"type\", \"button\", 1, \"ms-3\", \"p-0\", \"border-0\"], [1, \"enc-card\"], [1, \"enc-card\", 3, \"formGroup\"], [\"src\", \"assets/new/svgs/instruction-info.svg\"], [1, \"card-content\", \"text-center\", \"p-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\"], [1, \"card-content\"], [1, \"row\"], [1, \"col-md-6\", \"col-12\"], [1, \"form-control-group\"], [1, \"form-label\", \"required\"], [\"type\", \"text\", \"placeholder\", \"Enter Template Name\", \"id\", \"templateName\", \"formControlName\", \"templateName\", 1, \"form-control\", 3, \"readonly\"], [1, \"form-error\"], [\"id\", \"channelType\", \"formControlName\", \"channelType\", 1, \"form-select\", 3, \"disabled\"], [\"value\", \"\"], [\"value\", \"email\"], [\"value\", \"sms\"], [\"value\", \"letter\"], [1, \"form-control-group\", \"h-100\", \"d-flex\", \"flex-column\", \"justify-content-between\"], [\"formControlName\", \"entryPoint\", \"disabled\", \"\", 1, \"form-select\"], [\"formControlName\", \"recipientType\", \"disabled\", \"\", 1, \"form-select\"], [1, \"col-md-12\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"form-button-group\"], [\"btnRadioGroup\", \"\", \"formControlName\", \"activeLanguage\", 1, \"form-button-group\"], [\"id\", \"addLanguageBtn\", 1, \"btn\", \"btn-outline-primary\"], [1, \"card-footer\"], [1, \"btn\", \"btn-outline-primary\", \"mw-150px\", 3, \"routerLink\"], [3, \"selected\"], [\"formControlName\", \"entryPoint\", 1, \"form-select\", 3, \"disabled\"], [\"value\", \"Account\"], [\"value\", \"User\"], [1, \"text-danger\", \"small\", \"mt-1\"], [\"formControlName\", \"recipientType\", 1, \"form-select\", 3, \"disabled\"], [\"value\", \"Customer\"], [\"value\", \"Agent\"], [1, \"form-radio-group\", \"disabled\"], [1, \"form-radio-group\", 3, \"disabled\"], [\"type\", \"radio\", \"name\", \"allowAccessView\", \"id\", \"allowAccessYes\", \"value\", \"true\", \"disabled\", \"\", 3, \"checked\"], [\"type\", \"radio\", \"name\", \"allowAccessView\", \"id\", \"allowAccessNo\", \"value\", \"false\", \"disabled\", \"\", 3, \"checked\"], [1, \"form-radio-group\"], [\"type\", \"radio\", \"name\", \"allowAccessFromAccount\", \"id\", \"allowAccessYes\", \"value\", \"true\", \"formControlName\", \"allowAccessFromAccount\", 3, \"disabled\"], [\"type\", \"radio\", \"name\", \"allowAccessFromAccount\", \"id\", \"allowAccessNo\", \"value\", \"false\", \"formControlName\", \"allowAccessFromAccount\", 3, \"disabled\"], [1, \"language-button-wrapper\"], [1, \"language-btn\", 3, \"click\"], [1, \"language-btn\", 3, \"btnRadio\", \"id\"], [1, \"remove-language-btn\", 3, \"title\"], [1, \"remove-language-btn\", 3, \"click\", \"title\"], [\"id\", \"addLanguageBtn\", 1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [\"src\", \"assets/new/svgs/language.svg\", 1, \"me-2\"], [1, \"col-md-6\"], [1, \"form-label\"], [\"type\", \"file\", \"accept\", \".jpg,.jpeg,.png,.pdf\", \"disabled\", \"\", 1, \"form-control\", 3, \"change\"], [\"rows\", \"7\", \"readonly\", \"\", 1, \"form-control\", \"form-control-readonly\", 3, \"value\"], [\"appTemplatePreview\", \"\", 3, \"template\", \"variables\"], [\"type\", \"text\", \"readonly\", \"\", 1, \"form-control\", \"form-control-readonly\", 3, \"value\"], [3, \"formArrayName\"], [3, \"formGroupName\"], [1, \"d-flex\", \"align-items-center\"], [\"tooltip\", \"Use <<Var>> to add variables that can be mapped to template variables\", \"placement\", \"right\", \"container\", \"body\", \"tooltipClass\", \"enc-tooltip\", 1, \"fa\", \"fa-info-circle\", \"text-primary\", \"info-icon\", \"ms-2\"], [1, \"position-relative\"], [\"type\", \"button\", \"tooltip\", \"Add Variable\", 1, \"btn\", \"btn-primary\", \"rounded\", \"rounded-5\", \"icon-btn\", \"position-absolute\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"add-var-btn\", 3, \"click\"], [\"src\", \"assets/new/svgs/var.svg\", \"width\", \"16\", \"height\", \"16\"], [\"type\", \"text\", \"placeholder\", \"Enter Email Subject\", \"id\", \"emailSubject\", \"formControlName\", \"emailSubject\", 1, \"form-control\"], [1, \"email-body-container\"], [3, \"editor\", \"toolbar\"], [3, \"editor\", \"ngModel\", \"ngModelOptions\", \"placeholder\", \"disabled\"], [1, \"editor-loading\"], [1, \"email-body-help-text\", \"mt-1\"], [1, \"text-muted\"], [1, \"fas\", \"fa-info-circle\", \"me-1\"], [3, \"ngModelChange\", \"editor\", \"ngModel\", \"ngModelOptions\", \"placeholder\", \"disabled\"], [\"placeholder\", \"Enter Approved Template Content\", \"id\", \"templateBody\", \"rows\", \"7\", \"formControlName\", \"templateBody\", \"appRestrictHtmlTags\", \"\", 1, \"form-control\", 3, \"allowCopyPasteOnly\"], [1, \"text-muted\", \"mt-1\", \"small\"], [1, \"fas\", \"fa-mobile-alt\", \"me-1\"], [1, \"variable-map-legends\"], [1, \"unmapped\"], [1, \"mapped\"], [\"appTemplateVarConfig\", \"\", 3, \"selectVar\", \"change\", \"template\"], [1, \"btn\", \"btn-secondary\", \"mw-150px\", \"me-4\", 3, \"click\"], [1, \"modal-header\", \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"modal-title\"], [\"src\", \"assets/new/svgs/side-drawer-close.svg\", \"alt\", \"Close\", 1, \"modal-close-btn\", 3, \"click\"], [1, \"modal-body\"], [\"bindLabel\", \"name\", \"bindValue\", \"name\", \"placeholder\", \"Select Database Field\", 1, \"form-ng-select\", 3, \"ngModelChange\", \"items\", \"ngModel\", \"clearable\", \"searchable\"], [1, \"modal-footer\", \"justify-content-center\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"mw-150px\", 3, \"click\", \"disabled\"], [\"bindLabel\", \"name\", \"bindValue\", \"code\", \"placeholder\", \"Select Language\", 1, \"form-ng-select\", 3, \"ngModelChange\", \"items\", \"ngModel\", \"clearable\", \"searchable\"]],\n        template: function CreateTemplateComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 3);\n            i0.ɵɵelement(1, \"app-breadcrumb\", 4);\n            i0.ɵɵelementStart(2, \"div\", 5)(3, \"h2\", 6);\n            i0.ɵɵtemplate(4, CreateTemplateComponent_Conditional_4_Template, 1, 0)(5, CreateTemplateComponent_Conditional_5_Template, 1, 0)(6, CreateTemplateComponent_Conditional_6_Template, 1, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(7, CreateTemplateComponent_Conditional_7_Template, 2, 0, \"button\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, CreateTemplateComponent_Conditional_8_Template, 7, 0, \"div\", 8)(9, CreateTemplateComponent_Conditional_9_Template, 52, 17, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, CreateTemplateComponent_ng_template_10_Template, 12, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(12, CreateTemplateComponent_ng_template_12_Template, 12, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"data\", ctx.breadcrumbData);\n            i0.ɵɵadvance(3);\n            i0.ɵɵconditional(ctx.isViewMode ? 4 : ctx.isEditMode ? 5 : 6);\n            i0.ɵɵadvance(3);\n            i0.ɵɵconditional(!ctx.isViewMode ? 7 : -1);\n            i0.ɵɵadvance();\n            i0.ɵɵconditional(ctx.isLoading ? 8 : 9);\n          }\n        },\n        dependencies: [FormsModule, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, SharedModule, i2.RouterLink, i3.ButtonRadioDirective, i3.ButtonRadioGroupDirective, i4.NgSelectComponent, i5.TooltipDirective, i6.SvgIconComponent, i7.BreadcrumbComponent, i8.RestrictHtmlTagsDirective, TemplateVarConfigDirective, TemplatePreviewDirective, NgxEditorModule, i9.NgxEditorComponent, i9.NgxEditorMenuComponent],\n        styles: [\".variable-map-legends[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2.5rem;\\n}\\n.variable-map-legends[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.variable-map-legends[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  width: 0.825rem;\\n  height: 0.825rem;\\n  border: solid 1px gray;\\n  background-color: lightgray;\\n  border-radius: 50%;\\n  left: -1.25rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n.variable-map-legends[_ngcontent-%COMP%]   label.mapped[_ngcontent-%COMP%]::before {\\n  background-color: #dcfce7;\\n  border-color: #86efac;\\n}\\n.variable-map-legends[_ngcontent-%COMP%]   label.unmapped[_ngcontent-%COMP%]::before {\\n  background-color: #fef9c3;\\n  border-color: #fde047;\\n}\\n\\n.language-button-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.language-btn[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-right: 2rem !important;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.language-btn.active[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  border-width: 2px;\\n}\\n.language-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n}\\n.language-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n  opacity: 0.6;\\n}\\n\\n.remove-language-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 0.5rem;\\n  transform: translateY(-50%);\\n  color: #ef4444;\\n  cursor: pointer;\\n  font-size: 1.2rem;\\n  font-weight: bold;\\n  line-height: 1;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  z-index: 10;\\n}\\n.remove-language-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #fef2f2;\\n  color: #dc2626;\\n  transform: translateY(-50%) scale(1.1);\\n}\\n\\n  .modal-close-btn {\\n  width: 1.625rem;\\n  height: 1.625rem;\\n  cursor: pointer;\\n  border-radius: 50%;\\n}\\n\\n.form-control-readonly[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa !important;\\n  color: #6c757d !important;\\n  cursor: not-allowed !important;\\n  opacity: 0.8;\\n}\\n\\n.form-radio-group.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  pointer-events: none;\\n}\\n.form-radio-group.disabled[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  cursor: not-allowed;\\n}\\n.form-radio-group.disabled[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  cursor: not-allowed;\\n}\\n\\n.text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin-bottom: 0.5rem;\\n  display: block;\\n}\\n\\n.form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\\n}\\n\\n.invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  margin-top: 0.25rem;\\n  font-size: 0.875rem;\\n  color: #dc3545;\\n}\\n\\ntextarea.form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\\n}\\n\\n.add-var-btn[_ngcontent-%COMP%] {\\n  top: 10px;\\n  right: 10px;\\n  z-index: 5;\\n}\\n\\n.email-body-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor {\\n  min-height: 200px !important;\\n  border: 2px solid #e1e5e9;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor:focus-within {\\n  border-color: #0d6efd;\\n  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor .NgxEditor__Content {\\n  min-height: 180px;\\n  padding: 12px;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor .NgxEditor__Content:has-text(\\\"<<\\\") {\\n  background-color: #fff9c4;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar {\\n  border-bottom: 1px solid #e1e5e9;\\n  background-color: #f8f9fa;\\n  padding: 8px 12px;\\n  border-radius: 6px 6px 0 0;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem {\\n  opacity: 1 !important;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem:disabled {\\n  opacity: 0.6 !important;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar {\\n  border-bottom: 1px solid #e1e5e9;\\n  background-color: #f8f9fa;\\n  padding: 8px;\\n  border-radius: 6px 6px 0 0;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem {\\n  margin: 2px;\\n  border-radius: 4px;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem:hover {\\n  background-color: #e9ecef;\\n}\\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem.NgxEditor__MenuItem--Active {\\n  background-color: #0d6efd;\\n  color: white;\\n}\\n.email-body-container[_ngcontent-%COMP%]   .email-body-input[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  background-color: #fafbfc;\\n  border: 2px solid #e1e5e9;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.email-body-container[_ngcontent-%COMP%]   .email-body-input[_ngcontent-%COMP%]:focus {\\n  background-color: #ffffff;\\n  border-color: #0d6efd;\\n  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);\\n}\\n.email-body-container[_ngcontent-%COMP%]   .email-body-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  background-color: #fff5f5;\\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);\\n}\\n.email-body-container[_ngcontent-%COMP%]   .email-body-help-text[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\n.email-body-container[_ngcontent-%COMP%]   .email-body-help-text[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.8rem;\\n  display: flex;\\n  align-items: center;\\n}\\n.email-body-container[_ngcontent-%COMP%]   .email-body-help-text[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  margin-right: 0.25rem;\\n}\\n.email-body-container[_ngcontent-%COMP%]   .editor-loading[_ngcontent-%COMP%] {\\n  min-height: 200px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #f8f9fa;\\n  border: 2px solid #e1e5e9;\\n  border-radius: 0 0 6px 6px;\\n  border-top: none;\\n}\\n.email-body-container[_ngcontent-%COMP%]   .editor-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin: 0;\\n  font-style: italic;\\n  font-size: 0.9rem;\\n}\\n\\n  .NgxEditor__Content p:has-text(\\\"<<\\\") {\\n  background-color: rgba(255, 249, 196, 0.3);\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\"]\n      });\n    }\n  }\n  return CreateTemplateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}