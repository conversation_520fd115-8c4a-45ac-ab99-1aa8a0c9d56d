{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { ScopeOfWorkComponent } from './scope-of-work.component';\ndescribe('ScopeOfWorkComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [ScopeOfWorkComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(ScopeOfWorkComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "ScopeOfWorkComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\user-management\\staff\\common\\scope-of-work\\scope-of-work.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\r\n\r\nimport { ScopeOfWorkComponent } from './scope-of-work.component';\r\n\r\ndescribe('ScopeOfWorkComponent', () => {\r\n  let component: ScopeOfWorkComponent;\r\n  let fixture: ComponentFixture<ScopeOfWorkComponent>;\r\n\r\n  beforeEach(waitForAsync(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ ScopeOfWorkComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(ScopeOfWorkComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhEC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EAEnDC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,oBAAoB;KACrC,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,oBAAoB,CAAC;IACvDE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}