{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SearchAndSendDuplicateEmailEReceiptAndSMSComponent } from './search-and-send-duplicate-email-e-receipt-and-sms.component';\ndescribe('SearchAndSendDuplicateEmailEReceiptAndSMSComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SearchAndSendDuplicateEmailEReceiptAndSMSComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SearchAndSendDuplicateEmailEReceiptAndSMSComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SearchAndSendDuplicateEmailEReceiptAndSMSComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\payments\\v1\\search-and-send-duplicate-email-e-receipt-and-sms\\search-and-send-duplicate-email-e-receipt-and-sms.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\r\n\r\nimport { SearchAndSendDuplicateEmailEReceiptAndSMSComponent } from './search-and-send-duplicate-email-e-receipt-and-sms.component';\r\n\r\ndescribe('SearchAndSendDuplicateEmailEReceiptAndSMSComponent', () => {\r\n  let component: SearchAndSendDuplicateEmailEReceiptAndSMSComponent;\r\n  let fixture: ComponentFixture<SearchAndSendDuplicateEmailEReceiptAndSMSComponent>;\r\n\r\n  beforeEach(waitForAsync(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ SearchAndSendDuplicateEmailEReceiptAndSMSComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(SearchAndSendDuplicateEmailEReceiptAndSMSComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,kDAAkD,QAAQ,+DAA+D;AAElIC,QAAQ,CAAC,oDAAoD,EAAE,MAAK;EAClE,IAAIC,SAA6D;EACjE,IAAIC,OAA6E;EAEjFC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,kDAAkD;KACnE,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,kDAAkD,CAAC;IACrFE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}