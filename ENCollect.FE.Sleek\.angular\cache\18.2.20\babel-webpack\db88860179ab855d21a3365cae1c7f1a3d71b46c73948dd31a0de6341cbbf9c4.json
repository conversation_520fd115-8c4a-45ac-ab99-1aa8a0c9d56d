{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nimport { ApiService } from \"../shared/services/api.service\";\nlet legalService = class legalService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  getAccountDetails(data) {\n    return this.apiService.post(\"api/mvp/cure/account/search\", data);\n  }\n  saveCure(data) {\n    return this.apiService.post(\"api/mvp/cure/add\", data);\n  }\n  getworkflowStatusList(data) {\n    return this.apiService.post(\"api/mvp/Legal/getAllWorkFlowStatus\", data);\n  }\n  searchCure(data) {\n    return this.apiService.post(\"api/mvp/mycure/search\", data);\n  }\n  searchLegalQueue(data) {\n    return this.apiService.post(\"api/mvp/LegalListofCase/search\", data);\n  }\n  getLegalDetails(data) {\n    return this.apiService.post(\"api/mvp/Legal/GetLegalDetails\", data);\n  }\n  getAllDespositionGroups() {\n    return this.apiService.get(\"api/mvp/dispositiongroupmaster\");\n  }\n  dispostionCode(val) {\n    return this.apiService.post(\"api/mvp/dispositionCodemaster\", val);\n  }\n  postTrailDetails(data) {\n    return this.apiService.post(\"api/mvp/cure/addfeedback\", data);\n  }\n  getTrailHistory(data) {\n    return this.apiService.post(\"api/mvp/cure/getallfeedback\", data);\n  }\n  uploadFile(formData) {\n    const options = {};\n    return this.apiService.post(\"api/document/cure/upload\", formData, options);\n  }\n  filePreviewDoc(data) {\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\n  }\n  updateDocuments(data) {\n    return this.apiService.post(\"api/uploaddocument/Save\", data);\n  }\n  getWorkflowStatus(data) {\n    return this.apiService.post(\"api/mvp/Legal/getNextWorkFlowStatus\", data);\n  }\n  updateWorkflowStatus(data) {\n    return this.apiService.post(\"api/mvp/Legal/UpdateWorkFlowStatus\", data);\n  }\n  getDocumentList(data) {\n    return this.apiService.get(\"api/mvp/CureDocuments/\" + data);\n  }\n  sendDocument(data) {\n    return this.apiService.post(\"api/mvp/cure/senddocumentemail\", data);\n  }\n  sendEmail(data) {\n    return this.apiService.post(\"api/mvp/cure/sendemail\", data);\n  }\n  getcureQueue(data) {\n    return this.apiService.post(\"api/mvp/queuetab/cure\", data);\n  }\n  uploadLegalFile(formData) {\n    const options = {};\n    return this.apiService.post(\"api/UploadFile\", formData, options);\n  }\n  legalAllocationUpload(data) {\n    return this.apiService.post(\"api/mvp/LegalCaseUpload\", data);\n  }\n  getLegalFileUploadStatus(data) {\n    return this.apiService.post(\"api/mvp/LegalCaseUpload/status\", data);\n  }\n  addCaseInitiation(data) {\n    return this.apiService.post(\"api/mvp/Legal/CaseInitiate\", data);\n  }\n  updateCaseInitiation(data) {\n    return this.apiService.post(\"api/mvp/Legal/Update/CaseInitiate\", data);\n  }\n  getCaseTypeList() {\n    return this.apiService.get(\"api/mvp/get/primaryCategoryItems?categoryMasterId=LegalCaseTypes\");\n  }\n  getProductList() {\n    return this.apiService.get(\"api/mvp/get/primaryCategoryItems?categoryMasterId=Product\");\n  }\n  getBucketList() {\n    return this.apiService.get(\"api/mvp/bucketmaster\");\n  }\n  getBranchList() {\n    return this.apiService.get(\"api/mvp/get/basebranches\");\n  }\n  getLegalAccountDetails(data) {\n    return this.apiService.post(\"api/mvp/Legal/GetAccountDetails\", data);\n  }\n  getMasterCountry() {\n    return this.apiService.get(\"api/mvp/Master/Country\");\n  }\n  regionReport(data) {\n    return this.apiService.post(\"api/mvp/Master/Region\", data);\n  }\n  stateReport(data) {\n    return this.apiService.post(\"api/mvp/Master/State\", data);\n  }\n  cityReport(data) {\n    return this.apiService.post(\"api/mvp/Master/City\", data);\n  }\n  addUpdateArbitration(data) {\n    return this.apiService.post(\"api/mvp/Legal/Update/Arbitration\", data);\n  }\n  addUpdatePetition(data) {\n    return this.apiService.post(\"api/mvp/Legal/Update/ExecutionPetition\", data);\n  }\n  addUpdateCaseFiling(data) {\n    return this.apiService.post(\"api/mvp/Legal/Update/CaseFilling\", data);\n  }\n  saveUploadedDocs(data) {\n    return this.apiService.post(\"api/Legal/uploaddocument/Save\", data);\n  }\n  getDocuments(data) {\n    return this.apiService.post(\"api/mvp/legal/AllDocuments\", data);\n  }\n  getWorkFlowHistory(data) {\n    return this.apiService.post(\"api/mvp/legal/WorkFlowhistory\", data);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ApiService\n    }];\n  }\n};\nlegalService = __decorate([Injectable({\n  providedIn: 'root'\n})], legalService);\nexport { legalService };", "map": {"version": 3, "names": ["Injectable", "ApiService", "legalService", "constructor", "apiService", "getAccountDetails", "data", "post", "saveCure", "getworkflowStatusList", "searchCure", "searchLegalQueue", "getLegalDetails", "getAllDespositionGroups", "get", "dispostionCode", "val", "postTrailDetails", "getTrailHistory", "uploadFile", "formData", "options", "filePreviewDoc", "getRawImage", "updateDocuments", "getWorkflowStatus", "updateWorkflowStatus", "getDocumentList", "sendDocument", "sendEmail", "getcureQueue", "uploadLegalFile", "legalAllocationUpload", "getLegalFileUploadStatus", "addCaseInitiation", "updateCaseInitiation", "getCaseTypeList", "getProductList", "getBucketList", "getBranchList", "getLegalAccountDetails", "getMasterCountry", "regionReport", "stateReport", "cityReport", "addUpdateArbitration", "addUpdatePetition", "addUpdateCaseFiling", "saveUploadedDocs", "getDocuments", "getWorkFlowHistory", "__decorate", "providedIn"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\legal.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ApiService } from \"../shared/services/api.service\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class legalService {\r\n  constructor(private apiService: ApiService) {}\r\n\r\n  getAccountDetails(data) {\r\n    return this.apiService.post(\"api/mvp/cure/account/search\", data);\r\n  }\r\n\r\n  saveCure(data) {\r\n    return this.apiService.post(\"api/mvp/cure/add\", data);\r\n  }\r\n\r\n  getworkflowStatusList(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/getAllWorkFlowStatus\", data);\r\n  }\r\n\r\n  searchCure(data) {\r\n    return this.apiService.post(\"api/mvp/mycure/search\", data);\r\n  }\r\n\r\n  searchLegalQueue(data) {\r\n    return this.apiService.post(\"api/mvp/LegalListofCase/search\", data);\r\n  }\r\n\r\n  getLegalDetails(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/GetLegalDetails\", data);\r\n  }\r\n\r\n  getAllDespositionGroups() {\r\n    return this.apiService.get(\"api/mvp/dispositiongroupmaster\");\r\n  }\r\n\r\n  dispostionCode(val) {\r\n    return this.apiService.post(\"api/mvp/dispositionCodemaster\", val);\r\n  }\r\n\r\n  postTrailDetails(data) {\r\n    return this.apiService.post(\"api/mvp/cure/addfeedback\", data);\r\n  }\r\n\r\n  getTrailHistory(data) {\r\n    return this.apiService.post(\"api/mvp/cure/getallfeedback\", data);\r\n  }\r\n\r\n  uploadFile(formData: FormData) {\r\n    const options = {};\r\n    return this.apiService.post(\"api/document/cure/upload\", formData, options);\r\n  }\r\n\r\n  filePreviewDoc(data) {\r\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\r\n  }\r\n\r\n  updateDocuments(data) {\r\n    return this.apiService.post(\"api/uploaddocument/Save\", data);\r\n  }\r\n\r\n  getWorkflowStatus(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/getNextWorkFlowStatus\", data);\r\n  }\r\n\r\n  updateWorkflowStatus(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/UpdateWorkFlowStatus\", data);\r\n  }\r\n\r\n  getDocumentList(data) {\r\n    return this.apiService.get(\"api/mvp/CureDocuments/\" + data);\r\n  }\r\n\r\n  sendDocument(data) {\r\n    return this.apiService.post(\"api/mvp/cure/senddocumentemail\", data);\r\n  }\r\n\r\n  sendEmail(data) {\r\n    return this.apiService.post(\"api/mvp/cure/sendemail\", data);\r\n  }\r\n\r\n  getcureQueue(data) {\r\n    return this.apiService.post(\"api/mvp/queuetab/cure\", data);\r\n  }\r\n\r\n  uploadLegalFile(formData: FormData) {\r\n    const options = {};\r\n    return this.apiService.post(\"api/UploadFile\", formData, options);\r\n  }\r\n\r\n  legalAllocationUpload(data) {\r\n    return this.apiService.post(\"api/mvp/LegalCaseUpload\", data);\r\n  }\r\n\r\n  getLegalFileUploadStatus(data) {\r\n    return this.apiService.post(\"api/mvp/LegalCaseUpload/status\", data);\r\n  }\r\n\r\n  addCaseInitiation(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/CaseInitiate\", data);\r\n  }\r\n\r\n  updateCaseInitiation(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/Update/CaseInitiate\", data);\r\n  }\r\n\r\n  getCaseTypeList() {\r\n    return this.apiService.get(\r\n      \"api/mvp/get/primaryCategoryItems?categoryMasterId=LegalCaseTypes\"\r\n    );\r\n  }\r\n\r\n  getProductList() {\r\n    return this.apiService.get(\r\n      \"api/mvp/get/primaryCategoryItems?categoryMasterId=Product\"\r\n    );\r\n  }\r\n\r\n  getBucketList() {\r\n    return this.apiService.get(\"api/mvp/bucketmaster\");\r\n  }\r\n\r\n  getBranchList() {\r\n    return this.apiService.get(\"api/mvp/get/basebranches\");\r\n  }\r\n\r\n  getLegalAccountDetails(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/GetAccountDetails\", data);\r\n  }\r\n\r\n  getMasterCountry() {\r\n    return this.apiService.get(\"api/mvp/Master/Country\");\r\n  }\r\n\r\n  regionReport(data) {\r\n    return this.apiService.post(\"api/mvp/Master/Region\", data);\r\n  }\r\n\r\n  stateReport(data) {\r\n    return this.apiService.post(\"api/mvp/Master/State\", data);\r\n  }\r\n\r\n  cityReport(data) {\r\n    return this.apiService.post(\"api/mvp/Master/City\", data);\r\n  }\r\n\r\n  addUpdateArbitration(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/Update/Arbitration\", data);\r\n  }\r\n\r\n  addUpdatePetition(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/Update/ExecutionPetition\", data);\r\n  }\r\n\r\n  addUpdateCaseFiling(data) {\r\n    return this.apiService.post(\"api/mvp/Legal/Update/CaseFilling\", data);\r\n  }\r\n\r\n  saveUploadedDocs(data) {\r\n    return this.apiService.post(\"api/Legal/uploaddocument/Save\", data);\r\n  }\r\n\r\n  getDocuments(data) {\r\n    return this.apiService.post(\"api/mvp/legal/AllDocuments\", data);\r\n  }\r\n\r\n  getWorkFlowHistory(data) {\r\n    return this.apiService.post(\"api/mvp/legal/WorkFlowhistory\", data);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,gCAAgC;AAKpD,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EACvBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAe;EAE7CC,iBAAiBA,CAACC,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,6BAA6B,EAAED,IAAI,CAAC;EAClE;EAEAE,QAAQA,CAACF,IAAI;IACX,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,kBAAkB,EAAED,IAAI,CAAC;EACvD;EAEAG,qBAAqBA,CAACH,IAAI;IACxB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,oCAAoC,EAAED,IAAI,CAAC;EACzE;EAEAI,UAAUA,CAACJ,IAAI;IACb,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,uBAAuB,EAAED,IAAI,CAAC;EAC5D;EAEAK,gBAAgBA,CAACL,IAAI;IACnB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,gCAAgC,EAAED,IAAI,CAAC;EACrE;EAEAM,eAAeA,CAACN,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,+BAA+B,EAAED,IAAI,CAAC;EACpE;EAEAO,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACT,UAAU,CAACU,GAAG,CAAC,gCAAgC,CAAC;EAC9D;EAEAC,cAAcA,CAACC,GAAG;IAChB,OAAO,IAAI,CAACZ,UAAU,CAACG,IAAI,CAAC,+BAA+B,EAAES,GAAG,CAAC;EACnE;EAEAC,gBAAgBA,CAACX,IAAI;IACnB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,0BAA0B,EAAED,IAAI,CAAC;EAC/D;EAEAY,eAAeA,CAACZ,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,6BAA6B,EAAED,IAAI,CAAC;EAClE;EAEAa,UAAUA,CAACC,QAAkB;IAC3B,MAAMC,OAAO,GAAG,EAAE;IAClB,OAAO,IAAI,CAACjB,UAAU,CAACG,IAAI,CAAC,0BAA0B,EAAEa,QAAQ,EAAEC,OAAO,CAAC;EAC5E;EAEAC,cAAcA,CAAChB,IAAI;IACjB,OAAO,IAAI,CAACF,UAAU,CAACmB,WAAW,CAAC,4BAA4B,GAAGjB,IAAI,CAAC;EACzE;EAEAkB,eAAeA,CAAClB,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,yBAAyB,EAAED,IAAI,CAAC;EAC9D;EAEAmB,iBAAiBA,CAACnB,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,qCAAqC,EAAED,IAAI,CAAC;EAC1E;EAEAoB,oBAAoBA,CAACpB,IAAI;IACvB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,oCAAoC,EAAED,IAAI,CAAC;EACzE;EAEAqB,eAAeA,CAACrB,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACU,GAAG,CAAC,wBAAwB,GAAGR,IAAI,CAAC;EAC7D;EAEAsB,YAAYA,CAACtB,IAAI;IACf,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,gCAAgC,EAAED,IAAI,CAAC;EACrE;EAEAuB,SAASA,CAACvB,IAAI;IACZ,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,wBAAwB,EAAED,IAAI,CAAC;EAC7D;EAEAwB,YAAYA,CAACxB,IAAI;IACf,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,uBAAuB,EAAED,IAAI,CAAC;EAC5D;EAEAyB,eAAeA,CAACX,QAAkB;IAChC,MAAMC,OAAO,GAAG,EAAE;IAClB,OAAO,IAAI,CAACjB,UAAU,CAACG,IAAI,CAAC,gBAAgB,EAAEa,QAAQ,EAAEC,OAAO,CAAC;EAClE;EAEAW,qBAAqBA,CAAC1B,IAAI;IACxB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,yBAAyB,EAAED,IAAI,CAAC;EAC9D;EAEA2B,wBAAwBA,CAAC3B,IAAI;IAC3B,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,gCAAgC,EAAED,IAAI,CAAC;EACrE;EAEA4B,iBAAiBA,CAAC5B,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,4BAA4B,EAAED,IAAI,CAAC;EACjE;EAEA6B,oBAAoBA,CAAC7B,IAAI;IACvB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,mCAAmC,EAAED,IAAI,CAAC;EACxE;EAEA8B,eAAeA,CAAA;IACb,OAAO,IAAI,CAAChC,UAAU,CAACU,GAAG,CACxB,kEAAkE,CACnE;EACH;EAEAuB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACjC,UAAU,CAACU,GAAG,CACxB,2DAA2D,CAC5D;EACH;EAEAwB,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClC,UAAU,CAACU,GAAG,CAAC,sBAAsB,CAAC;EACpD;EAEAyB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACnC,UAAU,CAACU,GAAG,CAAC,0BAA0B,CAAC;EACxD;EAEA0B,sBAAsBA,CAAClC,IAAI;IACzB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,iCAAiC,EAAED,IAAI,CAAC;EACtE;EAEAmC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACrC,UAAU,CAACU,GAAG,CAAC,wBAAwB,CAAC;EACtD;EAEA4B,YAAYA,CAACpC,IAAI;IACf,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,uBAAuB,EAAED,IAAI,CAAC;EAC5D;EAEAqC,WAAWA,CAACrC,IAAI;IACd,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,sBAAsB,EAAED,IAAI,CAAC;EAC3D;EAEAsC,UAAUA,CAACtC,IAAI;IACb,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,qBAAqB,EAAED,IAAI,CAAC;EAC1D;EAEAuC,oBAAoBA,CAACvC,IAAI;IACvB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,kCAAkC,EAAED,IAAI,CAAC;EACvE;EAEAwC,iBAAiBA,CAACxC,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,wCAAwC,EAAED,IAAI,CAAC;EAC7E;EAEAyC,mBAAmBA,CAACzC,IAAI;IACtB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,kCAAkC,EAAED,IAAI,CAAC;EACvE;EAEA0C,gBAAgBA,CAAC1C,IAAI;IACnB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,+BAA+B,EAAED,IAAI,CAAC;EACpE;EAEA2C,YAAYA,CAAC3C,IAAI;IACf,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,4BAA4B,EAAED,IAAI,CAAC;EACjE;EAEA4C,kBAAkBA,CAAC5C,IAAI;IACrB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,+BAA+B,EAAED,IAAI,CAAC;EACpE;;;;;;;AAnKWJ,YAAY,GAAAiD,UAAA,EAHxBnD,UAAU,CAAC;EACVoD,UAAU,EAAE;CACb,CAAC,C,EACWlD,YAAY,CAoKxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}