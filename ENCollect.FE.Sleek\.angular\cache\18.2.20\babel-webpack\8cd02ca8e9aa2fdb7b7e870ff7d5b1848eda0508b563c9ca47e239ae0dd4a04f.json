{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./legal-allocation-status.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./legal-allocation-status.component.css?ngResource\";\nexport class SearchControls {}\nimport { Component } from '@angular/core';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { ToastrService } from 'ngx-toastr';\nimport { legalService } from '../legal.service';\nimport { legalConfigService } from '../legalconfig.service';\nimport { PaginationsComponent } from '../common/paginations/paginations.component';\nlet LegalAllocationStatusComponent = class LegalAllocationStatusComponent extends PaginationsComponent {\n  constructor(toastr, modalService, legalService, legalConfigService) {\n    super();\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.legalService = legalService;\n    this.legalConfigService = legalConfigService;\n    this.breadcrumbData = [{\n      label: \"legal\",\n      path: \"/encollect/legal/legal-allocation-status\"\n    }, {\n      label: \"Upload Status of Account Details file for Legal\",\n      path: \"/encollect/legal/legal-allocation-status\"\n    }];\n    this.searchControls = new SearchControls();\n    this.results = [];\n    this.currentRecords = [];\n    this.variableInit();\n    this.statusList = this.legalConfigService.legalAllocationStatusList();\n  }\n  variableInit() {\n    this.searchControls = {\n      fileName: '',\n      fileuploadDate: '',\n      status: '',\n      transactionId: ''\n    };\n    this.maxDate = new Date();\n    this.loader = {\n      isSearching: false\n    };\n  }\n  ngOnInit() {}\n  searchStatus() {\n    if (this.searchControls.fileName == '' && this.searchControls.status == '' && this.searchControls.transactionId == '' && this.searchControls.fileuploadDate == '') {\n      this.toastr.warning(\"Enter at least one filter value\");\n      return false;\n    }\n    this.loader.isSearching = true;\n    this.legalService.getLegalFileUploadStatus(this.searchControls).subscribe(response => {\n      this.loader.isSearching = false;\n      if (response.length === 0) {\n        this.toastr.info('No results found!');\n        return false;\n      }\n      this.results = response;\n      this.currentRecords = super.fetchRecordsByPage(1);\n    }, err => {\n      this.toastr.error(err);\n      this.loader.isSearching = false;\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: legalService\n    }, {\n      type: legalConfigService\n    }];\n  }\n};\nLegalAllocationStatusComponent = __decorate([Component({\n  selector: 'app-legal-allocation-status',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], LegalAllocationStatusComponent);\nexport { LegalAllocationStatusComponent };", "map": {"version": 3, "names": ["SearchControls", "Component", "BsModalService", "ToastrService", "legalService", "legalConfigService", "PaginationsComponent", "LegalAllocationStatusComponent", "constructor", "toastr", "modalService", "breadcrumbData", "label", "path", "searchControls", "results", "currentRecords", "variableInit", "statusList", "legalAllocationStatusList", "fileName", "fileuploadDate", "status", "transactionId", "maxDate", "Date", "loader", "isSearching", "ngOnInit", "searchStatus", "warning", "getLegalFileUploadStatus", "subscribe", "response", "length", "info", "fetchRecordsByPage", "err", "error", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\legal-allocation-status\\legal-allocation-status.component.ts"], "sourcesContent": ["export class SearchControls{\r\n  fileName: string;\r\n    fileuploadDate: string;\r\n    status: string;\r\n    transactionId: string;\r\n}\r\nexport interface Loader{\r\n  isSearching: boolean;\r\n}\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { BsModalService } from 'ngx-bootstrap/modal';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { legalService } from '../legal.service';\r\nimport { legalConfigService } from '../legalconfig.service';\r\nimport { PaginationsComponent } from '../common/paginations/paginations.component'\r\n\r\n@Component({\r\n  selector: 'app-legal-allocation-status',\r\n  templateUrl: './legal-allocation-status.component.html',\r\n  styleUrls: ['./legal-allocation-status.component.css']\r\n})\r\nexport class LegalAllocationStatusComponent extends PaginationsComponent implements OnInit {\r\n  public breadcrumbData = [\r\n    { label: \"legal\", path: \"/encollect/legal/legal-allocation-status\" },\r\n    { label: \"Upload Status of Account Details file for Legal\", path: \"/encollect/legal/legal-allocation-status\" },\r\n  ];\r\n  searchControls: SearchControls = new SearchControls();\r\n  loader: Loader;\r\n  maxDate: Date;\r\n  statusList: Array<string>;\r\n  results= [];\r\n  currentRecords = [];\r\n\r\n  constructor(public toastr: ToastrService,\r\n    private modalService: BsModalService,private legalService: legalService,\r\n    private legalConfigService: legalConfigService) {\r\n    super()\r\n  \tthis.variableInit();\r\n  \tthis.statusList= this.legalConfigService.legalAllocationStatusList()\r\n  }\r\n\r\n  variableInit(){\r\n    this.searchControls = {\r\n        fileName: '',\r\n        fileuploadDate: '',\r\n        status: '',\r\n        transactionId: ''\r\n    }\r\n    this.maxDate = new Date();\r\n    this.loader = {\r\n      isSearching: false\r\n    }\r\n}\r\n\r\n  ngOnInit() {\r\n  }\r\n  searchStatus(){\r\n    if( this.searchControls.fileName=='' && this.searchControls.status==''\r\n      && this.searchControls.transactionId=='' &&this.searchControls.fileuploadDate==''){\r\n     this.toastr.warning(\"Enter at least one filter value\");\r\n     return false;\r\n  }\r\n  this.loader.isSearching =  true\r\n  this.legalService\r\n    .getLegalFileUploadStatus(this.searchControls)\r\n    .subscribe(response => {\r\n       this.loader.isSearching =  false\r\n     if (response.length === 0) {\r\n       this.toastr.info('No results found!');\r\n       return false\r\n     }\r\n      this.results = response\r\n      this.currentRecords = super.fetchRecordsByPage(1);\r\n    },err=>{\r\n      this.toastr.error(err)\r\n      this.loader.isSearching =  false\r\n    })\r\n}\r\n\r\n}\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;AAS3B,SAASC,SAAS,QAAgB,eAAe;AACjD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,oBAAoB,QAAQ,6CAA6C;AAO3E,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQD,oBAAoB;EAYtEE,YAAmBC,MAAqB,EAC9BC,YAA4B,EAASN,YAA0B,EAC/DC,kBAAsC;IAC9C,KAAK,EAAE;IAHU,KAAAI,MAAM,GAANA,MAAM;IACf,KAAAC,YAAY,GAAZA,YAAY;IAAyB,KAAAN,YAAY,GAAZA,YAAY;IACjD,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAbrB,KAAAM,cAAc,GAAG,CACtB;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAA0C,CAAE,EACpE;MAAED,KAAK,EAAE,iDAAiD;MAAEC,IAAI,EAAE;IAA0C,CAAE,CAC/G;IACD,KAAAC,cAAc,GAAmB,IAAId,cAAc,EAAE;IAIrD,KAAAe,OAAO,GAAE,EAAE;IACX,KAAAC,cAAc,GAAG,EAAE;IAMlB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,UAAU,GAAE,IAAI,CAACb,kBAAkB,CAACc,yBAAyB,EAAE;EACrE;EAEAF,YAAYA,CAAA;IACV,IAAI,CAACH,cAAc,GAAG;MAClBM,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAE,EAAE;MACVC,aAAa,EAAE;KAClB;IACD,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE;IACzB,IAAI,CAACC,MAAM,GAAG;MACZC,WAAW,EAAE;KACd;EACL;EAEEC,QAAQA,CAAA,GACR;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACf,cAAc,CAACM,QAAQ,IAAE,EAAE,IAAI,IAAI,CAACN,cAAc,CAACQ,MAAM,IAAE,EAAE,IACjE,IAAI,CAACR,cAAc,CAACS,aAAa,IAAE,EAAE,IAAG,IAAI,CAACT,cAAc,CAACO,cAAc,IAAE,EAAE,EAAC;MACnF,IAAI,CAACZ,MAAM,CAACqB,OAAO,CAAC,iCAAiC,CAAC;MACtD,OAAO,KAAK;IACf;IACA,IAAI,CAACJ,MAAM,CAACC,WAAW,GAAI,IAAI;IAC/B,IAAI,CAACvB,YAAY,CACd2B,wBAAwB,CAAC,IAAI,CAACjB,cAAc,CAAC,CAC7CkB,SAAS,CAACC,QAAQ,IAAG;MACnB,IAAI,CAACP,MAAM,CAACC,WAAW,GAAI,KAAK;MAClC,IAAIM,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;QACzB,IAAI,CAACzB,MAAM,CAAC0B,IAAI,CAAC,mBAAmB,CAAC;QACrC,OAAO,KAAK;MACd;MACC,IAAI,CAACpB,OAAO,GAAGkB,QAAQ;MACvB,IAAI,CAACjB,cAAc,GAAG,KAAK,CAACoB,kBAAkB,CAAC,CAAC,CAAC;IACnD,CAAC,EAACC,GAAG,IAAE;MACL,IAAI,CAAC5B,MAAM,CAAC6B,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAACX,MAAM,CAACC,WAAW,GAAI,KAAK;IAClC,CAAC,CAAC;EACN;;;;;;;;;;;;;AAxDapB,8BAA8B,GAAAgC,UAAA,EAL1CtC,SAAS,CAAC;EACTuC,QAAQ,EAAE,6BAA6B;EACvCC,QAAA,EAAAC,oBAAuD;;CAExD,CAAC,C,EACWnC,8BAA8B,CA0D1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}