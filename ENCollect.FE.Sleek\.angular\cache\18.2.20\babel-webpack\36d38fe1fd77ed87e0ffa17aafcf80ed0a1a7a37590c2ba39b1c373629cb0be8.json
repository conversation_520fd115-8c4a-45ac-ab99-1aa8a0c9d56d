{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { SideDrawerComponent } from './side-drawer.component';\ndescribe('SideDrawerComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [SideDrawerComponent]\n    });\n    fixture = TestBed.createComponent(SideDrawerComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "SideDrawerComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\side-drawer\\side-drawer.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { SideDrawerComponent } from './side-drawer.component';\r\n\r\ndescribe('SideDrawerComponent', () => {\r\n  let component: SideDrawerComponent;\r\n  let fixture: ComponentFixture<SideDrawerComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [SideDrawerComponent]\r\n    });\r\n    fixture = TestBed.createComponent(SideDrawerComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,SAA8B;EAClC,IAAIC,OAA8C;EAElDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,mBAAmB;KACnC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,mBAAmB,CAAC;IACtDE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}