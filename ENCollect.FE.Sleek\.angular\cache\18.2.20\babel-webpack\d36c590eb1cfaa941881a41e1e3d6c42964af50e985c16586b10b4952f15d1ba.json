{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./allocation-bucket-config.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./allocation-bucket-config.component.css?ngResource\";\nimport { Component, ChangeDetectorRef } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { SettingsService } from '../settings.service';\nimport { SettingsConfigService } from '../settingsconfig.service';\nlet AllocationBucketConfigComponent = class AllocationBucketConfigComponent {\n  constructor(toastr, settingService, settingConfigService, detectchanges) {\n    this.toastr = toastr;\n    this.settingService = settingService;\n    this.settingConfigService = settingConfigService;\n    this.detectchanges = detectchanges;\n    this.breadcrumbData = [{\n      label: \"Settings Allocations\",\n      path: \"/settings/allocation-bucket-config\"\n    }, {\n      label: \"Bucket Configurations\",\n      path: \"/settings/allocation-bucket-config\"\n    }];\n    // BucketDataMain: Array<any>;\n    this.BucketDataMain = [];\n    this.Dayspastduefrom = [];\n    this.BOMbucket = [];\n    this.showAddBuketButton = false;\n    this.buketDataToremove = [];\n    this.bucketList = [];\n    this.loader = {\n      isSearching: false\n    };\n  }\n  ngOnInit() {\n    this.getBucketData();\n  }\n  addBucket() {\n    let obj = {\n      id: \"\",\n      DaysPastDue: '',\n      DaysPastDueTo: '',\n      BomBucket: '',\n      CurrentBucketName: '',\n      DefaultRemarks: ''\n    };\n    this.BucketDataMain.push(obj);\n  }\n  removeBucket(i, data) {\n    if (this.BucketDataMain.length > 1) {\n      if (data.id != \"\") {\n        data[\"isDeleted\"] = true;\n        this.buketDataToremove.push(data);\n      }\n      this.BucketDataMain.splice(i, 1);\n    } else {\n      this.toastr.warning(\"Atleast one record is required\");\n    }\n  }\n  // for dynamic data working\n  getBucketData() {\n    this.detectchanges;\n    let data = [];\n    this.settingService.getBuketConfigurations().subscribe(resp => {\n      this.detectchanges;\n      data = resp; // for user having data\n      if (data != null && data.length > 0) {\n        this.BucketDataMain = data;\n        this.showAddBuketButton = false;\n      } else {\n        this.showAddBuketButton = true;\n        this.addBucket();\n      }\n    }, error => {\n      this.toastr.error(error);\n    });\n  }\n  // new save\n  saveBucket() {\n    this.loader.isSearching = true;\n    if (this.BucketDataMain.length > 0 && this.showAddBuketButton === false) {\n      // edit part\n      // delete part before edit\n      if (this.buketDataToremove.length > 0) {\n        var inputParams = [];\n        for (let i = 0; i < this.buketDataToremove.length; i++) {\n          inputParams.push(this.buketDataToremove[i].id);\n        }\n        let inputDeleteID = {\n          BucketMasterIds: inputParams\n        };\n        this.settingService.deleteBucketConfiguration(inputDeleteID).subscribe(resp => {\n          this.toastr.success(\"Deleted Success\");\n        });\n      }\n      //\n      // edit - part\n      var dataToPush = [];\n      this.BucketDataMain.forEach(elem1 => {\n        let obj = {\n          Id: elem1.id,\n          DaysPastDue: elem1.daysPastDue,\n          BomBucket: elem1.bomBucket,\n          CurrentBucketName: elem1.currentBucketName,\n          DefaultRemarks: elem1.defaultRemarks\n        };\n        dataToPush.push(obj);\n      });\n      let inputJson = {\n        BucketMasterDetails: dataToPush\n      };\n      this.settingService.editBucketConfiguration(inputJson).subscribe(resp => {\n        if (resp === 'Success') {\n          this.detectchanges;\n          setTimeout(() => {\n            this.ngOnInit();\n            this.loader.isSearching = false;\n            this.toastr.success(resp);\n          }, 10000);\n        } else {\n          this.loader.isSearching = false;\n          this.toastr.error(resp);\n        }\n      }, error => {\n        this.loader.isSearching = false;\n        this.toastr.error(error);\n      });\n      // edit ends\n    } else {\n      let dataToPush1 = [];\n      this.BucketDataMain.forEach(elem1 => {\n        let obj = {\n          DaysPastDue: elem1.daysPastDue,\n          BomBucket: elem1.bomBucket,\n          CurrentBucketName: elem1.currentBucketName,\n          DefaultRemarks: elem1.defaultRemarks\n        };\n        dataToPush1.push(obj);\n      });\n      let inputJson = {\n        BucketMasterDetails: dataToPush1\n      };\n      this.settingService.addBucketConfiguration(inputJson).subscribe(resp => {\n        if (resp === 'Success') {\n          setTimeout(() => {\n            this.ngOnInit();\n            this.loader.isSearching = false;\n            this.toastr.success(resp);\n          }, 10000);\n        } else {\n          this.loader.isSearching = false;\n          this.toastr.error(resp);\n        }\n      }, error => {\n        this.loader.isSearching = false;\n        this.toastr.error(error);\n      });\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: SettingsService\n    }, {\n      type: SettingsConfigService\n    }, {\n      type: ChangeDetectorRef\n    }];\n  }\n};\nAllocationBucketConfigComponent = __decorate([Component({\n  selector: 'app-allocation-bucket-config',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AllocationBucketConfigComponent);\nexport { AllocationBucketConfigComponent };", "map": {"version": 3, "names": ["Component", "ChangeDetectorRef", "ToastrService", "SettingsService", "SettingsConfigService", "AllocationBucketConfigComponent", "constructor", "toastr", "settingService", "settingConfigService", "detectchanges", "breadcrumbData", "label", "path", "BucketDataMain", "Dayspastduefrom", "BOMbucket", "showAddBuketButton", "buketDataToremove", "bucketList", "loader", "isSearching", "ngOnInit", "getBucketData", "addBucket", "obj", "id", "DaysPastDue", "DaysPastDueTo", "BomBucket", "CurrentBucketName", "DefaultRemarks", "push", "removeBucket", "i", "data", "length", "splice", "warning", "getBuketConfigurations", "subscribe", "resp", "error", "saveBucket", "inputParams", "inputDeleteID", "BucketMasterIds", "deleteBucketConfiguration", "success", "dataToPush", "for<PERSON>ach", "elem1", "Id", "daysPastDue", "bomBucket", "currentBucketName", "defaultRemarks", "inputJson", "BucketMasterDetails", "editBucketConfiguration", "setTimeout", "dataToPush1", "addBucketConfiguration", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\allocation-bucket-config\\allocation-bucket-config.component.ts"], "sourcesContent": ["export interface Loader{\r\n    isSearching: boolean;\r\n}\r\nimport { Component, OnInit,TemplateRef, ChangeDetectorRef } from '@angular/core';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\nimport { SettingsService } from '../settings.service';\r\nimport { SettingsConfigService } from '../settingsconfig.service';\r\n\r\n@Component({\r\n  selector: 'app-allocation-bucket-config',\r\n  templateUrl: './allocation-bucket-config.component.html',\r\n  styleUrls: ['./allocation-bucket-config.component.css']\r\n})\r\nexport class AllocationBucketConfigComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Settings Allocations\", path: \"/settings/allocation-bucket-config\" },\r\n\t\t{ label: \"Bucket Configurations\", path: \"/settings/allocation-bucket-config\" },\r\n\t  ]\r\n  loader: Loader;\r\n  bucketList: any;\r\n\r\n  // BucketDataMain: Array<any>;\r\n  BucketDataMain = [];\r\n  Dayspastduefrom = [];\r\n  BOMbucket = []\r\n\r\n  showAddBuketButton = false;\r\n  buketDataToremove = []\r\n\r\n\r\n  constructor(public toastr: ToastrService, \r\n              private settingService: SettingsService,\r\n              private settingConfigService: SettingsConfigService,\r\n              private detectchanges : ChangeDetectorRef) {\r\n    this.bucketList = [];\r\n    \r\n    this.loader = {\r\n        isSearching: false\r\n    }\r\n  }\r\n\r\n\r\n  ngOnInit() {\r\n      this.getBucketData()\r\n  }\r\n  addBucket(){\r\n    let obj = {\r\n      id:\"\",\r\n      DaysPastDue: '',\r\n      DaysPastDueTo:'',\r\n      BomBucket: '',\r\n      CurrentBucketName: '',\r\n      DefaultRemarks: ''\r\n  }\r\n    this.BucketDataMain.push(obj)\r\n  }\r\n\r\n\r\n  removeBucket(i, data){\r\n    if(this.BucketDataMain.length>1){\r\n       if(data.id !=\"\"){\r\n        data[\"isDeleted\"] = true\r\n        this.buketDataToremove.push(data)\r\n      }\r\n      this.BucketDataMain.splice(i, 1);\r\n    }else{\r\n      this.toastr.warning(\"Atleast one record is required\");\r\n    }  \r\n  }\r\n\r\n  // for dynamic data working\r\n  getBucketData(){  \r\n    this.detectchanges \r\n    let data = []\r\n    this.settingService.getBuketConfigurations().subscribe(resp=>{\r\n      this.detectchanges \r\n      data =  resp  // for user having data\r\n      if(data != null && data.length > 0){\r\n        this.BucketDataMain = data\r\n        this.showAddBuketButton = false;  \r\n      }else {\r\n        this.showAddBuketButton = true;\r\n        this.addBucket()\r\n      } \r\n    }, error =>{\r\n      this.toastr.error(error)\r\n    })\r\n  }\r\n\r\n  // new save\r\n  saveBucket(){\r\n    this.loader.isSearching = true\r\n    if(this.BucketDataMain.length> 0 && this.showAddBuketButton === false){   // edit part\r\n      // delete part before edit\r\n      if(this.buketDataToremove.length > 0){\r\n        var inputParams = []\r\n        for(let i=0; i<this.buketDataToremove.length; i++){\r\n          inputParams.push(this.buketDataToremove[i].id)\r\n        } \r\n        let inputDeleteID = {\r\n          BucketMasterIds: inputParams\r\n        }\r\n        this.settingService.deleteBucketConfiguration(inputDeleteID).subscribe(resp => {\r\n           this.toastr.success(\"Deleted Success\")\r\n        })\r\n      }\r\n      //\r\n      // edit - part\r\n      var dataToPush = []\r\n      this.BucketDataMain.forEach(elem1 => {\r\n          let obj = {\r\n            Id: elem1.id,\r\n            DaysPastDue: elem1.daysPastDue,\r\n            BomBucket: elem1.bomBucket,\r\n            CurrentBucketName: elem1.currentBucketName,\r\n            DefaultRemarks: elem1.defaultRemarks\r\n          }\r\n          dataToPush.push(obj)\r\n      });\r\n      let inputJson = {\r\n        BucketMasterDetails: dataToPush\r\n      }\r\n      this.settingService.editBucketConfiguration(inputJson).subscribe(resp=>{\r\n        if(resp === 'Success'){\r\n          this.detectchanges;\r\n          setTimeout(() => {   \r\n            this.ngOnInit()\r\n            this.loader.isSearching = false;\r\n            this.toastr.success(resp)\r\n          },10000)\r\n        }else{\r\n          this.loader.isSearching = false;\r\n          this.toastr.error(resp)\r\n        }\r\n      }, error => {\r\n        this.loader.isSearching = false;\r\n        this.toastr.error(error)\r\n      })\r\n      // edit ends\r\n    } else {    \r\n      let dataToPush1 = []\r\n      this.BucketDataMain.forEach(elem1 => {\r\n        let obj = {\r\n          DaysPastDue: elem1.daysPastDue,\r\n          BomBucket: elem1.bomBucket,\r\n          CurrentBucketName: elem1.currentBucketName,\r\n          DefaultRemarks: elem1.defaultRemarks\r\n        }\r\n        dataToPush1.push(obj)\r\n      });\r\n      let inputJson = {\r\n        BucketMasterDetails: dataToPush1\r\n      }\r\n        this.settingService.addBucketConfiguration(inputJson).subscribe(resp=>{\r\n        if(resp === 'Success'){\r\n          setTimeout(() => {   \r\n            this.ngOnInit()\r\n            this.loader.isSearching = false;\r\n            this.toastr.success(resp)\r\n          },10000)\r\n\r\n        }else{\r\n          this.loader.isSearching = false;\r\n          this.toastr.error(resp)\r\n        }\r\n      }, error => {\r\n        this.loader.isSearching = false;\r\n        this.toastr.error(error)\r\n      })\r\n    } \r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;AAGA,SAASA,SAAS,EAAsBC,iBAAiB,QAAQ,eAAe;AAEhF,SAASC,aAAa,QAAQ,YAAY;AAG1C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,qBAAqB,QAAQ,2BAA2B;AAO1D,IAAMC,+BAA+B,GAArC,MAAMA,+BAA+B;EAiB1CC,YAAmBC,MAAqB,EACpBC,cAA+B,EAC/BC,oBAA2C,EAC3CC,aAAiC;IAHlC,KAAAH,MAAM,GAANA,MAAM;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,aAAa,GAAbA,aAAa;IAnB1B,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,sBAAsB;MAAEC,IAAI,EAAE;IAAoC,CAAE,EAC7E;MAAED,KAAK,EAAE,uBAAuB;MAAEC,IAAI,EAAE;IAAoC,CAAE,CAC5E;IAIF;IACA,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,SAAS,GAAG,EAAE;IAEd,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,iBAAiB,GAAG,EAAE;IAOpB,IAAI,CAACC,UAAU,GAAG,EAAE;IAEpB,IAAI,CAACC,MAAM,GAAG;MACVC,WAAW,EAAE;KAChB;EACH;EAGAC,QAAQA,CAAA;IACJ,IAAI,CAACC,aAAa,EAAE;EACxB;EACAC,SAASA,CAAA;IACP,IAAIC,GAAG,GAAG;MACRC,EAAE,EAAC,EAAE;MACLC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAC,EAAE;MAChBC,SAAS,EAAE,EAAE;MACbC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE;KACnB;IACC,IAAI,CAACjB,cAAc,CAACkB,IAAI,CAACP,GAAG,CAAC;EAC/B;EAGAQ,YAAYA,CAACC,CAAC,EAAEC,IAAI;IAClB,IAAG,IAAI,CAACrB,cAAc,CAACsB,MAAM,GAAC,CAAC,EAAC;MAC7B,IAAGD,IAAI,CAACT,EAAE,IAAG,EAAE,EAAC;QACfS,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI;QACxB,IAAI,CAACjB,iBAAiB,CAACc,IAAI,CAACG,IAAI,CAAC;MACnC;MACA,IAAI,CAACrB,cAAc,CAACuB,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC,MAAI;MACH,IAAI,CAAC3B,MAAM,CAAC+B,OAAO,CAAC,gCAAgC,CAAC;IACvD;EACF;EAEA;EACAf,aAAaA,CAAA;IACX,IAAI,CAACb,aAAa;IAClB,IAAIyB,IAAI,GAAG,EAAE;IACb,IAAI,CAAC3B,cAAc,CAAC+B,sBAAsB,EAAE,CAACC,SAAS,CAACC,IAAI,IAAE;MAC3D,IAAI,CAAC/B,aAAa;MAClByB,IAAI,GAAIM,IAAI,EAAE;MACd,IAAGN,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAC;QACjC,IAAI,CAACtB,cAAc,GAAGqB,IAAI;QAC1B,IAAI,CAAClB,kBAAkB,GAAG,KAAK;MACjC,CAAC,MAAK;QACJ,IAAI,CAACA,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACO,SAAS,EAAE;MAClB;IACF,CAAC,EAAEkB,KAAK,IAAG;MACT,IAAI,CAACnC,MAAM,CAACmC,KAAK,CAACA,KAAK,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEA;EACAC,UAAUA,CAAA;IACR,IAAI,CAACvB,MAAM,CAACC,WAAW,GAAG,IAAI;IAC9B,IAAG,IAAI,CAACP,cAAc,CAACsB,MAAM,GAAE,CAAC,IAAI,IAAI,CAACnB,kBAAkB,KAAK,KAAK,EAAC;MAAI;MACxE;MACA,IAAG,IAAI,CAACC,iBAAiB,CAACkB,MAAM,GAAG,CAAC,EAAC;QACnC,IAAIQ,WAAW,GAAG,EAAE;QACpB,KAAI,IAAIV,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAAChB,iBAAiB,CAACkB,MAAM,EAAEF,CAAC,EAAE,EAAC;UAChDU,WAAW,CAACZ,IAAI,CAAC,IAAI,CAACd,iBAAiB,CAACgB,CAAC,CAAC,CAACR,EAAE,CAAC;QAChD;QACA,IAAImB,aAAa,GAAG;UAClBC,eAAe,EAAEF;SAClB;QACD,IAAI,CAACpC,cAAc,CAACuC,yBAAyB,CAACF,aAAa,CAAC,CAACL,SAAS,CAACC,IAAI,IAAG;UAC3E,IAAI,CAAClC,MAAM,CAACyC,OAAO,CAAC,iBAAiB,CAAC;QACzC,CAAC,CAAC;MACJ;MACA;MACA;MACA,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAI,CAACnC,cAAc,CAACoC,OAAO,CAACC,KAAK,IAAG;QAChC,IAAI1B,GAAG,GAAG;UACR2B,EAAE,EAAED,KAAK,CAACzB,EAAE;UACZC,WAAW,EAAEwB,KAAK,CAACE,WAAW;UAC9BxB,SAAS,EAAEsB,KAAK,CAACG,SAAS;UAC1BxB,iBAAiB,EAAEqB,KAAK,CAACI,iBAAiB;UAC1CxB,cAAc,EAAEoB,KAAK,CAACK;SACvB;QACDP,UAAU,CAACjB,IAAI,CAACP,GAAG,CAAC;MACxB,CAAC,CAAC;MACF,IAAIgC,SAAS,GAAG;QACdC,mBAAmB,EAAET;OACtB;MACD,IAAI,CAACzC,cAAc,CAACmD,uBAAuB,CAACF,SAAS,CAAC,CAACjB,SAAS,CAACC,IAAI,IAAE;QACrE,IAAGA,IAAI,KAAK,SAAS,EAAC;UACpB,IAAI,CAAC/B,aAAa;UAClBkD,UAAU,CAAC,MAAK;YACd,IAAI,CAACtC,QAAQ,EAAE;YACf,IAAI,CAACF,MAAM,CAACC,WAAW,GAAG,KAAK;YAC/B,IAAI,CAACd,MAAM,CAACyC,OAAO,CAACP,IAAI,CAAC;UAC3B,CAAC,EAAC,KAAK,CAAC;QACV,CAAC,MAAI;UACH,IAAI,CAACrB,MAAM,CAACC,WAAW,GAAG,KAAK;UAC/B,IAAI,CAACd,MAAM,CAACmC,KAAK,CAACD,IAAI,CAAC;QACzB;MACF,CAAC,EAAEC,KAAK,IAAG;QACT,IAAI,CAACtB,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B,IAAI,CAACd,MAAM,CAACmC,KAAK,CAACA,KAAK,CAAC;MAC1B,CAAC,CAAC;MACF;IACF,CAAC,MAAM;MACL,IAAImB,WAAW,GAAG,EAAE;MACpB,IAAI,CAAC/C,cAAc,CAACoC,OAAO,CAACC,KAAK,IAAG;QAClC,IAAI1B,GAAG,GAAG;UACRE,WAAW,EAAEwB,KAAK,CAACE,WAAW;UAC9BxB,SAAS,EAAEsB,KAAK,CAACG,SAAS;UAC1BxB,iBAAiB,EAAEqB,KAAK,CAACI,iBAAiB;UAC1CxB,cAAc,EAAEoB,KAAK,CAACK;SACvB;QACDK,WAAW,CAAC7B,IAAI,CAACP,GAAG,CAAC;MACvB,CAAC,CAAC;MACF,IAAIgC,SAAS,GAAG;QACdC,mBAAmB,EAAEG;OACtB;MACC,IAAI,CAACrD,cAAc,CAACsD,sBAAsB,CAACL,SAAS,CAAC,CAACjB,SAAS,CAACC,IAAI,IAAE;QACtE,IAAGA,IAAI,KAAK,SAAS,EAAC;UACpBmB,UAAU,CAAC,MAAK;YACd,IAAI,CAACtC,QAAQ,EAAE;YACf,IAAI,CAACF,MAAM,CAACC,WAAW,GAAG,KAAK;YAC/B,IAAI,CAACd,MAAM,CAACyC,OAAO,CAACP,IAAI,CAAC;UAC3B,CAAC,EAAC,KAAK,CAAC;QAEV,CAAC,MAAI;UACH,IAAI,CAACrB,MAAM,CAACC,WAAW,GAAG,KAAK;UAC/B,IAAI,CAACd,MAAM,CAACmC,KAAK,CAACD,IAAI,CAAC;QACzB;MACF,CAAC,EAAEC,KAAK,IAAG;QACT,IAAI,CAACtB,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B,IAAI,CAACd,MAAM,CAACmC,KAAK,CAACA,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF;;;;;;;;;;;;;AA7JWrC,+BAA+B,GAAA0D,UAAA,EAL3C/D,SAAS,CAAC;EACTgE,QAAQ,EAAE,8BAA8B;EACxCC,QAAA,EAAAC,oBAAwD;;CAEzD,CAAC,C,EACW7D,+BAA+B,CA8J3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}