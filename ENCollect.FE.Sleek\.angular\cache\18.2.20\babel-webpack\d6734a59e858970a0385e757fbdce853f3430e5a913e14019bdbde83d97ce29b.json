{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nimport { ApiService } from \"../shared/services/api.service\";\nlet repoService = class repoService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  getLoanAccountVal(data) {\n    return this.apiService.post(\"api/mvp/repossession/getAccountById\", data);\n  }\n  getMasterDropList(data) {\n    return this.apiService.get(\"api/mvp/get/primaryCategoryItems?categoryMasterId=\" + data);\n  }\n  reportingAgency() {\n    return this.apiService.get(\"api/mvp/repossession/getReposessionAgencies\");\n  }\n  getStateList() {\n    return this.apiService.get(\"api/mvp/get/states\");\n  }\n  getCityList(data) {\n    return this.apiService.get(\"api/mvp/get/citiesbystate?stateId=\" + data);\n  }\n  getAreaList(data) {\n    return this.apiService.get(\"api/mvp/get/areaMasterBycity?cityId=\" + data);\n  }\n  createRepo(data) {\n    return this.apiService.post(\"api/mvp/repossession/add\", data);\n  }\n  uploadFile(formData) {\n    const options = {};\n    return this.apiService.post(\"api/UploadFile\", formData, options);\n  }\n  editRepo(data) {\n    return this.apiService.post(\"api/mvp/repossession/edit\", data);\n  }\n  getRepoAgencyList(data) {\n    return this.apiService.post(\"api/mvp/Repossession/GetReposessionAgencies\", data);\n  }\n  getCaseStatusList() {\n    return this.apiService.get(\"api/mvp/Repossession/GetAllRepossessionWorkFlowStatus\");\n  }\n  getMyRepoList(data) {\n    return this.apiService.post(\"api/mvp/Repossession/MyRepossession\", data);\n  }\n  getMyRepoQueueList(data) {\n    return this.apiService.post(\"api/mvp/Repossession/MyQueue\", data);\n  }\n  getRepoViewData(data) {\n    return this.apiService.post(\"api/mvp/repossession/getRepossessionById\", data);\n  }\n  getNextWorkFlowDataList(data) {\n    return this.apiService.post(\"api/mvp/repossession/getNextWorkFlowStatus\", data);\n  }\n  updateNextStatus(data) {\n    return this.apiService.post(\"api/mvp/repossession/updateWorkflowStatus\", data);\n  }\n  filePreview(data) {\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\n  }\n  genrateAutoDoc(data) {\n    return this.apiService.post(\"api/mvp/repossession/generateNoticeTemplate\", data);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ApiService\n    }];\n  }\n};\nrepoService = __decorate([Injectable({\n  providedIn: 'root'\n})], repoService);\nexport { repoService };", "map": {"version": 3, "names": ["Injectable", "ApiService", "repoService", "constructor", "apiService", "getLoanAccountVal", "data", "post", "getMasterDropList", "get", "reportingAgency", "getStateList", "getCityList", "getAreaList", "createRepo", "uploadFile", "formData", "options", "editRepo", "getRepoAgencyList", "getCaseStatusList", "getMyRepoList", "getMyRepoQueueList", "getRepoViewData", "getNextWorkFlowDataList", "updateNextStatus", "filePreview", "getRawImage", "genrateAutoDoc", "__decorate", "providedIn"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\repo\\repo.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ApiService } from \"../shared/services/api.service\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class repoService {\r\n  constructor(private apiService: ApiService) {}\r\n\r\n  getLoanAccountVal(data) {\r\n    return this.apiService.post(\"api/mvp/repossession/getAccountById\", data);\r\n  }\r\n\r\n  getMasterDropList(data) {\r\n    return this.apiService.get(\r\n      \"api/mvp/get/primaryCategoryItems?categoryMasterId=\" + data\r\n    );\r\n  }\r\n\r\n  reportingAgency() {\r\n    return this.apiService.get(\"api/mvp/repossession/getReposessionAgencies\");\r\n  }\r\n\r\n  getStateList() {\r\n    return this.apiService.get(\"api/mvp/get/states\");\r\n  }\r\n\r\n  getCityList(data) {\r\n    return this.apiService.get(\"api/mvp/get/citiesbystate?stateId=\" + data);\r\n  }\r\n\r\n  getAreaList(data) {\r\n    return this.apiService.get(\"api/mvp/get/areaMasterBycity?cityId=\" + data);\r\n  }\r\n\r\n  createRepo(data) {\r\n    return this.apiService.post(\"api/mvp/repossession/add\", data);\r\n  }\r\n\r\n  uploadFile(formData: FormData) {\r\n    const options = {};\r\n    return this.apiService.post(\"api/UploadFile\", formData, options);\r\n  }\r\n\r\n  editRepo(data) {\r\n    return this.apiService.post(\"api/mvp/repossession/edit\", data);\r\n  }\r\n\r\n  getRepoAgencyList(data) {\r\n    return this.apiService.post(\r\n      \"api/mvp/Repossession/GetReposessionAgencies\",\r\n      data\r\n    );\r\n  }\r\n\r\n  getCaseStatusList() {\r\n    return this.apiService.get(\r\n      \"api/mvp/Repossession/GetAllRepossessionWorkFlowStatus\"\r\n    );\r\n  }\r\n\r\n  getMyRepoList(data) {\r\n    return this.apiService.post(\"api/mvp/Repossession/MyRepossession\", data);\r\n  }\r\n\r\n  getMyRepoQueueList(data) {\r\n    return this.apiService.post(\"api/mvp/Repossession/MyQueue\", data);\r\n  }\r\n\r\n  getRepoViewData(data) {\r\n    return this.apiService.post(\r\n      \"api/mvp/repossession/getRepossessionById\",\r\n      data\r\n    );\r\n  }\r\n\r\n  getNextWorkFlowDataList(data) {\r\n    return this.apiService.post(\r\n      \"api/mvp/repossession/getNextWorkFlowStatus\",\r\n      data\r\n    );\r\n  }\r\n\r\n  updateNextStatus(data) {\r\n    return this.apiService.post(\r\n      \"api/mvp/repossession/updateWorkflowStatus\",\r\n      data\r\n    );\r\n  }\r\n\r\n  filePreview(data) {\r\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\r\n  }\r\n\r\n  genrateAutoDoc(data) {\r\n    return this.apiService.post(\r\n      \"api/mvp/repossession/generateNoticeTemplate\",\r\n      data\r\n    );\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,gCAAgC;AAKpD,IAAMC,WAAW,GAAjB,MAAMA,WAAW;EACtBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAe;EAE7CC,iBAAiBA,CAACC,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,qCAAqC,EAAED,IAAI,CAAC;EAC1E;EAEAE,iBAAiBA,CAACF,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACK,GAAG,CACxB,oDAAoD,GAAGH,IAAI,CAC5D;EACH;EAEAI,eAAeA,CAAA;IACb,OAAO,IAAI,CAACN,UAAU,CAACK,GAAG,CAAC,6CAA6C,CAAC;EAC3E;EAEAE,YAAYA,CAAA;IACV,OAAO,IAAI,CAACP,UAAU,CAACK,GAAG,CAAC,oBAAoB,CAAC;EAClD;EAEAG,WAAWA,CAACN,IAAI;IACd,OAAO,IAAI,CAACF,UAAU,CAACK,GAAG,CAAC,oCAAoC,GAAGH,IAAI,CAAC;EACzE;EAEAO,WAAWA,CAACP,IAAI;IACd,OAAO,IAAI,CAACF,UAAU,CAACK,GAAG,CAAC,sCAAsC,GAAGH,IAAI,CAAC;EAC3E;EAEAQ,UAAUA,CAACR,IAAI;IACb,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,0BAA0B,EAAED,IAAI,CAAC;EAC/D;EAEAS,UAAUA,CAACC,QAAkB;IAC3B,MAAMC,OAAO,GAAG,EAAE;IAClB,OAAO,IAAI,CAACb,UAAU,CAACG,IAAI,CAAC,gBAAgB,EAAES,QAAQ,EAAEC,OAAO,CAAC;EAClE;EAEAC,QAAQA,CAACZ,IAAI;IACX,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,2BAA2B,EAAED,IAAI,CAAC;EAChE;EAEAa,iBAAiBA,CAACb,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CACzB,6CAA6C,EAC7CD,IAAI,CACL;EACH;EAEAc,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAChB,UAAU,CAACK,GAAG,CACxB,uDAAuD,CACxD;EACH;EAEAY,aAAaA,CAACf,IAAI;IAChB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,qCAAqC,EAAED,IAAI,CAAC;EAC1E;EAEAgB,kBAAkBA,CAAChB,IAAI;IACrB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,8BAA8B,EAAED,IAAI,CAAC;EACnE;EAEAiB,eAAeA,CAACjB,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CACzB,0CAA0C,EAC1CD,IAAI,CACL;EACH;EAEAkB,uBAAuBA,CAAClB,IAAI;IAC1B,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CACzB,4CAA4C,EAC5CD,IAAI,CACL;EACH;EAEAmB,gBAAgBA,CAACnB,IAAI;IACnB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CACzB,2CAA2C,EAC3CD,IAAI,CACL;EACH;EAEAoB,WAAWA,CAACpB,IAAI;IACd,OAAO,IAAI,CAACF,UAAU,CAACuB,WAAW,CAAC,4BAA4B,GAAGrB,IAAI,CAAC;EACzE;EAEAsB,cAAcA,CAACtB,IAAI;IACjB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CACzB,6CAA6C,EAC7CD,IAAI,CACL;EACH;;;;;;;AA7FWJ,WAAW,GAAA2B,UAAA,EAHvB7B,UAAU,CAAC;EACV8B,UAAU,EAAE;CACb,CAAC,C,EACW5B,WAAW,CA8FvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}