{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-legal.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-legal.component.css?ngResource\";\nimport { Component } from '@angular/core';\nlet CreateLegalComponent = class CreateLegalComponent {\n  constructor() {\n    this.breadcrumbData = [{\n      label: \"Legal Management\",\n      path: \"/encollect/legal-custom/\"\n    }, {\n      label: \"Add Legal\",\n      path: \"/encollect/legal-custom/\"\n    }];\n  }\n  ngOnInit() {}\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nCreateLegalComponent = __decorate([Component({\n  selector: 'app-create-legal',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateLegalComponent);\nexport { CreateLegalComponent };", "map": {"version": 3, "names": ["Component", "CreateLegalComponent", "constructor", "breadcrumbData", "label", "path", "ngOnInit", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal-custom\\create-legal\\create-legal.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-create-legal',\r\n  templateUrl: './create-legal.component.html',\r\n  styleUrls: ['./create-legal.component.css']\r\n})\r\nexport class CreateLegalComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Legal Management\", path: \"/encollect/legal-custom/\" },\r\n\t\t{ label: \"Add Legal\", path: \"/encollect/legal-custom/\" },\r\n\t  ];\r\n  constructor() { }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AAO1C,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAK/BC,YAAA;IAJO,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAA0B,CAAE,EAC/D;MAAED,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE;IAA0B,CAAE,CACtD;EACc;EAEhBC,QAAQA,CAAA,GACR;;;;;AARWL,oBAAoB,GAAAM,UAAA,EALhCP,SAAS,CAAC;EACTQ,QAAQ,EAAE,kBAAkB;EAC5BC,QAAA,EAAAC,oBAA4C;;CAE7C,CAAC,C,EACWT,oBAAoB,CAUhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}