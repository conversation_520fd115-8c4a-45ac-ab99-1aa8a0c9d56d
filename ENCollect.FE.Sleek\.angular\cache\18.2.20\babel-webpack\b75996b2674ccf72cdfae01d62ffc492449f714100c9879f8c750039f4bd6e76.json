{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./view-budgeted-target.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./view-budgeted-target.component.css?ngResource\";\nconst statusOptions = [{\n  id: '1',\n  name: 'January'\n}, {\n  id: '2',\n  name: 'February'\n}, {\n  id: '3',\n  name: 'March'\n}, {\n  id: '4',\n  name: 'April'\n}, {\n  id: '5',\n  name: 'May'\n}, {\n  id: '6',\n  name: 'June'\n}, {\n  id: '7',\n  name: 'July'\n}, {\n  id: '8',\n  name: 'August'\n}, {\n  id: '9',\n  name: 'September'\n}, {\n  id: '10',\n  name: 'October'\n}, {\n  id: '11',\n  name: 'November'\n}, {\n  id: '12',\n  name: 'December'\n}];\nexport class SearchControls {}\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { TargetService } from '../target.service';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nlet ViewBudgetedTargetComponent = class ViewBudgetedTargetComponent {\n  constructor(toastr, TargetService, modalService) {\n    this.toastr = toastr;\n    this.TargetService = TargetService;\n    this.modalService = modalService;\n    this.breadcrumbData = [{\n      label: \"Target\",\n      path: \"/target/view-budgeted-target\"\n    }, {\n      label: \"View Budgeted Target\",\n      path: \"/target/view-budgeted-target\"\n    }];\n    this.currentRecords = [];\n    this.results = [];\n    this.statusOptions = statusOptions;\n    this.searchControls = new SearchControls();\n    this.loader = {\n      isSearching: false,\n      isDeleting: false\n    };\n    this.yearList = [];\n    this.targetItem = {};\n    this.currentPage = 1;\n    this.itemsPerPage = 5;\n    this.searchControls = {\n      targetMonths: '',\n      targetYears: ''\n    };\n  }\n  ngOnInit() {\n    this.prepareYearList();\n  }\n  prepareYearList() {\n    this.yearList = [];\n    let currentYear = new Date().getFullYear();\n    while (currentYear >= 2020) {\n      this.yearList.push(currentYear);\n      currentYear--;\n    }\n  }\n  search() {\n    if (this.searchControls.targetMonths == '' || this.searchControls.targetYears == '') {\n      this.toastr.warning(\"Enter at least one filter value\");\n      return false;\n    }\n    this.loader.isSearching = true;\n    this.TargetService.getbudgetedTargets(this.formatSearchParams()).subscribe(response => {\n      this.results = response;\n      this.loader.isSearching = false;\n      if (this.results.length == 0) {\n        this.toastr.info(\"No records found!\");\n      } else {\n        this.currentRecords = this.fetchRecordsByPage(1);\n      }\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.loader.isSearching = false;\n    });\n  }\n  fetchRecordsByPage(page) {\n    const results = this.results;\n    return results.slice((page - 1) * this.itemsPerPage, this.itemsPerPage * page);\n  }\n  formatSearchParams() {\n    const searchParams = {};\n    searchParams['TargetMonth'] = this.searchControls.targetMonths;\n    searchParams['TargetYear'] = this.searchControls.targetYears;\n    return searchParams;\n  }\n  changeItemsPerPage() {\n    this.currentRecords = this.fetchRecordsByPage(1);\n  }\n  pageChange(event) {\n    this.currentRecords = this.fetchRecordsByPage(event.page);\n  }\n  deleteTargetConfirmation(item, confirmation) {\n    this.selectedItem = item.id;\n    let config = {\n      ignoreBackdropClick: true\n    };\n    this.modalRef = this.modalService.show(confirmation, config);\n  }\n  deleteTarget() {\n    this.loader.isDeleting = true;\n    this.TargetService.deleteBudgetedTarget(this.selectedItem).subscribe(response => {\n      this.toastr.success(\"Target Deleted Successfully\");\n      this.loader.isDeleting = false;\n      this.search();\n      this.results = [];\n      this.currentRecords = [];\n      this.cancel();\n    }, err => {\n      this.loader.isDeleting = false;\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  cancel() {\n    this.modalRef?.hide();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: TargetService\n    }, {\n      type: BsModalService\n    }];\n  }\n};\nViewBudgetedTargetComponent = __decorate([Component({\n  selector: 'view-budgeted-target',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ViewBudgetedTargetComponent);\nexport { ViewBudgetedTargetComponent };", "map": {"version": 3, "names": ["statusOptions", "id", "name", "SearchControls", "Component", "ToastrService", "TargetService", "BsModalService", "ViewBudgetedTargetComponent", "constructor", "toastr", "modalService", "breadcrumbData", "label", "path", "currentRecords", "results", "searchControls", "loader", "isSearching", "isDeleting", "yearList", "targetItem", "currentPage", "itemsPerPage", "targetMonths", "targetYears", "ngOnInit", "prepareYearList", "currentYear", "Date", "getFullYear", "push", "search", "warning", "getbudgetedTargets", "formatSearchParams", "subscribe", "response", "length", "info", "fetchRecordsByPage", "err", "error", "page", "slice", "searchParams", "changeItemsPerPage", "pageChange", "event", "deleteTargetConfirmation", "item", "confirmation", "selectedItem", "config", "ignoreBackdropClick", "modalRef", "show", "deleteTarget", "deleteBudgetedTarget", "success", "cancel", "hide", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\target\\view-budgeted-target\\view-budgeted-target.component.ts"], "sourcesContent": ["const statusOptions = [ \r\n  { id:'1', name:'January'}, \r\n   { id:'2',name:  'February'}, \r\n   { id:'3',name: 'March'}, \r\n   { id:'4',name: 'April'}, \r\n   { id:'5',name: 'May'},\r\n   { id:'6',name: 'June'},\r\n   { id:'7',name: 'July'},\r\n   { id:'8',name: 'August'},\r\n   { id:'9',name: 'September'},\r\n   { id:'10',name: 'October'},\r\n   { id:'11',name: 'November'},\r\n   { id:'12',name: 'December'}]\r\n\r\nexport class SearchControls {\r\n  targetMonths:string;\r\n  targetYears: string;\r\n}\r\n\r\n\r\nimport { Component, OnInit,TemplateRef } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport{TargetService} from '../target.service';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\n@Component({\r\n  selector: 'view-budgeted-target',\r\n  templateUrl: './view-budgeted-target.component.html',\r\n  styleUrls: ['./view-budgeted-target.component.css']\r\n})\r\nexport class ViewBudgetedTargetComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Target\", path: \"/target/view-budgeted-target\" },\r\n\t\t{ label: \"View Budgeted Target\", path: \"/target/view-budgeted-target\" },\r\n\t  ]\r\n  currentRecords: Array<any> = [];\r\n  results: Array<any> = [];\r\n  targetId: any;\r\n  data: any;\r\n  statusOptions = statusOptions;\r\n  itemdata:any;\r\n  targetMonth:any;\r\n  targetItem:any;\r\n  itemsPerPage:any;\r\n  searchControls: SearchControls = new SearchControls();\r\n  currentPage:any;\r\n  modalRef: BsModalRef;\r\n  loader= {\r\n    isSearching: false,\r\n    isDeleting: false\r\n  }\r\n  yearList: number[] = [];\r\n\r\n constructor(\r\n        public toastr: ToastrService, \r\n        private TargetService: TargetService,\r\n    \t\tprivate modalService: BsModalService\r\n      ) {\r\n      this.targetItem={};\r\n      this.currentPage = 1;\r\n    \tthis.itemsPerPage = 5;\r\n      this.searchControls = {\r\n        targetMonths:'',\r\n        targetYears:''\r\n      }\r\n }\r\n     \r\n\r\n  ngOnInit(): void {\r\n    this.prepareYearList();\r\n  }\r\n\r\n  prepareYearList() {\r\n    this.yearList = [];\r\n    let currentYear = new Date().getFullYear();\r\n    while (currentYear >= 2020) {\r\n      this.yearList.push(currentYear);\r\n      currentYear--;\r\n    }\r\n  }\r\n\r\n  search() {    \r\n    if( this.searchControls.targetMonths=='' || this.searchControls.targetYears==''  ) {\r\n       this.toastr.warning(\"Enter at least one filter value\");\r\n       return false;\r\n    }\r\n    this.loader.isSearching = true\r\n    this.TargetService\r\n      .getbudgetedTargets(this.formatSearchParams())\r\n      .subscribe(response => {\r\n        this.results = response;\r\n        this.loader.isSearching = false\r\n\r\n         if(this.results.length == 0) {\r\n           this.toastr.info(\"No records found!\");\r\n        }\r\n        else {\r\n          this.currentRecords = this.fetchRecordsByPage(1);\r\n        }\r\n      },err=>{\r\n        this.toastr.error(err, \"Error!\")\r\n        this.loader.isSearching = false\r\n      });\r\n  }\r\n\r\n  fetchRecordsByPage(page: number): any {\r\n    const results = this.results;\r\n    return results.slice((page - 1) * this.itemsPerPage, this.itemsPerPage * page);\r\n  }\r\n\r\n  formatSearchParams() {\r\n    const searchParams = {};\r\n    searchParams['TargetMonth'] = this.searchControls.targetMonths;\r\n    searchParams['TargetYear'] = this.searchControls.targetYears;\r\n    return searchParams;\r\n  }\r\n\r\n  changeItemsPerPage(): void {\r\n    this.currentRecords = this.fetchRecordsByPage(1);\r\n  }\r\n\r\n  pageChange(event: any): void {\r\n    this.currentRecords = this.fetchRecordsByPage(event.page);\r\n  }\r\n\r\n  selectedItem: any;\r\n  deleteTargetConfirmation(item, confirmation: TemplateRef<any>){\r\n     this.selectedItem = item.id\r\n     let config = {\r\n          ignoreBackdropClick: true,\r\n        };\r\n     this.modalRef = this.modalService.show(confirmation,config);\r\n  }\r\n\r\n  deleteTarget(){\r\n    this.loader.isDeleting = true;\r\n    this.TargetService.deleteBudgetedTarget(this.selectedItem)\r\n      .subscribe(response => {\r\n        this.toastr.success(\"Target Deleted Successfully\");\r\n        this.loader.isDeleting = false;\r\n        this.search();\r\n        this.results=[];\r\n        this.currentRecords = [];\r\n        this.cancel();\r\n      },err=>{\r\n        this.loader.isDeleting = false;\r\n        this.toastr.error(err, \"Error!\")\r\n      });\r\n  }\r\n\r\n  cancel(){\r\n       this.modalRef?.hide()\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,MAAMA,aAAa,GAAG,CACpB;EAAEC,EAAE,EAAC,GAAG;EAAEC,IAAI,EAAC;AAAS,CAAC,EACxB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAG;AAAU,CAAC,EAC3B;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAO,CAAC,EACvB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAO,CAAC,EACvB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAK,CAAC,EACrB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAM,CAAC,EACtB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAM,CAAC,EACtB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAQ,CAAC,EACxB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAW,CAAC,EAC3B;EAAED,EAAE,EAAC,IAAI;EAACC,IAAI,EAAE;AAAS,CAAC,EAC1B;EAAED,EAAE,EAAC,IAAI;EAACC,IAAI,EAAE;AAAU,CAAC,EAC3B;EAAED,EAAE,EAAC,IAAI;EAACC,IAAI,EAAE;AAAU,CAAC,CAAC;AAE/B,OAAM,MAAOC,cAAc;AAM3B,SAASC,SAAS,QAA4B,eAAe;AAC7D,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAAOC,aAAa,QAAO,mBAAmB;AAC9C,SAASC,cAAc,QAAoB,qBAAqB;AAOzD,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAuBvCC,YACcC,MAAqB,EACpBJ,aAA4B,EAC9BK,YAA4B;IAF3B,KAAAD,MAAM,GAANA,MAAM;IACL,KAAAJ,aAAa,GAAbA,aAAa;IACf,KAAAK,YAAY,GAAZA,YAAY;IAzBjB,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAA8B,CAAE,EACzD;MAAED,KAAK,EAAE,sBAAsB;MAAEC,IAAI,EAAE;IAA8B,CAAE,CACrE;IACF,KAAAC,cAAc,GAAe,EAAE;IAC/B,KAAAC,OAAO,GAAe,EAAE;IAGxB,KAAAhB,aAAa,GAAGA,aAAa;IAK7B,KAAAiB,cAAc,GAAmB,IAAId,cAAc,EAAE;IAGrD,KAAAe,MAAM,GAAE;MACNC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE;KACb;IACD,KAAAC,QAAQ,GAAa,EAAE;IAOnB,IAAI,CAACC,UAAU,GAAC,EAAE;IAClB,IAAI,CAACC,WAAW,GAAG,CAAC;IACrB,IAAI,CAACC,YAAY,GAAG,CAAC;IACpB,IAAI,CAACP,cAAc,GAAG;MACpBQ,YAAY,EAAC,EAAE;MACfC,WAAW,EAAC;KACb;EACN;EAGCC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACP,QAAQ,GAAG,EAAE;IAClB,IAAIQ,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAC1C,OAAOF,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACR,QAAQ,CAACW,IAAI,CAACH,WAAW,CAAC;MAC/BA,WAAW,EAAE;IACf;EACF;EAEAI,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAChB,cAAc,CAACQ,YAAY,IAAE,EAAE,IAAI,IAAI,CAACR,cAAc,CAACS,WAAW,IAAE,EAAE,EAAI;MAChF,IAAI,CAAChB,MAAM,CAACwB,OAAO,CAAC,iCAAiC,CAAC;MACtD,OAAO,KAAK;IACf;IACA,IAAI,CAAChB,MAAM,CAACC,WAAW,GAAG,IAAI;IAC9B,IAAI,CAACb,aAAa,CACf6B,kBAAkB,CAAC,IAAI,CAACC,kBAAkB,EAAE,CAAC,CAC7CC,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAI,CAACtB,OAAO,GAAGsB,QAAQ;MACvB,IAAI,CAACpB,MAAM,CAACC,WAAW,GAAG,KAAK;MAE9B,IAAG,IAAI,CAACH,OAAO,CAACuB,MAAM,IAAI,CAAC,EAAE;QAC3B,IAAI,CAAC7B,MAAM,CAAC8B,IAAI,CAAC,mBAAmB,CAAC;MACxC,CAAC,MACI;QACH,IAAI,CAACzB,cAAc,GAAG,IAAI,CAAC0B,kBAAkB,CAAC,CAAC,CAAC;MAClD;IACF,CAAC,EAACC,GAAG,IAAE;MACL,IAAI,CAAChC,MAAM,CAACiC,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACxB,MAAM,CAACC,WAAW,GAAG,KAAK;IACjC,CAAC,CAAC;EACN;EAEAsB,kBAAkBA,CAACG,IAAY;IAC7B,MAAM5B,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,OAAOA,OAAO,CAAC6B,KAAK,CAAC,CAACD,IAAI,GAAG,CAAC,IAAI,IAAI,CAACpB,YAAY,EAAE,IAAI,CAACA,YAAY,GAAGoB,IAAI,CAAC;EAChF;EAEAR,kBAAkBA,CAAA;IAChB,MAAMU,YAAY,GAAG,EAAE;IACvBA,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC7B,cAAc,CAACQ,YAAY;IAC9DqB,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC7B,cAAc,CAACS,WAAW;IAC5D,OAAOoB,YAAY;EACrB;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAAChC,cAAc,GAAG,IAAI,CAAC0B,kBAAkB,CAAC,CAAC,CAAC;EAClD;EAEAO,UAAUA,CAACC,KAAU;IACnB,IAAI,CAAClC,cAAc,GAAG,IAAI,CAAC0B,kBAAkB,CAACQ,KAAK,CAACL,IAAI,CAAC;EAC3D;EAGAM,wBAAwBA,CAACC,IAAI,EAAEC,YAA8B;IAC1D,IAAI,CAACC,YAAY,GAAGF,IAAI,CAAClD,EAAE;IAC3B,IAAIqD,MAAM,GAAG;MACRC,mBAAmB,EAAE;KACtB;IACJ,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC7C,YAAY,CAAC8C,IAAI,CAACL,YAAY,EAACE,MAAM,CAAC;EAC9D;EAEAI,YAAYA,CAAA;IACV,IAAI,CAACxC,MAAM,CAACE,UAAU,GAAG,IAAI;IAC7B,IAAI,CAACd,aAAa,CAACqD,oBAAoB,CAAC,IAAI,CAACN,YAAY,CAAC,CACvDhB,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAI,CAAC5B,MAAM,CAACkD,OAAO,CAAC,6BAA6B,CAAC;MAClD,IAAI,CAAC1C,MAAM,CAACE,UAAU,GAAG,KAAK;MAC9B,IAAI,CAACa,MAAM,EAAE;MACb,IAAI,CAACjB,OAAO,GAAC,EAAE;MACf,IAAI,CAACD,cAAc,GAAG,EAAE;MACxB,IAAI,CAAC8C,MAAM,EAAE;IACf,CAAC,EAACnB,GAAG,IAAE;MACL,IAAI,CAACxB,MAAM,CAACE,UAAU,GAAG,KAAK;MAC9B,IAAI,CAACV,MAAM,CAACiC,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IAClC,CAAC,CAAC;EACN;EAEAmB,MAAMA,CAAA;IACD,IAAI,CAACL,QAAQ,EAAEM,IAAI,EAAE;EAC1B;;;;;;;;;;;AA1HWtD,2BAA2B,GAAAuD,UAAA,EALvC3D,SAAS,CAAC;EACT4D,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAoD;;CAErD,CAAC,C,EACW1D,2BAA2B,CA4HvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}