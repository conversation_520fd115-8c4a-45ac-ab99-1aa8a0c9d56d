{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./denominations.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./denominations.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { SettingsService } from '../../settings.service';\nimport { SettingsConfigService } from '../../settingsconfig.service';\nlet DenominationsComponent = class DenominationsComponent {\n  constructor(toastr, settingService, settingConfigService) {\n    this.toastr = toastr;\n    this.settingService = settingService;\n    this.settingConfigService = settingConfigService;\n    this.breadcrumbData = [{\n      label: \"Settings Payments\",\n      path: \"/settings/payments/denominations\"\n    }, {\n      label: \"Issue Receipt Masters Configuration\",\n      path: \"/settings/payments/denominations\"\n    }];\n    this.addModeDenominations = false;\n    this.addModeRelations = false;\n    this.deletedData = [];\n    this.deletedRelations = [];\n    this.denominations = [];\n    this.relationShipManagers = [];\n    this.loader = {\n      isSearching: false,\n      isSubmitDenomination: false,\n      isRelation: false\n    };\n  }\n  ngOnInit() {\n    this.settingService.getCategory(\"Denomination\").subscribe(response => {\n      if (response.length == 0) {\n        this.addModeDenominations = true;\n        this.addDenomination();\n      } else {\n        this.denominations = response;\n      }\n    }, err => {\n      this.toastr.error(err);\n      this.loader.isSearching = false;\n    });\n    this.settingService.getCategory(\"Relationships\").subscribe(response => {\n      if (response.length == 0) {\n        this.addModeRelations = true;\n        this.addRelationShipManager();\n      } else {\n        this.relationShipManagers = response;\n      }\n    }, err => {\n      this.toastr.error(err);\n      this.loader.isSearching = false;\n    });\n  }\n  addDenomination() {\n    this.denominations.push({\n      \"id\": \"\",\n      \"name\": \"\",\n      \"categoryMasterId\": \"Denomination\",\n      \"isDelete\": false\n    });\n  }\n  removeDenomination(obj, i) {\n    if (this.denominations.length > 1) {\n      if (obj.id != \"\") {\n        obj[\"isDelete\"] = true;\n        this.deletedData.push(obj);\n      }\n      this.denominations.splice(i, 1);\n    } else {\n      this.toastr.warning(\"Atleast one record is required\");\n    }\n  }\n  submitDenomiations() {\n    this.loader.isSubmitDenomination = true;\n    if (this.addModeDenominations) {\n      this.settingService.addCategory(this.denominations).subscribe(response => {\n        this.toastr.success(\"Denomination updated successfully\");\n        this.loader.isSubmitDenomination = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSubmitDenomination = false;\n      });\n    } else {\n      var inputParams = [];\n      for (let i = 0; i < this.deletedData.length; i++) {\n        inputParams.push(this.deletedData[i]);\n      }\n      for (let j = 0; j < this.denominations.length; j++) {\n        inputParams.push(this.denominations[j]);\n      }\n      this.settingService.updateCategory(inputParams).subscribe(response => {\n        this.toastr.success(\"Denomination updated successfully\");\n        this.loader.isSubmitDenomination = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSubmitDenomination = false;\n      });\n    }\n  }\n  addRelationShipManager() {\n    this.relationShipManagers.push({\n      \"name\": \"\",\n      \"categoryMasterId\": \"Relationships\"\n    });\n  }\n  removeRelationShipManager(obj, i) {\n    if (this.relationShipManagers.length > 1) {\n      if (obj.id != \"\") {\n        obj[\"isDelete\"] = true;\n        this.deletedRelations.push(obj);\n      }\n      this.relationShipManagers.splice(i, 1);\n    } else {\n      this.toastr.warning(\"Atleast one record is required\");\n    }\n  }\n  submitRelationshipManager() {\n    this.loader.isRelation = true;\n    if (this.addModeRelations) {\n      this.settingService.addCategory(this.relationShipManagers).subscribe(response => {\n        this.toastr.success(\"Relationship updated successfully\");\n        this.loader.isRelation = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isRelation = false;\n      });\n    } else {\n      var inputParams = [];\n      for (let i = 0; i < this.deletedRelations.length; i++) {\n        inputParams.push(this.deletedRelations[i]);\n      }\n      for (let j = 0; j < this.relationShipManagers.length; j++) {\n        inputParams.push(this.relationShipManagers[j]);\n      }\n      this.settingService.updateCategory(inputParams).subscribe(response => {\n        this.toastr.success(\"Relationship updated successfully\");\n        this.loader.isRelation = false;\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isRelation = false;\n      });\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: SettingsService\n    }, {\n      type: SettingsConfigService\n    }];\n  }\n};\nDenominationsComponent = __decorate([Component({\n  selector: 'app-denominations',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DenominationsComponent);\nexport { DenominationsComponent };", "map": {"version": 3, "names": ["Component", "ToastrService", "SettingsService", "SettingsConfigService", "DenominationsComponent", "constructor", "toastr", "settingService", "settingConfigService", "breadcrumbData", "label", "path", "addModeDenominations", "addModeRelations", "deletedData", "deletedRelations", "denominations", "relationShipManagers", "loader", "isSearching", "isSubmitDenomination", "isRelation", "ngOnInit", "getCategory", "subscribe", "response", "length", "addDenomination", "err", "error", "addRelationShipManager", "push", "removeDenomination", "obj", "i", "id", "splice", "warning", "submitDenomiations", "addCategory", "success", "inputParams", "j", "updateCategory", "removeRelationShipManager", "submitRelationshipManager", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\payments-config\\denominations\\denominations.component.ts"], "sourcesContent": ["export interface Loader{\r\n    isSearching: boolean;\r\n    isSubmitDenomination: boolean;\r\n    isRelation: boolean;\r\n}\r\nimport { Component, OnInit,TemplateRef } from '@angular/core';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\nimport { SettingsService } from '../../settings.service';\r\nimport { SettingsConfigService } from '../../settingsconfig.service';\r\n\r\n@Component({\r\n  selector: 'app-denominations',\r\n  templateUrl: './denominations.component.html',\r\n  styleUrls: ['./denominations.component.css']\r\n})\r\nexport class DenominationsComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Settings Payments\", path: \"/settings/payments/denominations\" },\r\n\t\t{ label: \"Issue Receipt Masters Configuration\", path: \"/settings/payments/denominations\" },\r\n\t  ]\r\n  loader: Loader;\r\n  denominations: any;\r\n  relationShipManagers: any;\r\n  addModeDenominations =  false\r\n  addModeRelations =  false\r\n  constructor(public toastr: ToastrService,\r\n              private settingService: SettingsService,\r\n              private settingConfigService: SettingsConfigService) {\r\n    this.denominations = [];\r\n    this.relationShipManagers = []\r\n\r\n    this.loader = {\r\n        isSearching: false,\r\n        isSubmitDenomination: false,\r\n        isRelation: false\r\n    }\r\n\r\n\r\n  }\r\n\r\n\r\n  ngOnInit() {\r\n     this.settingService.getCategory(\"Denomination\").subscribe(response => {\r\n        if(response.length==0){\r\n            this.addModeDenominations = true\r\n            this.addDenomination()\r\n        }else{\r\n          this.denominations = response\r\n        }\r\n\r\n      },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching =  false\r\n      })\r\n     this.settingService.getCategory(\"Relationships\").subscribe(response => {\r\n        if(response.length==0){\r\n            this.addModeRelations = true\r\n            this.addRelationShipManager()\r\n        }else{\r\n          this.relationShipManagers = response\r\n        }\r\n\r\n      },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching =  false\r\n      })\r\n  }\r\n\r\n  addDenomination(){\r\n     this.denominations.push({\r\n         \"id\":\"\",\r\n         \"name\":\"\",\r\n         \"categoryMasterId\": \"Denomination\",\r\n         \"isDelete\": false\r\n     })\r\n  }\r\n  deletedData = []\r\n  removeDenomination(obj,i){\r\n    if(this.denominations.length>1){\r\n      if(obj.id !=\"\"){\r\n        obj[\"isDelete\"] = true\r\n        this.deletedData.push(obj)\r\n      }\r\n      this.denominations.splice(i, 1);\r\n    }else{\r\n      this.toastr.warning(\"Atleast one record is required\");\r\n    }\r\n  }\r\n\r\n  submitDenomiations(){\r\n    this.loader.isSubmitDenomination =  true\r\n      if(this.addModeDenominations){\r\n        this.settingService.addCategory(this.denominations).subscribe(response => {\r\n          this.toastr.success(\"Denomination updated successfully\")\r\n          this.loader.isSubmitDenomination =  false\r\n        },err=>{\r\n          this.toastr.error(err)\r\n          this.loader.isSubmitDenomination =  false\r\n        })\r\n      }else{\r\n        var inputParams = []\r\n        for(let i=0; i<this.deletedData.length; i++){\r\n          inputParams.push(this.deletedData[i])\r\n        }\r\n        for(let j=0; j<this.denominations.length; j++){\r\n          inputParams.push(this.denominations[j])\r\n        }\r\n        this.settingService.updateCategory(inputParams).subscribe(response => {\r\n          this.toastr.success(\"Denomination updated successfully\")\r\n          this.loader.isSubmitDenomination =  false\r\n        },err=>{\r\n          this.toastr.error(err)\r\n          this.loader.isSubmitDenomination =  false\r\n        })\r\n      }\r\n  }\r\n\r\n\r\n addRelationShipManager(){\r\n     this.relationShipManagers.push({\r\n          \"name\":\"\",\r\n          \"categoryMasterId\": \"Relationships\",\r\n     })\r\n  }\r\n  deletedRelations = []\r\n  removeRelationShipManager(obj,i){\r\n    if(this.relationShipManagers.length>1){\r\n      if(obj.id !=\"\"){\r\n        obj[\"isDelete\"] = true\r\n        this.deletedRelations.push(obj)\r\n      }\r\n      this.relationShipManagers.splice(i, 1);\r\n    }else{\r\n      this.toastr.warning(\"Atleast one record is required\");\r\n    }\r\n  }\r\n\r\n  submitRelationshipManager(){\r\n    this.loader.isRelation =  true\r\n      if(this.addModeRelations){\r\n        this.settingService.addCategory(this.relationShipManagers).subscribe(response => {\r\n          this.toastr.success(\"Relationship updated successfully\")\r\n          this.loader.isRelation =  false\r\n        },err=>{\r\n          this.toastr.error(err)\r\n          this.loader.isRelation =  false\r\n        })\r\n      }else{\r\n          var inputParams = []\r\n        for(let i=0; i<this.deletedRelations.length; i++){\r\n          inputParams.push(this.deletedRelations[i])\r\n        }\r\n        for(let j=0; j<this.relationShipManagers.length; j++){\r\n          inputParams.push(this.relationShipManagers[j])\r\n        }\r\n        this.settingService.updateCategory(inputParams).subscribe(response => {\r\n          this.toastr.success(\"Relationship updated successfully\")\r\n          this.loader.isRelation =  false\r\n        },err=>{\r\n          this.toastr.error(err)\r\n          this.loader.isRelation =  false\r\n        })\r\n      }\r\n  }\r\n}\r\n"], "mappings": ";;;AAKA,SAASA,SAAS,QAA4B,eAAe;AAE7D,SAASC,aAAa,QAAQ,YAAY;AAG1C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,qBAAqB,QAAQ,8BAA8B;AAO7D,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAUjCC,YAAmBC,MAAqB,EACpBC,cAA+B,EAC/BC,oBAA2C;IAF5C,KAAAF,MAAM,GAANA,MAAM;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IAXjC,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAkC,CAAE,EACxE;MAAED,KAAK,EAAE,qCAAqC;MAAEC,IAAI,EAAE;IAAkC,CAAE,CACxF;IAIF,KAAAC,oBAAoB,GAAI,KAAK;IAC7B,KAAAC,gBAAgB,GAAI,KAAK;IAoDzB,KAAAC,WAAW,GAAG,EAAE;IAgDhB,KAAAC,gBAAgB,GAAG,EAAE;IAhGnB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACC,MAAM,GAAG;MACVC,WAAW,EAAE,KAAK;MAClBC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE;KACf;EAGH;EAGAC,QAAQA,CAAA;IACL,IAAI,CAACf,cAAc,CAACgB,WAAW,CAAC,cAAc,CAAC,CAACC,SAAS,CAACC,QAAQ,IAAG;MAClE,IAAGA,QAAQ,CAACC,MAAM,IAAE,CAAC,EAAC;QAClB,IAAI,CAACd,oBAAoB,GAAG,IAAI;QAChC,IAAI,CAACe,eAAe,EAAE;MAC1B,CAAC,MAAI;QACH,IAAI,CAACX,aAAa,GAAGS,QAAQ;MAC/B;IAEF,CAAC,EAACG,GAAG,IAAE;MACL,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAACV,MAAM,CAACC,WAAW,GAAI,KAAK;IAClC,CAAC,CAAC;IACH,IAAI,CAACZ,cAAc,CAACgB,WAAW,CAAC,eAAe,CAAC,CAACC,SAAS,CAACC,QAAQ,IAAG;MACnE,IAAGA,QAAQ,CAACC,MAAM,IAAE,CAAC,EAAC;QAClB,IAAI,CAACb,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACiB,sBAAsB,EAAE;MACjC,CAAC,MAAI;QACH,IAAI,CAACb,oBAAoB,GAAGQ,QAAQ;MACtC;IAEF,CAAC,EAACG,GAAG,IAAE;MACL,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAACV,MAAM,CAACC,WAAW,GAAI,KAAK;IAClC,CAAC,CAAC;EACN;EAEAQ,eAAeA,CAAA;IACZ,IAAI,CAACX,aAAa,CAACe,IAAI,CAAC;MACpB,IAAI,EAAC,EAAE;MACP,MAAM,EAAC,EAAE;MACT,kBAAkB,EAAE,cAAc;MAClC,UAAU,EAAE;KACf,CAAC;EACL;EAEAC,kBAAkBA,CAACC,GAAG,EAACC,CAAC;IACtB,IAAG,IAAI,CAAClB,aAAa,CAACU,MAAM,GAAC,CAAC,EAAC;MAC7B,IAAGO,GAAG,CAACE,EAAE,IAAG,EAAE,EAAC;QACbF,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;QACtB,IAAI,CAACnB,WAAW,CAACiB,IAAI,CAACE,GAAG,CAAC;MAC5B;MACA,IAAI,CAACjB,aAAa,CAACoB,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC,MAAI;MACH,IAAI,CAAC5B,MAAM,CAAC+B,OAAO,CAAC,gCAAgC,CAAC;IACvD;EACF;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAACpB,MAAM,CAACE,oBAAoB,GAAI,IAAI;IACtC,IAAG,IAAI,CAACR,oBAAoB,EAAC;MAC3B,IAAI,CAACL,cAAc,CAACgC,WAAW,CAAC,IAAI,CAACvB,aAAa,CAAC,CAACQ,SAAS,CAACC,QAAQ,IAAG;QACvE,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,mCAAmC,CAAC;QACxD,IAAI,CAACtB,MAAM,CAACE,oBAAoB,GAAI,KAAK;MAC3C,CAAC,EAACQ,GAAG,IAAE;QACL,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACV,MAAM,CAACE,oBAAoB,GAAI,KAAK;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAI;MACH,IAAIqB,WAAW,GAAG,EAAE;MACpB,KAAI,IAAIP,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAACpB,WAAW,CAACY,MAAM,EAAEQ,CAAC,EAAE,EAAC;QAC1CO,WAAW,CAACV,IAAI,CAAC,IAAI,CAACjB,WAAW,CAACoB,CAAC,CAAC,CAAC;MACvC;MACA,KAAI,IAAIQ,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAAC1B,aAAa,CAACU,MAAM,EAAEgB,CAAC,EAAE,EAAC;QAC5CD,WAAW,CAACV,IAAI,CAAC,IAAI,CAACf,aAAa,CAAC0B,CAAC,CAAC,CAAC;MACzC;MACA,IAAI,CAACnC,cAAc,CAACoC,cAAc,CAACF,WAAW,CAAC,CAACjB,SAAS,CAACC,QAAQ,IAAG;QACnE,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,mCAAmC,CAAC;QACxD,IAAI,CAACtB,MAAM,CAACE,oBAAoB,GAAI,KAAK;MAC3C,CAAC,EAACQ,GAAG,IAAE;QACL,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACV,MAAM,CAACE,oBAAoB,GAAI,KAAK;MAC3C,CAAC,CAAC;IACJ;EACJ;EAGDU,sBAAsBA,CAAA;IAClB,IAAI,CAACb,oBAAoB,CAACc,IAAI,CAAC;MAC1B,MAAM,EAAC,EAAE;MACT,kBAAkB,EAAE;KACxB,CAAC;EACL;EAEAa,yBAAyBA,CAACX,GAAG,EAACC,CAAC;IAC7B,IAAG,IAAI,CAACjB,oBAAoB,CAACS,MAAM,GAAC,CAAC,EAAC;MACpC,IAAGO,GAAG,CAACE,EAAE,IAAG,EAAE,EAAC;QACbF,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;QACtB,IAAI,CAAClB,gBAAgB,CAACgB,IAAI,CAACE,GAAG,CAAC;MACjC;MACA,IAAI,CAAChB,oBAAoB,CAACmB,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC,MAAI;MACH,IAAI,CAAC5B,MAAM,CAAC+B,OAAO,CAAC,gCAAgC,CAAC;IACvD;EACF;EAEAQ,yBAAyBA,CAAA;IACvB,IAAI,CAAC3B,MAAM,CAACG,UAAU,GAAI,IAAI;IAC5B,IAAG,IAAI,CAACR,gBAAgB,EAAC;MACvB,IAAI,CAACN,cAAc,CAACgC,WAAW,CAAC,IAAI,CAACtB,oBAAoB,CAAC,CAACO,SAAS,CAACC,QAAQ,IAAG;QAC9E,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,mCAAmC,CAAC;QACxD,IAAI,CAACtB,MAAM,CAACG,UAAU,GAAI,KAAK;MACjC,CAAC,EAACO,GAAG,IAAE;QACL,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACV,MAAM,CAACG,UAAU,GAAI,KAAK;MACjC,CAAC,CAAC;IACJ,CAAC,MAAI;MACD,IAAIoB,WAAW,GAAG,EAAE;MACtB,KAAI,IAAIP,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAACnB,gBAAgB,CAACW,MAAM,EAAEQ,CAAC,EAAE,EAAC;QAC/CO,WAAW,CAACV,IAAI,CAAC,IAAI,CAAChB,gBAAgB,CAACmB,CAAC,CAAC,CAAC;MAC5C;MACA,KAAI,IAAIQ,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC,IAAI,CAACzB,oBAAoB,CAACS,MAAM,EAAEgB,CAAC,EAAE,EAAC;QACnDD,WAAW,CAACV,IAAI,CAAC,IAAI,CAACd,oBAAoB,CAACyB,CAAC,CAAC,CAAC;MAChD;MACA,IAAI,CAACnC,cAAc,CAACoC,cAAc,CAACF,WAAW,CAAC,CAACjB,SAAS,CAACC,QAAQ,IAAG;QACnE,IAAI,CAACnB,MAAM,CAACkC,OAAO,CAAC,mCAAmC,CAAC;QACxD,IAAI,CAACtB,MAAM,CAACG,UAAU,GAAI,KAAK;MACjC,CAAC,EAACO,GAAG,IAAE;QACL,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAACV,MAAM,CAACG,UAAU,GAAI,KAAK;MACjC,CAAC,CAAC;IACJ;EACJ;;;;;;;;;;;AApJWjB,sBAAsB,GAAA0C,UAAA,EALlC9C,SAAS,CAAC;EACT+C,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA6C;;CAE9C,CAAC,C,EACW7C,sBAAsB,CAqJlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}