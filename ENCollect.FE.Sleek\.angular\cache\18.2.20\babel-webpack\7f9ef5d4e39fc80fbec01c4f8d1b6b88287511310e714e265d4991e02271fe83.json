{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-target.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-target.component.css?ngResource\";\nexport class SearchControls {\n  constructor() {\n    this.month = '';\n    this.year = '';\n    this.productGroup = '';\n    this.product = '';\n    this.subProduct = '';\n    this.bucket = '';\n    this.network = '';\n    this.zone = '';\n    this.region = '';\n    this.state = '';\n    this.city = '';\n    this.agencyType = '';\n    this.agencyName = '';\n  }\n}\nconst statusOptions = [{\n  id: '1',\n  name: 'January'\n}, {\n  id: '2',\n  name: 'February'\n}, {\n  id: '3',\n  name: 'March'\n}, {\n  id: '4',\n  name: 'April'\n}, {\n  id: '5',\n  name: 'May'\n}, {\n  id: '6',\n  name: 'June'\n}, {\n  id: '7',\n  name: 'July'\n}, {\n  id: '8',\n  name: 'August'\n}, {\n  id: '9',\n  name: 'September'\n}, {\n  id: '10',\n  name: 'October'\n}, {\n  id: '11',\n  name: 'November'\n}, {\n  id: '12',\n  name: 'December'\n}];\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { Router } from '@angular/router';\nimport { TargetService } from '../target.service';\nlet CreateTargetComponent = class CreateTargetComponent {\n  constructor(toastr, targetService, modalService, router) {\n    this.toastr = toastr;\n    this.targetService = targetService;\n    this.modalService = modalService;\n    this.router = router;\n    this.breadcrumbData = [{\n      label: \"Target\",\n      path: \"/target/create-target\"\n    }, {\n      label: \"Create Targets\",\n      path: \"/target/create-target\"\n    }];\n    this.searchControls = new SearchControls();\n    this.statusOptions = statusOptions;\n    this.productGroupList = [];\n    this.productList = [];\n    this.subProductList = [];\n    this.bucketList = [];\n    this.regionList = [];\n    this.zoneList = [];\n    this.agencyTypeList = [];\n    this.agencyNameList = [];\n    this.stateList = [];\n    this.cityList = [];\n    this.showTarget = false;\n    this.loader = {\n      isSearching: false,\n      isSubmit: false\n    };\n    this.targetService.getProductGroupList().subscribe(response => {\n      this.productGroupList = response;\n      this.productGroupList.splice(0, 0, {\n        \"id\": \"All\",\n        \"name\": \"All\",\n        \"code\": \"All\"\n      });\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n    });\n    this.targetService.getBucketList().subscribe(response => {\n      this.bucketList = response;\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n    });\n    this.targetService.fetchAgencyTypes().subscribe(agencyTypes => {\n      this.agencyTypeList = agencyTypes.filter(itm => {\n        return itm.agencyType == 'Collections';\n      });\n    });\n    this.targetService.getAgencyList().subscribe(response => {\n      this.agencyNameList = response;\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n    });\n    this.targetService.getMasterCountry().subscribe(response => {\n      this.geomasterList = response;\n      this.loadZone();\n    }, error => {\n      this.toastr.error(error, \"Error!\");\n    });\n    this.targetInfo = {};\n    this.targetInfo.target = {};\n    this.agencyList = [];\n    this.targetInfo.agencyList = this.agencyList;\n    this.selectedAgencyTarget = {\n      targetPOSforNorm: 0,\n      percentageNorm: 0,\n      accountCountNorm: 0,\n      accountPercentageNorm: 0,\n      targetPOSforRB: 0,\n      percentageRB: 0,\n      accountCountRB: 0,\n      accountPercentageRB: 0,\n      targetPOSforStab: 0,\n      percentageStab: 0,\n      accountPercentageStab: 0,\n      accountCountStab: 0,\n      targetPOSforRF: 0,\n      percentageRF: 0,\n      accountCountRF: 0,\n      accountPercentageRF: 0,\n      targetResolution: 0,\n      percentageResolution: 0,\n      accountCountMoneyResolution: 0,\n      accountPercentageResolution: 0,\n      targetMoneyCollected: 0,\n      percentageMoneyCollected: 0,\n      accountCountMoneyCollected: 0,\n      accountPercentageMoneyCollected: 0\n    };\n  }\n  ngOnInit() {}\n  getProducts() {\n    this.productList = [];\n    this.subProductList = [];\n    this.searchControls.product = \"\";\n    this.searchControls.subProduct = \"\";\n    if (this.searchControls.productGroup != \"All\" && this.searchControls.productGroup != \"\") {\n      this.targetService.getProductListByPG(this.searchControls.productGroup).subscribe(response => {\n        this.productList = response;\n        this.productList.splice(0, 0, {\n          \"id\": \"All\",\n          \"name\": \"All\",\n          \"code\": \"All\"\n        });\n      }, err => {\n        this.toastr.error(err, \"Error!\");\n      });\n    } else if (this.searchControls.productGroup == \"All\") {\n      this.productList.push({\n        \"id\": \"All\",\n        \"name\": \"All\",\n        \"code\": \"All\"\n      });\n    }\n  }\n  getSubproducts() {\n    this.subProductList = [];\n    this.searchControls.subProduct = \"\";\n    if (this.searchControls.product != \"All\" && this.searchControls.product != \"\") {\n      this.targetService.getSubproductListByProduct(this.searchControls.product).subscribe(response => {\n        this.subProductList = response;\n        this.subProductList.splice(0, 0, {\n          \"id\": \"All\",\n          \"name\": \"All\",\n          \"code\": \"All\"\n        });\n      }, err => {\n        this.toastr.error(err, \"Error!\");\n      });\n    } else if (this.searchControls.product == \"All\") {\n      this.subProductList.push({\n        \"id\": \"All\",\n        \"name\": \"All\",\n        \"code\": \"All\"\n      });\n    }\n  }\n  loadZone() {\n    this.zoneList = this.targetService.removeDuplicates(this.geomasterList, \"name\");\n  }\n  getRegions() {\n    this.regionAdd();\n    this.regionList = this.targetService.generalFilterByKey(this.regionList, this.searchControls.region, \"name\");\n    this.regionList = this.targetService.removeDuplicates(this.regionList, \"name\");\n    this.regionList = this.targetService.generalKeySort(this.regionList, \"name\");\n  }\n  regionAdd() {\n    this.regionList = [];\n    let data = {\n      \"country\": this.searchControls.zone\n    };\n    this.targetService.regionAllocation(data).subscribe(response => {\n      this.regionList = response;\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  stateAdd() {\n    let data = {\n      \"country\": this.searchControls.zone,\n      \"region\": this.searchControls.region\n    };\n    this.targetService.stateAllocation(data).subscribe(response => {\n      this.stateList = [];\n      this.stateList = response;\n    }, error => {\n      this.toastr.error(error, \"Error!\");\n    });\n  }\n  cityAdd() {\n    let data = {\n      \"country\": this.searchControls.zone,\n      \"region\": this.searchControls.region,\n      \"state\": this.searchControls.state\n    };\n    this.targetService.cityAllocation(data).subscribe(response => {\n      this.cityList = [];\n      this.cityList = response;\n    }, error => {\n      this.toastr.error(error, \"Error!\");\n    });\n  }\n  loadState() {\n    this.stateAdd();\n    this.stateList = this.targetService.generalFilterByKey(this.geomasterList, this.searchControls.state, \"name\");\n    this.stateList = this.targetService.removeDuplicates(this.stateList, \"name\");\n    this.stateList = this.targetService.generalKeySort(this.stateList, \"name\");\n  }\n  loadCity(state) {\n    this.cityAdd();\n    this.cityList = this.targetService.generalFilterByKey(this.geomasterList, this.searchControls.city, \"name\");\n    this.cityList = this.targetService.removeDuplicates(this.cityList, \"name\");\n    this.cityList = this.targetService.generalKeySort(this.cityList, \"name\");\n  }\n  search() {\n    if (this.searchControls.month == \"\" || this.searchControls.year == \"\" || this.searchControls.productGroup == \"\" || this.searchControls.agencyType == \"\" || this.searchControls.agencyName == \"\" || this.searchControls.bucket == \"\") {\n      this.toastr.error(\"please enter the mandatory fields\", \"Error!\");\n      return false;\n    }\n    let productGroup = this.productGroupList.find(obj => obj.id == this.searchControls.productGroup);\n    let productGroupname = productGroup[\"name\"];\n    var productName = \"\";\n    if (this.searchControls.product) {\n      productName = this.productList.find(obj => obj.id == this.searchControls.product)[\"name\"];\n    }\n    let searchData = {\n      \"month\": this.searchControls.month,\n      \"year\": this.searchControls.year,\n      \"productGroup\": productGroupname,\n      \"product\": productName,\n      \"subProduct\": this.searchControls.subProduct,\n      \"bucket\": this.searchControls.bucket,\n      \"zone\": this.searchControls.zone,\n      \"region\": this.searchControls.region,\n      \"state\": this.searchControls.state,\n      \"city\": this.searchControls.city,\n      \"agencyIds\": [this.searchControls.agencyName],\n      \"agencyType\": this.searchControls.agencyType\n    };\n    this.loader.isSearching = true;\n    this.targetService.searchTargetInfo(searchData).subscribe(data => {\n      this.loader.isSearching = false;\n      if (data.target == null) {\n        this.toastr.info(\"No results found!\");\n        return false;\n      }\n      this.targetInfo = data;\n      this.agencyList = data[\"agencyDetails\"];\n      if (this.agencyList.length == 0) {\n        this.toastr.info(\"There is no allocation to the selected agency. To proceed with target setting, please do the allocation.\");\n        return false;\n      }\n      this.selectedAgencyTarget.agencyId = data.agencyDetails[0].agencyId;\n      this.selectedAgency = data.agencyDetails[0];\n      this.calcResPendingTargetsAmt();\n      this.calcMoneyCollecteAmt();\n      this.calcMoneyCollectePers();\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.loader.isSearching = false;\n    });\n  }\n  // calculate resolutions pending targets amount\n  calcResPendingTargetsAmt() {\n    /*-- Resolution amount  total */\n    this.targetInfo.target.targetPOSforResolution = Number((this.targetInfo.target.targetPOSforNorm + this.targetInfo.target.targetPOSforRB + this.targetInfo.target.targetPOSforStab).toFixed(2));\n    /*--  Resolutions count total*/\n    this.targetInfo.target.accountCountResolution = Number((this.targetInfo.target.accountCountNorm + this.targetInfo.target.accountCountRB + this.targetInfo.target.accountCountStab).toFixed(2));\n    /*--norm percentage */\n    this.targetInfo.target.percentageNorm = Number((this.targetInfo.target.targetPOSforNorm / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.target.accountPercentageNorm = Number((this.targetInfo.target.accountCountNorm / this.selectedAgency.totalAccount * 100).toFixed(2));\n    /*--roll back percentage*/\n    this.targetInfo.target.percentageRB = Number((this.targetInfo.target.targetPOSforRB / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.target.accountPercentageRB = Number((this.targetInfo.target.accountCountRB / this.selectedAgency.totalAccount * 100).toFixed(2));\n    /*--Stabilization percentage */\n    this.targetInfo.target.percentageStab = Number((this.targetInfo.target.targetPOSforStab / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.target.accountPercentageStab = Number((this.targetInfo.target.accountCountStab / this.selectedAgency.totalAccount * 100).toFixed(2));\n    /*-- resolution amount percentage  total */\n    this.targetInfo.target.percentageResolution = Number((this.targetInfo.target.percentageNorm + this.targetInfo.target.percentageRB + this.targetInfo.target.percentageStab).toFixed(2));\n    /*--resolution count percentage  total*/\n    this.targetInfo.target.accountPercentageResolution = Number((this.targetInfo.target.accountPercentageNorm + this.targetInfo.target.accountPercentageRB + this.targetInfo.target.accountPercentageStab).toFixed(2));\n    /*--RF percentage */\n    this.targetInfo.target.percentageRF = Number((this.targetInfo.target.targetPOSforRF / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.target.accountPercentageRF = Number((this.targetInfo.target.accountCountRF / this.selectedAgency.totalAccount * 100).toFixed(2));\n  }\n  /*--money collected amount percentage*/\n  calcMoneyCollecteAmt() {\n    this.targetInfo.target.percentageMoneyCollected = Number((this.targetInfo.target.targetMoneyCollected / this.selectedAgency.totalArrear * 100).toFixed(2));\n  }\n  /*--money collected count percentage*/\n  calcMoneyCollectePers() {\n    this.targetInfo.target.accountPercentageMoneyCollected = Number((this.targetInfo.target.accountCountMoneyCollected / this.selectedAgency.totalAccount * 100).toFixed(2));\n  }\n  /*--normalization agency amount percentage*/\n  agencyNormPerCalc() {\n    this.selectedAgencyTarget.percentageNorm = Number((this.selectedAgencyTarget.targetPOSforNorm / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--roll back agency amount percentage*/\n  agencyRBPerCalc() {\n    this.selectedAgencyTarget.percentageRB = Number((this.selectedAgencyTarget.targetPOSforRB / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--Stabilization agency amount percentage*/\n  agencyStabPerCalc() {\n    this.selectedAgencyTarget.percentageStab = Number((this.selectedAgencyTarget.targetPOSforStab / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--normalization agency count percentage*/\n  agencyNormCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageNorm = Number((this.selectedAgencyTarget.accountCountNorm / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--roll back agency count percentage*/\n  agencyRBCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageRB = Number((this.selectedAgencyTarget.accountCountRB / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--Stabilization agency count percentage*/\n  agencyStabCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageStab = Number((this.selectedAgencyTarget.accountCountStab / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  agencyAmtResCalc() {\n    /*-- Resolution amount  total */\n    this.selectedAgencyTarget.targetResolution = Number((this.selectedAgencyTarget.targetPOSforNorm + this.selectedAgencyTarget.targetPOSforRB + this.selectedAgencyTarget.targetPOSforStab).toFixed(2));\n    /*-- resolution amount percentage  total */\n    this.selectedAgencyTarget.percentageResolution = Number((this.selectedAgencyTarget.percentageNorm + this.selectedAgencyTarget.percentageRB + this.selectedAgencyTarget.percentageStab).toFixed(2));\n  }\n  agencyCountResCalc() {\n    /*--  Resolutions count total*/\n    this.selectedAgencyTarget.accountCountMoneyResolution = Number((this.selectedAgencyTarget.accountCountNorm + this.selectedAgencyTarget.accountCountRB + this.selectedAgencyTarget.accountCountStab).toFixed(2));\n    /*--resolution count percentage  total*/\n    this.selectedAgencyTarget.accountPercentageResolution = Number((this.selectedAgencyTarget.accountPercentageNorm + this.selectedAgencyTarget.accountPercentageRB + this.selectedAgencyTarget.accountPercentageStab).toFixed(2));\n  }\n  /*--RF agency amount percentage*/\n  agencyRFPerCalc() {\n    this.selectedAgencyTarget.percentageRF = Number((this.selectedAgencyTarget.targetPOSforRF / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--RF agency count percentage*/\n  agencyRFCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageRF = Number((this.selectedAgencyTarget.accountCountRF / this.selectedAgency.totalAccount * 100).toFixed(2));\n  }\n  /*--Money Collected agency amount percentage*/\n  agencyMCPerCalc() {\n    this.selectedAgencyTarget.percentageMoneyCollected = Number((this.selectedAgencyTarget.targetMoneyCollected / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--Money Collected agency count percentage*/\n  agencyMCCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageMoneyCollected = Number((this.selectedAgencyTarget.accountCountMoneyCollected / this.selectedAgency.totalAccount * 100).toFixed(2));\n  }\n  submit() {\n    let inputParams = {\n      BudgetTargetId: this.targetInfo.target.id,\n      letterSequence: \"\",\n      agencyTargetList: []\n    };\n    inputParams.agencyTargetList.push(this.selectedAgencyTarget);\n    this.loader.isSubmit = true;\n    this.targetService.createTarget(inputParams).subscribe(data => {\n      this.toastr.success(\"Target info submiitted successfully\");\n      this.router.navigateByUrl('/target/search-target');\n      this.loader.isSubmit = false;\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.loader.isSubmit = false;\n    });\n  }\n  cancel() {\n    this.router.navigateByUrl('/home');\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: TargetService\n    }, {\n      type: BsModalService\n    }, {\n      type: Router\n    }];\n  }\n};\nCreateTargetComponent = __decorate([Component({\n  selector: 'create-target',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTargetComponent);\nexport { CreateTargetComponent };", "map": {"version": 3, "names": ["SearchControls", "constructor", "month", "year", "productGroup", "product", "subProduct", "bucket", "network", "zone", "region", "state", "city", "agencyType", "agencyName", "statusOptions", "id", "name", "Component", "ToastrService", "BsModalService", "Router", "TargetService", "CreateTargetComponent", "toastr", "targetService", "modalService", "router", "breadcrumbData", "label", "path", "searchControls", "productGroupList", "productList", "subProductList", "bucketList", "regionList", "zoneList", "agencyTypeList", "agencyNameList", "stateList", "cityList", "showTarget", "loader", "isSearching", "isSubmit", "getProductGroupList", "subscribe", "response", "splice", "err", "error", "getBucketList", "fetchAgencyTypes", "agencyTypes", "filter", "itm", "getAgencyList", "getMasterCountry", "geomasterList", "loadZone", "targetInfo", "target", "agencyList", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetPOSforNorm", "percentageNorm", "accountCountNorm", "accountPercentageNorm", "targetPOSforRB", "percentageRB", "accountCountRB", "accountPercentageRB", "targetPOSforStab", "percentageStab", "accountPercentageStab", "accountCountStab", "targetPOSforRF", "percentageRF", "accountCountRF", "accountPercentageRF", "targetResolution", "percentageResolution", "accountCountMoneyResolution", "accountPercentageResolution", "targetMoneyCollected", "percentageMoneyCollected", "accountCountMoneyCollected", "accountPercentageMoneyCollected", "ngOnInit", "getProducts", "getProductListByPG", "push", "getSubproducts", "getSubproductListByProduct", "removeDuplicates", "getRegions", "regionAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "regionAllocation", "stateAdd", "stateAllocation", "cityAdd", "cityAllocation", "loadState", "loadCity", "search", "find", "obj", "productGroupname", "productName", "searchData", "searchTargetInfo", "info", "length", "agencyId", "agencyDetails", "selectedAgency", "calcResPendingTargetsAmt", "calcMoneyCollecteAmt", "calcMoneyCollectePers", "targetPOSforResolution", "Number", "toFixed", "accountCountResolution", "totalPOS", "totalAccount", "totalArrear", "agencyNormPerCalc", "agencyRBPerCalc", "agencyStabPerCalc", "agencyNormCountPerCalc", "agencyRBCountPerCalc", "agencyStabCountPerCalc", "agencyAmtResCalc", "agencyCountResCalc", "agencyRFPerCalc", "agencyRFCountPerCalc", "agencyMCPerCalc", "agencyMCCountPerCalc", "submit", "inputParams", "BudgetTargetId", "letterSequence", "agencyTargetList", "createTarget", "success", "navigateByUrl", "cancel", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\target\\create-target\\create-target.component.ts"], "sourcesContent": ["export class SearchControls{\r\n  month:string = '';\r\n  year:string= '';\r\n  productGroup: string= '';\r\n  product: string= '';\r\n  subProduct: string= '';\r\n  bucket: string= '';\r\n  network: string= '';\r\n  zone:string= '';\r\n  region: string= '';\r\n  state:string= '';\r\n  city:string= '';\r\n  agencyType:string= '';\r\n  agencyName:string= '';\r\n}\r\n\r\nconst statusOptions = [ \r\n   { id:'1', name:'January'}, \r\n   { id:'2',name:  'February'}, \r\n   { id:'3',name: 'March'}, \r\n   { id:'4',name: 'April'}, \r\n   { id:'5',name: 'May'},\r\n   { id:'6',name: 'June'},\r\n   { id:'7',name: 'July'},\r\n   { id:'8',name: 'August'},\r\n   { id:'9',name: 'September'},\r\n   { id:'10',name: 'October'},\r\n   { id:'11',name: 'November'},\r\n   { id:'12',name: 'December'}]\r\n\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService } from 'ngx-bootstrap/modal';\r\nimport { Router } from '@angular/router';\r\nimport { TargetService } from '../target.service';\r\n\r\n\r\n@Component({\r\n  selector: 'create-target',\r\n  templateUrl: './create-target.component.html',\r\n  styleUrls: ['./create-target.component.css']\r\n})\r\nexport class CreateTargetComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Target\", path: \"/target/create-target\" },\r\n\t\t{ label: \"Create Targets\", path: \"/target/create-target\" },\r\n\t  ]\r\n  searchControls: SearchControls = new SearchControls();\r\n  statusOptions = statusOptions;\r\n  productGroupList: Array<object> = [];\r\n  productList: Array<object> = [];\r\n  subProductList: Array<object> = [];\r\n  bucketList: Array<object> = [];\r\n  regionList: Array<object> = [];\r\n  zoneList :Array<object> = [];\r\n  agencyTypeList :Array<object> = [];\r\n  agencyNameList :Array<object> = [];\r\n  stateList :Array<object> = [];\r\n  cityList :Array<object> = [];\r\n  geomasterList:any;\r\n  targetAgencyTargetList:any;\r\n\r\n  targetInfo:any;\r\n  agencyList:any;\r\n  agencyId1:any;\r\n  agencyId2:any;\r\n  showTarget:boolean=false;\r\n  selectedAgencyTarget: any;\r\n  selectedAgency: any;\r\n\r\n  loader = {\r\n    isSearching: false,\r\n    isSubmit: false\r\n  }\r\n\r\n  constructor(public toastr: ToastrService, \r\n    private targetService: TargetService,\r\n\t\tprivate modalService: BsModalService,\r\n    private router: Router) {\r\n    \r\n    this.targetService.getProductGroupList().subscribe(response=> {\r\n      this.productGroupList = response\r\n      this.productGroupList.splice( 0, 0, {\"id\":\"All\",\"name\":\"All\", \"code\":\"All\"});\r\n    }, err=>{\r\n         this.toastr.error(err, \"Error!\")\r\n    });\r\n\r\n    this.targetService.getBucketList().subscribe(response => {\r\n      this.bucketList = response\r\n    }, err=>{\r\n            this.toastr.error(err, \"Error!\")\r\n    });\r\n\r\n    this.targetService.fetchAgencyTypes().subscribe(agencyTypes => {\r\n        this.agencyTypeList=agencyTypes.filter((itm)=>{\r\n           return itm.agencyType =='Collections';\r\n         });\r\n    });\r\n\r\n    this.targetService.getAgencyList().subscribe(response=>{\r\n      this.agencyNameList = response\r\n    }, err=>{\r\n         this.toastr.error(err, \"Error!\")\r\n    });\r\n\r\n    this.targetService.getMasterCountry().subscribe(response =>{\r\n      this.geomasterList = response;\r\n      this.loadZone();\r\n    },(error) => {\r\n      this.toastr.error(error, \"Error!\")\r\n    });\r\n\r\n    this.targetInfo = {}\r\n    this.targetInfo.target = {}\r\n    this.agencyList = []\r\n    this.targetInfo.agencyList =  this.agencyList\r\n    this.selectedAgencyTarget = {\r\n      targetPOSforNorm:0,\r\n      percentageNorm: 0,\r\n      accountCountNorm: 0,\r\n      accountPercentageNorm: 0,\r\n\r\n      targetPOSforRB: 0,\r\n      percentageRB: 0,\r\n      accountCountRB: 0,\r\n      accountPercentageRB: 0,\r\n\r\n\r\n      targetPOSforStab: 0,\r\n      percentageStab: 0,\r\n      accountPercentageStab: 0,\r\n      accountCountStab: 0,\r\n\r\n      targetPOSforRF: 0,\r\n      percentageRF: 0,\r\n      accountCountRF: 0,\r\n      accountPercentageRF: 0,\r\n\r\n      targetResolution: 0,\r\n      percentageResolution: 0,\r\n      accountCountMoneyResolution: 0,\r\n      accountPercentageResolution: 0,\r\n\r\n      targetMoneyCollected: 0,\r\n      percentageMoneyCollected: 0,\r\n      accountCountMoneyCollected: 0,\r\n      accountPercentageMoneyCollected: 0\r\n    }\r\n     \r\n  }\r\n\r\n  ngOnInit(): void {}\r\n\r\n  getProducts(){\r\n    this.productList = []\r\n    this.subProductList = []\r\n    this.searchControls.product = \"\"\r\n    this.searchControls.subProduct = \"\"\r\n    if(this.searchControls.productGroup!=\"All\" && this.searchControls.productGroup!=\"\"){\r\n    this.targetService.getProductListByPG(this.searchControls.productGroup).subscribe(response=> {\r\n         this.productList = response\r\n         this.productList.splice( 0, 0, {\"id\":\"All\",\"name\":\"All\",\"code\":\"All\"});\r\n       },\r\n        err=>{\r\n           this.toastr.error(err, \"Error!\")\r\n      });\r\n    }\r\n      else if(this.searchControls.productGroup==\"All\"){\r\n       this.productList.push({\"id\":\"All\",\"name\":\"All\",\"code\":\"All\"});\r\n     }\r\n\r\n  }\r\n\r\n  getSubproducts(){\r\n    this.subProductList = []\r\n    this.searchControls.subProduct = \"\"\r\n    if(this.searchControls.product!=\"All\" && this.searchControls.product!=\"\"){\r\n        this.targetService.getSubproductListByProduct(this.searchControls.product).subscribe(response=> {\r\n          this.subProductList = response\r\n          this.subProductList.splice( 0, 0, {\"id\":\"All\",\"name\":\"All\",\"code\":\"All\"});\r\n        },err=>{\r\n            this.toastr.error(err, \"Error!\")\r\n        });\r\n    }else if(this.searchControls.product==\"All\"){\r\n      this.subProductList.push({\"id\":\"All\",\"name\":\"All\",\"code\":\"All\"});\r\n    }\r\n  }\r\n\r\n  loadZone(){\r\n   this.zoneList= this.targetService.removeDuplicates(this.geomasterList,\"name\")\r\n  }\r\n\r\n  getRegions(){\r\n    this.regionAdd();\r\n    this.regionList =  this.targetService.generalFilterByKey(this.regionList,this.searchControls.region, \"name\")\r\n    this.regionList= this.targetService.removeDuplicates(this.regionList,\"name\")\r\n    this.regionList =this.targetService.generalKeySort(this.regionList,\"name\")\r\n  }\r\n\r\n regionAdd(){\r\n    this.regionList=[]\r\n    let data={\r\n      \"country\":this.searchControls.zone\r\n    }\r\n    this.targetService.regionAllocation(data).subscribe(response => {\r\n      this.regionList = response\r\n    }, err=>{\r\n            this.toastr.error(err, \"Error!\")\r\n    });\r\n  }\r\n\r\n  stateAdd(){\r\n    let data={\r\n      \"country\":this.searchControls.zone,\r\n      \"region\":this.searchControls.region,\r\n    }\r\n    this.targetService.stateAllocation(data).subscribe((response: any) => {\r\n      this.stateList=[];\r\n      this.stateList=response;\r\n     },error=>{\r\n     this.toastr.error(error, \"Error!\")\r\n    })\r\n  }\r\n  \r\n  cityAdd(){\r\n    let data={\r\n      \"country\":this.searchControls.zone,\r\n      \"region\":this.searchControls.region,\r\n      \"state\":this.searchControls.state\r\n    }\r\n    this.targetService.cityAllocation(data).subscribe((response: any) => {\r\n      this.cityList=[];\r\n      this.cityList=response;\r\n     },error=>{\r\n     this.toastr.error(error, \"Error!\")\r\n    })\r\n  }\r\n\r\n  loadState(){\r\n      this.stateAdd();\r\n      this.stateList =  this.targetService.generalFilterByKey(this.geomasterList,this.searchControls.state, \"name\")\r\n      this.stateList= this.targetService.removeDuplicates(this.stateList,\"name\")\r\n      this.stateList =this.targetService.generalKeySort(this.stateList,\"name\")\r\n  }\r\n  \r\n  loadCity(state) { \r\n     this.cityAdd();\r\n     this.cityList =  this.targetService.generalFilterByKey(this.geomasterList,this.searchControls.city, \"name\")\r\n     this.cityList= this.targetService.removeDuplicates(this.cityList,\"name\")\r\n     this.cityList =this.targetService.generalKeySort(this.cityList,\"name\")\r\n  }\r\n\r\n  search(){\r\n      if(this.searchControls.month==\"\" || this.searchControls.year==\"\" || this.searchControls.productGroup ==\"\" ||\r\n        this.searchControls.agencyType ==\"\" || this.searchControls.agencyName==\"\" || this.searchControls.bucket==\"\"){\r\n        this.toastr.error(\"please enter the mandatory fields\", \"Error!\");\r\n        return false\r\n      }\r\n      let productGroup= this.productGroupList.find((obj: any)=> obj.id == this.searchControls.productGroup )\r\n      let productGroupname = productGroup[\"name\"]\r\n       \r\n      var productName = \"\"\r\n      if(this.searchControls.product){\r\n        productName= this.productList.find((obj: any)=> obj.id == this.searchControls.product)[\"name\"]\r\n      }\r\n      let searchData = {\r\n        \"month\": this.searchControls.month,\r\n        \"year\": this.searchControls.year,\r\n        \"productGroup\": productGroupname,\r\n        \"product\": productName,\r\n        \"subProduct\": this.searchControls.subProduct,\r\n        \"bucket\": this.searchControls.bucket,\r\n        \"zone\": this.searchControls.zone,\r\n        \"region\": this.searchControls.region,\r\n        \"state\": this.searchControls.state,\r\n        \"city\": this.searchControls.city,\r\n        \"agencyIds\": [this.searchControls.agencyName],\r\n        \"agencyType\": this.searchControls.agencyType\r\n     }\r\n     this.loader.isSearching = true;\r\n     this.targetService.searchTargetInfo(searchData)\r\n    \t  .subscribe(data => {\r\n          this.loader.isSearching = false;\r\n          if(data.target==null){\r\n             this.toastr.info(\"No results found!\");\r\n             return false;\r\n          }\r\n          this.targetInfo = data;\r\n          this.agencyList = data[\"agencyDetails\"]\r\n          if(this.agencyList.length==0){\r\n            this.toastr.info(\"There is no allocation to the selected agency. To proceed with target setting, please do the allocation.\");\r\n            return false\r\n          }\r\n          this.selectedAgencyTarget.agencyId = data.agencyDetails[0].agencyId;\r\n          this.selectedAgency =  data.agencyDetails[0]\r\n          this.calcResPendingTargetsAmt()\r\n          this.calcMoneyCollecteAmt()\r\n          this.calcMoneyCollectePers();\r\n\r\n     }, err=> {\r\n          this.toastr.error(err, \"Error!\");\r\n          this.loader.isSearching = false;\r\n     });\r\n\r\n  }\r\n\r\n    // calculate resolutions pending targets amount\r\n  calcResPendingTargetsAmt(){\r\n      /*-- Resolution amount  total */\r\n      this.targetInfo.target.targetPOSforResolution = Number((this.targetInfo.target.targetPOSforNorm +\r\n                                                      this.targetInfo.target.targetPOSforRB +\r\n                                                      this.targetInfo.target.targetPOSforStab).toFixed(2))\r\n\r\n     /*--  Resolutions count total*/\r\n      this.targetInfo.target.accountCountResolution = Number((this.targetInfo.target.accountCountNorm +\r\n                                                      this.targetInfo.target.accountCountRB +\r\n                                                      this.targetInfo.target.accountCountStab).toFixed(2))\r\n\r\n      /*--norm percentage */\r\n      this.targetInfo.target.percentageNorm = Number((this.targetInfo.target.targetPOSforNorm/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo.target.accountPercentageNorm = Number((this.targetInfo.target.accountCountNorm/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n      \r\n      /*--roll back percentage*/\r\n      this.targetInfo.target.percentageRB = Number((this.targetInfo.target.targetPOSforRB/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo.target.accountPercentageRB = Number((this.targetInfo.target.accountCountRB/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n\r\n      /*--Stabilization percentage */\r\n      this.targetInfo.target.percentageStab = Number((this.targetInfo.target.targetPOSforStab/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo.target.accountPercentageStab = Number((this.targetInfo.target.accountCountStab/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n\r\n\r\n\r\n      /*-- resolution amount percentage  total */\r\n      this.targetInfo.target.percentageResolution =  Number((this.targetInfo.target.percentageNorm +\r\n                                                            this.targetInfo.target.percentageRB +\r\n                                                            this.targetInfo.target.percentageStab).toFixed(2))\r\n\r\n\r\n      /*--resolution count percentage  total*/\r\n      this.targetInfo.target.accountPercentageResolution =  Number((this.targetInfo.target.accountPercentageNorm +\r\n                                                                    this.targetInfo.target.accountPercentageRB +\r\n                                                                    this.targetInfo.target.accountPercentageStab).toFixed(2))\r\n      /*--RF percentage */\r\n      this.targetInfo.target.percentageRF = Number((this.targetInfo.target.targetPOSforRF/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo.target.accountPercentageRF = Number((this.targetInfo.target.accountCountRF/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n\r\n\r\n    }\r\n\r\n    /*--money collected amount percentage*/\r\n    calcMoneyCollecteAmt(){\r\n       this.targetInfo.target.percentageMoneyCollected = Number((this.targetInfo.target.targetMoneyCollected/(this.selectedAgency.totalArrear)*100).toFixed(2))\r\n    }\r\n    /*--money collected count percentage*/\r\n\r\n    calcMoneyCollectePers(){\r\n       this.targetInfo.target.accountPercentageMoneyCollected = Number((this.targetInfo.target.accountCountMoneyCollected/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n      \r\n    }\r\n    /*--normalization agency amount percentage*/\r\n    agencyNormPerCalc(){\r\n      this.selectedAgencyTarget.percentageNorm = Number((this.selectedAgencyTarget.targetPOSforNorm/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n    /*--roll back agency amount percentage*/\r\n    agencyRBPerCalc(){\r\n      this.selectedAgencyTarget.percentageRB = Number((this.selectedAgencyTarget.targetPOSforRB/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n    /*--Stabilization agency amount percentage*/\r\n    agencyStabPerCalc(){\r\n      this.selectedAgencyTarget.percentageStab = Number((this.selectedAgencyTarget.targetPOSforStab/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n\r\n    /*--normalization agency count percentage*/\r\n    agencyNormCountPerCalc(){\r\n      this.selectedAgencyTarget.accountPercentageNorm = Number((this.selectedAgencyTarget.accountCountNorm/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n    /*--roll back agency count percentage*/\r\n    agencyRBCountPerCalc(){\r\n      this.selectedAgencyTarget.accountPercentageRB = Number((this.selectedAgencyTarget.accountCountRB/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n    /*--Stabilization agency count percentage*/\r\n    agencyStabCountPerCalc(){\r\n      this.selectedAgencyTarget.accountPercentageStab = Number((this.selectedAgencyTarget.accountCountStab/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n\r\n\r\n    agencyAmtResCalc(){\r\n      /*-- Resolution amount  total */\r\n      this.selectedAgencyTarget.targetResolution = Number((this.selectedAgencyTarget.targetPOSforNorm +\r\n                                                      this.selectedAgencyTarget.targetPOSforRB +\r\n                                                      this.selectedAgencyTarget.targetPOSforStab).toFixed(2))\r\n        /*-- resolution amount percentage  total */\r\n      this.selectedAgencyTarget.percentageResolution = Number((this.selectedAgencyTarget.percentageNorm +\r\n                                                      this.selectedAgencyTarget.percentageRB +\r\n                                                      this.selectedAgencyTarget.percentageStab).toFixed(2))\r\n      \r\n    }\r\n\r\n    agencyCountResCalc(){\r\n      /*--  Resolutions count total*/\r\n      this.selectedAgencyTarget.accountCountMoneyResolution = Number((this.selectedAgencyTarget.accountCountNorm +\r\n                                                      this.selectedAgencyTarget.accountCountRB +\r\n                                                      this.selectedAgencyTarget.accountCountStab).toFixed(2))\r\n      /*--resolution count percentage  total*/\r\n      this.selectedAgencyTarget.accountPercentageResolution  =  Number((this.selectedAgencyTarget.accountPercentageNorm +\r\n                                                      this.selectedAgencyTarget.accountPercentageRB +\r\n                                                      this.selectedAgencyTarget.accountPercentageStab).toFixed(2))\r\n    }\r\n\r\n\r\n    /*--RF agency amount percentage*/\r\n    agencyRFPerCalc(){\r\n      this.selectedAgencyTarget.percentageRF = Number((this.selectedAgencyTarget.targetPOSforRF/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n\r\n    /*--RF agency count percentage*/\r\n    agencyRFCountPerCalc(){\r\n      this.selectedAgencyTarget.accountPercentageRF = Number((this.selectedAgencyTarget.accountCountRF/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n    }\r\n\r\n    /*--Money Collected agency amount percentage*/\r\n    agencyMCPerCalc(){\r\n      this.selectedAgencyTarget.percentageMoneyCollected = Number((this.selectedAgencyTarget.targetMoneyCollected/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n    }\r\n\r\n\r\n    /*--Money Collected agency count percentage*/\r\n    agencyMCCountPerCalc(){\r\n      this.selectedAgencyTarget.accountPercentageMoneyCollected = Number((this.selectedAgencyTarget.accountCountMoneyCollected/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n    }\r\n\r\n\r\n    submit(){\r\n      let inputParams = {\r\n        BudgetTargetId: this.targetInfo.target.id,\r\n        letterSequence: \"\",\r\n        agencyTargetList: []\r\n      }\r\n      inputParams.agencyTargetList.push(this.selectedAgencyTarget);\r\n      this.loader.isSubmit = true\r\n      this.targetService.createTarget(inputParams)\r\n        .subscribe(data => {\r\n            this.toastr.success(\"Target info submiitted successfully\");\r\n            this.router.navigateByUrl('/target/search-target');\r\n            this.loader.isSubmit = false\r\n          }, err=> {\r\n            this.toastr.error(err, \"Error!\")\r\n            this.loader.isSubmit = false\r\n      });\r\n\r\n    }\r\n\r\n    cancel(){\r\n      this.router.navigateByUrl('/home');\r\n    }\r\n    \r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;EAA3BC,YAAA;IACE,KAAAC,KAAK,GAAU,EAAE;IACjB,KAAAC,IAAI,GAAS,EAAE;IACf,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAC,IAAI,GAAS,EAAE;IACf,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,KAAK,GAAS,EAAE;IAChB,KAAAC,IAAI,GAAS,EAAE;IACf,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,UAAU,GAAS,EAAE;EACvB;;AAEA,MAAMC,aAAa,GAAG,CACnB;EAAEC,EAAE,EAAC,GAAG;EAAEC,IAAI,EAAC;AAAS,CAAC,EACzB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAG;AAAU,CAAC,EAC3B;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAO,CAAC,EACvB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAO,CAAC,EACvB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAK,CAAC,EACrB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAM,CAAC,EACtB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAM,CAAC,EACtB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAQ,CAAC,EACxB;EAAED,EAAE,EAAC,GAAG;EAACC,IAAI,EAAE;AAAW,CAAC,EAC3B;EAAED,EAAE,EAAC,IAAI;EAACC,IAAI,EAAE;AAAS,CAAC,EAC1B;EAAED,EAAE,EAAC,IAAI;EAACC,IAAI,EAAE;AAAU,CAAC,EAC3B;EAAED,EAAE,EAAC,IAAI;EAACC,IAAI,EAAE;AAAU,CAAC,CAAC;AAE/B,SAASC,SAAS,QAAgB,eAAe;AACjD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,aAAa,QAAQ,mBAAmB;AAQ1C,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAiChCtB,YAAmBuB,MAAqB,EAC9BC,aAA4B,EAC9BC,YAA4B,EAC1BC,MAAc;IAHL,KAAAH,MAAM,GAANA,MAAM;IACf,KAAAC,aAAa,GAAbA,aAAa;IACf,KAAAC,YAAY,GAAZA,YAAY;IACV,KAAAC,MAAM,GAANA,MAAM;IAnCT,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAuB,CAAE,EAClD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAuB,CAAE,CACxD;IACF,KAAAC,cAAc,GAAmB,IAAI/B,cAAc,EAAE;IACrD,KAAAe,aAAa,GAAGA,aAAa;IAC7B,KAAAiB,gBAAgB,GAAkB,EAAE;IACpC,KAAAC,WAAW,GAAkB,EAAE;IAC/B,KAAAC,cAAc,GAAkB,EAAE;IAClC,KAAAC,UAAU,GAAkB,EAAE;IAC9B,KAAAC,UAAU,GAAkB,EAAE;IAC9B,KAAAC,QAAQ,GAAkB,EAAE;IAC5B,KAAAC,cAAc,GAAkB,EAAE;IAClC,KAAAC,cAAc,GAAkB,EAAE;IAClC,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,QAAQ,GAAkB,EAAE;IAQ5B,KAAAC,UAAU,GAAS,KAAK;IAIxB,KAAAC,MAAM,GAAG;MACPC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE;KACX;IAOC,IAAI,CAACpB,aAAa,CAACqB,mBAAmB,EAAE,CAACC,SAAS,CAACC,QAAQ,IAAE;MAC3D,IAAI,CAAChB,gBAAgB,GAAGgB,QAAQ;MAChC,IAAI,CAAChB,gBAAgB,CAACiB,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE;QAAC,IAAI,EAAC,KAAK;QAAC,MAAM,EAAC,KAAK;QAAE,MAAM,EAAC;MAAK,CAAC,CAAC;IAC9E,CAAC,EAAEC,GAAG,IAAE;MACH,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IACrC,CAAC,CAAC;IAEF,IAAI,CAACzB,aAAa,CAAC2B,aAAa,EAAE,CAACL,SAAS,CAACC,QAAQ,IAAG;MACtD,IAAI,CAACb,UAAU,GAAGa,QAAQ;IAC5B,CAAC,EAAEE,GAAG,IAAE;MACA,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IACxC,CAAC,CAAC;IAEF,IAAI,CAACzB,aAAa,CAAC4B,gBAAgB,EAAE,CAACN,SAAS,CAACO,WAAW,IAAG;MAC1D,IAAI,CAAChB,cAAc,GAACgB,WAAW,CAACC,MAAM,CAAEC,GAAG,IAAG;QAC3C,OAAOA,GAAG,CAAC3C,UAAU,IAAG,aAAa;MACvC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,IAAI,CAACY,aAAa,CAACgC,aAAa,EAAE,CAACV,SAAS,CAACC,QAAQ,IAAE;MACrD,IAAI,CAACT,cAAc,GAAGS,QAAQ;IAChC,CAAC,EAAEE,GAAG,IAAE;MACH,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IACrC,CAAC,CAAC;IAEF,IAAI,CAACzB,aAAa,CAACiC,gBAAgB,EAAE,CAACX,SAAS,CAACC,QAAQ,IAAG;MACzD,IAAI,CAACW,aAAa,GAAGX,QAAQ;MAC7B,IAAI,CAACY,QAAQ,EAAE;IACjB,CAAC,EAAET,KAAK,IAAI;MACV,IAAI,CAAC3B,MAAM,CAAC2B,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;IACpC,CAAC,CAAC;IAEF,IAAI,CAACU,UAAU,GAAG,EAAE;IACpB,IAAI,CAACA,UAAU,CAACC,MAAM,GAAG,EAAE;IAC3B,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACF,UAAU,CAACE,UAAU,GAAI,IAAI,CAACA,UAAU;IAC7C,IAAI,CAACC,oBAAoB,GAAG;MAC1BC,gBAAgB,EAAC,CAAC;MAClBC,cAAc,EAAE,CAAC;MACjBC,gBAAgB,EAAE,CAAC;MACnBC,qBAAqB,EAAE,CAAC;MAExBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,CAAC;MAGtBC,gBAAgB,EAAE,CAAC;MACnBC,cAAc,EAAE,CAAC;MACjBC,qBAAqB,EAAE,CAAC;MACxBC,gBAAgB,EAAE,CAAC;MAEnBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,CAAC;MAEtBC,gBAAgB,EAAE,CAAC;MACnBC,oBAAoB,EAAE,CAAC;MACvBC,2BAA2B,EAAE,CAAC;MAC9BC,2BAA2B,EAAE,CAAC;MAE9BC,oBAAoB,EAAE,CAAC;MACvBC,wBAAwB,EAAE,CAAC;MAC3BC,0BAA0B,EAAE,CAAC;MAC7BC,+BAA+B,EAAE;KAClC;EAEH;EAEAC,QAAQA,CAAA,GAAU;EAElBC,WAAWA,CAAA;IACT,IAAI,CAACzD,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACH,cAAc,CAAC1B,OAAO,GAAG,EAAE;IAChC,IAAI,CAAC0B,cAAc,CAACzB,UAAU,GAAG,EAAE;IACnC,IAAG,IAAI,CAACyB,cAAc,CAAC3B,YAAY,IAAE,KAAK,IAAI,IAAI,CAAC2B,cAAc,CAAC3B,YAAY,IAAE,EAAE,EAAC;MACnF,IAAI,CAACqB,aAAa,CAACkE,kBAAkB,CAAC,IAAI,CAAC5D,cAAc,CAAC3B,YAAY,CAAC,CAAC2C,SAAS,CAACC,QAAQ,IAAE;QACvF,IAAI,CAACf,WAAW,GAAGe,QAAQ;QAC3B,IAAI,CAACf,WAAW,CAACgB,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE;UAAC,IAAI,EAAC,KAAK;UAAC,MAAM,EAAC,KAAK;UAAC,MAAM,EAAC;QAAK,CAAC,CAAC;MACxE,CAAC,EACAC,GAAG,IAAE;QACF,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,MACM,IAAG,IAAI,CAACnB,cAAc,CAAC3B,YAAY,IAAE,KAAK,EAAC;MAC/C,IAAI,CAAC6B,WAAW,CAAC2D,IAAI,CAAC;QAAC,IAAI,EAAC,KAAK;QAAC,MAAM,EAAC,KAAK;QAAC,MAAM,EAAC;MAAK,CAAC,CAAC;IAC/D;EAEH;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC3D,cAAc,GAAG,EAAE;IACxB,IAAI,CAACH,cAAc,CAACzB,UAAU,GAAG,EAAE;IACnC,IAAG,IAAI,CAACyB,cAAc,CAAC1B,OAAO,IAAE,KAAK,IAAI,IAAI,CAAC0B,cAAc,CAAC1B,OAAO,IAAE,EAAE,EAAC;MACrE,IAAI,CAACoB,aAAa,CAACqE,0BAA0B,CAAC,IAAI,CAAC/D,cAAc,CAAC1B,OAAO,CAAC,CAAC0C,SAAS,CAACC,QAAQ,IAAE;QAC7F,IAAI,CAACd,cAAc,GAAGc,QAAQ;QAC9B,IAAI,CAACd,cAAc,CAACe,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE;UAAC,IAAI,EAAC,KAAK;UAAC,MAAM,EAAC,KAAK;UAAC,MAAM,EAAC;QAAK,CAAC,CAAC;MAC3E,CAAC,EAACC,GAAG,IAAE;QACH,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MACpC,CAAC,CAAC;IACN,CAAC,MAAK,IAAG,IAAI,CAACnB,cAAc,CAAC1B,OAAO,IAAE,KAAK,EAAC;MAC1C,IAAI,CAAC6B,cAAc,CAAC0D,IAAI,CAAC;QAAC,IAAI,EAAC,KAAK;QAAC,MAAM,EAAC,KAAK;QAAC,MAAM,EAAC;MAAK,CAAC,CAAC;IAClE;EACF;EAEAhC,QAAQA,CAAA;IACP,IAAI,CAACvB,QAAQ,GAAE,IAAI,CAACZ,aAAa,CAACsE,gBAAgB,CAAC,IAAI,CAACpC,aAAa,EAAC,MAAM,CAAC;EAC9E;EAEAqC,UAAUA,CAAA;IACR,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAAC7D,UAAU,GAAI,IAAI,CAACX,aAAa,CAACyE,kBAAkB,CAAC,IAAI,CAAC9D,UAAU,EAAC,IAAI,CAACL,cAAc,CAACrB,MAAM,EAAE,MAAM,CAAC;IAC5G,IAAI,CAAC0B,UAAU,GAAE,IAAI,CAACX,aAAa,CAACsE,gBAAgB,CAAC,IAAI,CAAC3D,UAAU,EAAC,MAAM,CAAC;IAC5E,IAAI,CAACA,UAAU,GAAE,IAAI,CAACX,aAAa,CAAC0E,cAAc,CAAC,IAAI,CAAC/D,UAAU,EAAC,MAAM,CAAC;EAC5E;EAED6D,SAASA,CAAA;IACN,IAAI,CAAC7D,UAAU,GAAC,EAAE;IAClB,IAAIgE,IAAI,GAAC;MACP,SAAS,EAAC,IAAI,CAACrE,cAAc,CAACtB;KAC/B;IACD,IAAI,CAACgB,aAAa,CAAC4E,gBAAgB,CAACD,IAAI,CAAC,CAACrD,SAAS,CAACC,QAAQ,IAAG;MAC7D,IAAI,CAACZ,UAAU,GAAGY,QAAQ;IAC5B,CAAC,EAAEE,GAAG,IAAE;MACA,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IACxC,CAAC,CAAC;EACJ;EAEAoD,QAAQA,CAAA;IACN,IAAIF,IAAI,GAAC;MACP,SAAS,EAAC,IAAI,CAACrE,cAAc,CAACtB,IAAI;MAClC,QAAQ,EAAC,IAAI,CAACsB,cAAc,CAACrB;KAC9B;IACD,IAAI,CAACe,aAAa,CAAC8E,eAAe,CAACH,IAAI,CAAC,CAACrD,SAAS,CAAEC,QAAa,IAAI;MACnE,IAAI,CAACR,SAAS,GAAC,EAAE;MACjB,IAAI,CAACA,SAAS,GAACQ,QAAQ;IACxB,CAAC,EAACG,KAAK,IAAE;MACT,IAAI,CAAC3B,MAAM,CAAC2B,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAqD,OAAOA,CAAA;IACL,IAAIJ,IAAI,GAAC;MACP,SAAS,EAAC,IAAI,CAACrE,cAAc,CAACtB,IAAI;MAClC,QAAQ,EAAC,IAAI,CAACsB,cAAc,CAACrB,MAAM;MACnC,OAAO,EAAC,IAAI,CAACqB,cAAc,CAACpB;KAC7B;IACD,IAAI,CAACc,aAAa,CAACgF,cAAc,CAACL,IAAI,CAAC,CAACrD,SAAS,CAAEC,QAAa,IAAI;MAClE,IAAI,CAACP,QAAQ,GAAC,EAAE;MAChB,IAAI,CAACA,QAAQ,GAACO,QAAQ;IACvB,CAAC,EAACG,KAAK,IAAE;MACT,IAAI,CAAC3B,MAAM,CAAC2B,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAuD,SAASA,CAAA;IACL,IAAI,CAACJ,QAAQ,EAAE;IACf,IAAI,CAAC9D,SAAS,GAAI,IAAI,CAACf,aAAa,CAACyE,kBAAkB,CAAC,IAAI,CAACvC,aAAa,EAAC,IAAI,CAAC5B,cAAc,CAACpB,KAAK,EAAE,MAAM,CAAC;IAC7G,IAAI,CAAC6B,SAAS,GAAE,IAAI,CAACf,aAAa,CAACsE,gBAAgB,CAAC,IAAI,CAACvD,SAAS,EAAC,MAAM,CAAC;IAC1E,IAAI,CAACA,SAAS,GAAE,IAAI,CAACf,aAAa,CAAC0E,cAAc,CAAC,IAAI,CAAC3D,SAAS,EAAC,MAAM,CAAC;EAC5E;EAEAmE,QAAQA,CAAChG,KAAK;IACX,IAAI,CAAC6F,OAAO,EAAE;IACd,IAAI,CAAC/D,QAAQ,GAAI,IAAI,CAAChB,aAAa,CAACyE,kBAAkB,CAAC,IAAI,CAACvC,aAAa,EAAC,IAAI,CAAC5B,cAAc,CAACnB,IAAI,EAAE,MAAM,CAAC;IAC3G,IAAI,CAAC6B,QAAQ,GAAE,IAAI,CAAChB,aAAa,CAACsE,gBAAgB,CAAC,IAAI,CAACtD,QAAQ,EAAC,MAAM,CAAC;IACxE,IAAI,CAACA,QAAQ,GAAE,IAAI,CAAChB,aAAa,CAAC0E,cAAc,CAAC,IAAI,CAAC1D,QAAQ,EAAC,MAAM,CAAC;EACzE;EAEAmE,MAAMA,CAAA;IACF,IAAG,IAAI,CAAC7E,cAAc,CAAC7B,KAAK,IAAE,EAAE,IAAI,IAAI,CAAC6B,cAAc,CAAC5B,IAAI,IAAE,EAAE,IAAI,IAAI,CAAC4B,cAAc,CAAC3B,YAAY,IAAG,EAAE,IACvG,IAAI,CAAC2B,cAAc,CAAClB,UAAU,IAAG,EAAE,IAAI,IAAI,CAACkB,cAAc,CAACjB,UAAU,IAAE,EAAE,IAAI,IAAI,CAACiB,cAAc,CAACxB,MAAM,IAAE,EAAE,EAAC;MAC5G,IAAI,CAACiB,MAAM,CAAC2B,KAAK,CAAC,mCAAmC,EAAE,QAAQ,CAAC;MAChE,OAAO,KAAK;IACd;IACA,IAAI/C,YAAY,GAAE,IAAI,CAAC4B,gBAAgB,CAAC6E,IAAI,CAAEC,GAAQ,IAAIA,GAAG,CAAC9F,EAAE,IAAI,IAAI,CAACe,cAAc,CAAC3B,YAAY,CAAE;IACtG,IAAI2G,gBAAgB,GAAG3G,YAAY,CAAC,MAAM,CAAC;IAE3C,IAAI4G,WAAW,GAAG,EAAE;IACpB,IAAG,IAAI,CAACjF,cAAc,CAAC1B,OAAO,EAAC;MAC7B2G,WAAW,GAAE,IAAI,CAAC/E,WAAW,CAAC4E,IAAI,CAAEC,GAAQ,IAAIA,GAAG,CAAC9F,EAAE,IAAI,IAAI,CAACe,cAAc,CAAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;IAChG;IACA,IAAI4G,UAAU,GAAG;MACf,OAAO,EAAE,IAAI,CAAClF,cAAc,CAAC7B,KAAK;MAClC,MAAM,EAAE,IAAI,CAAC6B,cAAc,CAAC5B,IAAI;MAChC,cAAc,EAAE4G,gBAAgB;MAChC,SAAS,EAAEC,WAAW;MACtB,YAAY,EAAE,IAAI,CAACjF,cAAc,CAACzB,UAAU;MAC5C,QAAQ,EAAE,IAAI,CAACyB,cAAc,CAACxB,MAAM;MACpC,MAAM,EAAE,IAAI,CAACwB,cAAc,CAACtB,IAAI;MAChC,QAAQ,EAAE,IAAI,CAACsB,cAAc,CAACrB,MAAM;MACpC,OAAO,EAAE,IAAI,CAACqB,cAAc,CAACpB,KAAK;MAClC,MAAM,EAAE,IAAI,CAACoB,cAAc,CAACnB,IAAI;MAChC,WAAW,EAAE,CAAC,IAAI,CAACmB,cAAc,CAACjB,UAAU,CAAC;MAC7C,YAAY,EAAE,IAAI,CAACiB,cAAc,CAAClB;KACpC;IACD,IAAI,CAAC8B,MAAM,CAACC,WAAW,GAAG,IAAI;IAC9B,IAAI,CAACnB,aAAa,CAACyF,gBAAgB,CAACD,UAAU,CAAC,CAC5ClE,SAAS,CAACqD,IAAI,IAAG;MACf,IAAI,CAACzD,MAAM,CAACC,WAAW,GAAG,KAAK;MAC/B,IAAGwD,IAAI,CAACtC,MAAM,IAAE,IAAI,EAAC;QAClB,IAAI,CAACtC,MAAM,CAAC2F,IAAI,CAAC,mBAAmB,CAAC;QACrC,OAAO,KAAK;MACf;MACA,IAAI,CAACtD,UAAU,GAAGuC,IAAI;MACtB,IAAI,CAACrC,UAAU,GAAGqC,IAAI,CAAC,eAAe,CAAC;MACvC,IAAG,IAAI,CAACrC,UAAU,CAACqD,MAAM,IAAE,CAAC,EAAC;QAC3B,IAAI,CAAC5F,MAAM,CAAC2F,IAAI,CAAC,0GAA0G,CAAC;QAC5H,OAAO,KAAK;MACd;MACA,IAAI,CAACnD,oBAAoB,CAACqD,QAAQ,GAAGjB,IAAI,CAACkB,aAAa,CAAC,CAAC,CAAC,CAACD,QAAQ;MACnE,IAAI,CAACE,cAAc,GAAInB,IAAI,CAACkB,aAAa,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACE,wBAAwB,EAAE;MAC/B,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAACC,qBAAqB,EAAE;IAEjC,CAAC,EAAExE,GAAG,IAAE;MACH,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACP,MAAM,CAACC,WAAW,GAAG,KAAK;IACpC,CAAC,CAAC;EAEL;EAEE;EACF4E,wBAAwBA,CAAA;IACpB;IACA,IAAI,CAAC3D,UAAU,CAACC,MAAM,CAAC6D,sBAAsB,GAAGC,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACG,gBAAgB,GAC/C,IAAI,CAACJ,UAAU,CAACC,MAAM,CAACO,cAAc,GACrC,IAAI,CAACR,UAAU,CAACC,MAAM,CAACW,gBAAgB,EAAEoD,OAAO,CAAC,CAAC,CAAC,CAAC;IAErG;IACC,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACgE,sBAAsB,GAAGF,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACK,gBAAgB,GAC/C,IAAI,CAACN,UAAU,CAACC,MAAM,CAACS,cAAc,GACrC,IAAI,CAACV,UAAU,CAACC,MAAM,CAACc,gBAAgB,EAAEiD,OAAO,CAAC,CAAC,CAAC,CAAC;IAEpG;IACA,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACI,cAAc,GAAG0D,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACG,gBAAgB,GAAE,IAAI,CAACsD,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IACvI,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACM,qBAAqB,GAAGwD,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACK,gBAAgB,GAAE,IAAI,CAACoD,cAAc,CAACS,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;IAElJ;IACA,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACQ,YAAY,GAAGsD,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACO,cAAc,GAAE,IAAI,CAACkD,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IACnI,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACU,mBAAmB,GAAGoD,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACS,cAAc,GAAE,IAAI,CAACgD,cAAc,CAACS,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;IAE9I;IACA,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACY,cAAc,GAAGkD,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACW,gBAAgB,GAAE,IAAI,CAAC8C,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IACvI,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACa,qBAAqB,GAAGiD,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACc,gBAAgB,GAAE,IAAI,CAAC2C,cAAc,CAACS,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;IAIlJ;IACA,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACoB,oBAAoB,GAAI0C,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACI,cAAc,GACtC,IAAI,CAACL,UAAU,CAACC,MAAM,CAACQ,YAAY,GACnC,IAAI,CAACT,UAAU,CAACC,MAAM,CAACY,cAAc,EAAEmD,OAAO,CAAC,CAAC,CAAC,CAAC;IAGxG;IACA,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACsB,2BAA2B,GAAIwC,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACM,qBAAqB,GAC5C,IAAI,CAACP,UAAU,CAACC,MAAM,CAACU,mBAAmB,GAC1C,IAAI,CAACX,UAAU,CAACC,MAAM,CAACa,qBAAqB,EAAEkD,OAAO,CAAC,CAAC,CAAC,CAAC;IACvH;IACA,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACgB,YAAY,GAAG8C,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACe,cAAc,GAAE,IAAI,CAAC0C,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IACnI,IAAI,CAAChE,UAAU,CAACC,MAAM,CAACkB,mBAAmB,GAAG4C,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACiB,cAAc,GAAE,IAAI,CAACwC,cAAc,CAACS,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;EAGhJ;EAEA;EACAJ,oBAAoBA,CAAA;IACjB,IAAI,CAAC5D,UAAU,CAACC,MAAM,CAACwB,wBAAwB,GAAGsC,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACuB,oBAAoB,GAAE,IAAI,CAACkC,cAAc,CAACU,WAAY,GAAC,GAAG,EAAEJ,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3J;EACA;EAEAH,qBAAqBA,CAAA;IAClB,IAAI,CAAC7D,UAAU,CAACC,MAAM,CAAC0B,+BAA+B,GAAGoC,MAAM,CAAC,CAAC,IAAI,CAAC/D,UAAU,CAACC,MAAM,CAACyB,0BAA0B,GAAE,IAAI,CAACgC,cAAc,CAACS,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;EAEzK;EACA;EACAK,iBAAiBA,CAAA;IACf,IAAI,CAAClE,oBAAoB,CAACE,cAAc,GAAG0D,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACC,gBAAgB,GAAE,IAAI,CAACsD,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/I;EACA;EACAM,eAAeA,CAAA;IACb,IAAI,CAACnE,oBAAoB,CAACM,YAAY,GAAGsD,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACK,cAAc,GAAE,IAAI,CAACkD,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3I;EACA;EACAO,iBAAiBA,CAAA;IACf,IAAI,CAACpE,oBAAoB,CAACU,cAAc,GAAGkD,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACS,gBAAgB,GAAE,IAAI,CAAC8C,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/I;EAEA;EACAQ,sBAAsBA,CAAA;IACpB,IAAI,CAACrE,oBAAoB,CAACI,qBAAqB,GAAGwD,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACG,gBAAgB,GAAE,IAAI,CAACoD,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EACtJ;EACA;EACAS,oBAAoBA,CAAA;IAClB,IAAI,CAACtE,oBAAoB,CAACQ,mBAAmB,GAAGoD,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACO,cAAc,GAAE,IAAI,CAACgD,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAClJ;EACA;EACAU,sBAAsBA,CAAA;IACpB,IAAI,CAACvE,oBAAoB,CAACW,qBAAqB,GAAGiD,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACY,gBAAgB,GAAE,IAAI,CAAC2C,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EACtJ;EAGAW,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACxE,oBAAoB,CAACiB,gBAAgB,GAAG2C,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACC,gBAAgB,GAC/C,IAAI,CAACD,oBAAoB,CAACK,cAAc,GACxC,IAAI,CAACL,oBAAoB,CAACS,gBAAgB,EAAEoD,OAAO,CAAC,CAAC,CAAC,CAAC;IACrG;IACF,IAAI,CAAC7D,oBAAoB,CAACkB,oBAAoB,GAAG0C,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACE,cAAc,GACjD,IAAI,CAACF,oBAAoB,CAACM,YAAY,GACtC,IAAI,CAACN,oBAAoB,CAACU,cAAc,EAAEmD,OAAO,CAAC,CAAC,CAAC,CAAC;EAEvG;EAEAY,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACzE,oBAAoB,CAACmB,2BAA2B,GAAGyC,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACG,gBAAgB,GAC1D,IAAI,CAACH,oBAAoB,CAACO,cAAc,GACxC,IAAI,CAACP,oBAAoB,CAACY,gBAAgB,EAAEiD,OAAO,CAAC,CAAC,CAAC,CAAC;IACvG;IACA,IAAI,CAAC7D,oBAAoB,CAACoB,2BAA2B,GAAKwC,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACI,qBAAqB,GACjE,IAAI,CAACJ,oBAAoB,CAACQ,mBAAmB,GAC7C,IAAI,CAACR,oBAAoB,CAACW,qBAAqB,EAAEkD,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9G;EAGA;EACAa,eAAeA,CAAA;IACb,IAAI,CAAC1E,oBAAoB,CAACc,YAAY,GAAG8C,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACa,cAAc,GAAE,IAAI,CAAC0C,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3I;EAEA;EACAc,oBAAoBA,CAAA;IAClB,IAAI,CAAC3E,oBAAoB,CAACgB,mBAAmB,GAAG4C,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACe,cAAc,GAAE,IAAI,CAACwC,cAAc,CAACS,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;EACtJ;EAEA;EACAe,eAAeA,CAAA;IACb,IAAI,CAAC5E,oBAAoB,CAACsB,wBAAwB,GAAGsC,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACqB,oBAAoB,GAAE,IAAI,CAACkC,cAAc,CAACQ,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7J;EAGA;EACAgB,oBAAoBA,CAAA;IAClB,IAAI,CAAC7E,oBAAoB,CAACwB,+BAA+B,GAAGoC,MAAM,CAAC,CAAC,IAAI,CAAC5D,oBAAoB,CAACuB,0BAA0B,GAAE,IAAI,CAACgC,cAAc,CAACS,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9K;EAGAiB,MAAMA,CAAA;IACJ,IAAIC,WAAW,GAAG;MAChBC,cAAc,EAAE,IAAI,CAACnF,UAAU,CAACC,MAAM,CAAC9C,EAAE;MACzCiI,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE;KACnB;IACDH,WAAW,CAACG,gBAAgB,CAACtD,IAAI,CAAC,IAAI,CAAC5B,oBAAoB,CAAC;IAC5D,IAAI,CAACrB,MAAM,CAACE,QAAQ,GAAG,IAAI;IAC3B,IAAI,CAACpB,aAAa,CAAC0H,YAAY,CAACJ,WAAW,CAAC,CACzChG,SAAS,CAACqD,IAAI,IAAG;MACd,IAAI,CAAC5E,MAAM,CAAC4H,OAAO,CAAC,qCAAqC,CAAC;MAC1D,IAAI,CAACzH,MAAM,CAAC0H,aAAa,CAAC,uBAAuB,CAAC;MAClD,IAAI,CAAC1G,MAAM,CAACE,QAAQ,GAAG,KAAK;IAC9B,CAAC,EAAEK,GAAG,IAAE;MACN,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACP,MAAM,CAACE,QAAQ,GAAG,KAAK;IAClC,CAAC,CAAC;EAEJ;EAEAyG,MAAMA,CAAA;IACJ,IAAI,CAAC3H,MAAM,CAAC0H,aAAa,CAAC,OAAO,CAAC;EACpC;;;;;;;;;;;;;AA5ZS9H,qBAAqB,GAAAgI,UAAA,EALjCrI,SAAS,CAAC;EACTsI,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAA6C;;CAE9C,CAAC,C,EACWnI,qBAAqB,CA8Z/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}