{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵPlatformNavigation, DOCUMENT, PlatformLocation, ɵnormalizeQueryParams, LocationStrategy, Location } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, Optional, inject, EventEmitter } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {\n  static {\n    this.ɵfac = function PlatformNavigation_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlatformNavigation)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PlatformNavigation,\n      factory: () => (() => window.navigation)(),\n      providedIn: 'platform'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformNavigation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => window.navigation\n    }]\n  }], null, null);\n})();\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nclass FakeNavigation {\n  /** Equivalent to `navigation.currentEntry`. */\n  get currentEntry() {\n    return this.entriesArr[this.currentEntryIndex];\n  }\n  get canGoBack() {\n    return this.currentEntryIndex > 0;\n  }\n  get canGoForward() {\n    return this.currentEntryIndex < this.entriesArr.length - 1;\n  }\n  constructor(window, startURL) {\n    this.window = window;\n    /**\n     * The fake implementation of an entries array. Only same-document entries\n     * allowed.\n     */\n    this.entriesArr = [];\n    /**\n     * The current active entry index into `entriesArr`.\n     */\n    this.currentEntryIndex = 0;\n    /**\n     * The current navigate event.\n     */\n    this.navigateEvent = undefined;\n    /**\n     * A Map of pending traversals, so that traversals to the same entry can be\n     * re-used.\n     */\n    this.traversalQueue = new Map();\n    /**\n     * A Promise that resolves when the previous traversals have finished. Used to\n     * simulate the cross-process communication necessary for traversals.\n     */\n    this.nextTraversal = Promise.resolve();\n    /**\n     * A prospective current active entry index, which includes unresolved\n     * traversals. Used by `go` to determine where navigations are intended to go.\n     */\n    this.prospectiveEntryIndex = 0;\n    /**\n     * A test-only option to make traversals synchronous, rather than emulate\n     * cross-process communication.\n     */\n    this.synchronousTraversals = false;\n    /** Whether to allow a call to setInitialEntryForTesting. */\n    this.canSetInitialEntry = true;\n    /** `EventTarget` to dispatch events. */\n    this.eventTarget = this.window.document.createElement('div');\n    /** The next unique id for created entries. Replace recreates this id. */\n    this.nextId = 0;\n    /** The next unique key for created entries. Replace inherits this id. */\n    this.nextKey = 0;\n    /** Whether this fake is disposed. */\n    this.disposed = false;\n    // First entry.\n    this.setInitialEntryForTesting(startURL);\n  }\n  /**\n   * Sets the initial entry.\n   */\n  setInitialEntryForTesting(url, options = {\n    historyState: null\n  }) {\n    if (!this.canSetInitialEntry) {\n      throw new Error('setInitialEntryForTesting can only be called before any ' + 'navigation has occurred');\n    }\n    const currentInitialEntry = this.entriesArr[0];\n    this.entriesArr[0] = new FakeNavigationHistoryEntry(new URL(url).toString(), {\n      index: 0,\n      key: currentInitialEntry?.key ?? String(this.nextKey++),\n      id: currentInitialEntry?.id ?? String(this.nextId++),\n      sameDocument: true,\n      historyState: options?.historyState,\n      state: options.state\n    });\n  }\n  /** Returns whether the initial entry is still eligible to be set. */\n  canSetInitialEntryForTesting() {\n    return this.canSetInitialEntry;\n  }\n  /**\n   * Sets whether to emulate traversals as synchronous rather than\n   * asynchronous.\n   */\n  setSynchronousTraversalsForTesting(synchronousTraversals) {\n    this.synchronousTraversals = synchronousTraversals;\n  }\n  /** Equivalent to `navigation.entries()`. */\n  entries() {\n    return this.entriesArr.slice();\n  }\n  /** Equivalent to `navigation.navigate()`. */\n  navigate(url, options) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const toUrl = new URL(url, this.currentEntry.url);\n    let navigationType;\n    if (!options?.history || options.history === 'auto') {\n      // Auto defaults to push, but if the URLs are the same, is a replace.\n      if (fromUrl.toString() === toUrl.toString()) {\n        navigationType = 'replace';\n      } else {\n        navigationType = 'push';\n      }\n    } else {\n      navigationType = options.history;\n    }\n    const hashChange = isHashChange(fromUrl, toUrl);\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      state: options?.state,\n      sameDocument: hashChange,\n      historyState: null\n    });\n    const result = new InternalNavigationResult();\n    this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for navigate().\n      userInitiated: false,\n      hashChange,\n      info: options?.info\n    });\n    return {\n      committed: result.committed,\n      finished: result.finished\n    };\n  }\n  /** Equivalent to `history.pushState()`. */\n  pushState(data, title, url) {\n    this.pushOrReplaceState('push', data, title, url);\n  }\n  /** Equivalent to `history.replaceState()`. */\n  replaceState(data, title, url) {\n    this.pushOrReplaceState('replace', data, title, url);\n  }\n  pushOrReplaceState(navigationType, data, _title, url) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const toUrl = url ? new URL(url, this.currentEntry.url) : fromUrl;\n    const hashChange = isHashChange(fromUrl, toUrl);\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      sameDocument: true,\n      historyState: data\n    });\n    const result = new InternalNavigationResult();\n    this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for pushState() or replaceState().\n      userInitiated: false,\n      hashChange,\n      skipPopState: true\n    });\n  }\n  /** Equivalent to `navigation.traverseTo()`. */\n  traverseTo(key, options) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const entry = this.findEntry(key);\n    if (!entry) {\n      const domException = new DOMException('Invalid key', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    if (entry === this.currentEntry) {\n      return {\n        committed: Promise.resolve(this.currentEntry),\n        finished: Promise.resolve(this.currentEntry)\n      };\n    }\n    if (this.traversalQueue.has(entry.key)) {\n      const existingResult = this.traversalQueue.get(entry.key);\n      return {\n        committed: existingResult.committed,\n        finished: existingResult.finished\n      };\n    }\n    const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n    const destination = new FakeNavigationDestination({\n      url: entry.url,\n      state: entry.getState(),\n      historyState: entry.getHistoryState(),\n      key: entry.key,\n      id: entry.id,\n      index: entry.index,\n      sameDocument: entry.sameDocument\n    });\n    this.prospectiveEntryIndex = entry.index;\n    const result = new InternalNavigationResult();\n    this.traversalQueue.set(entry.key, result);\n    this.runTraversal(() => {\n      this.traversalQueue.delete(entry.key);\n      this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for traverseTo().\n        userInitiated: false,\n        hashChange,\n        info: options?.info\n      });\n    });\n    return {\n      committed: result.committed,\n      finished: result.finished\n    };\n  }\n  /** Equivalent to `navigation.back()`. */\n  back(options) {\n    if (this.currentEntryIndex === 0) {\n      const domException = new DOMException('Cannot go back', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex - 1];\n    return this.traverseTo(entry.key, options);\n  }\n  /** Equivalent to `navigation.forward()`. */\n  forward(options) {\n    if (this.currentEntryIndex === this.entriesArr.length - 1) {\n      const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex + 1];\n    return this.traverseTo(entry.key, options);\n  }\n  /**\n   * Equivalent to `history.go()`.\n   * Note that this method does not actually work precisely to how Chrome\n   * does, instead choosing a simpler model with less unexpected behavior.\n   * Chrome has a few edge case optimizations, for instance with repeated\n   * `back(); forward()` chains it collapses certain traversals.\n   */\n  go(direction) {\n    const targetIndex = this.prospectiveEntryIndex + direction;\n    if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n      return;\n    }\n    this.prospectiveEntryIndex = targetIndex;\n    this.runTraversal(() => {\n      // Check again that destination is in the entries array.\n      if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n        return;\n      }\n      const fromUrl = new URL(this.currentEntry.url);\n      const entry = this.entriesArr[targetIndex];\n      const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n      const destination = new FakeNavigationDestination({\n        url: entry.url,\n        state: entry.getState(),\n        historyState: entry.getHistoryState(),\n        key: entry.key,\n        id: entry.id,\n        index: entry.index,\n        sameDocument: entry.sameDocument\n      });\n      const result = new InternalNavigationResult();\n      this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for go().\n        userInitiated: false,\n        hashChange\n      });\n    });\n  }\n  /** Runs a traversal synchronously or asynchronously */\n  runTraversal(traversal) {\n    if (this.synchronousTraversals) {\n      traversal();\n      return;\n    }\n    // Each traversal occupies a single timeout resolution.\n    // This means that Promises added to commit and finish should resolve\n    // before the next traversal.\n    this.nextTraversal = this.nextTraversal.then(() => {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve();\n          traversal();\n        });\n      });\n    });\n  }\n  /** Equivalent to `navigation.addEventListener()`. */\n  addEventListener(type, callback, options) {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n  /** Equivalent to `navigation.removeEventListener()`. */\n  removeEventListener(type, callback, options) {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n  /** Equivalent to `navigation.dispatchEvent()` */\n  dispatchEvent(event) {\n    return this.eventTarget.dispatchEvent(event);\n  }\n  /** Cleans up resources. */\n  dispose() {\n    // Recreate eventTarget to release current listeners.\n    // `document.createElement` because NodeJS `EventTarget` is incompatible with Domino's `Event`.\n    this.eventTarget = this.window.document.createElement('div');\n    this.disposed = true;\n  }\n  /** Returns whether this fake is disposed. */\n  isDisposed() {\n    return this.disposed;\n  }\n  /** Implementation for all navigations and traversals. */\n  userAgentNavigate(destination, result, options) {\n    // The first navigation should disallow any future calls to set the initial\n    // entry.\n    this.canSetInitialEntry = false;\n    if (this.navigateEvent) {\n      this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n      this.navigateEvent = undefined;\n    }\n    const navigateEvent = createFakeNavigateEvent({\n      navigationType: options.navigationType,\n      cancelable: options.cancelable,\n      canIntercept: options.canIntercept,\n      userInitiated: options.userInitiated,\n      hashChange: options.hashChange,\n      signal: result.signal,\n      destination,\n      info: options.info,\n      sameDocument: destination.sameDocument,\n      skipPopState: options.skipPopState,\n      result,\n      userAgentCommit: () => {\n        this.userAgentCommit();\n      }\n    });\n    this.navigateEvent = navigateEvent;\n    this.eventTarget.dispatchEvent(navigateEvent);\n    navigateEvent.dispatchedNavigateEvent();\n    if (navigateEvent.commitOption === 'immediate') {\n      navigateEvent.commit(/* internal= */true);\n    }\n  }\n  /** Implementation to commit a navigation. */\n  userAgentCommit() {\n    if (!this.navigateEvent) {\n      return;\n    }\n    const from = this.currentEntry;\n    if (!this.navigateEvent.sameDocument) {\n      const error = new Error('Cannot navigate to a non-same-document URL.');\n      this.navigateEvent.cancel(error);\n      throw error;\n    }\n    if (this.navigateEvent.navigationType === 'push' || this.navigateEvent.navigationType === 'replace') {\n      this.userAgentPushOrReplace(this.navigateEvent.destination, {\n        navigationType: this.navigateEvent.navigationType\n      });\n    } else if (this.navigateEvent.navigationType === 'traverse') {\n      this.userAgentTraverse(this.navigateEvent.destination);\n    }\n    this.navigateEvent.userAgentNavigated(this.currentEntry);\n    const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n      from,\n      navigationType: this.navigateEvent.navigationType\n    });\n    this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n    if (!this.navigateEvent.skipPopState) {\n      const popStateEvent = createPopStateEvent({\n        state: this.navigateEvent.destination.getHistoryState()\n      });\n      this.window.dispatchEvent(popStateEvent);\n    }\n  }\n  /** Implementation for a push or replace navigation. */\n  userAgentPushOrReplace(destination, {\n    navigationType\n  }) {\n    if (navigationType === 'push') {\n      this.currentEntryIndex++;\n      this.prospectiveEntryIndex = this.currentEntryIndex;\n    }\n    const index = this.currentEntryIndex;\n    const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n    const entry = new FakeNavigationHistoryEntry(destination.url, {\n      id: String(this.nextId++),\n      key,\n      index,\n      sameDocument: true,\n      state: destination.getState(),\n      historyState: destination.getHistoryState()\n    });\n    if (navigationType === 'push') {\n      this.entriesArr.splice(index, Infinity, entry);\n    } else {\n      this.entriesArr[index] = entry;\n    }\n  }\n  /** Implementation for a traverse navigation. */\n  userAgentTraverse(destination) {\n    this.currentEntryIndex = destination.index;\n  }\n  /** Utility method for finding entries with the given `key`. */\n  findEntry(key) {\n    for (const entry of this.entriesArr) {\n      if (entry.key === key) return entry;\n    }\n    return undefined;\n  }\n  set onnavigate(_handler) {\n    throw new Error('unimplemented');\n  }\n  get onnavigate() {\n    throw new Error('unimplemented');\n  }\n  set oncurrententrychange(_handler) {\n    throw new Error('unimplemented');\n  }\n  get oncurrententrychange() {\n    throw new Error('unimplemented');\n  }\n  set onnavigatesuccess(_handler) {\n    throw new Error('unimplemented');\n  }\n  get onnavigatesuccess() {\n    throw new Error('unimplemented');\n  }\n  set onnavigateerror(_handler) {\n    throw new Error('unimplemented');\n  }\n  get onnavigateerror() {\n    throw new Error('unimplemented');\n  }\n  get transition() {\n    throw new Error('unimplemented');\n  }\n  updateCurrentEntry(_options) {\n    throw new Error('unimplemented');\n  }\n  reload(_options) {\n    throw new Error('unimplemented');\n  }\n}\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nclass FakeNavigationHistoryEntry {\n  constructor(url, {\n    id,\n    key,\n    index,\n    sameDocument,\n    state,\n    historyState\n  }) {\n    this.url = url;\n    // tslint:disable-next-line:no-any\n    this.ondispose = null;\n    this.id = id;\n    this.key = key;\n    this.index = index;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n  }\n  getState() {\n    // Budget copy.\n    return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n  }\n  getHistoryState() {\n    // Budget copy.\n    return this.historyState ? JSON.parse(JSON.stringify(this.historyState)) : this.historyState;\n  }\n  addEventListener(type, callback, options) {\n    throw new Error('unimplemented');\n  }\n  removeEventListener(type, callback, options) {\n    throw new Error('unimplemented');\n  }\n  dispatchEvent(event) {\n    throw new Error('unimplemented');\n  }\n}\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigateEvent({\n  cancelable,\n  canIntercept,\n  userInitiated,\n  hashChange,\n  navigationType,\n  signal,\n  destination,\n  info,\n  sameDocument,\n  skipPopState,\n  result,\n  userAgentCommit\n}) {\n  const event = new Event('navigate', {\n    bubbles: false,\n    cancelable\n  });\n  event.canIntercept = canIntercept;\n  event.userInitiated = userInitiated;\n  event.hashChange = hashChange;\n  event.navigationType = navigationType;\n  event.signal = signal;\n  event.destination = destination;\n  event.info = info;\n  event.downloadRequest = null;\n  event.formData = null;\n  event.sameDocument = sameDocument;\n  event.skipPopState = skipPopState;\n  event.commitOption = 'immediate';\n  let handlerFinished = undefined;\n  let interceptCalled = false;\n  let dispatchedNavigateEvent = false;\n  let commitCalled = false;\n  event.intercept = function (options) {\n    interceptCalled = true;\n    event.sameDocument = true;\n    const handler = options?.handler;\n    if (handler) {\n      handlerFinished = handler();\n    }\n    if (options?.commit) {\n      event.commitOption = options.commit;\n    }\n    if (options?.focusReset !== undefined || options?.scroll !== undefined) {\n      throw new Error('unimplemented');\n    }\n  };\n  event.scroll = function () {\n    throw new Error('unimplemented');\n  };\n  event.commit = function (internal = false) {\n    if (!internal && !interceptCalled) {\n      throw new DOMException(`Failed to execute 'commit' on 'NavigateEvent': intercept() must be ` + `called before commit().`, 'InvalidStateError');\n    }\n    if (!dispatchedNavigateEvent) {\n      throw new DOMException(`Failed to execute 'commit' on 'NavigateEvent': commit() may not be ` + `called during event dispatch.`, 'InvalidStateError');\n    }\n    if (commitCalled) {\n      throw new DOMException(`Failed to execute 'commit' on 'NavigateEvent': commit() already ` + `called.`, 'InvalidStateError');\n    }\n    commitCalled = true;\n    userAgentCommit();\n  };\n  // Internal only.\n  event.cancel = function (reason) {\n    result.committedReject(reason);\n    result.finishedReject(reason);\n  };\n  // Internal only.\n  event.dispatchedNavigateEvent = function () {\n    dispatchedNavigateEvent = true;\n    if (event.commitOption === 'after-transition') {\n      // If handler finishes before commit, call commit.\n      handlerFinished?.then(() => {\n        if (!commitCalled) {\n          event.commit(/* internal */true);\n        }\n      }, () => {});\n    }\n    Promise.all([result.committed, handlerFinished]).then(([entry]) => {\n      result.finishedResolve(entry);\n    }, reason => {\n      result.finishedReject(reason);\n    });\n  };\n  // Internal only.\n  event.userAgentNavigated = function (entry) {\n    result.committedResolve(entry);\n  };\n  return event;\n}\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({\n  from,\n  navigationType\n}) {\n  const event = new Event('currententrychange', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.from = from;\n  event.navigationType = navigationType;\n  return event;\n}\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({\n  state\n}) {\n  const event = new Event('popstate', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.state = state;\n  return event;\n}\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nclass FakeNavigationDestination {\n  constructor({\n    url,\n    sameDocument,\n    historyState,\n    state,\n    key = null,\n    id = null,\n    index = -1\n  }) {\n    this.url = url;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n    this.key = key;\n    this.id = id;\n    this.index = index;\n  }\n  getState() {\n    return this.state;\n  }\n  getHistoryState() {\n    return this.historyState;\n  }\n}\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from, to) {\n  return to.hash !== from.hash && to.hostname === from.hostname && to.pathname === from.pathname && to.search === from.search;\n}\n/** Internal utility class for representing the result of a navigation.  */\nclass InternalNavigationResult {\n  get signal() {\n    return this.abortController.signal;\n  }\n  constructor() {\n    var _this = this;\n    this.abortController = new AbortController();\n    this.committed = new Promise((resolve, reject) => {\n      this.committedResolve = resolve;\n      this.committedReject = reject;\n    });\n    this.finished = new Promise(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (resolve, reject) {\n        _this.finishedResolve = resolve;\n        _this.finishedReject = reason => {\n          reject(reason);\n          _this.abortController.abort(reason);\n        };\n      });\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    // All rejections are handled.\n    this.committed.catch(() => {});\n    this.finished.catch(() => {});\n  }\n}\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\nfunction parseUrl(urlStr, baseHref) {\n  const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n  let serverBase;\n  // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n  // an arbitrary base URL which can be removed afterward.\n  if (!verifyProtocol.test(urlStr)) {\n    serverBase = 'http://empty.com/';\n  }\n  let parsedUrl;\n  try {\n    parsedUrl = new URL(urlStr, serverBase);\n  } catch (e) {\n    const result = urlParse.exec(serverBase || '' + urlStr);\n    if (!result) {\n      throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n    }\n    const hostSplit = result[4].split(':');\n    parsedUrl = {\n      protocol: result[1],\n      hostname: hostSplit[0],\n      port: hostSplit[1] || '',\n      pathname: result[5],\n      search: result[6],\n      hash: result[8]\n    };\n  }\n  if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n    parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n  }\n  return {\n    hostname: !serverBase && parsedUrl.hostname || '',\n    protocol: !serverBase && parsedUrl.protocol || '',\n    port: !serverBase && parsedUrl.port || '',\n    pathname: parsedUrl.pathname || '/',\n    search: parsedUrl.search || '',\n    hash: parsedUrl.hash || ''\n  };\n}\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nconst MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken('MOCK_PLATFORM_LOCATION_CONFIG');\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\nclass MockPlatformLocation {\n  constructor(config) {\n    this.baseHref = '';\n    this.hashUpdate = new Subject();\n    this.popStateSubject = new Subject();\n    this.urlChangeIndex = 0;\n    this.urlChanges = [{\n      hostname: '',\n      protocol: '',\n      port: '',\n      pathname: '/',\n      search: '',\n      hash: '',\n      state: null\n    }];\n    if (config) {\n      this.baseHref = config.appBaseHref || '';\n      const parsedChanges = this.parseChanges(null, config.startUrl || 'http://_empty_/', this.baseHref);\n      this.urlChanges[0] = {\n        ...parsedChanges\n      };\n    }\n  }\n  get hostname() {\n    return this.urlChanges[this.urlChangeIndex].hostname;\n  }\n  get protocol() {\n    return this.urlChanges[this.urlChangeIndex].protocol;\n  }\n  get port() {\n    return this.urlChanges[this.urlChangeIndex].port;\n  }\n  get pathname() {\n    return this.urlChanges[this.urlChangeIndex].pathname;\n  }\n  get search() {\n    return this.urlChanges[this.urlChangeIndex].search;\n  }\n  get hash() {\n    return this.urlChanges[this.urlChangeIndex].hash;\n  }\n  get state() {\n    return this.urlChanges[this.urlChangeIndex].state;\n  }\n  getBaseHrefFromDOM() {\n    return this.baseHref;\n  }\n  onPopState(fn) {\n    const subscription = this.popStateSubject.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n  onHashChange(fn) {\n    const subscription = this.hashUpdate.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n  get href() {\n    let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n    url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n    return url;\n  }\n  get url() {\n    return `${this.pathname}${this.search}${this.hash}`;\n  }\n  parseChanges(state, url, baseHref = '') {\n    // When the `history.state` value is stored, it is always copied.\n    state = JSON.parse(JSON.stringify(state));\n    return {\n      ...parseUrl(url, baseHref),\n      state\n    };\n  }\n  replaceState(state, title, newUrl) {\n    const {\n      pathname,\n      search,\n      state: parsedState,\n      hash\n    } = this.parseChanges(state, newUrl);\n    this.urlChanges[this.urlChangeIndex] = {\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState\n    };\n  }\n  pushState(state, title, newUrl) {\n    const {\n      pathname,\n      search,\n      state: parsedState,\n      hash\n    } = this.parseChanges(state, newUrl);\n    if (this.urlChangeIndex > 0) {\n      this.urlChanges.splice(this.urlChangeIndex + 1);\n    }\n    this.urlChanges.push({\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState\n    });\n    this.urlChangeIndex = this.urlChanges.length - 1;\n  }\n  forward() {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex < this.urlChanges.length) {\n      this.urlChangeIndex++;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  back() {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex > 0) {\n      this.urlChangeIndex--;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  historyGo(relativePosition = 0) {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    const nextPageIndex = this.urlChangeIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n      this.urlChangeIndex = nextPageIndex;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  getState() {\n    return this.state;\n  }\n  /**\n   * Browsers are inconsistent in when they fire events and perform the state updates\n   * The most easiest thing to do in our mock is synchronous and that happens to match\n   * Firefox and Chrome, at least somewhat closely\n   *\n   * https://github.com/WICG/navigation-api#watching-for-navigations\n   * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n   * popstate is always sent before hashchange:\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n   */\n  emitEvents(oldHash, oldUrl) {\n    this.popStateSubject.next({\n      type: 'popstate',\n      state: this.getState(),\n      oldUrl,\n      newUrl: this.url\n    });\n    if (oldHash !== this.hash) {\n      this.hashUpdate.next({\n        type: 'hashchange',\n        state: null,\n        oldUrl,\n        newUrl: this.url\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MockPlatformLocation_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MockPlatformLocation)(i0.ɵɵinject(MOCK_PLATFORM_LOCATION_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MockPlatformLocation,\n      factory: MockPlatformLocation.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MockPlatformLocation, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MOCK_PLATFORM_LOCATION_CONFIG]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n/**\n * Mock implementation of URL state.\n */\nclass FakeNavigationPlatformLocation {\n  constructor() {\n    this._platformNavigation = inject(ɵPlatformNavigation);\n    this.window = inject(DOCUMENT).defaultView;\n    this.config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {\n      optional: true\n    });\n    if (!(this._platformNavigation instanceof FakeNavigation)) {\n      throw new Error('FakePlatformNavigation cannot be used without FakeNavigation. Use ' + '`provideFakeNavigation` to have all these services provided together.');\n    }\n  }\n  getBaseHrefFromDOM() {\n    return this.config?.appBaseHref ?? '';\n  }\n  onPopState(fn) {\n    this.window.addEventListener('popstate', fn);\n    return () => this.window.removeEventListener('popstate', fn);\n  }\n  onHashChange(fn) {\n    this.window.addEventListener('hashchange', fn);\n    return () => this.window.removeEventListener('hashchange', fn);\n  }\n  get href() {\n    return this._platformNavigation.currentEntry.url;\n  }\n  get protocol() {\n    return new URL(this._platformNavigation.currentEntry.url).protocol;\n  }\n  get hostname() {\n    return new URL(this._platformNavigation.currentEntry.url).hostname;\n  }\n  get port() {\n    return new URL(this._platformNavigation.currentEntry.url).port;\n  }\n  get pathname() {\n    return new URL(this._platformNavigation.currentEntry.url).pathname;\n  }\n  get search() {\n    return new URL(this._platformNavigation.currentEntry.url).search;\n  }\n  get hash() {\n    return new URL(this._platformNavigation.currentEntry.url).hash;\n  }\n  pushState(state, title, url) {\n    this._platformNavigation.pushState(state, title, url);\n  }\n  replaceState(state, title, url) {\n    this._platformNavigation.replaceState(state, title, url);\n  }\n  forward() {\n    this._platformNavigation.forward();\n  }\n  back() {\n    this._platformNavigation.back();\n  }\n  historyGo(relativePosition = 0) {\n    this._platformNavigation.go(relativePosition);\n  }\n  getState() {\n    return this._platformNavigation.currentEntry.getHistoryState();\n  }\n  static {\n    this.ɵfac = function FakeNavigationPlatformLocation_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FakeNavigationPlatformLocation)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FakeNavigationPlatformLocation,\n      factory: FakeNavigationPlatformLocation.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FakeNavigationPlatformLocation, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * Return a provider for the `FakeNavigation` in place of the real Navigation API.\n */\nfunction provideFakePlatformNavigation() {\n  return [{\n    provide: PlatformNavigation,\n    useFactory: () => {\n      const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {\n        optional: true\n      });\n      return new FakeNavigation(inject(DOCUMENT).defaultView, config?.startUrl ?? 'http://_empty_/');\n    }\n  }, {\n    provide: PlatformLocation,\n    useClass: FakeNavigationPlatformLocation\n  }];\n}\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\nclass SpyLocation {\n  constructor() {\n    this.urlChanges = [];\n    this._history = [new LocationState('', '', null)];\n    this._historyIndex = 0;\n    /** @internal */\n    this._subject = new EventEmitter();\n    /** @internal */\n    this._basePath = '';\n    /** @internal */\n    this._locationStrategy = null;\n    /** @internal */\n    this._urlChangeListeners = [];\n    /** @internal */\n    this._urlChangeSubscription = null;\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n  setInitialPath(url) {\n    this._history[this._historyIndex].path = url;\n  }\n  setBaseHref(url) {\n    this._basePath = url;\n  }\n  path() {\n    return this._history[this._historyIndex].path;\n  }\n  getState() {\n    return this._history[this._historyIndex].state;\n  }\n  isCurrentPathEqualTo(path, query = '') {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath = this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n    return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n  }\n  simulateUrlPop(pathname) {\n    this._subject.emit({\n      'url': pathname,\n      'pop': true,\n      'type': 'popstate'\n    });\n  }\n  simulateHashChange(pathname) {\n    const path = this.prepareExternalUrl(pathname);\n    this.pushHistory(path, '', null);\n    this.urlChanges.push('hash: ' + pathname);\n    // the browser will automatically fire popstate event before each `hashchange` event, so we need\n    // to simulate it.\n    this._subject.emit({\n      'url': pathname,\n      'pop': true,\n      'type': 'popstate'\n    });\n    this._subject.emit({\n      'url': pathname,\n      'pop': true,\n      'type': 'hashchange'\n    });\n  }\n  prepareExternalUrl(url) {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._basePath + url;\n  }\n  go(path, query = '', state = null) {\n    path = this.prepareExternalUrl(path);\n    this.pushHistory(path, query, state);\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push(url);\n    this._notifyUrlChangeListeners(path + ɵnormalizeQueryParams(query), state);\n  }\n  replaceState(path, query = '', state = null) {\n    path = this.prepareExternalUrl(path);\n    const history = this._history[this._historyIndex];\n    history.state = state;\n    if (history.path == path && history.query == query) {\n      return;\n    }\n    history.path = path;\n    history.query = query;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push('replace: ' + url);\n    this._notifyUrlChangeListeners(path + ɵnormalizeQueryParams(query), state);\n  }\n  forward() {\n    if (this._historyIndex < this._history.length - 1) {\n      this._historyIndex++;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  historyGo(relativePosition = 0) {\n    const nextPageIndex = this._historyIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n      this._historyIndex = nextPageIndex;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  onUrlChange(fn) {\n    this._urlChangeListeners.push(fn);\n    this._urlChangeSubscription ??= this.subscribe(v => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n  /** @internal */\n  _notifyUrlChangeListeners(url = '', state) {\n    this._urlChangeListeners.forEach(fn => fn(url, state));\n  }\n  subscribe(onNext, onThrow, onReturn) {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow,\n      complete: onReturn\n    });\n  }\n  normalize(url) {\n    return null;\n  }\n  pushHistory(path, query, state) {\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query, state));\n    this._historyIndex = this._history.length - 1;\n  }\n  static {\n    this.ɵfac = function SpyLocation_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpyLocation)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SpyLocation,\n      factory: SpyLocation.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SpyLocation, [{\n    type: Injectable\n  }], null, null);\n})();\nclass LocationState {\n  constructor(path, query, state) {\n    this.path = path;\n    this.query = query;\n    this.state = state;\n  }\n}\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\nclass MockLocationStrategy extends LocationStrategy {\n  constructor() {\n    super();\n    this.internalBaseHref = '/';\n    this.internalPath = '/';\n    this.internalTitle = '';\n    this.urlChanges = [];\n    /** @internal */\n    this._subject = new EventEmitter();\n    this.stateChanges = [];\n  }\n  simulatePopState(url) {\n    this.internalPath = url;\n    this._subject.emit(new _MockPopStateEvent(this.path()));\n  }\n  path(includeHash = false) {\n    return this.internalPath;\n  }\n  prepareExternalUrl(internal) {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n  pushState(ctx, title, path, query) {\n    // Add state change to changes array\n    this.stateChanges.push(ctx);\n    this.internalTitle = title;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n  replaceState(ctx, title, path, query) {\n    // Reset the last index of stateChanges to the ctx (state) object\n    this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n    this.internalTitle = title;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n  onPopState(fn) {\n    this._subject.subscribe({\n      next: fn\n    });\n  }\n  getBaseHref() {\n    return this.internalBaseHref;\n  }\n  back() {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      this.stateChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n  forward() {\n    throw 'not implemented';\n  }\n  getState() {\n    return this.stateChanges[(this.stateChanges.length || 1) - 1];\n  }\n  static {\n    this.ɵfac = function MockLocationStrategy_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MockLocationStrategy)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MockLocationStrategy,\n      factory: MockLocationStrategy.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MockLocationStrategy, [{\n    type: Injectable\n  }], () => [], null);\n})();\nclass _MockPopStateEvent {\n  constructor(newUrl) {\n    this.newUrl = newUrl;\n    this.pop = true;\n    this.type = 'popstate';\n  }\n}\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nfunction provideLocationMocks() {\n  return [{\n    provide: Location,\n    useClass: SpyLocation\n  }, {\n    provide: LocationStrategy,\n    useClass: MockLocationStrategy\n  }];\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MOCK_PLATFORM_LOCATION_CONFIG, MockLocationStrategy, MockPlatformLocation, SpyLocation, provideLocationMocks, provideFakePlatformNavigation as ɵprovideFakePlatformNavigation };", "map": {"version": 3, "names": ["ɵPlatformNavigation", "DOCUMENT", "PlatformLocation", "ɵnormalizeQueryParams", "LocationStrategy", "Location", "i0", "Injectable", "InjectionToken", "Inject", "Optional", "inject", "EventEmitter", "Subject", "PlatformNavigation", "ɵfac", "PlatformNavigation_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "window", "navigation", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "useFactory", "FakeNavigation", "currentEntry", "entriesArr", "currentEntryIndex", "canGoBack", "canGoForward", "length", "constructor", "startURL", "navigateEvent", "undefined", "traversalQueue", "Map", "nextTraversal", "Promise", "resolve", "prospectiveEntryIndex", "synchronousTraversals", "canSetInitialEntry", "eventTarget", "document", "createElement", "nextId", "<PERSON><PERSON><PERSON>", "disposed", "setInitialEntryForTesting", "url", "options", "historyState", "Error", "currentInitialEntry", "FakeNavigationHistoryEntry", "URL", "toString", "index", "key", "String", "id", "sameDocument", "state", "canSetInitialEntryForTesting", "setSynchronousTraversalsForTesting", "entries", "slice", "navigate", "fromUrl", "toUrl", "navigationType", "history", "hashChange", "isHashChange", "destination", "FakeNavigationDestination", "result", "InternalNavigationResult", "userAgentNavigate", "cancelable", "canIntercept", "userInitiated", "info", "committed", "finished", "pushState", "data", "title", "pushOrReplaceState", "replaceState", "_title", "skipPopState", "traverseTo", "entry", "findEntry", "domException", "DOMException", "reject", "catch", "has", "existingResult", "get", "getState", "getHistoryState", "set", "runTraversal", "delete", "back", "forward", "go", "direction", "targetIndex", "traversal", "then", "setTimeout", "addEventListener", "callback", "removeEventListener", "dispatchEvent", "event", "dispose", "isDisposed", "cancel", "createFakeNavigateEvent", "signal", "userAgentCommit", "dispatchedNavigateEvent", "commitOption", "commit", "from", "error", "userAgentPushOrReplace", "userAgentTraverse", "userAgentNavigated", "currentEntryChangeEvent", "createFakeNavigationCurrentEntryChangeEvent", "popStateEvent", "createPopStateEvent", "splice", "Infinity", "onnavigate", "_handler", "oncurrententrychange", "onnavigatesuccess", "onnavigateerror", "transition", "updateCurrentEntry", "_options", "reload", "ondispose", "JSON", "parse", "stringify", "Event", "bubbles", "downloadRequest", "formData", "handlerFinished", "interceptCalled", "commitCalled", "intercept", "handler", "focusReset", "scroll", "internal", "reason", "committedReject", "finishedReject", "all", "finishedResolve", "committedResolve", "to", "hash", "hostname", "pathname", "search", "abortController", "_this", "AbortController", "_ref", "_asyncToGenerator", "abort", "_x", "_x2", "apply", "arguments", "urlParse", "parseUrl", "urlStr", "baseHref", "verifyProtocol", "serverBase", "test", "parsedUrl", "e", "exec", "hostSplit", "split", "protocol", "port", "indexOf", "substring", "MOCK_PLATFORM_LOCATION_CONFIG", "MockPlatformLocation", "config", "hashUpdate", "popStateSubject", "urlChangeIndex", "url<PERSON><PERSON><PERSON>", "appBaseHref", "parsedChanges", "parseChanges", "startUrl", "getBaseHrefFromDOM", "onPopState", "fn", "subscription", "subscribe", "unsubscribe", "onHashChange", "href", "newUrl", "parsedState", "push", "oldUrl", "oldHash", "emitEvents", "historyGo", "relativePosition", "nextPageIndex", "next", "MockPlatformLocation_Factory", "ɵɵinject", "decorators", "FakeNavigationPlatformLocation", "_platformNavigation", "defaultView", "optional", "FakeNavigationPlatformLocation_Factory", "provideFakePlatformNavigation", "provide", "useClass", "SpyLocation", "_history", "LocationState", "_historyIndex", "_subject", "_basePath", "_locationStrategy", "_urlChangeListeners", "_urlChangeSubscription", "ngOnDestroy", "setInitialPath", "path", "setBaseHref", "isCurrentPathEqualTo", "query", "<PERSON><PERSON><PERSON>", "endsWith", "currPath", "simulateUrlPop", "emit", "simulateHashChange", "prepareExternalUrl", "pushHistory", "startsWith", "locationState", "_notifyUrlChangeListeners", "onUrlChange", "v", "fnIndex", "for<PERSON>ach", "onNext", "onThrow", "onReturn", "complete", "normalize", "SpyLocation_Factory", "MockLocationStrategy", "internalBaseHref", "internalPath", "internalTitle", "stateChanges", "simulatePopState", "_MockPopStateEvent", "includeHash", "ctx", "externalUrl", "getBaseHref", "pop", "nextUrl", "MockLocationStrategy_Factory", "provideLocationMocks", "ɵprovideFakePlatformNavigation"], "sources": ["D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@angular/common/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵPlatformNavigation, DOCUMENT, PlatformLocation, ɵnormalizeQueryParams, LocationStrategy, Location } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, Optional, inject, EventEmitter } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: PlatformNavigation, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: PlatformNavigation, providedIn: 'platform', useFactory: () => window.navigation }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: PlatformNavigation, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform', useFactory: () => window.navigation }]\n        }] });\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nclass FakeNavigation {\n    /** Equivalent to `navigation.currentEntry`. */\n    get currentEntry() {\n        return this.entriesArr[this.currentEntryIndex];\n    }\n    get canGoBack() {\n        return this.currentEntryIndex > 0;\n    }\n    get canGoForward() {\n        return this.currentEntryIndex < this.entriesArr.length - 1;\n    }\n    constructor(window, startURL) {\n        this.window = window;\n        /**\n         * The fake implementation of an entries array. Only same-document entries\n         * allowed.\n         */\n        this.entriesArr = [];\n        /**\n         * The current active entry index into `entriesArr`.\n         */\n        this.currentEntryIndex = 0;\n        /**\n         * The current navigate event.\n         */\n        this.navigateEvent = undefined;\n        /**\n         * A Map of pending traversals, so that traversals to the same entry can be\n         * re-used.\n         */\n        this.traversalQueue = new Map();\n        /**\n         * A Promise that resolves when the previous traversals have finished. Used to\n         * simulate the cross-process communication necessary for traversals.\n         */\n        this.nextTraversal = Promise.resolve();\n        /**\n         * A prospective current active entry index, which includes unresolved\n         * traversals. Used by `go` to determine where navigations are intended to go.\n         */\n        this.prospectiveEntryIndex = 0;\n        /**\n         * A test-only option to make traversals synchronous, rather than emulate\n         * cross-process communication.\n         */\n        this.synchronousTraversals = false;\n        /** Whether to allow a call to setInitialEntryForTesting. */\n        this.canSetInitialEntry = true;\n        /** `EventTarget` to dispatch events. */\n        this.eventTarget = this.window.document.createElement('div');\n        /** The next unique id for created entries. Replace recreates this id. */\n        this.nextId = 0;\n        /** The next unique key for created entries. Replace inherits this id. */\n        this.nextKey = 0;\n        /** Whether this fake is disposed. */\n        this.disposed = false;\n        // First entry.\n        this.setInitialEntryForTesting(startURL);\n    }\n    /**\n     * Sets the initial entry.\n     */\n    setInitialEntryForTesting(url, options = { historyState: null }) {\n        if (!this.canSetInitialEntry) {\n            throw new Error('setInitialEntryForTesting can only be called before any ' + 'navigation has occurred');\n        }\n        const currentInitialEntry = this.entriesArr[0];\n        this.entriesArr[0] = new FakeNavigationHistoryEntry(new URL(url).toString(), {\n            index: 0,\n            key: currentInitialEntry?.key ?? String(this.nextKey++),\n            id: currentInitialEntry?.id ?? String(this.nextId++),\n            sameDocument: true,\n            historyState: options?.historyState,\n            state: options.state,\n        });\n    }\n    /** Returns whether the initial entry is still eligible to be set. */\n    canSetInitialEntryForTesting() {\n        return this.canSetInitialEntry;\n    }\n    /**\n     * Sets whether to emulate traversals as synchronous rather than\n     * asynchronous.\n     */\n    setSynchronousTraversalsForTesting(synchronousTraversals) {\n        this.synchronousTraversals = synchronousTraversals;\n    }\n    /** Equivalent to `navigation.entries()`. */\n    entries() {\n        return this.entriesArr.slice();\n    }\n    /** Equivalent to `navigation.navigate()`. */\n    navigate(url, options) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const toUrl = new URL(url, this.currentEntry.url);\n        let navigationType;\n        if (!options?.history || options.history === 'auto') {\n            // Auto defaults to push, but if the URLs are the same, is a replace.\n            if (fromUrl.toString() === toUrl.toString()) {\n                navigationType = 'replace';\n            }\n            else {\n                navigationType = 'push';\n            }\n        }\n        else {\n            navigationType = options.history;\n        }\n        const hashChange = isHashChange(fromUrl, toUrl);\n        const destination = new FakeNavigationDestination({\n            url: toUrl.toString(),\n            state: options?.state,\n            sameDocument: hashChange,\n            historyState: null,\n        });\n        const result = new InternalNavigationResult();\n        this.userAgentNavigate(destination, result, {\n            navigationType,\n            cancelable: true,\n            canIntercept: true,\n            // Always false for navigate().\n            userInitiated: false,\n            hashChange,\n            info: options?.info,\n        });\n        return {\n            committed: result.committed,\n            finished: result.finished,\n        };\n    }\n    /** Equivalent to `history.pushState()`. */\n    pushState(data, title, url) {\n        this.pushOrReplaceState('push', data, title, url);\n    }\n    /** Equivalent to `history.replaceState()`. */\n    replaceState(data, title, url) {\n        this.pushOrReplaceState('replace', data, title, url);\n    }\n    pushOrReplaceState(navigationType, data, _title, url) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const toUrl = url ? new URL(url, this.currentEntry.url) : fromUrl;\n        const hashChange = isHashChange(fromUrl, toUrl);\n        const destination = new FakeNavigationDestination({\n            url: toUrl.toString(),\n            sameDocument: true,\n            historyState: data,\n        });\n        const result = new InternalNavigationResult();\n        this.userAgentNavigate(destination, result, {\n            navigationType,\n            cancelable: true,\n            canIntercept: true,\n            // Always false for pushState() or replaceState().\n            userInitiated: false,\n            hashChange,\n            skipPopState: true,\n        });\n    }\n    /** Equivalent to `navigation.traverseTo()`. */\n    traverseTo(key, options) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const entry = this.findEntry(key);\n        if (!entry) {\n            const domException = new DOMException('Invalid key', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        if (entry === this.currentEntry) {\n            return {\n                committed: Promise.resolve(this.currentEntry),\n                finished: Promise.resolve(this.currentEntry),\n            };\n        }\n        if (this.traversalQueue.has(entry.key)) {\n            const existingResult = this.traversalQueue.get(entry.key);\n            return {\n                committed: existingResult.committed,\n                finished: existingResult.finished,\n            };\n        }\n        const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n        const destination = new FakeNavigationDestination({\n            url: entry.url,\n            state: entry.getState(),\n            historyState: entry.getHistoryState(),\n            key: entry.key,\n            id: entry.id,\n            index: entry.index,\n            sameDocument: entry.sameDocument,\n        });\n        this.prospectiveEntryIndex = entry.index;\n        const result = new InternalNavigationResult();\n        this.traversalQueue.set(entry.key, result);\n        this.runTraversal(() => {\n            this.traversalQueue.delete(entry.key);\n            this.userAgentNavigate(destination, result, {\n                navigationType: 'traverse',\n                cancelable: true,\n                canIntercept: true,\n                // Always false for traverseTo().\n                userInitiated: false,\n                hashChange,\n                info: options?.info,\n            });\n        });\n        return {\n            committed: result.committed,\n            finished: result.finished,\n        };\n    }\n    /** Equivalent to `navigation.back()`. */\n    back(options) {\n        if (this.currentEntryIndex === 0) {\n            const domException = new DOMException('Cannot go back', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        const entry = this.entriesArr[this.currentEntryIndex - 1];\n        return this.traverseTo(entry.key, options);\n    }\n    /** Equivalent to `navigation.forward()`. */\n    forward(options) {\n        if (this.currentEntryIndex === this.entriesArr.length - 1) {\n            const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        const entry = this.entriesArr[this.currentEntryIndex + 1];\n        return this.traverseTo(entry.key, options);\n    }\n    /**\n     * Equivalent to `history.go()`.\n     * Note that this method does not actually work precisely to how Chrome\n     * does, instead choosing a simpler model with less unexpected behavior.\n     * Chrome has a few edge case optimizations, for instance with repeated\n     * `back(); forward()` chains it collapses certain traversals.\n     */\n    go(direction) {\n        const targetIndex = this.prospectiveEntryIndex + direction;\n        if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n            return;\n        }\n        this.prospectiveEntryIndex = targetIndex;\n        this.runTraversal(() => {\n            // Check again that destination is in the entries array.\n            if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n                return;\n            }\n            const fromUrl = new URL(this.currentEntry.url);\n            const entry = this.entriesArr[targetIndex];\n            const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n            const destination = new FakeNavigationDestination({\n                url: entry.url,\n                state: entry.getState(),\n                historyState: entry.getHistoryState(),\n                key: entry.key,\n                id: entry.id,\n                index: entry.index,\n                sameDocument: entry.sameDocument,\n            });\n            const result = new InternalNavigationResult();\n            this.userAgentNavigate(destination, result, {\n                navigationType: 'traverse',\n                cancelable: true,\n                canIntercept: true,\n                // Always false for go().\n                userInitiated: false,\n                hashChange,\n            });\n        });\n    }\n    /** Runs a traversal synchronously or asynchronously */\n    runTraversal(traversal) {\n        if (this.synchronousTraversals) {\n            traversal();\n            return;\n        }\n        // Each traversal occupies a single timeout resolution.\n        // This means that Promises added to commit and finish should resolve\n        // before the next traversal.\n        this.nextTraversal = this.nextTraversal.then(() => {\n            return new Promise((resolve) => {\n                setTimeout(() => {\n                    resolve();\n                    traversal();\n                });\n            });\n        });\n    }\n    /** Equivalent to `navigation.addEventListener()`. */\n    addEventListener(type, callback, options) {\n        this.eventTarget.addEventListener(type, callback, options);\n    }\n    /** Equivalent to `navigation.removeEventListener()`. */\n    removeEventListener(type, callback, options) {\n        this.eventTarget.removeEventListener(type, callback, options);\n    }\n    /** Equivalent to `navigation.dispatchEvent()` */\n    dispatchEvent(event) {\n        return this.eventTarget.dispatchEvent(event);\n    }\n    /** Cleans up resources. */\n    dispose() {\n        // Recreate eventTarget to release current listeners.\n        // `document.createElement` because NodeJS `EventTarget` is incompatible with Domino's `Event`.\n        this.eventTarget = this.window.document.createElement('div');\n        this.disposed = true;\n    }\n    /** Returns whether this fake is disposed. */\n    isDisposed() {\n        return this.disposed;\n    }\n    /** Implementation for all navigations and traversals. */\n    userAgentNavigate(destination, result, options) {\n        // The first navigation should disallow any future calls to set the initial\n        // entry.\n        this.canSetInitialEntry = false;\n        if (this.navigateEvent) {\n            this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n            this.navigateEvent = undefined;\n        }\n        const navigateEvent = createFakeNavigateEvent({\n            navigationType: options.navigationType,\n            cancelable: options.cancelable,\n            canIntercept: options.canIntercept,\n            userInitiated: options.userInitiated,\n            hashChange: options.hashChange,\n            signal: result.signal,\n            destination,\n            info: options.info,\n            sameDocument: destination.sameDocument,\n            skipPopState: options.skipPopState,\n            result,\n            userAgentCommit: () => {\n                this.userAgentCommit();\n            },\n        });\n        this.navigateEvent = navigateEvent;\n        this.eventTarget.dispatchEvent(navigateEvent);\n        navigateEvent.dispatchedNavigateEvent();\n        if (navigateEvent.commitOption === 'immediate') {\n            navigateEvent.commit(/* internal= */ true);\n        }\n    }\n    /** Implementation to commit a navigation. */\n    userAgentCommit() {\n        if (!this.navigateEvent) {\n            return;\n        }\n        const from = this.currentEntry;\n        if (!this.navigateEvent.sameDocument) {\n            const error = new Error('Cannot navigate to a non-same-document URL.');\n            this.navigateEvent.cancel(error);\n            throw error;\n        }\n        if (this.navigateEvent.navigationType === 'push' ||\n            this.navigateEvent.navigationType === 'replace') {\n            this.userAgentPushOrReplace(this.navigateEvent.destination, {\n                navigationType: this.navigateEvent.navigationType,\n            });\n        }\n        else if (this.navigateEvent.navigationType === 'traverse') {\n            this.userAgentTraverse(this.navigateEvent.destination);\n        }\n        this.navigateEvent.userAgentNavigated(this.currentEntry);\n        const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n            from,\n            navigationType: this.navigateEvent.navigationType,\n        });\n        this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n        if (!this.navigateEvent.skipPopState) {\n            const popStateEvent = createPopStateEvent({\n                state: this.navigateEvent.destination.getHistoryState(),\n            });\n            this.window.dispatchEvent(popStateEvent);\n        }\n    }\n    /** Implementation for a push or replace navigation. */\n    userAgentPushOrReplace(destination, { navigationType }) {\n        if (navigationType === 'push') {\n            this.currentEntryIndex++;\n            this.prospectiveEntryIndex = this.currentEntryIndex;\n        }\n        const index = this.currentEntryIndex;\n        const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n        const entry = new FakeNavigationHistoryEntry(destination.url, {\n            id: String(this.nextId++),\n            key,\n            index,\n            sameDocument: true,\n            state: destination.getState(),\n            historyState: destination.getHistoryState(),\n        });\n        if (navigationType === 'push') {\n            this.entriesArr.splice(index, Infinity, entry);\n        }\n        else {\n            this.entriesArr[index] = entry;\n        }\n    }\n    /** Implementation for a traverse navigation. */\n    userAgentTraverse(destination) {\n        this.currentEntryIndex = destination.index;\n    }\n    /** Utility method for finding entries with the given `key`. */\n    findEntry(key) {\n        for (const entry of this.entriesArr) {\n            if (entry.key === key)\n                return entry;\n        }\n        return undefined;\n    }\n    set onnavigate(_handler) {\n        throw new Error('unimplemented');\n    }\n    get onnavigate() {\n        throw new Error('unimplemented');\n    }\n    set oncurrententrychange(_handler) {\n        throw new Error('unimplemented');\n    }\n    get oncurrententrychange() {\n        throw new Error('unimplemented');\n    }\n    set onnavigatesuccess(_handler) {\n        throw new Error('unimplemented');\n    }\n    get onnavigatesuccess() {\n        throw new Error('unimplemented');\n    }\n    set onnavigateerror(_handler) {\n        throw new Error('unimplemented');\n    }\n    get onnavigateerror() {\n        throw new Error('unimplemented');\n    }\n    get transition() {\n        throw new Error('unimplemented');\n    }\n    updateCurrentEntry(_options) {\n        throw new Error('unimplemented');\n    }\n    reload(_options) {\n        throw new Error('unimplemented');\n    }\n}\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nclass FakeNavigationHistoryEntry {\n    constructor(url, { id, key, index, sameDocument, state, historyState, }) {\n        this.url = url;\n        // tslint:disable-next-line:no-any\n        this.ondispose = null;\n        this.id = id;\n        this.key = key;\n        this.index = index;\n        this.sameDocument = sameDocument;\n        this.state = state;\n        this.historyState = historyState;\n    }\n    getState() {\n        // Budget copy.\n        return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n    }\n    getHistoryState() {\n        // Budget copy.\n        return this.historyState ? JSON.parse(JSON.stringify(this.historyState)) : this.historyState;\n    }\n    addEventListener(type, callback, options) {\n        throw new Error('unimplemented');\n    }\n    removeEventListener(type, callback, options) {\n        throw new Error('unimplemented');\n    }\n    dispatchEvent(event) {\n        throw new Error('unimplemented');\n    }\n}\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigateEvent({ cancelable, canIntercept, userInitiated, hashChange, navigationType, signal, destination, info, sameDocument, skipPopState, result, userAgentCommit, }) {\n    const event = new Event('navigate', { bubbles: false, cancelable });\n    event.canIntercept = canIntercept;\n    event.userInitiated = userInitiated;\n    event.hashChange = hashChange;\n    event.navigationType = navigationType;\n    event.signal = signal;\n    event.destination = destination;\n    event.info = info;\n    event.downloadRequest = null;\n    event.formData = null;\n    event.sameDocument = sameDocument;\n    event.skipPopState = skipPopState;\n    event.commitOption = 'immediate';\n    let handlerFinished = undefined;\n    let interceptCalled = false;\n    let dispatchedNavigateEvent = false;\n    let commitCalled = false;\n    event.intercept = function (options) {\n        interceptCalled = true;\n        event.sameDocument = true;\n        const handler = options?.handler;\n        if (handler) {\n            handlerFinished = handler();\n        }\n        if (options?.commit) {\n            event.commitOption = options.commit;\n        }\n        if (options?.focusReset !== undefined || options?.scroll !== undefined) {\n            throw new Error('unimplemented');\n        }\n    };\n    event.scroll = function () {\n        throw new Error('unimplemented');\n    };\n    event.commit = function (internal = false) {\n        if (!internal && !interceptCalled) {\n            throw new DOMException(`Failed to execute 'commit' on 'NavigateEvent': intercept() must be ` +\n                `called before commit().`, 'InvalidStateError');\n        }\n        if (!dispatchedNavigateEvent) {\n            throw new DOMException(`Failed to execute 'commit' on 'NavigateEvent': commit() may not be ` +\n                `called during event dispatch.`, 'InvalidStateError');\n        }\n        if (commitCalled) {\n            throw new DOMException(`Failed to execute 'commit' on 'NavigateEvent': commit() already ` + `called.`, 'InvalidStateError');\n        }\n        commitCalled = true;\n        userAgentCommit();\n    };\n    // Internal only.\n    event.cancel = function (reason) {\n        result.committedReject(reason);\n        result.finishedReject(reason);\n    };\n    // Internal only.\n    event.dispatchedNavigateEvent = function () {\n        dispatchedNavigateEvent = true;\n        if (event.commitOption === 'after-transition') {\n            // If handler finishes before commit, call commit.\n            handlerFinished?.then(() => {\n                if (!commitCalled) {\n                    event.commit(/* internal */ true);\n                }\n            }, () => { });\n        }\n        Promise.all([result.committed, handlerFinished]).then(([entry]) => {\n            result.finishedResolve(entry);\n        }, (reason) => {\n            result.finishedReject(reason);\n        });\n    };\n    // Internal only.\n    event.userAgentNavigated = function (entry) {\n        result.committedResolve(entry);\n    };\n    return event;\n}\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({ from, navigationType, }) {\n    const event = new Event('currententrychange', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.from = from;\n    event.navigationType = navigationType;\n    return event;\n}\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({ state }) {\n    const event = new Event('popstate', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.state = state;\n    return event;\n}\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nclass FakeNavigationDestination {\n    constructor({ url, sameDocument, historyState, state, key = null, id = null, index = -1, }) {\n        this.url = url;\n        this.sameDocument = sameDocument;\n        this.state = state;\n        this.historyState = historyState;\n        this.key = key;\n        this.id = id;\n        this.index = index;\n    }\n    getState() {\n        return this.state;\n    }\n    getHistoryState() {\n        return this.historyState;\n    }\n}\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from, to) {\n    return (to.hash !== from.hash &&\n        to.hostname === from.hostname &&\n        to.pathname === from.pathname &&\n        to.search === from.search);\n}\n/** Internal utility class for representing the result of a navigation.  */\nclass InternalNavigationResult {\n    get signal() {\n        return this.abortController.signal;\n    }\n    constructor() {\n        this.abortController = new AbortController();\n        this.committed = new Promise((resolve, reject) => {\n            this.committedResolve = resolve;\n            this.committedReject = reject;\n        });\n        this.finished = new Promise(async (resolve, reject) => {\n            this.finishedResolve = resolve;\n            this.finishedReject = (reason) => {\n                reject(reason);\n                this.abortController.abort(reason);\n            };\n        });\n        // All rejections are handled.\n        this.committed.catch(() => { });\n        this.finished.catch(() => { });\n    }\n}\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\nfunction parseUrl(urlStr, baseHref) {\n    const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n    let serverBase;\n    // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n    // an arbitrary base URL which can be removed afterward.\n    if (!verifyProtocol.test(urlStr)) {\n        serverBase = 'http://empty.com/';\n    }\n    let parsedUrl;\n    try {\n        parsedUrl = new URL(urlStr, serverBase);\n    }\n    catch (e) {\n        const result = urlParse.exec(serverBase || '' + urlStr);\n        if (!result) {\n            throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n        }\n        const hostSplit = result[4].split(':');\n        parsedUrl = {\n            protocol: result[1],\n            hostname: hostSplit[0],\n            port: hostSplit[1] || '',\n            pathname: result[5],\n            search: result[6],\n            hash: result[8],\n        };\n    }\n    if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n        parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n    }\n    return {\n        hostname: (!serverBase && parsedUrl.hostname) || '',\n        protocol: (!serverBase && parsedUrl.protocol) || '',\n        port: (!serverBase && parsedUrl.port) || '',\n        pathname: parsedUrl.pathname || '/',\n        search: parsedUrl.search || '',\n        hash: parsedUrl.hash || '',\n    };\n}\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nconst MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken('MOCK_PLATFORM_LOCATION_CONFIG');\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\nclass MockPlatformLocation {\n    constructor(config) {\n        this.baseHref = '';\n        this.hashUpdate = new Subject();\n        this.popStateSubject = new Subject();\n        this.urlChangeIndex = 0;\n        this.urlChanges = [{ hostname: '', protocol: '', port: '', pathname: '/', search: '', hash: '', state: null }];\n        if (config) {\n            this.baseHref = config.appBaseHref || '';\n            const parsedChanges = this.parseChanges(null, config.startUrl || 'http://_empty_/', this.baseHref);\n            this.urlChanges[0] = { ...parsedChanges };\n        }\n    }\n    get hostname() {\n        return this.urlChanges[this.urlChangeIndex].hostname;\n    }\n    get protocol() {\n        return this.urlChanges[this.urlChangeIndex].protocol;\n    }\n    get port() {\n        return this.urlChanges[this.urlChangeIndex].port;\n    }\n    get pathname() {\n        return this.urlChanges[this.urlChangeIndex].pathname;\n    }\n    get search() {\n        return this.urlChanges[this.urlChangeIndex].search;\n    }\n    get hash() {\n        return this.urlChanges[this.urlChangeIndex].hash;\n    }\n    get state() {\n        return this.urlChanges[this.urlChangeIndex].state;\n    }\n    getBaseHrefFromDOM() {\n        return this.baseHref;\n    }\n    onPopState(fn) {\n        const subscription = this.popStateSubject.subscribe(fn);\n        return () => subscription.unsubscribe();\n    }\n    onHashChange(fn) {\n        const subscription = this.hashUpdate.subscribe(fn);\n        return () => subscription.unsubscribe();\n    }\n    get href() {\n        let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n        url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n        return url;\n    }\n    get url() {\n        return `${this.pathname}${this.search}${this.hash}`;\n    }\n    parseChanges(state, url, baseHref = '') {\n        // When the `history.state` value is stored, it is always copied.\n        state = JSON.parse(JSON.stringify(state));\n        return { ...parseUrl(url, baseHref), state };\n    }\n    replaceState(state, title, newUrl) {\n        const { pathname, search, state: parsedState, hash } = this.parseChanges(state, newUrl);\n        this.urlChanges[this.urlChangeIndex] = {\n            ...this.urlChanges[this.urlChangeIndex],\n            pathname,\n            search,\n            hash,\n            state: parsedState,\n        };\n    }\n    pushState(state, title, newUrl) {\n        const { pathname, search, state: parsedState, hash } = this.parseChanges(state, newUrl);\n        if (this.urlChangeIndex > 0) {\n            this.urlChanges.splice(this.urlChangeIndex + 1);\n        }\n        this.urlChanges.push({\n            ...this.urlChanges[this.urlChangeIndex],\n            pathname,\n            search,\n            hash,\n            state: parsedState,\n        });\n        this.urlChangeIndex = this.urlChanges.length - 1;\n    }\n    forward() {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        if (this.urlChangeIndex < this.urlChanges.length) {\n            this.urlChangeIndex++;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    back() {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        if (this.urlChangeIndex > 0) {\n            this.urlChangeIndex--;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    historyGo(relativePosition = 0) {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        const nextPageIndex = this.urlChangeIndex + relativePosition;\n        if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n            this.urlChangeIndex = nextPageIndex;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    getState() {\n        return this.state;\n    }\n    /**\n     * Browsers are inconsistent in when they fire events and perform the state updates\n     * The most easiest thing to do in our mock is synchronous and that happens to match\n     * Firefox and Chrome, at least somewhat closely\n     *\n     * https://github.com/WICG/navigation-api#watching-for-navigations\n     * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n     * popstate is always sent before hashchange:\n     * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n     */\n    emitEvents(oldHash, oldUrl) {\n        this.popStateSubject.next({\n            type: 'popstate',\n            state: this.getState(),\n            oldUrl,\n            newUrl: this.url,\n        });\n        if (oldHash !== this.hash) {\n            this.hashUpdate.next({\n                type: 'hashchange',\n                state: null,\n                oldUrl,\n                newUrl: this.url,\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: MockPlatformLocation, deps: [{ token: MOCK_PLATFORM_LOCATION_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: MockPlatformLocation }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: MockPlatformLocation, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MOCK_PLATFORM_LOCATION_CONFIG]\n                }, {\n                    type: Optional\n                }] }] });\n/**\n * Mock implementation of URL state.\n */\nclass FakeNavigationPlatformLocation {\n    constructor() {\n        this._platformNavigation = inject(ɵPlatformNavigation);\n        this.window = inject(DOCUMENT).defaultView;\n        this.config = inject(MOCK_PLATFORM_LOCATION_CONFIG, { optional: true });\n        if (!(this._platformNavigation instanceof FakeNavigation)) {\n            throw new Error('FakePlatformNavigation cannot be used without FakeNavigation. Use ' +\n                '`provideFakeNavigation` to have all these services provided together.');\n        }\n    }\n    getBaseHrefFromDOM() {\n        return this.config?.appBaseHref ?? '';\n    }\n    onPopState(fn) {\n        this.window.addEventListener('popstate', fn);\n        return () => this.window.removeEventListener('popstate', fn);\n    }\n    onHashChange(fn) {\n        this.window.addEventListener('hashchange', fn);\n        return () => this.window.removeEventListener('hashchange', fn);\n    }\n    get href() {\n        return this._platformNavigation.currentEntry.url;\n    }\n    get protocol() {\n        return new URL(this._platformNavigation.currentEntry.url).protocol;\n    }\n    get hostname() {\n        return new URL(this._platformNavigation.currentEntry.url).hostname;\n    }\n    get port() {\n        return new URL(this._platformNavigation.currentEntry.url).port;\n    }\n    get pathname() {\n        return new URL(this._platformNavigation.currentEntry.url).pathname;\n    }\n    get search() {\n        return new URL(this._platformNavigation.currentEntry.url).search;\n    }\n    get hash() {\n        return new URL(this._platformNavigation.currentEntry.url).hash;\n    }\n    pushState(state, title, url) {\n        this._platformNavigation.pushState(state, title, url);\n    }\n    replaceState(state, title, url) {\n        this._platformNavigation.replaceState(state, title, url);\n    }\n    forward() {\n        this._platformNavigation.forward();\n    }\n    back() {\n        this._platformNavigation.back();\n    }\n    historyGo(relativePosition = 0) {\n        this._platformNavigation.go(relativePosition);\n    }\n    getState() {\n        return this._platformNavigation.currentEntry.getHistoryState();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: FakeNavigationPlatformLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: FakeNavigationPlatformLocation }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: FakeNavigationPlatformLocation, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n\n/**\n * Return a provider for the `FakeNavigation` in place of the real Navigation API.\n */\nfunction provideFakePlatformNavigation() {\n    return [\n        {\n            provide: PlatformNavigation,\n            useFactory: () => {\n                const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, { optional: true });\n                return new FakeNavigation(inject(DOCUMENT).defaultView, config?.startUrl ?? 'http://_empty_/');\n            },\n        },\n        { provide: PlatformLocation, useClass: FakeNavigationPlatformLocation },\n    ];\n}\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\nclass SpyLocation {\n    constructor() {\n        this.urlChanges = [];\n        this._history = [new LocationState('', '', null)];\n        this._historyIndex = 0;\n        /** @internal */\n        this._subject = new EventEmitter();\n        /** @internal */\n        this._basePath = '';\n        /** @internal */\n        this._locationStrategy = null;\n        /** @internal */\n        this._urlChangeListeners = [];\n        /** @internal */\n        this._urlChangeSubscription = null;\n    }\n    /** @nodoc */\n    ngOnDestroy() {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeListeners = [];\n    }\n    setInitialPath(url) {\n        this._history[this._historyIndex].path = url;\n    }\n    setBaseHref(url) {\n        this._basePath = url;\n    }\n    path() {\n        return this._history[this._historyIndex].path;\n    }\n    getState() {\n        return this._history[this._historyIndex].state;\n    }\n    isCurrentPathEqualTo(path, query = '') {\n        const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n        const currPath = this.path().endsWith('/')\n            ? this.path().substring(0, this.path().length - 1)\n            : this.path();\n        return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n    }\n    simulateUrlPop(pathname) {\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'popstate' });\n    }\n    simulateHashChange(pathname) {\n        const path = this.prepareExternalUrl(pathname);\n        this.pushHistory(path, '', null);\n        this.urlChanges.push('hash: ' + pathname);\n        // the browser will automatically fire popstate event before each `hashchange` event, so we need\n        // to simulate it.\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'popstate' });\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'hashchange' });\n    }\n    prepareExternalUrl(url) {\n        if (url.length > 0 && !url.startsWith('/')) {\n            url = '/' + url;\n        }\n        return this._basePath + url;\n    }\n    go(path, query = '', state = null) {\n        path = this.prepareExternalUrl(path);\n        this.pushHistory(path, query, state);\n        const locationState = this._history[this._historyIndex - 1];\n        if (locationState.path == path && locationState.query == query) {\n            return;\n        }\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.urlChanges.push(url);\n        this._notifyUrlChangeListeners(path + ɵnormalizeQueryParams(query), state);\n    }\n    replaceState(path, query = '', state = null) {\n        path = this.prepareExternalUrl(path);\n        const history = this._history[this._historyIndex];\n        history.state = state;\n        if (history.path == path && history.query == query) {\n            return;\n        }\n        history.path = path;\n        history.query = query;\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.urlChanges.push('replace: ' + url);\n        this._notifyUrlChangeListeners(path + ɵnormalizeQueryParams(query), state);\n    }\n    forward() {\n        if (this._historyIndex < this._history.length - 1) {\n            this._historyIndex++;\n            this._subject.emit({\n                'url': this.path(),\n                'state': this.getState(),\n                'pop': true,\n                'type': 'popstate',\n            });\n        }\n    }\n    back() {\n        if (this._historyIndex > 0) {\n            this._historyIndex--;\n            this._subject.emit({\n                'url': this.path(),\n                'state': this.getState(),\n                'pop': true,\n                'type': 'popstate',\n            });\n        }\n    }\n    historyGo(relativePosition = 0) {\n        const nextPageIndex = this._historyIndex + relativePosition;\n        if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n            this._historyIndex = nextPageIndex;\n            this._subject.emit({\n                'url': this.path(),\n                'state': this.getState(),\n                'pop': true,\n                'type': 'popstate',\n            });\n        }\n    }\n    onUrlChange(fn) {\n        this._urlChangeListeners.push(fn);\n        this._urlChangeSubscription ??= this.subscribe((v) => {\n            this._notifyUrlChangeListeners(v.url, v.state);\n        });\n        return () => {\n            const fnIndex = this._urlChangeListeners.indexOf(fn);\n            this._urlChangeListeners.splice(fnIndex, 1);\n            if (this._urlChangeListeners.length === 0) {\n                this._urlChangeSubscription?.unsubscribe();\n                this._urlChangeSubscription = null;\n            }\n        };\n    }\n    /** @internal */\n    _notifyUrlChangeListeners(url = '', state) {\n        this._urlChangeListeners.forEach((fn) => fn(url, state));\n    }\n    subscribe(onNext, onThrow, onReturn) {\n        return this._subject.subscribe({ next: onNext, error: onThrow, complete: onReturn });\n    }\n    normalize(url) {\n        return null;\n    }\n    pushHistory(path, query, state) {\n        if (this._historyIndex > 0) {\n            this._history.splice(this._historyIndex + 1);\n        }\n        this._history.push(new LocationState(path, query, state));\n        this._historyIndex = this._history.length - 1;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: SpyLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: SpyLocation }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: SpyLocation, decorators: [{\n            type: Injectable\n        }] });\nclass LocationState {\n    constructor(path, query, state) {\n        this.path = path;\n        this.query = query;\n        this.state = state;\n    }\n}\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\nclass MockLocationStrategy extends LocationStrategy {\n    constructor() {\n        super();\n        this.internalBaseHref = '/';\n        this.internalPath = '/';\n        this.internalTitle = '';\n        this.urlChanges = [];\n        /** @internal */\n        this._subject = new EventEmitter();\n        this.stateChanges = [];\n    }\n    simulatePopState(url) {\n        this.internalPath = url;\n        this._subject.emit(new _MockPopStateEvent(this.path()));\n    }\n    path(includeHash = false) {\n        return this.internalPath;\n    }\n    prepareExternalUrl(internal) {\n        if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n            return this.internalBaseHref + internal.substring(1);\n        }\n        return this.internalBaseHref + internal;\n    }\n    pushState(ctx, title, path, query) {\n        // Add state change to changes array\n        this.stateChanges.push(ctx);\n        this.internalTitle = title;\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.internalPath = url;\n        const externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push(externalUrl);\n    }\n    replaceState(ctx, title, path, query) {\n        // Reset the last index of stateChanges to the ctx (state) object\n        this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n        this.internalTitle = title;\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.internalPath = url;\n        const externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push('replace: ' + externalUrl);\n    }\n    onPopState(fn) {\n        this._subject.subscribe({ next: fn });\n    }\n    getBaseHref() {\n        return this.internalBaseHref;\n    }\n    back() {\n        if (this.urlChanges.length > 0) {\n            this.urlChanges.pop();\n            this.stateChanges.pop();\n            const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n            this.simulatePopState(nextUrl);\n        }\n    }\n    forward() {\n        throw 'not implemented';\n    }\n    getState() {\n        return this.stateChanges[(this.stateChanges.length || 1) - 1];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: MockLocationStrategy, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: MockLocationStrategy }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: MockLocationStrategy, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\nclass _MockPopStateEvent {\n    constructor(newUrl) {\n        this.newUrl = newUrl;\n        this.pop = true;\n        this.type = 'popstate';\n    }\n}\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nfunction provideLocationMocks() {\n    return [\n        { provide: Location, useClass: SpyLocation },\n        { provide: LocationStrategy, useClass: MockLocationStrategy },\n    ];\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MOCK_PLATFORM_LOCATION_CONFIG, MockLocationStrategy, MockPlatformLocation, SpyLocation, provideLocationMocks, provideFakePlatformNavigation as ɵprovideFakePlatformNavigation };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,mBAAmB,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,iBAAiB;AACpI,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AAClG,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACC,IAAI,YAAAC,2BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAyFH,kBAAkB;IAAA,CAAoD;EAAE;EACnL;IAAS,IAAI,CAACI,KAAK,kBAD8EZ,EAAE,CAAAa,kBAAA;MAAAC,KAAA,EACYN,kBAAkB;MAAAO,OAAA,EAAAA,CAAA,MAAsC,MAAMC,MAAM,CAACC,UAAU;MAAAC,UAAA,EAA/C;IAAU,EAAwC;EAAE;AACvM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGnB,EAAE,CAAAoB,iBAAA,CAGXZ,kBAAkB,EAAc,CAAC;IACjHa,IAAI,EAAEpB,UAAU;IAChBqB,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE,UAAU;MAAEK,UAAU,EAAEA,CAAA,KAAMP,MAAM,CAACC;IAAW,CAAC;EAC1E,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMO,cAAc,CAAC;EACjB;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC;EAClD;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,iBAAiB,GAAG,CAAC;EACrC;EACA,IAAIE,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACF,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAACI,MAAM,GAAG,CAAC;EAC9D;EACAC,WAAWA,CAACf,MAAM,EAAEgB,QAAQ,EAAE;IAC1B,IAAI,CAAChB,MAAM,GAAGA,MAAM;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACU,UAAU,GAAG,EAAE;IACpB;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAACM,aAAa,GAAGC,SAAS;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5D;IACA,IAAI,CAACC,MAAM,GAAG,CAAC;IACf;IACA,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACC,yBAAyB,CAACjB,QAAQ,CAAC;EAC5C;EACA;AACJ;AACA;EACIiB,yBAAyBA,CAACC,GAAG,EAAEC,OAAO,GAAG;IAAEC,YAAY,EAAE;EAAK,CAAC,EAAE;IAC7D,IAAI,CAAC,IAAI,CAACV,kBAAkB,EAAE;MAC1B,MAAM,IAAIW,KAAK,CAAC,0DAA0D,GAAG,yBAAyB,CAAC;IAC3G;IACA,MAAMC,mBAAmB,GAAG,IAAI,CAAC5B,UAAU,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACA,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI6B,0BAA0B,CAAC,IAAIC,GAAG,CAACN,GAAG,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE;MACzEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAEL,mBAAmB,EAAEK,GAAG,IAAIC,MAAM,CAAC,IAAI,CAACb,OAAO,EAAE,CAAC;MACvDc,EAAE,EAAEP,mBAAmB,EAAEO,EAAE,IAAID,MAAM,CAAC,IAAI,CAACd,MAAM,EAAE,CAAC;MACpDgB,YAAY,EAAE,IAAI;MAClBV,YAAY,EAAED,OAAO,EAAEC,YAAY;MACnCW,KAAK,EAAEZ,OAAO,CAACY;IACnB,CAAC,CAAC;EACN;EACA;EACAC,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACtB,kBAAkB;EAClC;EACA;AACJ;AACA;AACA;EACIuB,kCAAkCA,CAACxB,qBAAqB,EAAE;IACtD,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;EACtD;EACA;EACAyB,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACxC,UAAU,CAACyC,KAAK,CAAC,CAAC;EAClC;EACA;EACAC,QAAQA,CAAClB,GAAG,EAAEC,OAAO,EAAE;IACnB,MAAMkB,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAAC/B,YAAY,CAACyB,GAAG,CAAC;IAC9C,MAAMoB,KAAK,GAAG,IAAId,GAAG,CAACN,GAAG,EAAE,IAAI,CAACzB,YAAY,CAACyB,GAAG,CAAC;IACjD,IAAIqB,cAAc;IAClB,IAAI,CAACpB,OAAO,EAAEqB,OAAO,IAAIrB,OAAO,CAACqB,OAAO,KAAK,MAAM,EAAE;MACjD;MACA,IAAIH,OAAO,CAACZ,QAAQ,CAAC,CAAC,KAAKa,KAAK,CAACb,QAAQ,CAAC,CAAC,EAAE;QACzCc,cAAc,GAAG,SAAS;MAC9B,CAAC,MACI;QACDA,cAAc,GAAG,MAAM;MAC3B;IACJ,CAAC,MACI;MACDA,cAAc,GAAGpB,OAAO,CAACqB,OAAO;IACpC;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAEC,KAAK,CAAC;IAC/C,MAAMK,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9C1B,GAAG,EAAEoB,KAAK,CAACb,QAAQ,CAAC,CAAC;MACrBM,KAAK,EAAEZ,OAAO,EAAEY,KAAK;MACrBD,YAAY,EAAEW,UAAU;MACxBrB,YAAY,EAAE;IAClB,CAAC,CAAC;IACF,MAAMyB,MAAM,GAAG,IAAIC,wBAAwB,CAAC,CAAC;IAC7C,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAEE,MAAM,EAAE;MACxCN,cAAc;MACdS,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,aAAa,EAAE,KAAK;MACpBT,UAAU;MACVU,IAAI,EAAEhC,OAAO,EAAEgC;IACnB,CAAC,CAAC;IACF,OAAO;MACHC,SAAS,EAAEP,MAAM,CAACO,SAAS;MAC3BC,QAAQ,EAAER,MAAM,CAACQ;IACrB,CAAC;EACL;EACA;EACAC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAEtC,GAAG,EAAE;IACxB,IAAI,CAACuC,kBAAkB,CAAC,MAAM,EAAEF,IAAI,EAAEC,KAAK,EAAEtC,GAAG,CAAC;EACrD;EACA;EACAwC,YAAYA,CAACH,IAAI,EAAEC,KAAK,EAAEtC,GAAG,EAAE;IAC3B,IAAI,CAACuC,kBAAkB,CAAC,SAAS,EAAEF,IAAI,EAAEC,KAAK,EAAEtC,GAAG,CAAC;EACxD;EACAuC,kBAAkBA,CAAClB,cAAc,EAAEgB,IAAI,EAAEI,MAAM,EAAEzC,GAAG,EAAE;IAClD,MAAMmB,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAAC/B,YAAY,CAACyB,GAAG,CAAC;IAC9C,MAAMoB,KAAK,GAAGpB,GAAG,GAAG,IAAIM,GAAG,CAACN,GAAG,EAAE,IAAI,CAACzB,YAAY,CAACyB,GAAG,CAAC,GAAGmB,OAAO;IACjE,MAAMI,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAEC,KAAK,CAAC;IAC/C,MAAMK,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9C1B,GAAG,EAAEoB,KAAK,CAACb,QAAQ,CAAC,CAAC;MACrBK,YAAY,EAAE,IAAI;MAClBV,YAAY,EAAEmC;IAClB,CAAC,CAAC;IACF,MAAMV,MAAM,GAAG,IAAIC,wBAAwB,CAAC,CAAC;IAC7C,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAEE,MAAM,EAAE;MACxCN,cAAc;MACdS,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,aAAa,EAAE,KAAK;MACpBT,UAAU;MACVmB,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;EACAC,UAAUA,CAAClC,GAAG,EAAER,OAAO,EAAE;IACrB,MAAMkB,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAAC/B,YAAY,CAACyB,GAAG,CAAC;IAC9C,MAAM4C,KAAK,GAAG,IAAI,CAACC,SAAS,CAACpC,GAAG,CAAC;IACjC,IAAI,CAACmC,KAAK,EAAE;MACR,MAAME,YAAY,GAAG,IAAIC,YAAY,CAAC,aAAa,EAAE,mBAAmB,CAAC;MACzE,MAAMb,SAAS,GAAG9C,OAAO,CAAC4D,MAAM,CAACF,YAAY,CAAC;MAC9C,MAAMX,QAAQ,GAAG/C,OAAO,CAAC4D,MAAM,CAACF,YAAY,CAAC;MAC7CZ,SAAS,CAACe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1Bd,QAAQ,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHf,SAAS;QACTC;MACJ,CAAC;IACL;IACA,IAAIS,KAAK,KAAK,IAAI,CAACrE,YAAY,EAAE;MAC7B,OAAO;QACH2D,SAAS,EAAE9C,OAAO,CAACC,OAAO,CAAC,IAAI,CAACd,YAAY,CAAC;QAC7C4D,QAAQ,EAAE/C,OAAO,CAACC,OAAO,CAAC,IAAI,CAACd,YAAY;MAC/C,CAAC;IACL;IACA,IAAI,IAAI,CAACU,cAAc,CAACiE,GAAG,CAACN,KAAK,CAACnC,GAAG,CAAC,EAAE;MACpC,MAAM0C,cAAc,GAAG,IAAI,CAAClE,cAAc,CAACmE,GAAG,CAACR,KAAK,CAACnC,GAAG,CAAC;MACzD,OAAO;QACHyB,SAAS,EAAEiB,cAAc,CAACjB,SAAS;QACnCC,QAAQ,EAAEgB,cAAc,CAAChB;MAC7B,CAAC;IACL;IACA,MAAMZ,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAE,IAAIb,GAAG,CAACsC,KAAK,CAAC5C,GAAG,EAAE,IAAI,CAACzB,YAAY,CAACyB,GAAG,CAAC,CAAC;IACnF,MAAMyB,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9C1B,GAAG,EAAE4C,KAAK,CAAC5C,GAAG;MACda,KAAK,EAAE+B,KAAK,CAACS,QAAQ,CAAC,CAAC;MACvBnD,YAAY,EAAE0C,KAAK,CAACU,eAAe,CAAC,CAAC;MACrC7C,GAAG,EAAEmC,KAAK,CAACnC,GAAG;MACdE,EAAE,EAAEiC,KAAK,CAACjC,EAAE;MACZH,KAAK,EAAEoC,KAAK,CAACpC,KAAK;MAClBI,YAAY,EAAEgC,KAAK,CAAChC;IACxB,CAAC,CAAC;IACF,IAAI,CAACtB,qBAAqB,GAAGsD,KAAK,CAACpC,KAAK;IACxC,MAAMmB,MAAM,GAAG,IAAIC,wBAAwB,CAAC,CAAC;IAC7C,IAAI,CAAC3C,cAAc,CAACsE,GAAG,CAACX,KAAK,CAACnC,GAAG,EAAEkB,MAAM,CAAC;IAC1C,IAAI,CAAC6B,YAAY,CAAC,MAAM;MACpB,IAAI,CAACvE,cAAc,CAACwE,MAAM,CAACb,KAAK,CAACnC,GAAG,CAAC;MACrC,IAAI,CAACoB,iBAAiB,CAACJ,WAAW,EAAEE,MAAM,EAAE;QACxCN,cAAc,EAAE,UAAU;QAC1BS,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClB;QACAC,aAAa,EAAE,KAAK;QACpBT,UAAU;QACVU,IAAI,EAAEhC,OAAO,EAAEgC;MACnB,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO;MACHC,SAAS,EAAEP,MAAM,CAACO,SAAS;MAC3BC,QAAQ,EAAER,MAAM,CAACQ;IACrB,CAAC;EACL;EACA;EACAuB,IAAIA,CAACzD,OAAO,EAAE;IACV,IAAI,IAAI,CAACxB,iBAAiB,KAAK,CAAC,EAAE;MAC9B,MAAMqE,YAAY,GAAG,IAAIC,YAAY,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;MAC5E,MAAMb,SAAS,GAAG9C,OAAO,CAAC4D,MAAM,CAACF,YAAY,CAAC;MAC9C,MAAMX,QAAQ,GAAG/C,OAAO,CAAC4D,MAAM,CAACF,YAAY,CAAC;MAC7CZ,SAAS,CAACe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1Bd,QAAQ,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHf,SAAS;QACTC;MACJ,CAAC;IACL;IACA,MAAMS,KAAK,GAAG,IAAI,CAACpE,UAAU,CAAC,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IACzD,OAAO,IAAI,CAACkE,UAAU,CAACC,KAAK,CAACnC,GAAG,EAAER,OAAO,CAAC;EAC9C;EACA;EACA0D,OAAOA,CAAC1D,OAAO,EAAE;IACb,IAAI,IAAI,CAACxB,iBAAiB,KAAK,IAAI,CAACD,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACvD,MAAMkE,YAAY,GAAG,IAAIC,YAAY,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAC/E,MAAMb,SAAS,GAAG9C,OAAO,CAAC4D,MAAM,CAACF,YAAY,CAAC;MAC9C,MAAMX,QAAQ,GAAG/C,OAAO,CAAC4D,MAAM,CAACF,YAAY,CAAC;MAC7CZ,SAAS,CAACe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1Bd,QAAQ,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHf,SAAS;QACTC;MACJ,CAAC;IACL;IACA,MAAMS,KAAK,GAAG,IAAI,CAACpE,UAAU,CAAC,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IACzD,OAAO,IAAI,CAACkE,UAAU,CAACC,KAAK,CAACnC,GAAG,EAAER,OAAO,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2D,EAAEA,CAACC,SAAS,EAAE;IACV,MAAMC,WAAW,GAAG,IAAI,CAACxE,qBAAqB,GAAGuE,SAAS;IAC1D,IAAIC,WAAW,IAAI,IAAI,CAACtF,UAAU,CAACI,MAAM,IAAIkF,WAAW,GAAG,CAAC,EAAE;MAC1D;IACJ;IACA,IAAI,CAACxE,qBAAqB,GAAGwE,WAAW;IACxC,IAAI,CAACN,YAAY,CAAC,MAAM;MACpB;MACA,IAAIM,WAAW,IAAI,IAAI,CAACtF,UAAU,CAACI,MAAM,IAAIkF,WAAW,GAAG,CAAC,EAAE;QAC1D;MACJ;MACA,MAAM3C,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAAC/B,YAAY,CAACyB,GAAG,CAAC;MAC9C,MAAM4C,KAAK,GAAG,IAAI,CAACpE,UAAU,CAACsF,WAAW,CAAC;MAC1C,MAAMvC,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAE,IAAIb,GAAG,CAACsC,KAAK,CAAC5C,GAAG,EAAE,IAAI,CAACzB,YAAY,CAACyB,GAAG,CAAC,CAAC;MACnF,MAAMyB,WAAW,GAAG,IAAIC,yBAAyB,CAAC;QAC9C1B,GAAG,EAAE4C,KAAK,CAAC5C,GAAG;QACda,KAAK,EAAE+B,KAAK,CAACS,QAAQ,CAAC,CAAC;QACvBnD,YAAY,EAAE0C,KAAK,CAACU,eAAe,CAAC,CAAC;QACrC7C,GAAG,EAAEmC,KAAK,CAACnC,GAAG;QACdE,EAAE,EAAEiC,KAAK,CAACjC,EAAE;QACZH,KAAK,EAAEoC,KAAK,CAACpC,KAAK;QAClBI,YAAY,EAAEgC,KAAK,CAAChC;MACxB,CAAC,CAAC;MACF,MAAMe,MAAM,GAAG,IAAIC,wBAAwB,CAAC,CAAC;MAC7C,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAEE,MAAM,EAAE;QACxCN,cAAc,EAAE,UAAU;QAC1BS,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClB;QACAC,aAAa,EAAE,KAAK;QACpBT;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAiC,YAAYA,CAACO,SAAS,EAAE;IACpB,IAAI,IAAI,CAACxE,qBAAqB,EAAE;MAC5BwE,SAAS,CAAC,CAAC;MACX;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC5E,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC6E,IAAI,CAAC,MAAM;MAC/C,OAAO,IAAI5E,OAAO,CAAEC,OAAO,IAAK;QAC5B4E,UAAU,CAAC,MAAM;UACb5E,OAAO,CAAC,CAAC;UACT0E,SAAS,CAAC,CAAC;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAG,gBAAgBA,CAAC/F,IAAI,EAAEgG,QAAQ,EAAElE,OAAO,EAAE;IACtC,IAAI,CAACR,WAAW,CAACyE,gBAAgB,CAAC/F,IAAI,EAAEgG,QAAQ,EAAElE,OAAO,CAAC;EAC9D;EACA;EACAmE,mBAAmBA,CAACjG,IAAI,EAAEgG,QAAQ,EAAElE,OAAO,EAAE;IACzC,IAAI,CAACR,WAAW,CAAC2E,mBAAmB,CAACjG,IAAI,EAAEgG,QAAQ,EAAElE,OAAO,CAAC;EACjE;EACA;EACAoE,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC7E,WAAW,CAAC4E,aAAa,CAACC,KAAK,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN;IACA;IACA,IAAI,CAAC9E,WAAW,GAAG,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5D,IAAI,CAACG,QAAQ,GAAG,IAAI;EACxB;EACA;EACA0E,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1E,QAAQ;EACxB;EACA;EACA+B,iBAAiBA,CAACJ,WAAW,EAAEE,MAAM,EAAE1B,OAAO,EAAE;IAC5C;IACA;IACA,IAAI,CAACT,kBAAkB,GAAG,KAAK;IAC/B,IAAI,IAAI,CAACT,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC0F,MAAM,CAAC,IAAI1B,YAAY,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;MACnF,IAAI,CAAChE,aAAa,GAAGC,SAAS;IAClC;IACA,MAAMD,aAAa,GAAG2F,uBAAuB,CAAC;MAC1CrD,cAAc,EAAEpB,OAAO,CAACoB,cAAc;MACtCS,UAAU,EAAE7B,OAAO,CAAC6B,UAAU;MAC9BC,YAAY,EAAE9B,OAAO,CAAC8B,YAAY;MAClCC,aAAa,EAAE/B,OAAO,CAAC+B,aAAa;MACpCT,UAAU,EAAEtB,OAAO,CAACsB,UAAU;MAC9BoD,MAAM,EAAEhD,MAAM,CAACgD,MAAM;MACrBlD,WAAW;MACXQ,IAAI,EAAEhC,OAAO,CAACgC,IAAI;MAClBrB,YAAY,EAAEa,WAAW,CAACb,YAAY;MACtC8B,YAAY,EAAEzC,OAAO,CAACyC,YAAY;MAClCf,MAAM;MACNiD,eAAe,EAAEA,CAAA,KAAM;QACnB,IAAI,CAACA,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC7F,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACU,WAAW,CAAC4E,aAAa,CAACtF,aAAa,CAAC;IAC7CA,aAAa,CAAC8F,uBAAuB,CAAC,CAAC;IACvC,IAAI9F,aAAa,CAAC+F,YAAY,KAAK,WAAW,EAAE;MAC5C/F,aAAa,CAACgG,MAAM,CAAC,eAAgB,IAAI,CAAC;IAC9C;EACJ;EACA;EACAH,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAC7F,aAAa,EAAE;MACrB;IACJ;IACA,MAAMiG,IAAI,GAAG,IAAI,CAACzG,YAAY;IAC9B,IAAI,CAAC,IAAI,CAACQ,aAAa,CAAC6B,YAAY,EAAE;MAClC,MAAMqE,KAAK,GAAG,IAAI9E,KAAK,CAAC,6CAA6C,CAAC;MACtE,IAAI,CAACpB,aAAa,CAAC0F,MAAM,CAACQ,KAAK,CAAC;MAChC,MAAMA,KAAK;IACf;IACA,IAAI,IAAI,CAAClG,aAAa,CAACsC,cAAc,KAAK,MAAM,IAC5C,IAAI,CAACtC,aAAa,CAACsC,cAAc,KAAK,SAAS,EAAE;MACjD,IAAI,CAAC6D,sBAAsB,CAAC,IAAI,CAACnG,aAAa,CAAC0C,WAAW,EAAE;QACxDJ,cAAc,EAAE,IAAI,CAACtC,aAAa,CAACsC;MACvC,CAAC,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAACtC,aAAa,CAACsC,cAAc,KAAK,UAAU,EAAE;MACvD,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACpG,aAAa,CAAC0C,WAAW,CAAC;IAC1D;IACA,IAAI,CAAC1C,aAAa,CAACqG,kBAAkB,CAAC,IAAI,CAAC7G,YAAY,CAAC;IACxD,MAAM8G,uBAAuB,GAAGC,2CAA2C,CAAC;MACxEN,IAAI;MACJ3D,cAAc,EAAE,IAAI,CAACtC,aAAa,CAACsC;IACvC,CAAC,CAAC;IACF,IAAI,CAAC5B,WAAW,CAAC4E,aAAa,CAACgB,uBAAuB,CAAC;IACvD,IAAI,CAAC,IAAI,CAACtG,aAAa,CAAC2D,YAAY,EAAE;MAClC,MAAM6C,aAAa,GAAGC,mBAAmB,CAAC;QACtC3E,KAAK,EAAE,IAAI,CAAC9B,aAAa,CAAC0C,WAAW,CAAC6B,eAAe,CAAC;MAC1D,CAAC,CAAC;MACF,IAAI,CAACxF,MAAM,CAACuG,aAAa,CAACkB,aAAa,CAAC;IAC5C;EACJ;EACA;EACAL,sBAAsBA,CAACzD,WAAW,EAAE;IAAEJ;EAAe,CAAC,EAAE;IACpD,IAAIA,cAAc,KAAK,MAAM,EAAE;MAC3B,IAAI,CAAC5C,iBAAiB,EAAE;MACxB,IAAI,CAACa,qBAAqB,GAAG,IAAI,CAACb,iBAAiB;IACvD;IACA,MAAM+B,KAAK,GAAG,IAAI,CAAC/B,iBAAiB;IACpC,MAAMgC,GAAG,GAAGY,cAAc,KAAK,MAAM,GAAGX,MAAM,CAAC,IAAI,CAACb,OAAO,EAAE,CAAC,GAAG,IAAI,CAACtB,YAAY,CAACkC,GAAG;IACtF,MAAMmC,KAAK,GAAG,IAAIvC,0BAA0B,CAACoB,WAAW,CAACzB,GAAG,EAAE;MAC1DW,EAAE,EAAED,MAAM,CAAC,IAAI,CAACd,MAAM,EAAE,CAAC;MACzBa,GAAG;MACHD,KAAK;MACLI,YAAY,EAAE,IAAI;MAClBC,KAAK,EAAEY,WAAW,CAAC4B,QAAQ,CAAC,CAAC;MAC7BnD,YAAY,EAAEuB,WAAW,CAAC6B,eAAe,CAAC;IAC9C,CAAC,CAAC;IACF,IAAIjC,cAAc,KAAK,MAAM,EAAE;MAC3B,IAAI,CAAC7C,UAAU,CAACiH,MAAM,CAACjF,KAAK,EAAEkF,QAAQ,EAAE9C,KAAK,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACpE,UAAU,CAACgC,KAAK,CAAC,GAAGoC,KAAK;IAClC;EACJ;EACA;EACAuC,iBAAiBA,CAAC1D,WAAW,EAAE;IAC3B,IAAI,CAAChD,iBAAiB,GAAGgD,WAAW,CAACjB,KAAK;EAC9C;EACA;EACAqC,SAASA,CAACpC,GAAG,EAAE;IACX,KAAK,MAAMmC,KAAK,IAAI,IAAI,CAACpE,UAAU,EAAE;MACjC,IAAIoE,KAAK,CAACnC,GAAG,KAAKA,GAAG,EACjB,OAAOmC,KAAK;IACpB;IACA,OAAO5D,SAAS;EACpB;EACA,IAAI2G,UAAUA,CAACC,QAAQ,EAAE;IACrB,MAAM,IAAIzF,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAIwF,UAAUA,CAAA,EAAG;IACb,MAAM,IAAIxF,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI0F,oBAAoBA,CAACD,QAAQ,EAAE;IAC/B,MAAM,IAAIzF,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI0F,oBAAoBA,CAAA,EAAG;IACvB,MAAM,IAAI1F,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI2F,iBAAiBA,CAACF,QAAQ,EAAE;IAC5B,MAAM,IAAIzF,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI2F,iBAAiBA,CAAA,EAAG;IACpB,MAAM,IAAI3F,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI4F,eAAeA,CAACH,QAAQ,EAAE;IAC1B,MAAM,IAAIzF,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI4F,eAAeA,CAAA,EAAG;IAClB,MAAM,IAAI5F,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI6F,UAAUA,CAAA,EAAG;IACb,MAAM,IAAI7F,KAAK,CAAC,eAAe,CAAC;EACpC;EACA8F,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,MAAM,IAAI/F,KAAK,CAAC,eAAe,CAAC;EACpC;EACAgG,MAAMA,CAACD,QAAQ,EAAE;IACb,MAAM,IAAI/F,KAAK,CAAC,eAAe,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA,MAAME,0BAA0B,CAAC;EAC7BxB,WAAWA,CAACmB,GAAG,EAAE;IAAEW,EAAE;IAAEF,GAAG;IAAED,KAAK;IAAEI,YAAY;IAAEC,KAAK;IAAEX;EAAc,CAAC,EAAE;IACrE,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd;IACA,IAAI,CAACoG,SAAS,GAAG,IAAI;IACrB,IAAI,CAACzF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACX,YAAY,GAAGA,YAAY;EACpC;EACAmD,QAAQA,CAAA,EAAG;IACP;IACA,OAAO,IAAI,CAACxC,KAAK,GAAGwF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC1F,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,KAAK;EAC3E;EACAyC,eAAeA,CAAA,EAAG;IACd;IACA,OAAO,IAAI,CAACpD,YAAY,GAAGmG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACrG,YAAY,CAAC,CAAC,GAAG,IAAI,CAACA,YAAY;EAChG;EACAgE,gBAAgBA,CAAC/F,IAAI,EAAEgG,QAAQ,EAAElE,OAAO,EAAE;IACtC,MAAM,IAAIE,KAAK,CAAC,eAAe,CAAC;EACpC;EACAiE,mBAAmBA,CAACjG,IAAI,EAAEgG,QAAQ,EAAElE,OAAO,EAAE;IACzC,MAAM,IAAIE,KAAK,CAAC,eAAe,CAAC;EACpC;EACAkE,aAAaA,CAACC,KAAK,EAAE;IACjB,MAAM,IAAInE,KAAK,CAAC,eAAe,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA,SAASuE,uBAAuBA,CAAC;EAAE5C,UAAU;EAAEC,YAAY;EAAEC,aAAa;EAAET,UAAU;EAAEF,cAAc;EAAEsD,MAAM;EAAElD,WAAW;EAAEQ,IAAI;EAAErB,YAAY;EAAE8B,YAAY;EAAEf,MAAM;EAAEiD;AAAiB,CAAC,EAAE;EACvL,MAAMN,KAAK,GAAG,IAAIkC,KAAK,CAAC,UAAU,EAAE;IAAEC,OAAO,EAAE,KAAK;IAAE3E;EAAW,CAAC,CAAC;EACnEwC,KAAK,CAACvC,YAAY,GAAGA,YAAY;EACjCuC,KAAK,CAACtC,aAAa,GAAGA,aAAa;EACnCsC,KAAK,CAAC/C,UAAU,GAAGA,UAAU;EAC7B+C,KAAK,CAACjD,cAAc,GAAGA,cAAc;EACrCiD,KAAK,CAACK,MAAM,GAAGA,MAAM;EACrBL,KAAK,CAAC7C,WAAW,GAAGA,WAAW;EAC/B6C,KAAK,CAACrC,IAAI,GAAGA,IAAI;EACjBqC,KAAK,CAACoC,eAAe,GAAG,IAAI;EAC5BpC,KAAK,CAACqC,QAAQ,GAAG,IAAI;EACrBrC,KAAK,CAAC1D,YAAY,GAAGA,YAAY;EACjC0D,KAAK,CAAC5B,YAAY,GAAGA,YAAY;EACjC4B,KAAK,CAACQ,YAAY,GAAG,WAAW;EAChC,IAAI8B,eAAe,GAAG5H,SAAS;EAC/B,IAAI6H,eAAe,GAAG,KAAK;EAC3B,IAAIhC,uBAAuB,GAAG,KAAK;EACnC,IAAIiC,YAAY,GAAG,KAAK;EACxBxC,KAAK,CAACyC,SAAS,GAAG,UAAU9G,OAAO,EAAE;IACjC4G,eAAe,GAAG,IAAI;IACtBvC,KAAK,CAAC1D,YAAY,GAAG,IAAI;IACzB,MAAMoG,OAAO,GAAG/G,OAAO,EAAE+G,OAAO;IAChC,IAAIA,OAAO,EAAE;MACTJ,eAAe,GAAGI,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI/G,OAAO,EAAE8E,MAAM,EAAE;MACjBT,KAAK,CAACQ,YAAY,GAAG7E,OAAO,CAAC8E,MAAM;IACvC;IACA,IAAI9E,OAAO,EAAEgH,UAAU,KAAKjI,SAAS,IAAIiB,OAAO,EAAEiH,MAAM,KAAKlI,SAAS,EAAE;MACpE,MAAM,IAAImB,KAAK,CAAC,eAAe,CAAC;IACpC;EACJ,CAAC;EACDmE,KAAK,CAAC4C,MAAM,GAAG,YAAY;IACvB,MAAM,IAAI/G,KAAK,CAAC,eAAe,CAAC;EACpC,CAAC;EACDmE,KAAK,CAACS,MAAM,GAAG,UAAUoC,QAAQ,GAAG,KAAK,EAAE;IACvC,IAAI,CAACA,QAAQ,IAAI,CAACN,eAAe,EAAE;MAC/B,MAAM,IAAI9D,YAAY,CAAC,qEAAqE,GACxF,yBAAyB,EAAE,mBAAmB,CAAC;IACvD;IACA,IAAI,CAAC8B,uBAAuB,EAAE;MAC1B,MAAM,IAAI9B,YAAY,CAAC,qEAAqE,GACxF,+BAA+B,EAAE,mBAAmB,CAAC;IAC7D;IACA,IAAI+D,YAAY,EAAE;MACd,MAAM,IAAI/D,YAAY,CAAC,kEAAkE,GAAG,SAAS,EAAE,mBAAmB,CAAC;IAC/H;IACA+D,YAAY,GAAG,IAAI;IACnBlC,eAAe,CAAC,CAAC;EACrB,CAAC;EACD;EACAN,KAAK,CAACG,MAAM,GAAG,UAAU2C,MAAM,EAAE;IAC7BzF,MAAM,CAAC0F,eAAe,CAACD,MAAM,CAAC;IAC9BzF,MAAM,CAAC2F,cAAc,CAACF,MAAM,CAAC;EACjC,CAAC;EACD;EACA9C,KAAK,CAACO,uBAAuB,GAAG,YAAY;IACxCA,uBAAuB,GAAG,IAAI;IAC9B,IAAIP,KAAK,CAACQ,YAAY,KAAK,kBAAkB,EAAE;MAC3C;MACA8B,eAAe,EAAE5C,IAAI,CAAC,MAAM;QACxB,IAAI,CAAC8C,YAAY,EAAE;UACfxC,KAAK,CAACS,MAAM,CAAC,cAAe,IAAI,CAAC;QACrC;MACJ,CAAC,EAAE,MAAM,CAAE,CAAC,CAAC;IACjB;IACA3F,OAAO,CAACmI,GAAG,CAAC,CAAC5F,MAAM,CAACO,SAAS,EAAE0E,eAAe,CAAC,CAAC,CAAC5C,IAAI,CAAC,CAAC,CAACpB,KAAK,CAAC,KAAK;MAC/DjB,MAAM,CAAC6F,eAAe,CAAC5E,KAAK,CAAC;IACjC,CAAC,EAAGwE,MAAM,IAAK;MACXzF,MAAM,CAAC2F,cAAc,CAACF,MAAM,CAAC;IACjC,CAAC,CAAC;EACN,CAAC;EACD;EACA9C,KAAK,CAACc,kBAAkB,GAAG,UAAUxC,KAAK,EAAE;IACxCjB,MAAM,CAAC8F,gBAAgB,CAAC7E,KAAK,CAAC;EAClC,CAAC;EACD,OAAO0B,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAASgB,2CAA2CA,CAAC;EAAEN,IAAI;EAAE3D;AAAgB,CAAC,EAAE;EAC5E,MAAMiD,KAAK,GAAG,IAAIkC,KAAK,CAAC,oBAAoB,EAAE;IAC1CC,OAAO,EAAE,KAAK;IACd3E,UAAU,EAAE;EAChB,CAAC,CAAC;EACFwC,KAAK,CAACU,IAAI,GAAGA,IAAI;EACjBV,KAAK,CAACjD,cAAc,GAAGA,cAAc;EACrC,OAAOiD,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAASkB,mBAAmBA,CAAC;EAAE3E;AAAM,CAAC,EAAE;EACpC,MAAMyD,KAAK,GAAG,IAAIkC,KAAK,CAAC,UAAU,EAAE;IAChCC,OAAO,EAAE,KAAK;IACd3E,UAAU,EAAE;EAChB,CAAC,CAAC;EACFwC,KAAK,CAACzD,KAAK,GAAGA,KAAK;EACnB,OAAOyD,KAAK;AAChB;AACA;AACA;AACA;AACA,MAAM5C,yBAAyB,CAAC;EAC5B7C,WAAWA,CAAC;IAAEmB,GAAG;IAAEY,YAAY;IAAEV,YAAY;IAAEW,KAAK;IAAEJ,GAAG,GAAG,IAAI;IAAEE,EAAE,GAAG,IAAI;IAAEH,KAAK,GAAG,CAAC;EAAG,CAAC,EAAE;IACxF,IAAI,CAACR,GAAG,GAAGA,GAAG;IACd,IAAI,CAACY,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACX,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACO,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACH,KAAK,GAAGA,KAAK;EACtB;EACA6C,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxC,KAAK;EACrB;EACAyC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpD,YAAY;EAC5B;AACJ;AACA;AACA,SAASsB,YAAYA,CAACwD,IAAI,EAAE0C,EAAE,EAAE;EAC5B,OAAQA,EAAE,CAACC,IAAI,KAAK3C,IAAI,CAAC2C,IAAI,IACzBD,EAAE,CAACE,QAAQ,KAAK5C,IAAI,CAAC4C,QAAQ,IAC7BF,EAAE,CAACG,QAAQ,KAAK7C,IAAI,CAAC6C,QAAQ,IAC7BH,EAAE,CAACI,MAAM,KAAK9C,IAAI,CAAC8C,MAAM;AACjC;AACA;AACA,MAAMlG,wBAAwB,CAAC;EAC3B,IAAI+C,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACoD,eAAe,CAACpD,MAAM;EACtC;EACA9F,WAAWA,CAAA,EAAG;IAAA,IAAAmJ,KAAA;IACV,IAAI,CAACD,eAAe,GAAG,IAAIE,eAAe,CAAC,CAAC;IAC5C,IAAI,CAAC/F,SAAS,GAAG,IAAI9C,OAAO,CAAC,CAACC,OAAO,EAAE2D,MAAM,KAAK;MAC9C,IAAI,CAACyE,gBAAgB,GAAGpI,OAAO;MAC/B,IAAI,CAACgI,eAAe,GAAGrE,MAAM;IACjC,CAAC,CAAC;IACF,IAAI,CAACb,QAAQ,GAAG,IAAI/C,OAAO;MAAA,IAAA8I,IAAA,GAAAC,iBAAA,CAAC,WAAO9I,OAAO,EAAE2D,MAAM,EAAK;QACnDgF,KAAI,CAACR,eAAe,GAAGnI,OAAO;QAC9B2I,KAAI,CAACV,cAAc,GAAIF,MAAM,IAAK;UAC9BpE,MAAM,CAACoE,MAAM,CAAC;UACdY,KAAI,CAACD,eAAe,CAACK,KAAK,CAAChB,MAAM,CAAC;QACtC,CAAC;MACL,CAAC;MAAA,iBAAAiB,EAAA,EAAAC,GAAA;QAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;IACF;IACA,IAAI,CAACtG,SAAS,CAACe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IAC/B,IAAI,CAACd,QAAQ,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EAClC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwF,QAAQ,GAAG,+DAA+D;AAChF,SAASC,QAAQA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAChC,MAAMC,cAAc,GAAG,wBAAwB;EAC/C,IAAIC,UAAU;EACd;EACA;EACA,IAAI,CAACD,cAAc,CAACE,IAAI,CAACJ,MAAM,CAAC,EAAE;IAC9BG,UAAU,GAAG,mBAAmB;EACpC;EACA,IAAIE,SAAS;EACb,IAAI;IACAA,SAAS,GAAG,IAAI1I,GAAG,CAACqI,MAAM,EAAEG,UAAU,CAAC;EAC3C,CAAC,CACD,OAAOG,CAAC,EAAE;IACN,MAAMtH,MAAM,GAAG8G,QAAQ,CAACS,IAAI,CAACJ,UAAU,IAAI,EAAE,GAAGH,MAAM,CAAC;IACvD,IAAI,CAAChH,MAAM,EAAE;MACT,MAAM,IAAIxB,KAAK,CAAC,gBAAgBwI,MAAM,eAAeC,QAAQ,EAAE,CAAC;IACpE;IACA,MAAMO,SAAS,GAAGxH,MAAM,CAAC,CAAC,CAAC,CAACyH,KAAK,CAAC,GAAG,CAAC;IACtCJ,SAAS,GAAG;MACRK,QAAQ,EAAE1H,MAAM,CAAC,CAAC,CAAC;MACnBiG,QAAQ,EAAEuB,SAAS,CAAC,CAAC,CAAC;MACtBG,IAAI,EAAEH,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;MACxBtB,QAAQ,EAAElG,MAAM,CAAC,CAAC,CAAC;MACnBmG,MAAM,EAAEnG,MAAM,CAAC,CAAC,CAAC;MACjBgG,IAAI,EAAEhG,MAAM,CAAC,CAAC;IAClB,CAAC;EACL;EACA,IAAIqH,SAAS,CAACnB,QAAQ,IAAImB,SAAS,CAACnB,QAAQ,CAAC0B,OAAO,CAACX,QAAQ,CAAC,KAAK,CAAC,EAAE;IAClEI,SAAS,CAACnB,QAAQ,GAAGmB,SAAS,CAACnB,QAAQ,CAAC2B,SAAS,CAACZ,QAAQ,CAAChK,MAAM,CAAC;EACtE;EACA,OAAO;IACHgJ,QAAQ,EAAG,CAACkB,UAAU,IAAIE,SAAS,CAACpB,QAAQ,IAAK,EAAE;IACnDyB,QAAQ,EAAG,CAACP,UAAU,IAAIE,SAAS,CAACK,QAAQ,IAAK,EAAE;IACnDC,IAAI,EAAG,CAACR,UAAU,IAAIE,SAAS,CAACM,IAAI,IAAK,EAAE;IAC3CzB,QAAQ,EAAEmB,SAAS,CAACnB,QAAQ,IAAI,GAAG;IACnCC,MAAM,EAAEkB,SAAS,CAAClB,MAAM,IAAI,EAAE;IAC9BH,IAAI,EAAEqB,SAAS,CAACrB,IAAI,IAAI;EAC5B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8B,6BAA6B,GAAG,IAAIzM,cAAc,CAAC,+BAA+B,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA,MAAM0M,oBAAoB,CAAC;EACvB7K,WAAWA,CAAC8K,MAAM,EAAE;IAChB,IAAI,CAACf,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACgB,UAAU,GAAG,IAAIvM,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACwM,eAAe,GAAG,IAAIxM,OAAO,CAAC,CAAC;IACpC,IAAI,CAACyM,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,UAAU,GAAG,CAAC;MAAEnC,QAAQ,EAAE,EAAE;MAAEyB,QAAQ,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEzB,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAE,EAAE;MAAEH,IAAI,EAAE,EAAE;MAAE9G,KAAK,EAAE;IAAK,CAAC,CAAC;IAC9G,IAAI8I,MAAM,EAAE;MACR,IAAI,CAACf,QAAQ,GAAGe,MAAM,CAACK,WAAW,IAAI,EAAE;MACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,EAAEP,MAAM,CAACQ,QAAQ,IAAI,iBAAiB,EAAE,IAAI,CAACvB,QAAQ,CAAC;MAClG,IAAI,CAACmB,UAAU,CAAC,CAAC,CAAC,GAAG;QAAE,GAAGE;MAAc,CAAC;IAC7C;EACJ;EACA,IAAIrC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACmC,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAAClC,QAAQ;EACxD;EACA,IAAIyB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACU,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACT,QAAQ;EACxD;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACS,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACR,IAAI;EACpD;EACA,IAAIzB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACkC,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACjC,QAAQ;EACxD;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACiC,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAAChC,MAAM;EACtD;EACA,IAAIH,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoC,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACnC,IAAI;EACpD;EACA,IAAI9G,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACkJ,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACjJ,KAAK;EACrD;EACAuJ,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxB,QAAQ;EACxB;EACAyB,UAAUA,CAACC,EAAE,EAAE;IACX,MAAMC,YAAY,GAAG,IAAI,CAACV,eAAe,CAACW,SAAS,CAACF,EAAE,CAAC;IACvD,OAAO,MAAMC,YAAY,CAACE,WAAW,CAAC,CAAC;EAC3C;EACAC,YAAYA,CAACJ,EAAE,EAAE;IACb,MAAMC,YAAY,GAAG,IAAI,CAACX,UAAU,CAACY,SAAS,CAACF,EAAE,CAAC;IAClD,OAAO,MAAMC,YAAY,CAACE,WAAW,CAAC,CAAC;EAC3C;EACA,IAAIE,IAAIA,CAAA,EAAG;IACP,IAAI3K,GAAG,GAAG,GAAG,IAAI,CAACqJ,QAAQ,KAAK,IAAI,CAACzB,QAAQ,GAAG,IAAI,CAAC0B,IAAI,GAAG,GAAG,GAAG,IAAI,CAACA,IAAI,GAAG,EAAE,EAAE;IACjFtJ,GAAG,IAAI,GAAG,IAAI,CAAC6H,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACH,IAAI,EAAE;IAChF,OAAO3H,GAAG;EACd;EACA,IAAIA,GAAGA,CAAA,EAAG;IACN,OAAO,GAAG,IAAI,CAAC6H,QAAQ,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACH,IAAI,EAAE;EACvD;EACAuC,YAAYA,CAACrJ,KAAK,EAAEb,GAAG,EAAE4I,QAAQ,GAAG,EAAE,EAAE;IACpC;IACA/H,KAAK,GAAGwF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC1F,KAAK,CAAC,CAAC;IACzC,OAAO;MAAE,GAAG6H,QAAQ,CAAC1I,GAAG,EAAE4I,QAAQ,CAAC;MAAE/H;IAAM,CAAC;EAChD;EACA2B,YAAYA,CAAC3B,KAAK,EAAEyB,KAAK,EAAEsI,MAAM,EAAE;IAC/B,MAAM;MAAE/C,QAAQ;MAAEC,MAAM;MAAEjH,KAAK,EAAEgK,WAAW;MAAElD;IAAK,CAAC,GAAG,IAAI,CAACuC,YAAY,CAACrJ,KAAK,EAAE+J,MAAM,CAAC;IACvF,IAAI,CAACb,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,GAAG;MACnC,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC;MACvCjC,QAAQ;MACRC,MAAM;MACNH,IAAI;MACJ9G,KAAK,EAAEgK;IACX,CAAC;EACL;EACAzI,SAASA,CAACvB,KAAK,EAAEyB,KAAK,EAAEsI,MAAM,EAAE;IAC5B,MAAM;MAAE/C,QAAQ;MAAEC,MAAM;MAAEjH,KAAK,EAAEgK,WAAW;MAAElD;IAAK,CAAC,GAAG,IAAI,CAACuC,YAAY,CAACrJ,KAAK,EAAE+J,MAAM,CAAC;IACvF,IAAI,IAAI,CAACd,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACC,UAAU,CAACtE,MAAM,CAAC,IAAI,CAACqE,cAAc,GAAG,CAAC,CAAC;IACnD;IACA,IAAI,CAACC,UAAU,CAACe,IAAI,CAAC;MACjB,GAAG,IAAI,CAACf,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC;MACvCjC,QAAQ;MACRC,MAAM;MACNH,IAAI;MACJ9G,KAAK,EAAEgK;IACX,CAAC,CAAC;IACF,IAAI,CAACf,cAAc,GAAG,IAAI,CAACC,UAAU,CAACnL,MAAM,GAAG,CAAC;EACpD;EACA+E,OAAOA,CAAA,EAAG;IACN,MAAMoH,MAAM,GAAG,IAAI,CAAC/K,GAAG;IACvB,MAAMgL,OAAO,GAAG,IAAI,CAACrD,IAAI;IACzB,IAAI,IAAI,CAACmC,cAAc,GAAG,IAAI,CAACC,UAAU,CAACnL,MAAM,EAAE;MAC9C,IAAI,CAACkL,cAAc,EAAE;IACzB;IACA,IAAI,CAACmB,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACArH,IAAIA,CAAA,EAAG;IACH,MAAMqH,MAAM,GAAG,IAAI,CAAC/K,GAAG;IACvB,MAAMgL,OAAO,GAAG,IAAI,CAACrD,IAAI;IACzB,IAAI,IAAI,CAACmC,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,cAAc,EAAE;IACzB;IACA,IAAI,CAACmB,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACAG,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,MAAMJ,MAAM,GAAG,IAAI,CAAC/K,GAAG;IACvB,MAAMgL,OAAO,GAAG,IAAI,CAACrD,IAAI;IACzB,MAAMyD,aAAa,GAAG,IAAI,CAACtB,cAAc,GAAGqB,gBAAgB;IAC5D,IAAIC,aAAa,IAAI,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACrB,UAAU,CAACnL,MAAM,EAAE;MAC9D,IAAI,CAACkL,cAAc,GAAGsB,aAAa;IACvC;IACA,IAAI,CAACH,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACA1H,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxC,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoK,UAAUA,CAACD,OAAO,EAAED,MAAM,EAAE;IACxB,IAAI,CAAClB,eAAe,CAACwB,IAAI,CAAC;MACtBlN,IAAI,EAAE,UAAU;MAChB0C,KAAK,EAAE,IAAI,CAACwC,QAAQ,CAAC,CAAC;MACtB0H,MAAM;MACNH,MAAM,EAAE,IAAI,CAAC5K;IACjB,CAAC,CAAC;IACF,IAAIgL,OAAO,KAAK,IAAI,CAACrD,IAAI,EAAE;MACvB,IAAI,CAACiC,UAAU,CAACyB,IAAI,CAAC;QACjBlN,IAAI,EAAE,YAAY;QAClB0C,KAAK,EAAE,IAAI;QACXkK,MAAM;QACNH,MAAM,EAAE,IAAI,CAAC5K;MACjB,CAAC,CAAC;IACN;EACJ;EACA;IAAS,IAAI,CAACzC,IAAI,YAAA+N,6BAAA7N,iBAAA;MAAA,YAAAA,iBAAA,IAAyFiM,oBAAoB,EA12B9B5M,EAAE,CAAAyO,QAAA,CA02B8C9B,6BAA6B;IAAA,CAA6D;EAAE;EAC7O;IAAS,IAAI,CAAC/L,KAAK,kBA32B8EZ,EAAE,CAAAa,kBAAA;MAAAC,KAAA,EA22BY8L,oBAAoB;MAAA7L,OAAA,EAApB6L,oBAAoB,CAAAnM;IAAA,EAAG;EAAE;AAC5I;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KA72BqGnB,EAAE,CAAAoB,iBAAA,CA62BXwL,oBAAoB,EAAc,CAAC;IACnHvL,IAAI,EAAEpB;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoB,IAAI,EAAEa,SAAS;IAAEwM,UAAU,EAAE,CAAC;MAC/CrN,IAAI,EAAElB,MAAM;MACZmB,IAAI,EAAE,CAACqL,6BAA6B;IACxC,CAAC,EAAE;MACCtL,IAAI,EAAEjB;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,MAAMuO,8BAA8B,CAAC;EACjC5M,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6M,mBAAmB,GAAGvO,MAAM,CAACX,mBAAmB,CAAC;IACtD,IAAI,CAACsB,MAAM,GAAGX,MAAM,CAACV,QAAQ,CAAC,CAACkP,WAAW;IAC1C,IAAI,CAAChC,MAAM,GAAGxM,MAAM,CAACsM,6BAA6B,EAAE;MAAEmC,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,EAAE,IAAI,CAACF,mBAAmB,YAAYpN,cAAc,CAAC,EAAE;MACvD,MAAM,IAAI6B,KAAK,CAAC,oEAAoE,GAChF,uEAAuE,CAAC;IAChF;EACJ;EACAiK,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACT,MAAM,EAAEK,WAAW,IAAI,EAAE;EACzC;EACAK,UAAUA,CAACC,EAAE,EAAE;IACX,IAAI,CAACxM,MAAM,CAACoG,gBAAgB,CAAC,UAAU,EAAEoG,EAAE,CAAC;IAC5C,OAAO,MAAM,IAAI,CAACxM,MAAM,CAACsG,mBAAmB,CAAC,UAAU,EAAEkG,EAAE,CAAC;EAChE;EACAI,YAAYA,CAACJ,EAAE,EAAE;IACb,IAAI,CAACxM,MAAM,CAACoG,gBAAgB,CAAC,YAAY,EAAEoG,EAAE,CAAC;IAC9C,OAAO,MAAM,IAAI,CAACxM,MAAM,CAACsG,mBAAmB,CAAC,YAAY,EAAEkG,EAAE,CAAC;EAClE;EACA,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACe,mBAAmB,CAACnN,YAAY,CAACyB,GAAG;EACpD;EACA,IAAIqJ,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI/I,GAAG,CAAC,IAAI,CAACoL,mBAAmB,CAACnN,YAAY,CAACyB,GAAG,CAAC,CAACqJ,QAAQ;EACtE;EACA,IAAIzB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAItH,GAAG,CAAC,IAAI,CAACoL,mBAAmB,CAACnN,YAAY,CAACyB,GAAG,CAAC,CAAC4H,QAAQ;EACtE;EACA,IAAI0B,IAAIA,CAAA,EAAG;IACP,OAAO,IAAIhJ,GAAG,CAAC,IAAI,CAACoL,mBAAmB,CAACnN,YAAY,CAACyB,GAAG,CAAC,CAACsJ,IAAI;EAClE;EACA,IAAIzB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAIvH,GAAG,CAAC,IAAI,CAACoL,mBAAmB,CAACnN,YAAY,CAACyB,GAAG,CAAC,CAAC6H,QAAQ;EACtE;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAIxH,GAAG,CAAC,IAAI,CAACoL,mBAAmB,CAACnN,YAAY,CAACyB,GAAG,CAAC,CAAC8H,MAAM;EACpE;EACA,IAAIH,IAAIA,CAAA,EAAG;IACP,OAAO,IAAIrH,GAAG,CAAC,IAAI,CAACoL,mBAAmB,CAACnN,YAAY,CAACyB,GAAG,CAAC,CAAC2H,IAAI;EAClE;EACAvF,SAASA,CAACvB,KAAK,EAAEyB,KAAK,EAAEtC,GAAG,EAAE;IACzB,IAAI,CAAC0L,mBAAmB,CAACtJ,SAAS,CAACvB,KAAK,EAAEyB,KAAK,EAAEtC,GAAG,CAAC;EACzD;EACAwC,YAAYA,CAAC3B,KAAK,EAAEyB,KAAK,EAAEtC,GAAG,EAAE;IAC5B,IAAI,CAAC0L,mBAAmB,CAAClJ,YAAY,CAAC3B,KAAK,EAAEyB,KAAK,EAAEtC,GAAG,CAAC;EAC5D;EACA2D,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC+H,mBAAmB,CAAC/H,OAAO,CAAC,CAAC;EACtC;EACAD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACgI,mBAAmB,CAAChI,IAAI,CAAC,CAAC;EACnC;EACAwH,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACO,mBAAmB,CAAC9H,EAAE,CAACuH,gBAAgB,CAAC;EACjD;EACA9H,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACqI,mBAAmB,CAACnN,YAAY,CAAC+E,eAAe,CAAC,CAAC;EAClE;EACA;IAAS,IAAI,CAAC/F,IAAI,YAAAsO,uCAAApO,iBAAA;MAAA,YAAAA,iBAAA,IAAyFgO,8BAA8B;IAAA,CAAoD;EAAE;EAC/L;IAAS,IAAI,CAAC/N,KAAK,kBAr7B8EZ,EAAE,CAAAa,kBAAA;MAAAC,KAAA,EAq7BY6N,8BAA8B;MAAA5N,OAAA,EAA9B4N,8BAA8B,CAAAlO;IAAA,EAAG;EAAE;AACtJ;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAv7BqGnB,EAAE,CAAAoB,iBAAA,CAu7BXuN,8BAA8B,EAAc,CAAC;IAC7HtN,IAAI,EAAEpB;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA,SAAS+O,6BAA6BA,CAAA,EAAG;EACrC,OAAO,CACH;IACIC,OAAO,EAAEzO,kBAAkB;IAC3Be,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMsL,MAAM,GAAGxM,MAAM,CAACsM,6BAA6B,EAAE;QAAEmC,QAAQ,EAAE;MAAK,CAAC,CAAC;MACxE,OAAO,IAAItN,cAAc,CAACnB,MAAM,CAACV,QAAQ,CAAC,CAACkP,WAAW,EAAEhC,MAAM,EAAEQ,QAAQ,IAAI,iBAAiB,CAAC;IAClG;EACJ,CAAC,EACD;IAAE4B,OAAO,EAAErP,gBAAgB;IAAEsP,QAAQ,EAAEP;EAA+B,CAAC,CAC1E;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMQ,WAAW,CAAC;EACdpN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkL,UAAU,GAAG,EAAE;IACpB,IAAI,CAACmC,QAAQ,GAAG,CAAC,IAAIC,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IACjD,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIjP,YAAY,CAAC,CAAC;IAClC;IACA,IAAI,CAACkP,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAI;EACtC;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,sBAAsB,EAAEhC,WAAW,CAAC,CAAC;IAC1C,IAAI,CAAC+B,mBAAmB,GAAG,EAAE;EACjC;EACAG,cAAcA,CAAC3M,GAAG,EAAE;IAChB,IAAI,CAACkM,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACQ,IAAI,GAAG5M,GAAG;EAChD;EACA6M,WAAWA,CAAC7M,GAAG,EAAE;IACb,IAAI,CAACsM,SAAS,GAAGtM,GAAG;EACxB;EACA4M,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACV,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACQ,IAAI;EACjD;EACAvJ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC6I,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACvL,KAAK;EAClD;EACAiM,oBAAoBA,CAACF,IAAI,EAAEG,KAAK,GAAG,EAAE,EAAE;IACnC,MAAMC,SAAS,GAAGJ,IAAI,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,IAAI,CAACpD,SAAS,CAAC,CAAC,EAAEoD,IAAI,CAAChO,MAAM,GAAG,CAAC,CAAC,GAAGgO,IAAI;IAChF,MAAMM,QAAQ,GAAG,IAAI,CAACN,IAAI,CAAC,CAAC,CAACK,QAAQ,CAAC,GAAG,CAAC,GACpC,IAAI,CAACL,IAAI,CAAC,CAAC,CAACpD,SAAS,CAAC,CAAC,EAAE,IAAI,CAACoD,IAAI,CAAC,CAAC,CAAChO,MAAM,GAAG,CAAC,CAAC,GAChD,IAAI,CAACgO,IAAI,CAAC,CAAC;IACjB,OAAOM,QAAQ,IAAIF,SAAS,IAAID,KAAK,CAACnO,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGmO,KAAK,GAAG,EAAE,CAAC;EACxE;EACAI,cAAcA,CAACtF,QAAQ,EAAE;IACrB,IAAI,CAACwE,QAAQ,CAACe,IAAI,CAAC;MAAE,KAAK,EAAEvF,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAW,CAAC,CAAC;EAC5E;EACAwF,kBAAkBA,CAACxF,QAAQ,EAAE;IACzB,MAAM+E,IAAI,GAAG,IAAI,CAACU,kBAAkB,CAACzF,QAAQ,CAAC;IAC9C,IAAI,CAAC0F,WAAW,CAACX,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;IAChC,IAAI,CAAC7C,UAAU,CAACe,IAAI,CAAC,QAAQ,GAAGjD,QAAQ,CAAC;IACzC;IACA;IACA,IAAI,CAACwE,QAAQ,CAACe,IAAI,CAAC;MAAE,KAAK,EAAEvF,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAW,CAAC,CAAC;IACxE,IAAI,CAACwE,QAAQ,CAACe,IAAI,CAAC;MAAE,KAAK,EAAEvF,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAa,CAAC,CAAC;EAC9E;EACAyF,kBAAkBA,CAACtN,GAAG,EAAE;IACpB,IAAIA,GAAG,CAACpB,MAAM,GAAG,CAAC,IAAI,CAACoB,GAAG,CAACwN,UAAU,CAAC,GAAG,CAAC,EAAE;MACxCxN,GAAG,GAAG,GAAG,GAAGA,GAAG;IACnB;IACA,OAAO,IAAI,CAACsM,SAAS,GAAGtM,GAAG;EAC/B;EACA4D,EAAEA,CAACgJ,IAAI,EAAEG,KAAK,GAAG,EAAE,EAAElM,KAAK,GAAG,IAAI,EAAE;IAC/B+L,IAAI,GAAG,IAAI,CAACU,kBAAkB,CAACV,IAAI,CAAC;IACpC,IAAI,CAACW,WAAW,CAACX,IAAI,EAAEG,KAAK,EAAElM,KAAK,CAAC;IACpC,MAAM4M,aAAa,GAAG,IAAI,CAACvB,QAAQ,CAAC,IAAI,CAACE,aAAa,GAAG,CAAC,CAAC;IAC3D,IAAIqB,aAAa,CAACb,IAAI,IAAIA,IAAI,IAAIa,aAAa,CAACV,KAAK,IAAIA,KAAK,EAAE;MAC5D;IACJ;IACA,MAAM/M,GAAG,GAAG4M,IAAI,IAAIG,KAAK,CAACnO,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGmO,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAAChD,UAAU,CAACe,IAAI,CAAC9K,GAAG,CAAC;IACzB,IAAI,CAAC0N,yBAAyB,CAACd,IAAI,GAAGjQ,qBAAqB,CAACoQ,KAAK,CAAC,EAAElM,KAAK,CAAC;EAC9E;EACA2B,YAAYA,CAACoK,IAAI,EAAEG,KAAK,GAAG,EAAE,EAAElM,KAAK,GAAG,IAAI,EAAE;IACzC+L,IAAI,GAAG,IAAI,CAACU,kBAAkB,CAACV,IAAI,CAAC;IACpC,MAAMtL,OAAO,GAAG,IAAI,CAAC4K,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC;IACjD9K,OAAO,CAACT,KAAK,GAAGA,KAAK;IACrB,IAAIS,OAAO,CAACsL,IAAI,IAAIA,IAAI,IAAItL,OAAO,CAACyL,KAAK,IAAIA,KAAK,EAAE;MAChD;IACJ;IACAzL,OAAO,CAACsL,IAAI,GAAGA,IAAI;IACnBtL,OAAO,CAACyL,KAAK,GAAGA,KAAK;IACrB,MAAM/M,GAAG,GAAG4M,IAAI,IAAIG,KAAK,CAACnO,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGmO,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAAChD,UAAU,CAACe,IAAI,CAAC,WAAW,GAAG9K,GAAG,CAAC;IACvC,IAAI,CAAC0N,yBAAyB,CAACd,IAAI,GAAGjQ,qBAAqB,CAACoQ,KAAK,CAAC,EAAElM,KAAK,CAAC;EAC9E;EACA8C,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyI,aAAa,GAAG,IAAI,CAACF,QAAQ,CAACtN,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACwN,aAAa,EAAE;MACpB,IAAI,CAACC,QAAQ,CAACe,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAACR,IAAI,CAAC,CAAC;QAClB,OAAO,EAAE,IAAI,CAACvJ,QAAQ,CAAC,CAAC;QACxB,KAAK,EAAE,IAAI;QACX,MAAM,EAAE;MACZ,CAAC,CAAC;IACN;EACJ;EACAK,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC0I,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,aAAa,EAAE;MACpB,IAAI,CAACC,QAAQ,CAACe,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAACR,IAAI,CAAC,CAAC;QAClB,OAAO,EAAE,IAAI,CAACvJ,QAAQ,CAAC,CAAC;QACxB,KAAK,EAAE,IAAI;QACX,MAAM,EAAE;MACZ,CAAC,CAAC;IACN;EACJ;EACA6H,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,MAAMC,aAAa,GAAG,IAAI,CAACgB,aAAa,GAAGjB,gBAAgB;IAC3D,IAAIC,aAAa,IAAI,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACc,QAAQ,CAACtN,MAAM,EAAE;MAC5D,IAAI,CAACwN,aAAa,GAAGhB,aAAa;MAClC,IAAI,CAACiB,QAAQ,CAACe,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAACR,IAAI,CAAC,CAAC;QAClB,OAAO,EAAE,IAAI,CAACvJ,QAAQ,CAAC,CAAC;QACxB,KAAK,EAAE,IAAI;QACX,MAAM,EAAE;MACZ,CAAC,CAAC;IACN;EACJ;EACAsK,WAAWA,CAACrD,EAAE,EAAE;IACZ,IAAI,CAACkC,mBAAmB,CAAC1B,IAAI,CAACR,EAAE,CAAC;IACjC,IAAI,CAACmC,sBAAsB,KAAK,IAAI,CAACjC,SAAS,CAAEoD,CAAC,IAAK;MAClD,IAAI,CAACF,yBAAyB,CAACE,CAAC,CAAC5N,GAAG,EAAE4N,CAAC,CAAC/M,KAAK,CAAC;IAClD,CAAC,CAAC;IACF,OAAO,MAAM;MACT,MAAMgN,OAAO,GAAG,IAAI,CAACrB,mBAAmB,CAACjD,OAAO,CAACe,EAAE,CAAC;MACpD,IAAI,CAACkC,mBAAmB,CAAC/G,MAAM,CAACoI,OAAO,EAAE,CAAC,CAAC;MAC3C,IAAI,IAAI,CAACrB,mBAAmB,CAAC5N,MAAM,KAAK,CAAC,EAAE;QACvC,IAAI,CAAC6N,sBAAsB,EAAEhC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAACgC,sBAAsB,GAAG,IAAI;MACtC;IACJ,CAAC;EACL;EACA;EACAiB,yBAAyBA,CAAC1N,GAAG,GAAG,EAAE,EAAEa,KAAK,EAAE;IACvC,IAAI,CAAC2L,mBAAmB,CAACsB,OAAO,CAAExD,EAAE,IAAKA,EAAE,CAACtK,GAAG,EAAEa,KAAK,CAAC,CAAC;EAC5D;EACA2J,SAASA,CAACuD,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAAC5B,QAAQ,CAAC7B,SAAS,CAAC;MAAEa,IAAI,EAAE0C,MAAM;MAAE9I,KAAK,EAAE+I,OAAO;MAAEE,QAAQ,EAAED;IAAS,CAAC,CAAC;EACxF;EACAE,SAASA,CAACnO,GAAG,EAAE;IACX,OAAO,IAAI;EACf;EACAuN,WAAWA,CAACX,IAAI,EAAEG,KAAK,EAAElM,KAAK,EAAE;IAC5B,IAAI,IAAI,CAACuL,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACF,QAAQ,CAACzG,MAAM,CAAC,IAAI,CAAC2G,aAAa,GAAG,CAAC,CAAC;IAChD;IACA,IAAI,CAACF,QAAQ,CAACpB,IAAI,CAAC,IAAIqB,aAAa,CAACS,IAAI,EAAEG,KAAK,EAAElM,KAAK,CAAC,CAAC;IACzD,IAAI,CAACuL,aAAa,GAAG,IAAI,CAACF,QAAQ,CAACtN,MAAM,GAAG,CAAC;EACjD;EACA;IAAS,IAAI,CAACrB,IAAI,YAAA6Q,oBAAA3Q,iBAAA;MAAA,YAAAA,iBAAA,IAAyFwO,WAAW;IAAA,CAAoD;EAAE;EAC5K;IAAS,IAAI,CAACvO,KAAK,kBApmC8EZ,EAAE,CAAAa,kBAAA;MAAAC,KAAA,EAomCYqO,WAAW;MAAApO,OAAA,EAAXoO,WAAW,CAAA1O;IAAA,EAAG;EAAE;AACnI;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAtmCqGnB,EAAE,CAAAoB,iBAAA,CAsmCX+N,WAAW,EAAc,CAAC;IAC1G9N,IAAI,EAAEpB;EACV,CAAC,CAAC;AAAA;AACV,MAAMoP,aAAa,CAAC;EAChBtN,WAAWA,CAAC+N,IAAI,EAAEG,KAAK,EAAElM,KAAK,EAAE;IAC5B,IAAI,CAAC+L,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAClM,KAAK,GAAGA,KAAK;EACtB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwN,oBAAoB,SAASzR,gBAAgB,CAAC;EAChDiC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACyP,gBAAgB,GAAG,GAAG;IAC3B,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACzE,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACsC,QAAQ,GAAG,IAAIjP,YAAY,CAAC,CAAC;IAClC,IAAI,CAACqR,YAAY,GAAG,EAAE;EAC1B;EACAC,gBAAgBA,CAAC1O,GAAG,EAAE;IAClB,IAAI,CAACuO,YAAY,GAAGvO,GAAG;IACvB,IAAI,CAACqM,QAAQ,CAACe,IAAI,CAAC,IAAIuB,kBAAkB,CAAC,IAAI,CAAC/B,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3D;EACAA,IAAIA,CAACgC,WAAW,GAAG,KAAK,EAAE;IACtB,OAAO,IAAI,CAACL,YAAY;EAC5B;EACAjB,kBAAkBA,CAACnG,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAACqG,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAACc,gBAAgB,CAACrB,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAACqB,gBAAgB,GAAGnH,QAAQ,CAACqC,SAAS,CAAC,CAAC,CAAC;IACxD;IACA,OAAO,IAAI,CAAC8E,gBAAgB,GAAGnH,QAAQ;EAC3C;EACA/E,SAASA,CAACyM,GAAG,EAAEvM,KAAK,EAAEsK,IAAI,EAAEG,KAAK,EAAE;IAC/B;IACA,IAAI,CAAC0B,YAAY,CAAC3D,IAAI,CAAC+D,GAAG,CAAC;IAC3B,IAAI,CAACL,aAAa,GAAGlM,KAAK;IAC1B,MAAMtC,GAAG,GAAG4M,IAAI,IAAIG,KAAK,CAACnO,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGmO,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAACwB,YAAY,GAAGvO,GAAG;IACvB,MAAM8O,WAAW,GAAG,IAAI,CAACxB,kBAAkB,CAACtN,GAAG,CAAC;IAChD,IAAI,CAAC+J,UAAU,CAACe,IAAI,CAACgE,WAAW,CAAC;EACrC;EACAtM,YAAYA,CAACqM,GAAG,EAAEvM,KAAK,EAAEsK,IAAI,EAAEG,KAAK,EAAE;IAClC;IACA,IAAI,CAAC0B,YAAY,CAAC,CAAC,IAAI,CAACA,YAAY,CAAC7P,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,GAAGiQ,GAAG;IAC5D,IAAI,CAACL,aAAa,GAAGlM,KAAK;IAC1B,MAAMtC,GAAG,GAAG4M,IAAI,IAAIG,KAAK,CAACnO,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGmO,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAACwB,YAAY,GAAGvO,GAAG;IACvB,MAAM8O,WAAW,GAAG,IAAI,CAACxB,kBAAkB,CAACtN,GAAG,CAAC;IAChD,IAAI,CAAC+J,UAAU,CAACe,IAAI,CAAC,WAAW,GAAGgE,WAAW,CAAC;EACnD;EACAzE,UAAUA,CAACC,EAAE,EAAE;IACX,IAAI,CAAC+B,QAAQ,CAAC7B,SAAS,CAAC;MAAEa,IAAI,EAAEf;IAAG,CAAC,CAAC;EACzC;EACAyE,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACT,gBAAgB;EAChC;EACA5K,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACqG,UAAU,CAACnL,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACmL,UAAU,CAACiF,GAAG,CAAC,CAAC;MACrB,IAAI,CAACP,YAAY,CAACO,GAAG,CAAC,CAAC;MACvB,MAAMC,OAAO,GAAG,IAAI,CAAClF,UAAU,CAACnL,MAAM,GAAG,CAAC,GAAG,IAAI,CAACmL,UAAU,CAAC,IAAI,CAACA,UAAU,CAACnL,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;MAC7F,IAAI,CAAC8P,gBAAgB,CAACO,OAAO,CAAC;IAClC;EACJ;EACAtL,OAAOA,CAAA,EAAG;IACN,MAAM,iBAAiB;EAC3B;EACAN,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoL,YAAY,CAAC,CAAC,IAAI,CAACA,YAAY,CAAC7P,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;EACjE;EACA;IAAS,IAAI,CAACrB,IAAI,YAAA2R,6BAAAzR,iBAAA;MAAA,YAAAA,iBAAA,IAAyF4Q,oBAAoB;IAAA,CAAoD;EAAE;EACrL;IAAS,IAAI,CAAC3Q,KAAK,kBAtrC8EZ,EAAE,CAAAa,kBAAA;MAAAC,KAAA,EAsrCYyQ,oBAAoB;MAAAxQ,OAAA,EAApBwQ,oBAAoB,CAAA9Q;IAAA,EAAG;EAAE;AAC5I;AACA;EAAA,QAAAU,SAAA,oBAAAA,SAAA,KAxrCqGnB,EAAE,CAAAoB,iBAAA,CAwrCXmQ,oBAAoB,EAAc,CAAC;IACnHlQ,IAAI,EAAEpB;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC,MAAM4R,kBAAkB,CAAC;EACrB9P,WAAWA,CAAC+L,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACoE,GAAG,GAAG,IAAI;IACf,IAAI,CAAC7Q,IAAI,GAAG,UAAU;EAC1B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgR,oBAAoBA,CAAA,EAAG;EAC5B,OAAO,CACH;IAAEpD,OAAO,EAAElP,QAAQ;IAAEmP,QAAQ,EAAEC;EAAY,CAAC,EAC5C;IAAEF,OAAO,EAAEnP,gBAAgB;IAAEoP,QAAQ,EAAEqC;EAAqB,CAAC,CAChE;AACL;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS5E,6BAA6B,EAAE4E,oBAAoB,EAAE3E,oBAAoB,EAAEuC,WAAW,EAAEkD,oBAAoB,EAAErD,6BAA6B,IAAIsD,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}