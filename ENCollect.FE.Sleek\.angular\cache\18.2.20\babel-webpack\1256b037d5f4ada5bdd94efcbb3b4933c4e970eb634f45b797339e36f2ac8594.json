{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { LevelShiftButtonComponent } from './level-shift-button.component';\ndescribe('LevelShiftButtonComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [LevelShiftButtonComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(LevelShiftButtonComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "LevelShiftButtonComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\level-shift-button\\level-shift-button.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { LevelShiftButtonComponent } from './level-shift-button.component';\r\n\r\ndescribe('LevelShiftButtonComponent', () => {\r\n  let component: LevelShiftButtonComponent;\r\n  let fixture: ComponentFixture<LevelShiftButtonComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [LevelShiftButtonComponent]\r\n    })\r\n    .compileComponents();\r\n    \r\n    fixture = TestBed.createComponent(LevelShiftButtonComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,yBAAyB,QAAQ,gCAAgC;AAE1EC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EAExDC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,yBAAyB;KACpC,CAAC,CACDQ,iBAAiB,EAAE;IAEpBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,yBAAyB,CAAC;IAC5DE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}