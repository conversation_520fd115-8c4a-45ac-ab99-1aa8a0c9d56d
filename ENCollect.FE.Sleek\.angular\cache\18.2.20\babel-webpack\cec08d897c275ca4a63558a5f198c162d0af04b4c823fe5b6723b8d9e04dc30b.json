{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./acm-config.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./acm-config.component.css?ngResource\";\nimport { Component } from \"@angular/core\";\nimport { SettingsService } from \"../settings.service\";\nimport { FormBuilder, Validators } from \"@angular/forms\";\nimport { ActivatedRoute } from \"@angular/router\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { scrollToTarget } from \"src/app/shared/utils/scrollToTarget\";\nlet AcmConfigComponent = class AcmConfigComponent {\n  constructor(settingsService, route, fb, toastr) {\n    this.settingsService = settingsService;\n    this.route = route;\n    this.fb = fb;\n    this.toastr = toastr;\n    this.breadcrumbData = [{\n      label: \"Settings\",\n      path: \"/settings/define-web-acm\"\n    }, {\n      label: \"Define Access Control Matrix\",\n      path: \"/settings/define-web-acm\"\n    }];\n    this.acmData = null;\n    this.departmentList = [];\n    this.designationList = [];\n  }\n  ngOnInit() {\n    this.getDepartments();\n    this.buildForm();\n  }\n  // Build Formgroup\n  buildForm() {\n    this.formGroup = this.fb.group({\n      department: [null, [Validators.required]],\n      designation: [null, [Validators.required]],\n      accountability: [null, [Validators.required]],\n      isMobile: [this.isMobile]\n    });\n    // this.formGroup.get(\"accountability\").disable();\n  }\n  // Form Value\n  get fValue() {\n    return this.formGroup.value;\n  }\n  // Is Mobile\n  get isMobile() {\n    return this.route.snapshot.data[\"isMobile\"] ? true : false;\n  }\n  // Get Departments\n  getDepartments() {\n    this.settingsService.getDepartmentsDD().subscribe(res => {\n      this.departmentList = res;\n    });\n  }\n  // Get Designations By Department\n  getDesignations() {\n    this.designationList = [];\n    this.formGroup.patchValue({\n      designation: null,\n      accountability: null\n    });\n    this.settingsService.getDesignationsDD(this.fValue.department).subscribe(res => {\n      this.designationList = res;\n    });\n  }\n  // Get Accountabilities\n  getAccountability() {\n    this.formGroup.patchValue({\n      accountability: null\n    });\n    this.settingsService.getAccountability(this.fValue.department, this.fValue.designation).subscribe(res => {\n      this.formGroup.patchValue({\n        accountability: res?.accountability\n      });\n    }, err => {\n      if (typeof err === \"string\") {\n        this.toastr.error(err);\n      }\n      if (err instanceof Array) {\n        err.forEach(e => this.toastr.error(e));\n      } else {\n        this.toastr.error(\"Something went wrong, please try after some time.\");\n      }\n    });\n  }\n  // On Search\n  onSearchACMData() {\n    this.settingsService.getACMData(this.fValue.accountability, this.fValue.isMobile).subscribe(res => {\n      this.acmData = this.groupAcmApiResponse(res || []);\n      scrollToTarget('.inner-layout-container', '#scroll-target');\n    }, err => {\n      if (typeof err === \"string\") {\n        this.toastr.error(err);\n      }\n      if (err instanceof Array) {\n        err.forEach(e => this.toastr.error(e));\n      } else {\n        this.toastr.error(\"Something went wrong, please try after some time.\");\n      }\n    });\n  }\n  // On Update ACM Data\n  onUpdateACMData() {\n    const acm = this.unGroupACMApiRequest(this.acmData);\n    this.settingsService.updateACMData(this.fValue.accountability, acm).subscribe(res => {\n      this.toastr.success(\"Access Control has been updated successfully.\");\n      this.acmData = [];\n      this.formGroup.reset({\n        isMobile: this.isMobile\n      });\n    }, err => {\n      if (typeof err === \"string\") {\n        this.toastr.error(err);\n      }\n      if (err instanceof Array) {\n        err.forEach(e => this.toastr.error(e));\n      } else {\n        this.toastr.error(\"Something went wrong, please try after some time.\");\n      }\n    });\n  }\n  // Group ACM API Response\n  groupAcmApiResponse(apiAcm) {\n    const groupBy = (array, groupKey, listKey) => {\n      return array.reduce((result, item) => {\n        const findGroup = result.find(o => o[groupKey] === item[groupKey]);\n        if (findGroup) {\n          delete item[groupKey];\n          findGroup[`${listKey}s`].push(item);\n        } else {\n          const resultData = {};\n          resultData[groupKey] = item[groupKey];\n          delete item[groupKey];\n          resultData[`${listKey}s`] = [item];\n          result.push(resultData);\n        }\n        return result;\n      }, []);\n    };\n    const menuData = groupBy(apiAcm, \"menu\", \"subMenu\");\n    const menus = menuData.reduce((_menu, menu) => {\n      const subMenuData = groupBy(menu.subMenus, \"subMenu\", \"scope\");\n      const menuData = {\n        menu: menu.menu,\n        subMenus: subMenuData\n      };\n      _menu.push(menuData);\n      return _menu;\n    }, []);\n    return menus;\n  }\n  // Un Group ACM Submit Api Request\n  unGroupACMApiRequest(acmGroup) {\n    const acm = acmGroup.reduce((_menu, menu) => {\n      const acmSub = menu.subMenus.reduce((_sub, sub) => {\n        const acmScope = sub.scopes.reduce((_sco, sco) => {\n          _sco.push({\n            menu: menu.menu,\n            subMenu: sub.subMenu,\n            ...sco\n          });\n          return _sco;\n        }, []);\n        _sub = _sub.concat(acmScope);\n        return _sub;\n      }, []);\n      _menu = _menu.concat(acmSub);\n      return _menu;\n    }, []);\n    return acm;\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: SettingsService\n    }, {\n      type: ActivatedRoute\n    }, {\n      type: FormBuilder\n    }, {\n      type: ToastrService\n    }];\n  }\n};\nAcmConfigComponent = __decorate([Component({\n  selector: \"app-acm-config\",\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AcmConfigComponent);\nexport { AcmConfigComponent };", "map": {"version": 3, "names": ["Component", "SettingsService", "FormBuilder", "Validators", "ActivatedRoute", "ToastrService", "scrollToTarget", "AcmConfigComponent", "constructor", "settingsService", "route", "fb", "toastr", "breadcrumbData", "label", "path", "acmData", "departmentList", "designationList", "ngOnInit", "getDepartments", "buildForm", "formGroup", "group", "department", "required", "designation", "accountability", "isMobile", "fValue", "value", "snapshot", "data", "getDepartmentsDD", "subscribe", "res", "getDesignations", "patchValue", "getDesignationsDD", "getAccountability", "err", "error", "Array", "for<PERSON>ach", "e", "onSearchACMData", "getACMData", "groupAcmApiResponse", "onUpdateACMData", "acm", "unGroupACMApiRequest", "updateACMData", "success", "reset", "apiAcm", "groupBy", "array", "groupKey", "<PERSON><PERSON><PERSON>", "reduce", "result", "item", "findGroup", "find", "o", "push", "resultData", "menuData", "menus", "_menu", "menu", "subMenuData", "subMenus", "acmGroup", "acmSub", "_sub", "sub", "acmScope", "scopes", "_sco", "sco", "subMenu", "concat", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\acm-config\\acm-config.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\";\r\nimport { SettingsService } from \"../settings.service\";\r\nimport { FormBuilder, FormGroup, Validators } from \"@angular/forms\";\r\nimport { ActivatedRoute } from \"@angular/router\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { scrollToTarget } from \"src/app/shared/utils/scrollToTarget\";\r\n\r\n@Component({\r\n  selector: \"app-acm-config\",\r\n  templateUrl: \"./acm-config.component.html\",\r\n  styleUrls: [\"./acm-config.component.css\"],\r\n})\r\nexport class AcmConfigComponent implements OnInit {\r\n  public breadcrumbData = [\r\n    { label: \"Settings\", path: \"/settings/define-web-acm\" },\r\n    { label: \"Define Access Control Matrix\", path: \"/settings/define-web-acm\" },\r\n  ];\r\n  public formGroup: FormGroup;\r\n  public acmData: any = null;\r\n  public departmentList: any[] = [];\r\n  public designationList: any[] = [];\r\n\r\n  constructor(\r\n    private settingsService: SettingsService,\r\n    private route: ActivatedRoute,\r\n    private fb: FormBuilder,\r\n    private toastr: ToastrService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.getDepartments();\r\n    this.buildForm();\r\n  }\r\n\r\n  // Build Formgroup\r\n  buildForm() {\r\n    this.formGroup = this.fb.group({\r\n      department: [null, [Validators.required]],\r\n      designation: [null, [Validators.required]],\r\n      accountability: [null, [Validators.required]],\r\n      isMobile: [this.isMobile],\r\n    });\r\n    // this.formGroup.get(\"accountability\").disable();\r\n  }\r\n\r\n  // Form Value\r\n  get fValue(): any {\r\n    return this.formGroup.value;\r\n  }\r\n\r\n  // Is Mobile\r\n  get isMobile(): boolean {\r\n    return this.route.snapshot.data[\"isMobile\"] ? true : false;\r\n  }\r\n\r\n  // Get Departments\r\n  getDepartments() {\r\n    this.settingsService.getDepartmentsDD().subscribe((res: any) => {\r\n      this.departmentList = res;\r\n    });\r\n  }\r\n\r\n  // Get Designations By Department\r\n  getDesignations() {\r\n    this.designationList = [];\r\n    this.formGroup.patchValue({\r\n      designation: null,\r\n      accountability: null,\r\n    });\r\n    this.settingsService\r\n      .getDesignationsDD(this.fValue.department)\r\n      .subscribe((res: any) => {\r\n        this.designationList = res;\r\n      });\r\n  }\r\n\r\n  // Get Accountabilities\r\n  getAccountability() {\r\n    this.formGroup.patchValue({\r\n      accountability: null,\r\n    });\r\n    this.settingsService\r\n      .getAccountability(this.fValue.department, this.fValue.designation)\r\n      .subscribe(\r\n        (res: any) => {\r\n          this.formGroup.patchValue({ accountability: res?.accountability });\r\n        },\r\n        (err: any) => {\r\n          if (typeof err === \"string\") {\r\n            this.toastr.error(err);\r\n          }\r\n          if (err instanceof Array) {\r\n            err.forEach((e) => this.toastr.error(e));\r\n          } else {\r\n            this.toastr.error(\r\n              \"Something went wrong, please try after some time.\"\r\n            );\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  // On Search\r\n  onSearchACMData() {\r\n    this.settingsService\r\n      .getACMData(this.fValue.accountability, this.fValue.isMobile)\r\n      .subscribe(\r\n        (res: any) => {\r\n          this.acmData = this.groupAcmApiResponse(res || []);\r\n          scrollToTarget('.inner-layout-container', '#scroll-target');\r\n        },\r\n        (err: any) => {\r\n          if (typeof err === \"string\") {\r\n            this.toastr.error(err);\r\n          }\r\n          if (err instanceof Array) {\r\n            err.forEach((e) => this.toastr.error(e));\r\n          } else {\r\n            this.toastr.error(\r\n              \"Something went wrong, please try after some time.\"\r\n            );\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  // On Update ACM Data\r\n  onUpdateACMData() {\r\n    const acm = this.unGroupACMApiRequest(this.acmData);\r\n    this.settingsService\r\n      .updateACMData(this.fValue.accountability, acm)\r\n      .subscribe(\r\n        (res: any) => {\r\n          this.toastr.success(\"Access Control has been updated successfully.\");\r\n          this.acmData = [];\r\n          this.formGroup.reset({ isMobile: this.isMobile });\r\n        },\r\n        (err: any) => {\r\n          if (typeof err === \"string\") {\r\n            this.toastr.error(err);\r\n          }\r\n          if (err instanceof Array) {\r\n            err.forEach((e) => this.toastr.error(e));\r\n          } else {\r\n            this.toastr.error(\r\n              \"Something went wrong, please try after some time.\"\r\n            );\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  // Group ACM API Response\r\n  groupAcmApiResponse(apiAcm: any[]): any[] {\r\n    const groupBy = (array, groupKey, listKey) => {\r\n      return array.reduce((result, item) => {\r\n        const findGroup = result.find((o) => o[groupKey] === item[groupKey]);\r\n        if (findGroup) {\r\n          delete item[groupKey];\r\n          findGroup[`${listKey}s`].push(item);\r\n        } else {\r\n          const resultData = {};\r\n          resultData[groupKey] = item[groupKey];\r\n          delete item[groupKey];\r\n          resultData[`${listKey}s`] = [item];\r\n          result.push(resultData);\r\n        }\r\n        return result;\r\n      }, []);\r\n    };\r\n\r\n    const menuData = groupBy(apiAcm, \"menu\", \"subMenu\");\r\n\r\n    const menus = menuData.reduce((_menu, menu) => {\r\n      const subMenuData = groupBy(menu.subMenus, \"subMenu\", \"scope\");\r\n      const menuData = {\r\n        menu: menu.menu,\r\n        subMenus: subMenuData,\r\n      };\r\n      _menu.push(menuData);\r\n      return _menu;\r\n    }, []);\r\n    return menus;\r\n  }\r\n\r\n  // Un Group ACM Submit Api Request\r\n  unGroupACMApiRequest(acmGroup: any[]): any[] {\r\n    const acm = acmGroup.reduce((_menu, menu) => {\r\n      const acmSub = menu.subMenus.reduce((_sub, sub) => {\r\n        const acmScope = sub.scopes.reduce((_sco, sco) => {\r\n          _sco.push({\r\n            menu: menu.menu,\r\n            subMenu: sub.subMenu,\r\n            ...sco,\r\n          });\r\n          return _sco;\r\n        }, []);\r\n        _sub = _sub.concat(acmScope);\r\n        return _sub;\r\n      }, []);\r\n      _menu = _menu.concat(acmSub);\r\n      return _menu;\r\n    }, []);\r\n    return acm;\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAQ,qCAAqC;AAO7D,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAU7BC,YACUC,eAAgC,EAChCC,KAAqB,EACrBC,EAAe,EACfC,MAAqB;IAHrB,KAAAH,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IAbT,KAAAC,cAAc,GAAG,CACtB;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAA0B,CAAE,EACvD;MAAED,KAAK,EAAE,8BAA8B;MAAEC,IAAI,EAAE;IAA0B,CAAE,CAC5E;IAEM,KAAAC,OAAO,GAAQ,IAAI;IACnB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,eAAe,GAAU,EAAE;EAO/B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;EACAA,SAASA,CAAA;IACP,IAAI,CAACC,SAAS,GAAG,IAAI,CAACX,EAAE,CAACY,KAAK,CAAC;MAC7BC,UAAU,EAAE,CAAC,IAAI,EAAE,CAACrB,UAAU,CAACsB,QAAQ,CAAC,CAAC;MACzCC,WAAW,EAAE,CAAC,IAAI,EAAE,CAACvB,UAAU,CAACsB,QAAQ,CAAC,CAAC;MAC1CE,cAAc,EAAE,CAAC,IAAI,EAAE,CAACxB,UAAU,CAACsB,QAAQ,CAAC,CAAC;MAC7CG,QAAQ,EAAE,CAAC,IAAI,CAACA,QAAQ;KACzB,CAAC;IACF;EACF;EAEA;EACA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACP,SAAS,CAACQ,KAAK;EAC7B;EAEA;EACA,IAAIF,QAAQA,CAAA;IACV,OAAO,IAAI,CAAClB,KAAK,CAACqB,QAAQ,CAACC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK;EAC5D;EAEA;EACAZ,cAAcA,CAAA;IACZ,IAAI,CAACX,eAAe,CAACwB,gBAAgB,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;MAC7D,IAAI,CAAClB,cAAc,GAAGkB,GAAG;IAC3B,CAAC,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,IAAI,CAAClB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACI,SAAS,CAACe,UAAU,CAAC;MACxBX,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE;KACjB,CAAC;IACF,IAAI,CAAClB,eAAe,CACjB6B,iBAAiB,CAAC,IAAI,CAACT,MAAM,CAACL,UAAU,CAAC,CACzCU,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAI,CAACjB,eAAe,GAAGiB,GAAG;IAC5B,CAAC,CAAC;EACN;EAEA;EACAI,iBAAiBA,CAAA;IACf,IAAI,CAACjB,SAAS,CAACe,UAAU,CAAC;MACxBV,cAAc,EAAE;KACjB,CAAC;IACF,IAAI,CAAClB,eAAe,CACjB8B,iBAAiB,CAAC,IAAI,CAACV,MAAM,CAACL,UAAU,EAAE,IAAI,CAACK,MAAM,CAACH,WAAW,CAAC,CAClEQ,SAAS,CACPC,GAAQ,IAAI;MACX,IAAI,CAACb,SAAS,CAACe,UAAU,CAAC;QAAEV,cAAc,EAAEQ,GAAG,EAAER;MAAc,CAAE,CAAC;IACpE,CAAC,EACAa,GAAQ,IAAI;MACX,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAC3B,IAAI,CAAC5B,MAAM,CAAC6B,KAAK,CAACD,GAAG,CAAC;MACxB;MACA,IAAIA,GAAG,YAAYE,KAAK,EAAE;QACxBF,GAAG,CAACG,OAAO,CAAEC,CAAC,IAAK,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CAACG,CAAC,CAAC,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CACf,mDAAmD,CACpD;MACH;IACF,CAAC,CACF;EACL;EAEA;EACAI,eAAeA,CAAA;IACb,IAAI,CAACpC,eAAe,CACjBqC,UAAU,CAAC,IAAI,CAACjB,MAAM,CAACF,cAAc,EAAE,IAAI,CAACE,MAAM,CAACD,QAAQ,CAAC,CAC5DM,SAAS,CACPC,GAAQ,IAAI;MACX,IAAI,CAACnB,OAAO,GAAG,IAAI,CAAC+B,mBAAmB,CAACZ,GAAG,IAAI,EAAE,CAAC;MAClD7B,cAAc,CAAC,yBAAyB,EAAE,gBAAgB,CAAC;IAC7D,CAAC,EACAkC,GAAQ,IAAI;MACX,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAC3B,IAAI,CAAC5B,MAAM,CAAC6B,KAAK,CAACD,GAAG,CAAC;MACxB;MACA,IAAIA,GAAG,YAAYE,KAAK,EAAE;QACxBF,GAAG,CAACG,OAAO,CAAEC,CAAC,IAAK,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CAACG,CAAC,CAAC,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CACf,mDAAmD,CACpD;MACH;IACF,CAAC,CACF;EACL;EAEA;EACAO,eAAeA,CAAA;IACb,MAAMC,GAAG,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAClC,OAAO,CAAC;IACnD,IAAI,CAACP,eAAe,CACjB0C,aAAa,CAAC,IAAI,CAACtB,MAAM,CAACF,cAAc,EAAEsB,GAAG,CAAC,CAC9Cf,SAAS,CACPC,GAAQ,IAAI;MACX,IAAI,CAACvB,MAAM,CAACwC,OAAO,CAAC,+CAA+C,CAAC;MACpE,IAAI,CAACpC,OAAO,GAAG,EAAE;MACjB,IAAI,CAACM,SAAS,CAAC+B,KAAK,CAAC;QAAEzB,QAAQ,EAAE,IAAI,CAACA;MAAQ,CAAE,CAAC;IACnD,CAAC,EACAY,GAAQ,IAAI;MACX,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAC3B,IAAI,CAAC5B,MAAM,CAAC6B,KAAK,CAACD,GAAG,CAAC;MACxB;MACA,IAAIA,GAAG,YAAYE,KAAK,EAAE;QACxBF,GAAG,CAACG,OAAO,CAAEC,CAAC,IAAK,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CAACG,CAAC,CAAC,CAAC;MAC1C,CAAC,MAAM;QACL,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CACf,mDAAmD,CACpD;MACH;IACF,CAAC,CACF;EACL;EAEA;EACAM,mBAAmBA,CAACO,MAAa;IAC/B,MAAMC,OAAO,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,KAAI;MAC3C,OAAOF,KAAK,CAACG,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAI;QACnC,MAAMC,SAAS,GAAGF,MAAM,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACP,QAAQ,CAAC,KAAKI,IAAI,CAACJ,QAAQ,CAAC,CAAC;QACpE,IAAIK,SAAS,EAAE;UACb,OAAOD,IAAI,CAACJ,QAAQ,CAAC;UACrBK,SAAS,CAAC,GAAGJ,OAAO,GAAG,CAAC,CAACO,IAAI,CAACJ,IAAI,CAAC;QACrC,CAAC,MAAM;UACL,MAAMK,UAAU,GAAG,EAAE;UACrBA,UAAU,CAACT,QAAQ,CAAC,GAAGI,IAAI,CAACJ,QAAQ,CAAC;UACrC,OAAOI,IAAI,CAACJ,QAAQ,CAAC;UACrBS,UAAU,CAAC,GAAGR,OAAO,GAAG,CAAC,GAAG,CAACG,IAAI,CAAC;UAClCD,MAAM,CAACK,IAAI,CAACC,UAAU,CAAC;QACzB;QACA,OAAON,MAAM;MACf,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IAED,MAAMO,QAAQ,GAAGZ,OAAO,CAACD,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;IAEnD,MAAMc,KAAK,GAAGD,QAAQ,CAACR,MAAM,CAAC,CAACU,KAAK,EAAEC,IAAI,KAAI;MAC5C,MAAMC,WAAW,GAAGhB,OAAO,CAACe,IAAI,CAACE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;MAC9D,MAAML,QAAQ,GAAG;QACfG,IAAI,EAAEA,IAAI,CAACA,IAAI;QACfE,QAAQ,EAAED;OACX;MACDF,KAAK,CAACJ,IAAI,CAACE,QAAQ,CAAC;MACpB,OAAOE,KAAK;IACd,CAAC,EAAE,EAAE,CAAC;IACN,OAAOD,KAAK;EACd;EAEA;EACAlB,oBAAoBA,CAACuB,QAAe;IAClC,MAAMxB,GAAG,GAAGwB,QAAQ,CAACd,MAAM,CAAC,CAACU,KAAK,EAAEC,IAAI,KAAI;MAC1C,MAAMI,MAAM,GAAGJ,IAAI,CAACE,QAAQ,CAACb,MAAM,CAAC,CAACgB,IAAI,EAAEC,GAAG,KAAI;QAChD,MAAMC,QAAQ,GAAGD,GAAG,CAACE,MAAM,CAACnB,MAAM,CAAC,CAACoB,IAAI,EAAEC,GAAG,KAAI;UAC/CD,IAAI,CAACd,IAAI,CAAC;YACRK,IAAI,EAAEA,IAAI,CAACA,IAAI;YACfW,OAAO,EAAEL,GAAG,CAACK,OAAO;YACpB,GAAGD;WACJ,CAAC;UACF,OAAOD,IAAI;QACb,CAAC,EAAE,EAAE,CAAC;QACNJ,IAAI,GAAGA,IAAI,CAACO,MAAM,CAACL,QAAQ,CAAC;QAC5B,OAAOF,IAAI;MACb,CAAC,EAAE,EAAE,CAAC;MACNN,KAAK,GAAGA,KAAK,CAACa,MAAM,CAACR,MAAM,CAAC;MAC5B,OAAOL,KAAK;IACd,CAAC,EAAE,EAAE,CAAC;IACN,OAAOpB,GAAG;EACZ;;;;;;;;;;;;;AAhMW1C,kBAAkB,GAAA4E,UAAA,EAL9BnF,SAAS,CAAC;EACToF,QAAQ,EAAE,gBAAgB;EAC1BC,QAAA,EAAAC,oBAA0C;;CAE3C,CAAC,C,EACW/E,kBAAkB,CAiM9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}