{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./upload-bank-master.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./upload-bank-master.component.css?ngResource\";\nexport class UploadControls {}\nimport { Component, ViewChild } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { JwtService } from '../../../authentication/jwt.service';\nimport { SettingsService } from '../../settings.service';\nimport { SettingsConfigService } from '../../settingsconfig.service';\nimport { Router } from '@angular/router';\nlet UploadBankMasterComponent = class UploadBankMasterComponent {\n  constructor(jwtService, toastr, modalService, settingsService, router, settingsConfigService) {\n    this.jwtService = jwtService;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.settingsService = settingsService;\n    this.router = router;\n    this.settingsConfigService = settingsConfigService;\n    this.breadcrumbData = [{\n      label: \"Bank Config\",\n      path: \"/settings/upload-bank-master\"\n    }, {\n      label: \"Upload Bank Master\",\n      path: \"/settings/upload-bank-master\"\n    }];\n    this.uploadControls = new UploadControls();\n    this.uploadTemp = false;\n  }\n  ngOnInit() {\n    // Set the user name \n    this.currentUserData = this.jwtService.getUser();\n    this.varInit();\n  }\n  varInit() {\n    this.uploadControls = {\n      template: '',\n      allocationType: false,\n      fileName: ''\n    };\n    this.loader = {\n      isDownload: false,\n      isSubmit: false,\n      product: false,\n      subproduct: false\n    };\n  }\n  fileUploadConfirmation(event, confirmation) {\n    this.fileList = event.target.files;\n    var file_extension = this.fileList[0].name.split('.').pop();\n    this.uploadControls.fileName = this.fileList[0].name;\n    if (file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension != \"XLS\") {\n      this.fileList = [];\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.info('You can only upload the file with extension xls or xlsx');\n      return;\n    } else {\n      let config = {\n        ignoreBackdropClick: true\n      };\n      this.modalRef = this.modalService.show(confirmation, config);\n    }\n  }\n  fIleUpload() {\n    this.modalRef?.hide();\n    this.uploadTemp = true;\n    const file = this.fileList[0];\n    const fd = new FormData();\n    fd.append('file', file);\n    this.settingsService.uploadFile(fd).subscribe(data => {\n      this.bankMasterAllocation(data);\n    }, err => {\n      this.uploadTemp = false;\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.error(err);\n    });\n  }\n  bankMasterAllocation(data) {\n    let inputParams = {\n      \"AllocationFileName\": data[\"name\"],\n      \"FileType\": \"bankmaster\"\n    };\n    this.settingsService.bankBulkupload(inputParams).subscribe(data => {\n      setTimeout(() => {\n        this.fileList = [];\n        this.uploadControls = new UploadControls();\n        this.uploadTemp = false;\n        this.fileUploader.nativeElement.value = null;\n        this.toastr.success(\"File Uploaded Successfully. Transaction ID : \" + data.transactionId);\n        this.router.navigateByUrl('/', {\n          skipLocationChange: true\n        }).then(() => this.router.navigate(['settings/upload-bank-master']));\n      }, 3000);\n    }, err => {\n      this.uploadTemp = false;\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.error(err);\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: JwtService\n    }, {\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: SettingsService\n    }, {\n      type: Router\n    }, {\n      type: SettingsConfigService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      fileUploader: [{\n        type: ViewChild,\n        args: ['fileUploader']\n      }]\n    };\n  }\n};\nUploadBankMasterComponent = __decorate([Component({\n  selector: 'app-upload-bank-master',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], UploadBankMasterComponent);\nexport { UploadBankMasterComponent };", "map": {"version": 3, "names": ["UploadControls", "Component", "ViewChild", "ToastrService", "BsModalService", "JwtService", "SettingsService", "SettingsConfigService", "Router", "UploadBankMasterComponent", "constructor", "jwtService", "toastr", "modalService", "settingsService", "router", "settingsConfigService", "breadcrumbData", "label", "path", "uploadControls", "uploadTemp", "ngOnInit", "currentUserData", "getUser", "varInit", "template", "allocationType", "fileName", "loader", "isDownload", "isSubmit", "product", "subproduct", "fileUploadConfirmation", "event", "confirmation", "fileList", "target", "files", "file_extension", "name", "split", "pop", "fileUploader", "nativeElement", "value", "info", "config", "ignoreBackdropClick", "modalRef", "show", "fIleUpload", "hide", "file", "fd", "FormData", "append", "uploadFile", "subscribe", "data", "bankMasterAllocation", "err", "error", "inputParams", "bankBulkupload", "setTimeout", "success", "transactionId", "navigateByUrl", "skipLocationChange", "then", "navigate", "args", "__decorate", "selector", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\bank-config\\upload-bank-master\\upload-bank-master.component.ts"], "sourcesContent": ["export class UploadControls{\r\n   template: string;\r\n   allocationType: boolean;\r\n   fileName: string;\r\n}\r\nexport interface Loader{\r\n    isDownload: boolean;\r\n    isSubmit: boolean;\r\n    product: boolean;\r\n    subproduct: boolean;\r\n}\r\nimport { Component, OnInit,ViewChild,Output,TemplateRef, ElementRef } from '@angular/core';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\nimport { JwtService } from '../../../authentication/jwt.service';\r\n\r\nimport { SettingsService } from '../../settings.service';\r\nimport { SettingsConfigService } from '../../settingsconfig.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-upload-bank-master',\r\n  templateUrl: './upload-bank-master.component.html',\r\n  styleUrls: ['./upload-bank-master.component.css']\r\n})\r\nexport class UploadBankMasterComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Bank Config\", path: \"/settings/upload-bank-master\" },\r\n\t\t{ label: \"Upload Bank Master\", path: \"/settings/upload-bank-master\" },\r\n\t  ]\r\n  @ViewChild('fileUploader') fileUploader:ElementRef;\r\n  uploadControls: UploadControls = new UploadControls();\r\n  loader: Loader;\r\n  fileList: any;\r\n  modalRef: BsModalRef;\r\n  attachedFile:any;\r\n  currentUserData: any;\r\n  uploadTemp = false\r\n  constructor(private jwtService: JwtService,\r\n  \t  \t\t  public toastr: ToastrService, \r\n              private modalService: BsModalService,\r\n  \t\t\t  private settingsService: SettingsService,private router: Router,\r\n  \t\t      private settingsConfigService: SettingsConfigService) { }\r\n  ngOnInit() {\r\n     // Set the user name \r\n  \t this.currentUserData =  this.jwtService.getUser()\r\n     this.varInit()\r\n  }\r\n\r\n  varInit(){\r\n    this.uploadControls = {\r\n       template: '',\r\n       allocationType: false,\r\n       fileName: ''\r\n    }\r\n    this.loader = {\r\n      isDownload: false,\r\n      isSubmit: false,\r\n      product: false,\r\n      subproduct: false,\r\n    }\r\n }\r\n\r\n fileUploadConfirmation(event,confirmation: TemplateRef<any>) {\r\n      this.fileList = event.target.files;\r\n      var file_extension =  this.fileList[0].name.split('.').pop();\r\n      this.uploadControls.fileName = this.fileList[0].name\r\n      if(file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension !=\"XLS\" ){\r\n        this.fileList=[]\r\n        this.fileUploader.nativeElement.value = null;\r\n        this.toastr.info('You can only upload the file with extension xls or xlsx');\r\n        return;\r\n      }else {\r\n        let config = {\r\n          ignoreBackdropClick: true,\r\n        };\r\n        this.modalRef = this.modalService.show(confirmation,config);\r\n      }\r\n  }\r\n\r\n  fIleUpload() {\r\n    this.modalRef?.hide()\r\n    this.uploadTemp = true\r\n    const file: File = this.fileList[0];\r\n    const fd: FormData = new FormData();\r\n    fd.append('file', file);\r\n    this.settingsService.uploadFile(fd).subscribe(data => {\r\n        this.bankMasterAllocation(data);\r\n    }, err=>{\r\n      this.uploadTemp = false\r\n      this.fileUploader.nativeElement.value = null;\r\n      this.toastr.error(err)\r\n    });\r\n  }\r\n\r\n  bankMasterAllocation(data){\r\n      let inputParams = {\r\n\t      \"AllocationFileName\": data[\"name\"],\r\n\t      \"FileType\": \"bankmaster\"\r\n\t  }\r\n    this.settingsService.bankBulkupload(inputParams)\r\n      .subscribe(data => {\r\n        setTimeout(()=>{ \r\n          this.fileList = []\r\n          this.uploadControls = new UploadControls();\r\n          this.uploadTemp = false\r\n          this.fileUploader.nativeElement.value = null;\r\n          this.toastr.success(\"File Uploaded Successfully. Transaction ID : \"+ data.transactionId);\r\n          this.router.navigateByUrl('/', {skipLocationChange: true}).then(()=>\r\n          this.router.navigate(['settings/upload-bank-master']));\r\n         }, 3000);\r\n     }, err=>{\r\n      this.uploadTemp = false\r\n      this.fileUploader.nativeElement.value = null;\r\n      this.toastr.error(err)\r\n    });\r\n   }\r\n}\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;AAW3B,SAASC,SAAS,EAASC,SAAS,QAAuC,eAAe;AAE1F,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAoB,qBAAqB;AAEhE,SAASC,UAAU,QAAQ,qCAAqC;AAEhE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,MAAM,QAAQ,iBAAiB;AAOjC,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAapCC,YAAoBC,UAAsB,EAC5BC,MAAqB,EACfC,YAA4B,EACnCC,eAAgC,EAASC,MAAc,EACpDC,qBAA4C;IAJxC,KAAAL,UAAU,GAAVA,UAAU;IAChB,KAAAC,MAAM,GAANA,MAAM;IACA,KAAAC,YAAY,GAAZA,YAAY;IACnB,KAAAC,eAAe,GAAfA,eAAe;IAA0B,KAAAC,MAAM,GAANA,MAAM;IAC5C,KAAAC,qBAAqB,GAArBA,qBAAqB;IAhB9B,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAA8B,CAAE,EAC9D;MAAED,KAAK,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAA8B,CAAE,CACnE;IAEF,KAAAC,cAAc,GAAmB,IAAIpB,cAAc,EAAE;IAMrD,KAAAqB,UAAU,GAAG,KAAK;EAK8C;EAChEC,QAAQA,CAAA;IACL;IACD,IAAI,CAACC,eAAe,GAAI,IAAI,CAACZ,UAAU,CAACa,OAAO,EAAE;IAChD,IAAI,CAACC,OAAO,EAAE;EACjB;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACL,cAAc,GAAG;MACnBM,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,KAAK;MACrBC,QAAQ,EAAE;KACZ;IACD,IAAI,CAACC,MAAM,GAAG;MACZC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;KACb;EACJ;EAEAC,sBAAsBA,CAACC,KAAK,EAACC,YAA8B;IACtD,IAAI,CAACC,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK;IAClC,IAAIC,cAAc,GAAI,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAC5D,IAAI,CAACvB,cAAc,CAACQ,QAAQ,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI;IACpD,IAAGD,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,KAAK,IAAIA,cAAc,IAAG,KAAK,EAAE;MAC5G,IAAI,CAACH,QAAQ,GAAC,EAAE;MAChB,IAAI,CAACO,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAAClC,MAAM,CAACmC,IAAI,CAAC,yDAAyD,CAAC;MAC3E;IACF,CAAC,MAAK;MACJ,IAAIC,MAAM,GAAG;QACXC,mBAAmB,EAAE;OACtB;MACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACrC,YAAY,CAACsC,IAAI,CAACf,YAAY,EAACY,MAAM,CAAC;IAC7D;EACJ;EAEAI,UAAUA,CAAA;IACR,IAAI,CAACF,QAAQ,EAAEG,IAAI,EAAE;IACrB,IAAI,CAAChC,UAAU,GAAG,IAAI;IACtB,MAAMiC,IAAI,GAAS,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAAC;IACnC,MAAMkB,EAAE,GAAa,IAAIC,QAAQ,EAAE;IACnCD,EAAE,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IACvB,IAAI,CAACxC,eAAe,CAAC4C,UAAU,CAACH,EAAE,CAAC,CAACI,SAAS,CAACC,IAAI,IAAG;MACjD,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;IACnC,CAAC,EAAEE,GAAG,IAAE;MACN,IAAI,CAACzC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACuB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAAClC,MAAM,CAACmD,KAAK,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC;EACJ;EAEAD,oBAAoBA,CAACD,IAAI;IACrB,IAAII,WAAW,GAAG;MACjB,oBAAoB,EAAEJ,IAAI,CAAC,MAAM,CAAC;MAClC,UAAU,EAAE;KACf;IACA,IAAI,CAAC9C,eAAe,CAACmD,cAAc,CAACD,WAAW,CAAC,CAC7CL,SAAS,CAACC,IAAI,IAAG;MAChBM,UAAU,CAAC,MAAI;QACb,IAAI,CAAC7B,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACjB,cAAc,GAAG,IAAIpB,cAAc,EAAE;QAC1C,IAAI,CAACqB,UAAU,GAAG,KAAK;QACvB,IAAI,CAACuB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;QAC5C,IAAI,CAAClC,MAAM,CAACuD,OAAO,CAAC,+CAA+C,GAAEP,IAAI,CAACQ,aAAa,CAAC;QACxF,IAAI,CAACrD,MAAM,CAACsD,aAAa,CAAC,GAAG,EAAE;UAACC,kBAAkB,EAAE;QAAI,CAAC,CAAC,CAACC,IAAI,CAAC,MAChE,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,EAAEV,GAAG,IAAE;MACP,IAAI,CAACzC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACuB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAAClC,MAAM,CAACmD,KAAK,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC;EACH;;;;;;;;;;;;;;;;;;;cAtFA5D,SAAS;QAAAuE,IAAA,GAAC,cAAc;MAAA;;;;AALdhE,yBAAyB,GAAAiE,UAAA,EALrCzE,SAAS,CAAC;EACT0E,QAAQ,EAAE,wBAAwB;EAClCjD,QAAA,EAAAkD,oBAAkD;;CAEnD,CAAC,C,EACWnE,yBAAyB,CA4FrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}