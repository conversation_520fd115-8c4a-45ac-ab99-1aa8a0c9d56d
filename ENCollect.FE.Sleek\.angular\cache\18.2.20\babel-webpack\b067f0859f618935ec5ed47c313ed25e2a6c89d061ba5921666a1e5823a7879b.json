{"ast": null, "code": "/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_INITIALIZER, createPlatformFactory, platformCore, APP_ID, ɵinternalProvideZoneChangeDetection, ɵChangeDetectionScheduler, ɵChangeDetectionSchedulerImpl, NgModule } from '@angular/core';\nimport { ɵBrowserDomAdapter, BrowserModule } from '@angular/platform-browser';\nfunction initBrowserTests() {\n  ɵBrowserDomAdapter.makeCurrent();\n}\nconst _TEST_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_INITIALIZER,\n  useValue: initBrowserTests,\n  multi: true\n}];\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n  static {\n    this.ɵfac = function BrowserTestingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BrowserTestingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserTestingModule,\n      exports: [BrowserModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: APP_ID,\n        useValue: 'a'\n      }, ɵinternalProvideZoneChangeDetection({}), {\n        provide: ɵChangeDetectionScheduler,\n        useExisting: ɵChangeDetectionSchedulerImpl\n      }, {\n        provide: PlatformLocation,\n        useClass: MockPlatformLocation\n      }],\n      imports: [BrowserModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: [{\n        provide: APP_ID,\n        useValue: 'a'\n      }, ɵinternalProvideZoneChangeDetection({}), {\n        provide: ɵChangeDetectionScheduler,\n        useExisting: ɵChangeDetectionSchedulerImpl\n      }, {\n        provide: PlatformLocation,\n        useClass: MockPlatformLocation\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserTestingModule, platformBrowserTesting };", "map": {"version": 3, "names": ["PlatformLocation", "MockPlatformLocation", "i0", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "APP_ID", "ɵinternalProvideZoneChangeDetection", "ɵChangeDetectionScheduler", "ɵChangeDetectionSchedulerImpl", "NgModule", "ɵBrowserDomAdapter", "BrowserModule", "initBrowserTests", "makeCurrent", "_TEST_BROWSER_PLATFORM_PROVIDERS", "provide", "useValue", "multi", "platformBrowserTesting", "BrowserTestingModule", "ɵfac", "BrowserTestingModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "exports", "ɵinj", "ɵɵdefineInjector", "providers", "useExisting", "useClass", "imports", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@angular/platform-browser/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_INITIALIZER, createPlatformFactory, platformCore, APP_ID, ɵinternalProvideZoneChangeDetection, ɵChangeDetectionScheduler, ɵChangeDetectionSchedulerImpl, NgModule } from '@angular/core';\nimport { ɵBrowserDomAdapter, BrowserModule } from '@angular/platform-browser';\n\nfunction initBrowserTests() {\n    ɵBrowserDomAdapter.makeCurrent();\n}\nconst _TEST_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_INITIALIZER, useValue: initBrowserTests, multi: true },\n];\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformCore, 'browserTesting', _TEST_BROWSER_PLATFORM_PROVIDERS);\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserTestingModule, exports: [BrowserModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserTestingModule, providers: [\n            { provide: APP_ID, useValue: 'a' },\n            ɵinternalProvideZoneChangeDetection({}),\n            { provide: ɵChangeDetectionScheduler, useExisting: ɵChangeDetectionSchedulerImpl },\n            { provide: PlatformLocation, useClass: MockPlatformLocation },\n        ], imports: [BrowserModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: [\n                        { provide: APP_ID, useValue: 'a' },\n                        ɵinternalProvideZoneChangeDetection({}),\n                        { provide: ɵChangeDetectionScheduler, useExisting: ɵChangeDetectionSchedulerImpl },\n                        { provide: PlatformLocation, useClass: MockPlatformLocation },\n                    ],\n                }]\n        }] });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserTestingModule, platformBrowserTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,mCAAmC,EAAEC,yBAAyB,EAAEC,6BAA6B,EAAEC,QAAQ,QAAQ,eAAe;AAC1M,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,2BAA2B;AAE7E,SAASC,gBAAgBA,CAAA,EAAG;EACxBF,kBAAkB,CAACG,WAAW,CAAC,CAAC;AACpC;AACA,MAAMC,gCAAgC,GAAG,CACrC;EAAEC,OAAO,EAAEb,oBAAoB;EAAEc,QAAQ,EAAEJ,gBAAgB;EAAEK,KAAK,EAAE;AAAK,CAAC,CAC7E;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGf,qBAAqB,CAACC,YAAY,EAAE,gBAAgB,EAAEU,gCAAgC,CAAC;AACtH;AACA;AACA;AACA;AACA;AACA,MAAMK,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACC,IAAI,YAAAC,6BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAyFH,oBAAoB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACI,IAAI,kBAD+EtB,EAAE,CAAAuB,gBAAA;MAAAC,IAAA,EACSN,oBAAoB;MAAAO,OAAA,GAAYf,aAAa;IAAA,EAAI;EAAE;EAC/J;IAAS,IAAI,CAACgB,IAAI,kBAF+E1B,EAAE,CAAA2B,gBAAA;MAAAC,SAAA,EAE0C,CACrI;QAAEd,OAAO,EAAEV,MAAM;QAAEW,QAAQ,EAAE;MAAI,CAAC,EAClCV,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvC;QAAES,OAAO,EAAER,yBAAyB;QAAEuB,WAAW,EAAEtB;MAA8B,CAAC,EAClF;QAAEO,OAAO,EAAEhB,gBAAgB;QAAEgC,QAAQ,EAAE/B;MAAqB,CAAC,CAChE;MAAAgC,OAAA,GAAYrB,aAAa;IAAA,EAAI;EAAE;AACxC;AACA;EAAA,QAAAsB,SAAA,oBAAAA,SAAA,KATqGhC,EAAE,CAAAiC,iBAAA,CASXf,oBAAoB,EAAc,CAAC;IACnHM,IAAI,EAAEhB,QAAQ;IACd0B,IAAI,EAAE,CAAC;MACCT,OAAO,EAAE,CAACf,aAAa,CAAC;MACxBkB,SAAS,EAAE,CACP;QAAEd,OAAO,EAAEV,MAAM;QAAEW,QAAQ,EAAE;MAAI,CAAC,EAClCV,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvC;QAAES,OAAO,EAAER,yBAAyB;QAAEuB,WAAW,EAAEtB;MAA8B,CAAC,EAClF;QAAEO,OAAO,EAAEhB,gBAAgB;QAAEgC,QAAQ,EAAE/B;MAAqB,CAAC;IAErE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASmB,oBAAoB,EAAED,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}