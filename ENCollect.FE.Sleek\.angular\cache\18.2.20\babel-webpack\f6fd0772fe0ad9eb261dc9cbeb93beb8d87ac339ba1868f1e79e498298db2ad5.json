{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./geo-config.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./geo-config.component.css?ngResource\";\nimport { Component } from '@angular/core';\nlet GeoConfigComponent = class GeoConfigComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nGeoConfigComponent = __decorate([Component({\n  selector: 'app-geo-config',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], GeoConfigComponent);\nexport { GeoConfigComponent };", "map": {"version": 3, "names": ["Component", "GeoConfigComponent", "constructor", "ngOnInit", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\geo-config\\geo-config.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-geo-config',\r\n  templateUrl: './geo-config.component.html',\r\n  styleUrls: ['./geo-config.component.css']\r\n})\r\nexport class GeoConfigComponent implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AAO1C,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAE7BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;;;AALWF,kBAAkB,GAAAG,UAAA,EAL9BJ,SAAS,CAAC;EACTK,QAAQ,EAAE,gBAAgB;EAC1BC,QAAA,EAAAC,oBAA0C;;CAE3C,CAAC,C,EACWN,kBAAkB,CAO9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}