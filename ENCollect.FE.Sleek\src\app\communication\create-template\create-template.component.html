<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"></app-breadcrumb>
  <div class="d-flex align-items-center mb-4">
    <h2 class="title mb-0">
      @if (isViewMode) {
        View Communication Template
      } @else if (isEditMode) {
        Edit Communication Template
      } @else {
        Create Communication Template
      }
    </h2>
    @if (!isViewMode) {
      <button
        tooltip="Design templates for different communication channels with variable
        support and multilingual capabilities"
        class="ms-3 p-0 border-0"
        type="button"
      >
        <svg-icon src="assets/new/svgs/instruction-info.svg"></svg-icon>
      </button>
    }
  </div>

  @if (isLoading) {
    <div class="enc-card">
      <div class="card-content text-center p-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Loading template data...</p>
      </div>
    </div>
  } @else {
    <div class="enc-card" [formGroup]="createForm">
      <div class="card-content">
        <div class="row">
         

          
             <div class="col-md-6 col-12">
          <div class="form-control-group">
            <label class="form-label required">Template Name</label>
            <input type="text" class="form-control" [class.is-invalid]="isFieldInvalid('templateName')" placeholder="Enter Template Name" id="templateName" formControlName="templateName" [readonly]="isEditMode || isViewMode" [class.form-control-readonly]="isEditMode || isViewMode" />
            @if (isFieldInvalid('templateName')) {
            <div class="form-error">
              {{ getFieldErrorMessage('templateName') }}
            </div>
            }
          </div>
        </div>
        <div class="col-md-6 col-12">
          <div class="form-control-group">
            <label class="form-label required">Channel Type</label>
            <select class="form-select" id="channelType" formControlName="channelType" [disabled]="isEditMode || isViewMode">
              <option value="">Select Channel Type</option>
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="letter">Letter</option>
            </select>
          </div>
        </div>
        <!-- Second row: Channel Type first, then Allow Access -->
        <div class="col-md-6 col-12">
          <div class="form-control-group h-100 d-flex flex-column justify-content-between">
            <div class="form-control-group">
              <label class="form-label required">Entry Point</label>
              @if (isViewMode && currentTemplateData) {
              <select class="form-select" formControlName="entryPoint" disabled>
                <option value="">Select Entry Point</option>
                <option [selected]="currentTemplateData.entryPoint === Account">Account</option>
                <option [selected]="currentTemplateData.entryPoint === User">User</option>
              </select>
              } @else {
              <select class="form-select" formControlName="entryPoint" [disabled]="isViewMode">
                <option value="">Select Entry Point</option>
                <option value="Account">Account</option>
                <option value="User">User</option>
              </select>
              @if (isFieldInvalid('entryPoint')) {
                <div class="text-danger small mt-1">{{ getFieldErrorMessage('entryPoint') }}</div>
              }
              }
            </div>
          </div>
        </div>
        <div class="col-md-6 col-12">
          <div class="form-control-group h-100 d-flex flex-column justify-content-between">
            <div class="form-control-group">
              <label class="form-label required">Recipient Type</label>
              @if (isViewMode && currentTemplateData) {
              <select class="form-select" formControlName="recipientType" disabled>
                <option value="">Select Recipient Type</option>
                <option [selected]="currentTemplateData.recipientType === Customer">Customer</option>
                <option [selected]="currentTemplateData.recipientType=== Agent">Agent</option>
              </select>
              } @else {
              <select class="form-select" formControlName="recipientType" [disabled]="isViewMode">
                <option value="">Select Recipient Type</option>
                <option value="Customer">Customer</option>
                <option value="Agent">Agent</option>
              </select>
              @if (isFieldInvalid('recipientType')) {
                <div class="text-danger small mt-1">{{ getFieldErrorMessage('recipientType') }}</div>
              }
              }
            </div>
          </div>
        </div>
          @if ((isViewMode && currentTemplateData && (currentTemplateData.templateType?.toLowerCase() === 'email' || currentTemplateData.templateType?.toLowerCase() === 'sms')) || (isEditMode && currentTemplateData && (currentTemplateData.templateType?.toLowerCase() === 'email' || currentTemplateData.templateType?.toLowerCase() === 'sms')) || (!isViewMode && !isEditMode && (fValue?.channelType === 'email' || fValue?.channelType === 'sms'))) {
            <div class="col-md-12">
              <div class="form-control-group">
                <label class="form-label required">Allow Access from Account Details</label>
                @if (isViewMode && currentTemplateData) {
                  <div class="form-radio-group disabled">
                    <label>
                      <input type="radio" name="allowAccessView" id="allowAccessYes" value="true"
                        [checked]="currentTemplateData.isAvailableInAccountDetails === true" disabled />
                      Yes
                    </label>
                    <label>
                      <input type="radio" name="allowAccessView" id="allowAccessNo" value="false"
                        [checked]="currentTemplateData.isAvailableInAccountDetails === false" disabled />
                      No
                    </label>
                  </div>
                } @else {
                  <div class="form-radio-group" [class.disabled]="isViewMode">
                    <label>
                      <input type="radio" name="allowAccessFromAccount" id="allowAccessYes" value="true"
                        formControlName="allowAccessFromAccount" [disabled]="isViewMode" />
                      Yes
                    </label>
                    <label>
                      <input type="radio" name="allowAccessFromAccount" id="allowAccessNo" value="false"
                        formControlName="allowAccessFromAccount" [disabled]="isViewMode" />
                      No
                    </label>
                  </div>
                }
              </div>
            </div>
          }

          <div class="col-md-12">
            <div class="form-control-group">
              <label class="form-label required">Template Language</label>
              <div class="d-flex align-items-center justify-content-between">
                @if (isViewMode && currentTemplateData?.communicationTemplateDetails?.length > 0) {
                  <div class="form-button-group">
                    @for (detail of currentTemplateData.communicationTemplateDetails; let i = $index; track detail.language) {
                      <div class="language-button-wrapper">
                        <button
                          class="language-btn"
                          [class.active]="selectedViewLanguageIndex === i"
                          (click)="selectViewLanguage(i)">
                          {{ detail.language }}
                        </button>
                      </div>
                    }
                  </div>
                } @else {
                  <div class="form-button-group" btnRadioGroup formControlName="activeLanguage">
                    @for (lang of fValue?.languages; let i = $index; track lang.languageCode) {
                      <div class="language-button-wrapper">
                        <button [btnRadio]="lang?.languageCode" id="template-language-{{ lang?.languageCode }}"
                          class="language-btn">
                          {{ lang?.languageName }}
                          @if (fValue?.languages?.length > 1 && !isViewMode) {
                            <span class="remove-language-btn" (click)="removeLanguage(i); $event.stopPropagation()"
                              title="Remove {{ lang?.languageName }}">
                              ×
                            </span>
                          }
                        </button>
                      </div>
                    }
                  </div>
                }
                @if (!isViewMode) {
                  <button class="btn btn-outline-primary" id="addLanguageBtn" (click)="openAddLangModal(addLangModal)">
                    <svg-icon src="assets/new/svgs/language.svg" class="me-2"></svg-icon>
                    <span>Add Language</span>
                  </button>
                }
              </div>
            </div>
          </div>

          @if (createForm?.get('channelType')?.value === 'letter') {
            <div class="col-md-6">
              <div class="form-control-group">
                <label class="form-label">Upload Header</label>
                <input type="file" class="form-control" accept=".jpg,.jpeg,.png,.pdf"
                  (change)="onHeaderUpload($event)" disabled />
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-control-group">
                <label class="form-label">Upload Footer</label>
                <input type="file" class="form-control" accept=".jpg,.jpeg,.png,.pdf"
                  (change)="onFooterUpload($event)" disabled />
              </div>
            </div>
          }

          @if (isViewMode && currentTemplateData?.communicationTemplateDetails?.length > 0) {
            @if (currentTemplateData.communicationTemplateDetails.length > 1) {
              <!-- div class="col-md-12">
                <div class="text-muted">
                  Viewing content in: <strong>{{ getSelectedLanguageDetail()?.language || 'English' }}</strong>
                </div>
              </div> -->
            }
            @if (currentTemplateData.templateType?.toLowerCase() === 'email') {
              <div class="col-md-12">
                <div class="form-control-group">
                  <label class="form-label required">Subject Line</label>
                  <input type="text" class="form-control form-control-readonly"
                    [value]="getSelectedLanguageDetail()?.subject || ''" readonly />
                </div>
              </div>
            }
            <div class="col-md-12">
              <div class="form-control-group">
                <label class="form-label required">Template Body</label>
                <textarea class="form-control form-control-readonly" rows="7"
                  [value]="getSelectedLanguageDetail()?.body || ''" readonly></textarea>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-control-group">
                <label class="form-label">Template Preview</label>
                <div appTemplatePreview
                  [template]="getSelectedLanguageDetail()?.body || ''"
                  [variables]="variables"></div>
              </div>
            </div>
          }

          @if (!isViewMode) {
            @for (lang of fValue?.languages; let i = $index; track lang.languageCode) {
              <ng-container [formArrayName]="'languages'">
                <ng-container [formGroupName]="i">
                  @if (shouldShowLanguageFields(i)) {
                    @if ((fValue?.channelType === 'email') || (isEditMode && currentTemplateData?.templateType?.toLowerCase() === 'email')) {
                      <div class="col-md-12">
                        <div class="form-control-group">
                          <label class="form-label required">Subject Line</label>
                          <input type="text" class="form-control" [class.is-invalid]="isLanguageFieldInvalid(i, 'emailSubject')"
                            placeholder="Enter Email Subject" id="emailSubject" formControlName="emailSubject" />
                          @if (isLanguageFieldInvalid(i, 'emailSubject')) {
                            <div class="form-error">
                              {{ getLanguageFieldErrorMessage(i, 'emailSubject') }}
                            </div>
                          }
                        </div>
                      </div>
                    }
                    <div class="col-md-12">
                      <div class="form-control-group">
                        <div class="d-flex align-items-center">
                          <label class="form-label required">Template Body</label>
                          <label class="form-label">
                             <i class="fa fa-info-circle text-primary info-icon ms-2" tooltip="Use <<Var>> to add variables that can be mapped to template variables" placement="right" container="body" tooltipClass="enc-tooltip"></i>
                          </label>
                        </div>
                        <div class="position-relative">
                          <button
                            type="button"
                            class="btn btn-primary rounded rounded-5 icon-btn position-absolute d-flex align-items-center justify-content-center add-var-btn"
                            (click)="insertVariable()"
                            tooltip="Add Variable"
                            >
                            <svg-icon src="assets/new/svgs/var.svg" width="16" height="16"></svg-icon>
                          </button>

                          @if ((fValue?.channelType === 'email') || (isEditMode && currentTemplateData?.templateType?.toLowerCase() === 'email')) {
                            <!-- Email Body Input - NGX Rich Text Editor -->
                            <div class="email-body-container">
                              @if (editors[i]) {
                                <ngx-editor-menu [editor]="editors[i]" [toolbar]="toolbar"></ngx-editor-menu>
                                <ngx-editor
                                  [editor]="editors[i]"
                                  [(ngModel)]="htmlContents[i]"
                                  [ngModelOptions]="{standalone: true}"
                                  [placeholder]="'Enter Email Template Content'"
                                  (ngModelChange)="onEditorContentChange(i)"
                                  [disabled]="false">
                                </ngx-editor>
                              } @else {
                                <div class="editor-loading">
                                  <p>Loading editor...</p>
                                </div>
                              }
                              <div class="email-body-help-text mt-1">
                                <small class="text-muted">
                                  <i class="fas fa-info-circle me-1"></i>
                                  Email templates support rich text formatting. Use the toolbar for styling.
                                </small>
                              </div>
                            </div>
                            @if (isLanguageFieldInvalid(i, 'emailBody')) {
                              <div class="form-error">
                                {{ getLanguageFieldErrorMessage(i, 'emailBody') }}
                              </div>
                            }
                          } @else {
                            <!-- SMS/Letter Body Input - Plain Text -->
                            <textarea class="form-control"
                              [class.is-invalid]="isLanguageFieldInvalid(i, 'templateBody')"
                              placeholder="Enter Approved Template Content"
                              id="templateBody"
                              rows="7"
                              formControlName="templateBody"
                              appRestrictHtmlTags
                              [allowCopyPasteOnly]="(fValue?.channelType === 'sms') || (isEditMode && currentTemplateData?.templateType?.toLowerCase() === 'sms')"></textarea>
                            @if (isLanguageFieldInvalid(i, 'templateBody')) {
                              <div class="form-error">
                                {{ getLanguageFieldErrorMessage(i, 'templateBody') }}
                              </div>
                            }
                            @if ((fValue?.channelType === 'sms') || (isEditMode && currentTemplateData?.templateType?.toLowerCase() === 'sms')) {
                              <div class="text-muted mt-1 small">
                                <i class="fas fa-mobile-alt me-1"></i>
                                <i>SMS templates: Copy, paste and delete operations only allowed.</i>
                              </div>
                            }
                          }
                        </div>
                      </div>
                    </div>
                    @if (getCurrentTemplateBody(i) && !isViewMode) {
                      <div class="col-md-12">
                        <div class="form-control-group">
                          <div class="d-flex align-items-center justify-content-between">
                            <label class="form-label required">Template Variable Mapping</label>
                            <div class="variable-map-legends">
                              <label class="unmapped">Unmapped Variable</label>
                              <label class="mapped">Mapped Variable</label>
                            </div>
                          </div>
                          <div appTemplateVarConfig #templateVarConfig="appTemplateVarConfig"
                            [template]="getCurrentTemplateBody(i)"
                            (selectVar)="openMapVariableModal($event, mapVariableModal)"
                            (change)="updateTemplateValue($event, i)"></div>
                        </div>
                      </div>
                      <div class="col-md-12">
                        <div class="form-control-group">
                          <div class="d-flex align-items-center justify-content-between">
                            <label class="form-label required">Message Preview Using Template</label>
                          </div>
                          <div appTemplatePreview
                            [template]="getCurrentTemplateBody(i)"
                            [variables]="variables"></div>
                        </div>
                      </div>
                    }
                  }
                </ng-container>
              </ng-container>
            }
          }


        </div>
      </div>
      <div class="card-footer">
        @if (isViewMode) {
          <button class="btn btn-outline-primary mw-150px"
            [routerLink]="['/communication/search-communication-templates']">Back to Search</button>
        } @else if (isEditMode) {
          <button class="btn btn-secondary mw-150px me-4" (click)="updateTemplate()">Update Template</button>
          <button class="btn btn-outline-primary mw-150px"
            [routerLink]="['/communication/search-communication-templates']">Cancel</button>
        } @else {
          <button class="btn btn-secondary mw-150px me-4" (click)="createTemplate()">Create Template</button>
          <button class="btn btn-outline-primary mw-150px"
            [routerLink]="['/communication/search-communication-templates']">Cancel</button>
        }
      </div>
    </div>
  }
</div>

<ng-template #mapVariableModal>
  <div class="modal-header d-flex align-items-center justify-content-between">
    <h4 class="modal-title">Template Variable Mapping</h4>
    <img src="assets/new/svgs/side-drawer-close.svg" alt="Close" class="modal-close-btn"
      (click)="mapVarModalRef?.hide()" />
  </div>
  <div class="modal-body">
    <div class="form-control-group">
      <label class="form-label required">Mapping Fields</label>
      <ng-select class="form-ng-select" [items]="variables" bindLabel="name" bindValue="name"
        placeholder="Select Database Field" [(ngModel)]="selectedVariable.value" [clearable]="false"
        [searchable]="true">
      </ng-select>
    </div>
  </div>
  <div class="modal-footer justify-content-center">
    <button type="button" class="btn btn-success mw-150px" [disabled]="!selectedVariable?.value"
      (click)="assignVariable()">
      Map
    </button>
  </div>
</ng-template>

<ng-template #addLangModal>
  <div class="modal-header d-flex align-items-center justify-content-between">
    <h4 class="modal-title">Add New Language</h4>
    <img src="assets/new/svgs/side-drawer-close.svg" alt="Close" class="modal-close-btn"
      (click)="addLangModalRef?.hide()" />
  </div>
  <div class="modal-body">
    <div class="form-control-group">
      <label class="form-label required">Language</label>
      <ng-select class="form-ng-select" [items]="allLanguages" bindLabel="name" bindValue="code"
        placeholder="Select Language" [(ngModel)]="selectedLanguage" [clearable]="false" [searchable]="true">
      </ng-select>
    </div>
  </div>
  <div class="modal-footer justify-content-center">
    <button type="button" class="btn btn-success mw-150px" [disabled]="!selectedLanguage" (click)="addLanguage()">
      Add Language
    </button>
  </div>
</ng-template>
