{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./view-edit-target.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./view-edit-target.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { TargetService } from '../target.service';\nimport { ActivatedRoute, Router } from '@angular/router';\nlet ViewEditTargetComponent = class ViewEditTargetComponent {\n  constructor(toastr, targetService, modalService, route, router) {\n    this.toastr = toastr;\n    this.targetService = targetService;\n    this.modalService = modalService;\n    this.route = route;\n    this.router = router;\n    this.breadcrumbData = [\n      // { label: \"Target\", path: \"/target/view-target/:id\" },\n      // { label: \"View Target\", path: \"/target/view-target/:id\" },\n    ];\n    this.showTarget = false;\n    this.selectedAgency = {\n      agencyName: ''\n    };\n    this.loader = {\n      isSearching: false,\n      isSubmit: false\n    };\n    this.sub = this.route.params.subscribe(params => {\n      this.id = params['id']; // (+) converts string 'id' to a number\n    });\n    this.breadcrumbData = [{\n      label: \"Target\",\n      path: \"/target/view-target/:\" + this.id\n    }, {\n      label: \"View Target\",\n      path: \"/target/view-target/:\" + this.id\n    }];\n    this.targetInfo = {};\n    this.targetInfo = {};\n    this.agencyList = [];\n    this.targetInfo.agencyList = this.agencyList;\n    this.selectedAgencyTarget = {\n      targetPOSforNorm: 0,\n      percentageNorm: 0,\n      accountCountNorm: 0,\n      accountPercentageNorm: 0,\n      targetPOSforRB: 0,\n      percentageRB: 0,\n      accountCountRB: 0,\n      accountPercentageRB: 0,\n      targetPOSforStab: 0,\n      percentageStab: 0,\n      accountPercentageStab: 0,\n      accountCountStab: 0,\n      targetPOSforRF: 0,\n      percentageRF: 0,\n      accountCountRF: 0,\n      accountPercentageRF: 0,\n      targetResolution: 0,\n      percentageResolution: 0,\n      accountCountMoneyResolution: 0,\n      accountPercentageResolution: 0,\n      targetMoneyCollected: 0,\n      percentageMoneyCollected: 0,\n      accountCountMoneyCollected: 0,\n      accountPercentageMoneyCollected: 0\n    };\n  }\n  ngOnInit() {\n    let id = this.route.snapshot.params.id;\n    this.targetId = id;\n    this.targetService.viewTarget(id).subscribe(data => {\n      this.fillForm(data);\n    });\n  }\n  fillForm(data) {\n    this.targetInfo = data[\"agencytargetDetails\"];\n    this.agencyList = [data[\"agencyDetails\"]];\n    this.selectedAgencyTarget.agencyId = this.agencyList.agencyId;\n    this.selectedAgency = this.agencyList[0];\n    this.selectedAgencyTarget = data[\"agencytargetDetails\"];\n    this.selectedAgencyTarget.targetPOSforNorm = data[\"agencytargetDetails\"][\"budgetedTargetPOSforNorm\"];\n    this.selectedAgencyTarget.accountCountNorm = data[\"agencytargetDetails\"][\"budgetedAccountCountNorm\"];\n    this.selectedAgencyTarget.targetPOSforRB = data[\"agencytargetDetails\"][\"budgetedTargetPOSforRB\"];\n    this.selectedAgencyTarget.accountCountRB = data[\"agencytargetDetails\"][\"budgetedAccountCountRB\"];\n    this.selectedAgencyTarget.targetPOSforStab = data[\"agencytargetDetails\"][\"budgetedTargetPOSforStab\"];\n    this.selectedAgencyTarget.accountCountStab = data[\"agencytargetDetails\"][\"budgetedAccountCountStab\"];\n    this.selectedAgencyTarget.targetResolution = data[\"agencytargetDetails\"][\"budgetedTargetPOSforResolution\"];\n    this.selectedAgencyTarget.accountCountMoneyResolution = data[\"agencytargetDetails\"][\"budgetedAccountCountResolution\"];\n    this.selectedAgencyTarget.targetPOSforRF = data[\"agencytargetDetails\"][\"budgetedTargetPOSforRF\"];\n    this.selectedAgencyTarget.accountCountRF = data[\"agencytargetDetails\"][\"budgetedAccountCountRF\"];\n    this.calcResPendingTargetsAmt();\n    this.calcMoneyCollecteAmt();\n    this.calcMoneyCollectePers();\n    this.agencyNormPerCalc();\n    this.agencyRBPerCalc();\n    this.agencyStabPerCalc();\n    this.agencyNormCountPerCalc();\n    this.agencyRBCountPerCalc();\n    this.agencyStabCountPerCalc();\n    this.agencyAmtResCalc();\n    this.agencyCountResCalc();\n    this.agencyRFPerCalc();\n    this.agencyRFCountPerCalc();\n    this.agencyMCPerCalc();\n    this.agencyMCCountPerCalc();\n  }\n  // calculate resolutions pending targets amount\n  calcResPendingTargetsAmt() {\n    /*-- Resolution amount  total */\n    this.targetInfo.targetPOSforResolution = Number((this.targetInfo.targetPOSforNorm + this.targetInfo.targetPOSforRB + this.targetInfo.targetPOSforStab).toFixed(2));\n    /*--  Resolutions count total*/\n    this.targetInfo.accountCountResolution = Number((this.targetInfo.accountCountNorm + this.targetInfo.accountCountRB + this.targetInfo.accountCountStab).toFixed(2));\n    /*--norm percentage */\n    this.targetInfo.percentageNorm = Number((this.targetInfo.targetPOSforNorm / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.accountPercentageNorm = Number((this.targetInfo.accountCountNorm / this.selectedAgency.totalAccount * 100).toFixed(2));\n    /*--roll back percentage*/\n    this.targetInfo.percentageRB = Number((this.targetInfo.targetPOSforRB / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.accountPercentageRB = Number((this.targetInfo.accountCountRB / this.selectedAgency.totalAccount * 100).toFixed(2));\n    /*--Stabilization percentage */\n    this.targetInfo.percentageStab = Number((this.targetInfo.targetPOSforStab / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.accountPercentageStab = Number((this.targetInfo.accountCountStab / this.selectedAgency.totalAccount * 100).toFixed(2));\n    /*-- resolution amount percentage  total */\n    this.targetInfo.percentageResolution = Number((this.targetInfo.percentageNorm + this.targetInfo.percentageRB + this.targetInfo.percentageStab).toFixed(2));\n    /*--resolution count percentage  total*/\n    this.targetInfo.accountPercentageResolution = Number((this.targetInfo.accountPercentageNorm + this.targetInfo.accountPercentageRB + this.targetInfo.accountPercentageStab).toFixed(2));\n    /*--RF percentage */\n    this.targetInfo.percentageRF = Number((this.targetInfo.targetPOSforRF / this.selectedAgency.totalPOS * 100).toFixed(2));\n    this.targetInfo.accountPercentageRF = Number((this.targetInfo.accountCountRF / this.selectedAgency.totalAccount * 100).toFixed(2));\n  }\n  /*--money collected amount percentage*/\n  calcMoneyCollecteAmt() {\n    this.targetInfo.percentageMoneyCollected = Number((this.targetInfo.targetMoneyCollected / this.selectedAgency.totalArrear * 100).toFixed(2));\n  }\n  /*--money collected count percentage*/\n  calcMoneyCollectePers() {\n    this.targetInfo.accountPercentageMoneyCollected = Number((this.targetInfo.accountCountMoneyCollected / this.selectedAgency.totalAccount * 100).toFixed(2));\n  }\n  /*--normalization agency amount percentage*/\n  agencyNormPerCalc() {\n    this.selectedAgencyTarget.percentageNorm = Number((this.selectedAgencyTarget.targetPOSforNorm / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--roll back agency amount percentage*/\n  agencyRBPerCalc() {\n    this.selectedAgencyTarget.percentageRB = Number((this.selectedAgencyTarget.targetPOSforRB / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--Stabilization agency amount percentage*/\n  agencyStabPerCalc() {\n    this.selectedAgencyTarget.percentageStab = Number((this.selectedAgencyTarget.targetPOSforStab / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--normalization agency count percentage*/\n  agencyNormCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageNorm = Number((this.selectedAgencyTarget.accountCountNorm / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--roll back agency count percentage*/\n  agencyRBCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageRB = Number((this.selectedAgencyTarget.accountCountRB / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--Stabilization agency count percentage*/\n  agencyStabCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageStab = Number((this.selectedAgencyTarget.accountCountStab / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  agencyAmtResCalc() {\n    /*-- Resolution amount  total */\n    this.selectedAgencyTarget.targetResolution = Number((this.selectedAgencyTarget.targetPOSforNorm + this.selectedAgencyTarget.targetPOSforRB + this.selectedAgencyTarget.targetPOSforStab).toFixed(2));\n    /*-- resolution amount percentage  total */\n    this.selectedAgencyTarget.percentageResolution = Number((this.selectedAgencyTarget.percentageNorm + this.selectedAgencyTarget.percentageRB + this.selectedAgencyTarget.percentageStab).toFixed(2));\n  }\n  agencyCountResCalc() {\n    /*--  Resolutions count total*/\n    this.selectedAgencyTarget.accountCountMoneyResolution = Number((this.selectedAgencyTarget.accountCountNorm + this.selectedAgencyTarget.accountCountRB + this.selectedAgencyTarget.accountCountStab).toFixed(2));\n    /*--resolution count percentage  total*/\n    this.selectedAgencyTarget.accountPercentageResolution = Number((this.selectedAgencyTarget.accountPercentageNorm + this.selectedAgencyTarget.accountPercentageRB + this.selectedAgencyTarget.accountPercentageStab).toFixed(2));\n  }\n  /*--RF agency amount percentage*/\n  agencyRFPerCalc() {\n    this.selectedAgencyTarget.percentageRF = Number((this.selectedAgencyTarget.targetPOSforRF / this.selectedAgency.targetPOSforRF * 100).toFixed(2));\n  }\n  /*--RF agency count percentage*/\n  agencyRFCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageRF = Number((this.selectedAgencyTarget.accountCountRF / this.selectedAgency.accountCountRF * 100).toFixed(2));\n  }\n  /*--Money Collected agency amount percentage*/\n  agencyMCPerCalc() {\n    this.selectedAgencyTarget.percentageMoneyCollected = Number((this.selectedAgencyTarget.targetMoneyCollected / this.selectedAgency.totalPOS * 100).toFixed(2));\n  }\n  /*--Money Collected agency count percentage*/\n  agencyMCCountPerCalc() {\n    this.selectedAgencyTarget.accountPercentageMoneyCollected = Number((this.selectedAgencyTarget.accountCountMoneyCollected / this.selectedAgency.totalAccount * 100).toFixed(2));\n  }\n  edit() {\n    this.router.navigateByUrl('/target/edit-target/' + this.targetId);\n  }\n  cancel() {\n    this.router.navigateByUrl('/home');\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: TargetService\n    }, {\n      type: BsModalService\n    }, {\n      type: ActivatedRoute\n    }, {\n      type: Router\n    }];\n  }\n};\nViewEditTargetComponent = __decorate([Component({\n  selector: 'app-view-edit-target',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ViewEditTargetComponent);\nexport { ViewEditTargetComponent };", "map": {"version": 3, "names": ["Component", "ToastrService", "BsModalService", "TargetService", "ActivatedRoute", "Router", "ViewEditTargetComponent", "constructor", "toastr", "targetService", "modalService", "route", "router", "breadcrumbData", "showTarget", "selectedAgency", "agencyName", "loader", "isSearching", "isSubmit", "sub", "params", "subscribe", "id", "label", "path", "targetInfo", "agencyList", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetPOSforNorm", "percentageNorm", "accountCountNorm", "accountPercentageNorm", "targetPOSforRB", "percentageRB", "accountCountRB", "accountPercentageRB", "targetPOSforStab", "percentageStab", "accountPercentageStab", "accountCountStab", "targetPOSforRF", "percentageRF", "accountCountRF", "accountPercentageRF", "targetResolution", "percentageResolution", "accountCountMoneyResolution", "accountPercentageResolution", "targetMoneyCollected", "percentageMoneyCollected", "accountCountMoneyCollected", "accountPercentageMoneyCollected", "ngOnInit", "snapshot", "targetId", "viewTarget", "data", "fillForm", "agencyId", "calcResPendingTargetsAmt", "calcMoneyCollecteAmt", "calcMoneyCollectePers", "agencyNormPerCalc", "agencyRBPerCalc", "agencyStabPerCalc", "agencyNormCountPerCalc", "agencyRBCountPerCalc", "agencyStabCountPerCalc", "agencyAmtResCalc", "agencyCountResCalc", "agencyRFPerCalc", "agencyRFCountPerCalc", "agencyMCPerCalc", "agencyMCCountPerCalc", "targetPOSforResolution", "Number", "toFixed", "accountCountResolution", "totalPOS", "totalAccount", "totalArrear", "edit", "navigateByUrl", "cancel", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\target\\view-edit-target\\view-edit-target.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService } from 'ngx-bootstrap/modal';\r\nimport { TargetService } from '../target.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-view-edit-target',\r\n  templateUrl: './view-edit-target.component.html',\r\n  styleUrls: ['./view-edit-target.component.css']\r\n})\r\nexport class ViewEditTargetComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t// { label: \"Target\", path: \"/target/view-target/:id\" },\r\n\t\t// { label: \"View Target\", path: \"/target/view-target/:id\" },\r\n\t  ]\r\n  targetInfo:any;\r\n  agencyList:any;\r\n  agencyId1:any;\r\n  agencyId2:any;\r\n  showTarget:boolean=false;\r\n  selectedAgencyTarget: any;\r\n  selectedAgency: any={\r\n    agencyName:''\r\n  };\r\n\r\n  loader = {\r\n    isSearching: false,\r\n    isSubmit: false\r\n  }\r\n  sub: any;\r\n  id: any;\r\n\r\n  constructor(public toastr: ToastrService, \r\n    private targetService: TargetService,\r\n    private modalService: BsModalService,\r\n    private route: ActivatedRoute,\r\n    private router: Router) {\r\n    \r\n      this.sub = this.route.params.subscribe(params => {\r\n        this.id = params['id']; // (+) converts string 'id' to a number\r\n      });\r\n      this.breadcrumbData = [\r\n        { label: \"Target\", path: \"/target/view-target/:\"+this.id },\r\n        { label: \"View Target\", path: \"/target/view-target/:\"+this.id },\r\n        ]\r\n    this.targetInfo = {}\r\n    this.targetInfo  = {}\r\n    this.agencyList = []\r\n    this.targetInfo.agencyList =  this.agencyList\r\n    this.selectedAgencyTarget = {\r\n      targetPOSforNorm:0,\r\n      percentageNorm: 0,\r\n      accountCountNorm: 0,\r\n      accountPercentageNorm: 0,\r\n\r\n      targetPOSforRB: 0,\r\n      percentageRB: 0,\r\n      accountCountRB: 0,\r\n      accountPercentageRB: 0,\r\n\r\n\r\n      targetPOSforStab: 0,\r\n      percentageStab: 0,\r\n      accountPercentageStab: 0,\r\n      accountCountStab: 0,\r\n\r\n      targetPOSforRF: 0,\r\n      percentageRF: 0,\r\n      accountCountRF: 0,\r\n      accountPercentageRF: 0,\r\n\r\n      targetResolution: 0,\r\n      percentageResolution: 0,\r\n      accountCountMoneyResolution: 0,\r\n      accountPercentageResolution: 0,\r\n\r\n      targetMoneyCollected: 0,\r\n      percentageMoneyCollected: 0,\r\n      accountCountMoneyCollected: 0,\r\n      accountPercentageMoneyCollected: 0\r\n    }\r\n\r\n\r\n  }\r\n\r\n  targetId: any;\r\n\r\n  ngOnInit(): void {\r\n    let id = this.route.snapshot.params.id\r\n    this.targetId = id;\r\n     this.targetService.viewTarget(id)\r\n        .subscribe(data => {\r\n          this.fillForm(data)\r\n    })\r\n\r\n  }\r\n\r\n  fillForm(data){\r\n    this.targetInfo = data[\"agencytargetDetails\"];\r\n    this.agencyList = [data[\"agencyDetails\"]]\r\n    this.selectedAgencyTarget.agencyId = this.agencyList.agencyId;\r\n    this.selectedAgency =  this.agencyList[0]\r\n    this.selectedAgencyTarget =  data[\"agencytargetDetails\"];\r\n\r\n    this.selectedAgencyTarget.targetPOSforNorm = data[\"agencytargetDetails\"][\"budgetedTargetPOSforNorm\"];\r\n    this.selectedAgencyTarget.accountCountNorm = data[\"agencytargetDetails\"][\"budgetedAccountCountNorm\"];\r\n\r\n    this.selectedAgencyTarget.targetPOSforRB = data[\"agencytargetDetails\"][\"budgetedTargetPOSforRB\"];\r\n    this.selectedAgencyTarget.accountCountRB = data[\"agencytargetDetails\"][\"budgetedAccountCountRB\"];\r\n\r\n    this.selectedAgencyTarget.targetPOSforStab = data[\"agencytargetDetails\"][\"budgetedTargetPOSforStab\"];\r\n    this.selectedAgencyTarget.accountCountStab = data[\"agencytargetDetails\"][\"budgetedAccountCountStab\"];\r\n\r\n    this.selectedAgencyTarget.targetResolution = data[\"agencytargetDetails\"][\"budgetedTargetPOSforResolution\"];\r\n    this.selectedAgencyTarget.accountCountMoneyResolution = data[\"agencytargetDetails\"][\"budgetedAccountCountResolution\"];\r\n\r\n    this.selectedAgencyTarget.targetPOSforRF = data[\"agencytargetDetails\"][\"budgetedTargetPOSforRF\"];\r\n    this.selectedAgencyTarget.accountCountRF = data[\"agencytargetDetails\"][\"budgetedAccountCountRF\"];\r\n\r\n    this.calcResPendingTargetsAmt()\r\n    this.calcMoneyCollecteAmt()\r\n    this.calcMoneyCollectePers();\r\n\r\n     this.agencyNormPerCalc()\r\n     this.agencyRBPerCalc()\r\n     this.agencyStabPerCalc()\r\n     this.agencyNormCountPerCalc()\r\n     this.agencyRBCountPerCalc()\r\n     this.agencyStabCountPerCalc()\r\n     this.agencyAmtResCalc()\r\n     this.agencyCountResCalc()\r\n     this.agencyRFPerCalc()\r\n     this.agencyRFCountPerCalc()\r\n     this.agencyMCPerCalc();\r\n     this.agencyMCCountPerCalc();\r\n  }\r\n\r\n    // calculate resolutions pending targets amount\r\n  calcResPendingTargetsAmt(){\r\n      /*-- Resolution amount  total */\r\n      this.targetInfo .targetPOSforResolution = Number((this.targetInfo .targetPOSforNorm +\r\n                                                      this.targetInfo .targetPOSforRB +\r\n                                                      this.targetInfo .targetPOSforStab).toFixed(2))\r\n\r\n     /*--  Resolutions count total*/\r\n      this.targetInfo .accountCountResolution = Number((this.targetInfo .accountCountNorm +\r\n                                                      this.targetInfo .accountCountRB +\r\n                                                      this.targetInfo .accountCountStab).toFixed(2))\r\n\r\n      /*--norm percentage */\r\n      this.targetInfo .percentageNorm = Number((this.targetInfo .targetPOSforNorm/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo .accountPercentageNorm = Number((this.targetInfo .accountCountNorm/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n      \r\n      /*--roll back percentage*/\r\n      this.targetInfo .percentageRB = Number((this.targetInfo .targetPOSforRB/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo .accountPercentageRB = Number((this.targetInfo .accountCountRB/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n\r\n      /*--Stabilization percentage */\r\n      this.targetInfo .percentageStab = Number((this.targetInfo .targetPOSforStab/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo .accountPercentageStab = Number((this.targetInfo .accountCountStab/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n\r\n\r\n\r\n      /*-- resolution amount percentage  total */\r\n      this.targetInfo .percentageResolution =  Number((this.targetInfo .percentageNorm +\r\n                                                            this.targetInfo .percentageRB +\r\n                                                            this.targetInfo .percentageStab).toFixed(2))\r\n\r\n\r\n      /*--resolution count percentage  total*/\r\n      this.targetInfo .accountPercentageResolution =  Number((this.targetInfo .accountPercentageNorm +\r\n                                                                    this.targetInfo .accountPercentageRB +\r\n                                                                    this.targetInfo .accountPercentageStab).toFixed(2))\r\n      /*--RF percentage */\r\n      this.targetInfo .percentageRF = Number((this.targetInfo .targetPOSforRF/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n      this.targetInfo .accountPercentageRF = Number((this.targetInfo .accountCountRF/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n  }\r\n\r\n  /*--money collected amount percentage*/\r\n  calcMoneyCollecteAmt(){\r\n     this.targetInfo .percentageMoneyCollected = Number((this.targetInfo .targetMoneyCollected/(this.selectedAgency.totalArrear)*100).toFixed(2))\r\n  }\r\n  /*--money collected count percentage*/\r\n\r\n  calcMoneyCollectePers(){\r\n     this.targetInfo .accountPercentageMoneyCollected = Number((this.targetInfo .accountCountMoneyCollected/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n    \r\n  }\r\n  /*--normalization agency amount percentage*/\r\n  agencyNormPerCalc(){\r\n    this.selectedAgencyTarget.percentageNorm = Number((this.selectedAgencyTarget.targetPOSforNorm/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n  }\r\n  /*--roll back agency amount percentage*/\r\n  agencyRBPerCalc(){\r\n    this.selectedAgencyTarget.percentageRB = Number((this.selectedAgencyTarget.targetPOSforRB/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n  }\r\n  /*--Stabilization agency amount percentage*/\r\n  agencyStabPerCalc(){\r\n    this.selectedAgencyTarget.percentageStab = Number((this.selectedAgencyTarget.targetPOSforStab/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n  }\r\n\r\n  /*--normalization agency count percentage*/\r\n  agencyNormCountPerCalc(){\r\n    this.selectedAgencyTarget.accountPercentageNorm = Number((this.selectedAgencyTarget.accountCountNorm/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n  }\r\n  /*--roll back agency count percentage*/\r\n  agencyRBCountPerCalc(){\r\n    this.selectedAgencyTarget.accountPercentageRB = Number((this.selectedAgencyTarget.accountCountRB/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n  }\r\n  /*--Stabilization agency count percentage*/\r\n  agencyStabCountPerCalc(){\r\n    this.selectedAgencyTarget.accountPercentageStab = Number((this.selectedAgencyTarget.accountCountStab/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n  }\r\n\r\n\r\n  agencyAmtResCalc(){\r\n    /*-- Resolution amount  total */\r\n    this.selectedAgencyTarget.targetResolution = Number((this.selectedAgencyTarget.targetPOSforNorm +\r\n                                                    this.selectedAgencyTarget.targetPOSforRB +\r\n                                                    this.selectedAgencyTarget.targetPOSforStab).toFixed(2))\r\n      /*-- resolution amount percentage  total */\r\n    this.selectedAgencyTarget.percentageResolution = Number((this.selectedAgencyTarget.percentageNorm +\r\n                                                    this.selectedAgencyTarget.percentageRB +\r\n                                                    this.selectedAgencyTarget.percentageStab).toFixed(2))\r\n    \r\n  }\r\n\r\n  agencyCountResCalc(){\r\n    /*--  Resolutions count total*/\r\n    this.selectedAgencyTarget.accountCountMoneyResolution = Number((this.selectedAgencyTarget.accountCountNorm +\r\n                                                    this.selectedAgencyTarget.accountCountRB +\r\n                                                    this.selectedAgencyTarget.accountCountStab).toFixed(2))\r\n    /*--resolution count percentage  total*/\r\n    this.selectedAgencyTarget.accountPercentageResolution  =  Number((this.selectedAgencyTarget.accountPercentageNorm +\r\n                                                    this.selectedAgencyTarget.accountPercentageRB +\r\n                                                    this.selectedAgencyTarget.accountPercentageStab).toFixed(2))\r\n  }\r\n\r\n\r\n  /*--RF agency amount percentage*/\r\n  agencyRFPerCalc(){\r\n    this.selectedAgencyTarget.percentageRF = Number((this.selectedAgencyTarget.targetPOSforRF/(this.selectedAgency.targetPOSforRF)*100).toFixed(2))\r\n  }\r\n\r\n  /*--RF agency count percentage*/\r\n  agencyRFCountPerCalc(){\r\n    this.selectedAgencyTarget.accountPercentageRF = Number((this.selectedAgencyTarget.accountCountRF/(this.selectedAgency.accountCountRF)*100).toFixed(2))\r\n  }\r\n\r\n\r\n  /*--Money Collected agency amount percentage*/\r\n  agencyMCPerCalc(){\r\n    this.selectedAgencyTarget.percentageMoneyCollected = Number((this.selectedAgencyTarget.targetMoneyCollected/(this.selectedAgency.totalPOS)*100).toFixed(2))\r\n  }\r\n\r\n\r\n  /*--Money Collected agency count percentage*/\r\n  agencyMCCountPerCalc(){\r\n    this.selectedAgencyTarget.accountPercentageMoneyCollected = Number((this.selectedAgencyTarget.accountCountMoneyCollected/(this.selectedAgency.totalAccount)*100).toFixed(2))\r\n  }\r\n\r\n\r\n\r\n  edit(){\r\n     this.router.navigateByUrl('/target/edit-target/'+ this.targetId);\r\n   \r\n  }\r\n\r\n  cancel(){\r\n          this.router.navigateByUrl('/home');\r\n\r\n  }\r\n}"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AAOjD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAsBlCC,YAAmBC,MAAqB,EAC9BC,aAA4B,EAC5BC,YAA4B,EAC5BC,KAAqB,EACrBC,MAAc;IAJL,KAAAJ,MAAM,GAANA,MAAM;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAzBT,KAAAC,cAAc,GAAG;MACxB;MACA;IAAA,CACE;IAKF,KAAAC,UAAU,GAAS,KAAK;IAExB,KAAAC,cAAc,GAAM;MAClBC,UAAU,EAAC;KACZ;IAED,KAAAC,MAAM,GAAG;MACPC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE;KACX;IAUG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACT,KAAK,CAACU,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MAC9C,IAAI,CAACE,EAAE,GAAGF,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAACR,cAAc,GAAG,CACpB;MAAEW,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,uBAAuB,GAAC,IAAI,CAACF;IAAE,CAAE,EAC1D;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE,uBAAuB,GAAC,IAAI,CAACF;IAAE,CAAE,CAC9D;IACL,IAAI,CAACG,UAAU,GAAG,EAAE;IACpB,IAAI,CAACA,UAAU,GAAI,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACD,UAAU,CAACC,UAAU,GAAI,IAAI,CAACA,UAAU;IAC7C,IAAI,CAACC,oBAAoB,GAAG;MAC1BC,gBAAgB,EAAC,CAAC;MAClBC,cAAc,EAAE,CAAC;MACjBC,gBAAgB,EAAE,CAAC;MACnBC,qBAAqB,EAAE,CAAC;MAExBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,CAAC;MAGtBC,gBAAgB,EAAE,CAAC;MACnBC,cAAc,EAAE,CAAC;MACjBC,qBAAqB,EAAE,CAAC;MACxBC,gBAAgB,EAAE,CAAC;MAEnBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,CAAC;MAEtBC,gBAAgB,EAAE,CAAC;MACnBC,oBAAoB,EAAE,CAAC;MACvBC,2BAA2B,EAAE,CAAC;MAC9BC,2BAA2B,EAAE,CAAC;MAE9BC,oBAAoB,EAAE,CAAC;MACvBC,wBAAwB,EAAE,CAAC;MAC3BC,0BAA0B,EAAE,CAAC;MAC7BC,+BAA+B,EAAE;KAClC;EAGH;EAIAC,QAAQA,CAAA;IACN,IAAI9B,EAAE,GAAG,IAAI,CAACZ,KAAK,CAAC2C,QAAQ,CAACjC,MAAM,CAACE,EAAE;IACtC,IAAI,CAACgC,QAAQ,GAAGhC,EAAE;IACjB,IAAI,CAACd,aAAa,CAAC+C,UAAU,CAACjC,EAAE,CAAC,CAC7BD,SAAS,CAACmC,IAAI,IAAG;MAChB,IAAI,CAACC,QAAQ,CAACD,IAAI,CAAC;IACzB,CAAC,CAAC;EAEJ;EAEAC,QAAQA,CAACD,IAAI;IACX,IAAI,CAAC/B,UAAU,GAAG+B,IAAI,CAAC,qBAAqB,CAAC;IAC7C,IAAI,CAAC9B,UAAU,GAAG,CAAC8B,IAAI,CAAC,eAAe,CAAC,CAAC;IACzC,IAAI,CAAC7B,oBAAoB,CAAC+B,QAAQ,GAAG,IAAI,CAAChC,UAAU,CAACgC,QAAQ;IAC7D,IAAI,CAAC5C,cAAc,GAAI,IAAI,CAACY,UAAU,CAAC,CAAC,CAAC;IACzC,IAAI,CAACC,oBAAoB,GAAI6B,IAAI,CAAC,qBAAqB,CAAC;IAExD,IAAI,CAAC7B,oBAAoB,CAACC,gBAAgB,GAAG4B,IAAI,CAAC,qBAAqB,CAAC,CAAC,0BAA0B,CAAC;IACpG,IAAI,CAAC7B,oBAAoB,CAACG,gBAAgB,GAAG0B,IAAI,CAAC,qBAAqB,CAAC,CAAC,0BAA0B,CAAC;IAEpG,IAAI,CAAC7B,oBAAoB,CAACK,cAAc,GAAGwB,IAAI,CAAC,qBAAqB,CAAC,CAAC,wBAAwB,CAAC;IAChG,IAAI,CAAC7B,oBAAoB,CAACO,cAAc,GAAGsB,IAAI,CAAC,qBAAqB,CAAC,CAAC,wBAAwB,CAAC;IAEhG,IAAI,CAAC7B,oBAAoB,CAACS,gBAAgB,GAAGoB,IAAI,CAAC,qBAAqB,CAAC,CAAC,0BAA0B,CAAC;IACpG,IAAI,CAAC7B,oBAAoB,CAACY,gBAAgB,GAAGiB,IAAI,CAAC,qBAAqB,CAAC,CAAC,0BAA0B,CAAC;IAEpG,IAAI,CAAC7B,oBAAoB,CAACiB,gBAAgB,GAAGY,IAAI,CAAC,qBAAqB,CAAC,CAAC,gCAAgC,CAAC;IAC1G,IAAI,CAAC7B,oBAAoB,CAACmB,2BAA2B,GAAGU,IAAI,CAAC,qBAAqB,CAAC,CAAC,gCAAgC,CAAC;IAErH,IAAI,CAAC7B,oBAAoB,CAACa,cAAc,GAAGgB,IAAI,CAAC,qBAAqB,CAAC,CAAC,wBAAwB,CAAC;IAChG,IAAI,CAAC7B,oBAAoB,CAACe,cAAc,GAAGc,IAAI,CAAC,qBAAqB,CAAC,CAAC,wBAAwB,CAAC;IAEhG,IAAI,CAACG,wBAAwB,EAAE;IAC/B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,qBAAqB,EAAE;IAE3B,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;EAC9B;EAEE;EACFd,wBAAwBA,CAAA;IACpB;IACA,IAAI,CAAClC,UAAU,CAAEiD,sBAAsB,GAAGC,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEG,gBAAgB,GACnC,IAAI,CAACH,UAAU,CAAEO,cAAc,GAC/B,IAAI,CAACP,UAAU,CAAEW,gBAAgB,EAAEwC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE/F;IACC,IAAI,CAACnD,UAAU,CAAEoD,sBAAsB,GAAGF,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEK,gBAAgB,GACnC,IAAI,CAACL,UAAU,CAAES,cAAc,GAC/B,IAAI,CAACT,UAAU,CAAEc,gBAAgB,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE9F;IACA,IAAI,CAACnD,UAAU,CAAEI,cAAc,GAAG8C,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEG,gBAAgB,GAAE,IAAI,CAACd,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3H,IAAI,CAACnD,UAAU,CAAEM,qBAAqB,GAAG4C,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEK,gBAAgB,GAAE,IAAI,CAAChB,cAAc,CAACiE,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;IAEtI;IACA,IAAI,CAACnD,UAAU,CAAEQ,YAAY,GAAG0C,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEO,cAAc,GAAE,IAAI,CAAClB,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IACvH,IAAI,CAACnD,UAAU,CAAEU,mBAAmB,GAAGwC,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAES,cAAc,GAAE,IAAI,CAACpB,cAAc,CAACiE,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;IAElI;IACA,IAAI,CAACnD,UAAU,CAAEY,cAAc,GAAGsC,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEW,gBAAgB,GAAE,IAAI,CAACtB,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3H,IAAI,CAACnD,UAAU,CAAEa,qBAAqB,GAAGqC,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEc,gBAAgB,GAAE,IAAI,CAACzB,cAAc,CAACiE,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;IAItI;IACA,IAAI,CAACnD,UAAU,CAAEoB,oBAAoB,GAAI8B,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEI,cAAc,GAC1B,IAAI,CAACJ,UAAU,CAAEQ,YAAY,GAC7B,IAAI,CAACR,UAAU,CAAEY,cAAc,EAAEuC,OAAO,CAAC,CAAC,CAAC,CAAC;IAGlG;IACA,IAAI,CAACnD,UAAU,CAAEsB,2BAA2B,GAAI4B,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEM,qBAAqB,GAChC,IAAI,CAACN,UAAU,CAAEU,mBAAmB,GACpC,IAAI,CAACV,UAAU,CAAEa,qBAAqB,EAAEsC,OAAO,CAAC,CAAC,CAAC,CAAC;IACjH;IACA,IAAI,CAACnD,UAAU,CAAEgB,YAAY,GAAGkC,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEe,cAAc,GAAE,IAAI,CAAC1B,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;IACvH,IAAI,CAACnD,UAAU,CAAEkB,mBAAmB,GAAGgC,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEiB,cAAc,GAAE,IAAI,CAAC5B,cAAc,CAACiE,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;EACtI;EAEA;EACAhB,oBAAoBA,CAAA;IACjB,IAAI,CAACnC,UAAU,CAAEwB,wBAAwB,GAAG0B,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEuB,oBAAoB,GAAE,IAAI,CAAClC,cAAc,CAACkE,WAAY,GAAC,GAAG,EAAEJ,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/I;EACA;EAEAf,qBAAqBA,CAAA;IAClB,IAAI,CAACpC,UAAU,CAAE0B,+BAA+B,GAAGwB,MAAM,CAAC,CAAC,IAAI,CAAClD,UAAU,CAAEyB,0BAA0B,GAAE,IAAI,CAACpC,cAAc,CAACiE,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;EAE7J;EACA;EACAd,iBAAiBA,CAAA;IACf,IAAI,CAACnC,oBAAoB,CAACE,cAAc,GAAG8C,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACC,gBAAgB,GAAE,IAAI,CAACd,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/I;EACA;EACAb,eAAeA,CAAA;IACb,IAAI,CAACpC,oBAAoB,CAACM,YAAY,GAAG0C,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACK,cAAc,GAAE,IAAI,CAAClB,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3I;EACA;EACAZ,iBAAiBA,CAAA;IACf,IAAI,CAACrC,oBAAoB,CAACU,cAAc,GAAGsC,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACS,gBAAgB,GAAE,IAAI,CAACtB,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/I;EAEA;EACAX,sBAAsBA,CAAA;IACpB,IAAI,CAACtC,oBAAoB,CAACI,qBAAqB,GAAG4C,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACG,gBAAgB,GAAE,IAAI,CAAChB,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EACtJ;EACA;EACAV,oBAAoBA,CAAA;IAClB,IAAI,CAACvC,oBAAoB,CAACQ,mBAAmB,GAAGwC,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACO,cAAc,GAAE,IAAI,CAACpB,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAClJ;EACA;EACAT,sBAAsBA,CAAA;IACpB,IAAI,CAACxC,oBAAoB,CAACW,qBAAqB,GAAGqC,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACY,gBAAgB,GAAE,IAAI,CAACzB,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EACtJ;EAGAR,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACzC,oBAAoB,CAACiB,gBAAgB,GAAG+B,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACC,gBAAgB,GAC/C,IAAI,CAACD,oBAAoB,CAACK,cAAc,GACxC,IAAI,CAACL,oBAAoB,CAACS,gBAAgB,EAAEwC,OAAO,CAAC,CAAC,CAAC,CAAC;IACrG;IACF,IAAI,CAACjD,oBAAoB,CAACkB,oBAAoB,GAAG8B,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACE,cAAc,GACjD,IAAI,CAACF,oBAAoB,CAACM,YAAY,GACtC,IAAI,CAACN,oBAAoB,CAACU,cAAc,EAAEuC,OAAO,CAAC,CAAC,CAAC,CAAC;EAEvG;EAEAP,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC1C,oBAAoB,CAACmB,2BAA2B,GAAG6B,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACG,gBAAgB,GAC1D,IAAI,CAACH,oBAAoB,CAACO,cAAc,GACxC,IAAI,CAACP,oBAAoB,CAACY,gBAAgB,EAAEqC,OAAO,CAAC,CAAC,CAAC,CAAC;IACvG;IACA,IAAI,CAACjD,oBAAoB,CAACoB,2BAA2B,GAAK4B,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACI,qBAAqB,GACjE,IAAI,CAACJ,oBAAoB,CAACQ,mBAAmB,GAC7C,IAAI,CAACR,oBAAoB,CAACW,qBAAqB,EAAEsC,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9G;EAGA;EACAN,eAAeA,CAAA;IACb,IAAI,CAAC3C,oBAAoB,CAACc,YAAY,GAAGkC,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACa,cAAc,GAAE,IAAI,CAAC1B,cAAc,CAAC0B,cAAe,GAAC,GAAG,EAAEoC,OAAO,CAAC,CAAC,CAAC,CAAC;EACjJ;EAEA;EACAL,oBAAoBA,CAAA;IAClB,IAAI,CAAC5C,oBAAoB,CAACgB,mBAAmB,GAAGgC,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACe,cAAc,GAAE,IAAI,CAAC5B,cAAc,CAAC4B,cAAe,GAAC,GAAG,EAAEkC,OAAO,CAAC,CAAC,CAAC,CAAC;EACxJ;EAGA;EACAJ,eAAeA,CAAA;IACb,IAAI,CAAC7C,oBAAoB,CAACsB,wBAAwB,GAAG0B,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACqB,oBAAoB,GAAE,IAAI,CAAClC,cAAc,CAACgE,QAAS,GAAC,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7J;EAGA;EACAH,oBAAoBA,CAAA;IAClB,IAAI,CAAC9C,oBAAoB,CAACwB,+BAA+B,GAAGwB,MAAM,CAAC,CAAC,IAAI,CAAChD,oBAAoB,CAACuB,0BAA0B,GAAE,IAAI,CAACpC,cAAc,CAACiE,YAAa,GAAC,GAAG,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9K;EAIAK,IAAIA,CAAA;IACD,IAAI,CAACtE,MAAM,CAACuE,aAAa,CAAC,sBAAsB,GAAE,IAAI,CAAC5B,QAAQ,CAAC;EAEnE;EAEA6B,MAAMA,CAAA;IACE,IAAI,CAACxE,MAAM,CAACuE,aAAa,CAAC,OAAO,CAAC;EAE1C;;;;;;;;;;;;;;;AArQW7E,uBAAuB,GAAA+E,UAAA,EALnCrF,SAAS,CAAC;EACTsF,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;;CAEjD,CAAC,C,EACWlF,uBAAuB,CAsQnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}