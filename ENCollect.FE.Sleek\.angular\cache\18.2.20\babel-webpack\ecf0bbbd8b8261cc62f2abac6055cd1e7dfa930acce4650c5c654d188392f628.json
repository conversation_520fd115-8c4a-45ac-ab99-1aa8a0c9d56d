{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { AddTrailComponent } from './add-trail.component';\ndescribe('AddTrailComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [AddTrailComponent]\n    });\n    fixture = TestBed.createComponent(AddTrailComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "AddTrailComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\add-trail\\add-trail.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { AddTrailComponent } from './add-trail.component';\r\n\r\ndescribe('AddTrailComponent', () => {\r\n  let component: AddTrailComponent;\r\n  let fixture: ComponentFixture<AddTrailComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [AddTrailComponent]\r\n    });\r\n    fixture = TestBed.createComponent(AddTrailComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,iBAAiB,QAAQ,uBAAuB;AAEzDC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,SAA4B;EAChC,IAAIC,OAA4C;EAEhDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,iBAAiB;KACjC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,iBAAiB,CAAC;IACpDE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}