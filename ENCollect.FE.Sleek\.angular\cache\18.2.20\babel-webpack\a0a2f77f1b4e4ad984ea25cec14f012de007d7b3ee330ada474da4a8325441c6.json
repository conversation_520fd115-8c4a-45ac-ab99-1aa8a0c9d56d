{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./paginations.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./paginations.component.css?ngResource\";\nimport { Component, Input } from '@angular/core';\nlet PaginationsComponent = class PaginationsComponent {\n  constructor() {\n    this.currentRecords = [];\n    this.results = [];\n    // super()\n    this.currentPage = 1;\n    this.itemsPerPage = 5;\n  }\n  ngOnInit() {}\n  changeItemsPerPage() {\n    this.currentRecords = this.fetchRecordsByPage(1);\n  }\n  fetchRecordsByPage(page) {\n    const results = this.results;\n    return results.slice((page - 1) * this.itemsPerPage, this.itemsPerPage * page);\n  }\n  pageChange(event) {\n    this.currentRecords = this.fetchRecordsByPage(event.page);\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      currentRecords: [{\n        type: Input\n      }],\n      results: [{\n        type: Input\n      }]\n    };\n  }\n};\nPaginationsComponent = __decorate([Component({\n  selector: 'app-paginations',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], PaginationsComponent);\nexport { PaginationsComponent };", "map": {"version": 3, "names": ["Component", "Input", "PaginationsComponent", "constructor", "currentRecords", "results", "currentPage", "itemsPerPage", "ngOnInit", "changeItemsPerPage", "fetchRecordsByPage", "page", "slice", "pageChange", "event", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\common\\paginations\\paginations.component.ts"], "sourcesContent": ["import { Component, OnInit,Input,ViewChild } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-paginations',\r\n  templateUrl: './paginations.component.html',\r\n  styleUrls: ['./paginations.component.css']\r\n})\r\nexport class PaginationsComponent implements OnInit {\r\n\r\n  @Input() currentRecords = [];\r\n  @Input() results = [];\r\n  currentPage: number;\r\n  itemsPerPage: number;\r\n  constructor() {\r\n    // super()\r\n  \tthis.currentPage = 1;\r\n  \tthis.itemsPerPage = 5;\r\n  }\r\n  ngOnInit() {\r\n\r\n  }\r\n  changeItemsPerPage(): void {\r\n    this.currentRecords = this.fetchRecordsByPage(1);\r\n  }\r\n\r\n  fetchRecordsByPage(page: number): any {\r\n    const results = this.results;\r\n    return results.slice((page - 1) * this.itemsPerPage, this.itemsPerPage * page);\r\n  }\r\n\r\n  pageChange(event: any): void {\r\n    this.currentRecords = this.fetchRecordsByPage(event.page);\r\n  }\r\n \r\n}"], "mappings": ";;;AAAA,SAASA,SAAS,EAASC,KAAK,QAAkB,eAAe;AAO1D,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAM/BC,YAAA;IAJS,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,OAAO,GAAG,EAAE;IAInB;IACD,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;EACtB;EACAC,QAAQA,CAAA,GAER;EACAC,kBAAkBA,CAAA;IAChB,IAAI,CAACL,cAAc,GAAG,IAAI,CAACM,kBAAkB,CAAC,CAAC,CAAC;EAClD;EAEAA,kBAAkBA,CAACC,IAAY;IAC7B,MAAMN,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,OAAOA,OAAO,CAACO,KAAK,CAAC,CAACD,IAAI,GAAG,CAAC,IAAI,IAAI,CAACJ,YAAY,EAAE,IAAI,CAACA,YAAY,GAAGI,IAAI,CAAC;EAChF;EAEAE,UAAUA,CAACC,KAAU;IACnB,IAAI,CAACV,cAAc,GAAG,IAAI,CAACM,kBAAkB,CAACI,KAAK,CAACH,IAAI,CAAC;EAC3D;;;;;;;cAvBCV;MAAK;;cACLA;MAAK;;;;AAHKC,oBAAoB,GAAAa,UAAA,EALhCf,SAAS,CAAC;EACTgB,QAAQ,EAAE,iBAAiB;EAC3BC,QAAA,EAAAC,oBAA2C;;CAE5C,CAAC,C,EACWhB,oBAAoB,CA2BhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}