{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { PrimaryAllocationInsightFilterComponent } from './primary-allocation-insight-filter.component';\ndescribe('PrimaryAllocationInsightFilterComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [PrimaryAllocationInsightFilterComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(PrimaryAllocationInsightFilterComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "PrimaryAllocationInsightFilterComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\insights\\components\\primary-allocation-insight-filter\\primary-allocation-insight-filter.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { PrimaryAllocationInsightFilterComponent } from './primary-allocation-insight-filter.component';\r\n\r\ndescribe('PrimaryAllocationInsightFilterComponent', () => {\r\n  let component: PrimaryAllocationInsightFilterComponent;\r\n  let fixture: ComponentFixture<PrimaryAllocationInsightFilterComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [PrimaryAllocationInsightFilterComponent]\r\n    })\r\n    .compileComponents();\r\n    \r\n    fixture = TestBed.createComponent(PrimaryAllocationInsightFilterComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,uCAAuC,QAAQ,+CAA+C;AAEvGC,QAAQ,CAAC,yCAAyC,EAAE,MAAK;EACvD,IAAIC,SAAkD;EACtD,IAAIC,OAAkE;EAEtEC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,uCAAuC;KAClD,CAAC,CACDQ,iBAAiB,EAAE;IAEpBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,uCAAuC,CAAC;IAC1EE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}