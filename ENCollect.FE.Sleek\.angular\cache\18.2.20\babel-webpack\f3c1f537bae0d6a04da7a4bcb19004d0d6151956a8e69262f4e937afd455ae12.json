{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵDeferBlockState, ɵtriggerResourceLoading, ɵrenderDeferBlockState, ɵCONTAINER_HEADER_OFFSET, ɵgetDeferBlocks, ɵDeferBlockBehavior, InjectionToken, inject as inject$1, NgZone, ErrorHandler, Injectable, ɵNoopNgZone, ɵEffectScheduler, ApplicationRef, ɵPendingTasks, getDebugNode, RendererFactory2, ɵdetectChangesInViewIfRequired, ɵstringify, ɵReflectionCapabilities, Directive, Component, Pipe, NgModule, ɵgetAsyncClassMetadataFn, ɵgenerateStandaloneInDeclarationsError, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker, ɵgetInjectableDef, resolveForwardRef, ɵNG_COMP_DEF, ɵisComponentDefPendingResolution, ɵresolveComponentResources, ɵRender3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID, ɵsetLocaleId, ɵRender3ComponentFactory, ɵcompileComponent, ɵNG_DIR_DEF, ɵcompileDirective, ɵNG_PIPE_DEF, ɵcompilePipe, ɵNG_MOD_DEF, ɵtransitiveScopesFor, ɵpatchComponentDefWithScope, ɵNG_INJ_DEF, ɵcompileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue, ɵinternalProvideZoneChangeDetection, ɵINTERNAL_APPLICATION_ERROR_HANDLER, ɵZONELESS_ENABLED, ɵChangeDetectionScheduler, ɵChangeDetectionSchedulerImpl, Compiler, ɵDEFER_BLOCK_CONFIG, COMPILER_OPTIONS, Injector, ɵisEnvironmentProviders, ɵNgModuleFactory, ModuleWithComponentFactories, ɵconvertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents, ɵsetUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode, runInInjectionContext, EnvironmentInjector, ɵflushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n  const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n  if (!_Zone) {\n    return function () {\n      return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js');\n    };\n  }\n  const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n  if (typeof asyncTest === 'function') {\n    return asyncTest(fn);\n  }\n  return function () {\n    return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/testing');\n  };\n}\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n */\nclass DeferBlockFixture {\n  /** @nodoc */\n  constructor(block, componentFixture) {\n    this.block = block;\n    this.componentFixture = componentFixture;\n  }\n  /**\n   * Renders the specified state of the defer fixture.\n   * @param state the defer state to render\n   */\n  render(state) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!hasStateTemplate(state, _this.block)) {\n        const stateAsString = getDeferBlockStateNameFromEnum(state);\n        throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` + `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n      }\n      if (state === ɵDeferBlockState.Complete) {\n        yield ɵtriggerResourceLoading(_this.block.tDetails, _this.block.lView, _this.block.tNode);\n      }\n      // If the `render` method is used explicitly - skip timer-based scheduling for\n      // `@placeholder` and `@loading` blocks and render them immediately.\n      const skipTimerScheduling = true;\n      ɵrenderDeferBlockState(state, _this.block.tNode, _this.block.lContainer, skipTimerScheduling);\n      _this.componentFixture.detectChanges();\n    })();\n  }\n  /**\n   * Retrieves all nested child defer block fixtures\n   * in a given defer block.\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    // An LContainer that represents a defer block has at most 1 view, which is\n    // located right after an LContainer header. Get a hold of that view and inspect\n    // it for nested defer blocks.\n    const deferBlockFixtures = [];\n    if (this.block.lContainer.length >= ɵCONTAINER_HEADER_OFFSET) {\n      const lView = this.block.lContainer[ɵCONTAINER_HEADER_OFFSET];\n      ɵgetDeferBlocks(lView, deferBlocks);\n      for (const block of deferBlocks) {\n        deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n      }\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n}\nfunction hasStateTemplate(state, block) {\n  switch (state) {\n    case ɵDeferBlockState.Placeholder:\n      return block.tDetails.placeholderTmplIndex !== null;\n    case ɵDeferBlockState.Loading:\n      return block.tDetails.loadingTmplIndex !== null;\n    case ɵDeferBlockState.Error:\n      return block.tDetails.errorTmplIndex !== null;\n    case ɵDeferBlockState.Complete:\n      return true;\n    default:\n      return false;\n  }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n  switch (state) {\n    case ɵDeferBlockState.Placeholder:\n      return 'Placeholder';\n    case ɵDeferBlockState.Loading:\n      return 'Loading';\n    case ɵDeferBlockState.Error:\n      return 'Error';\n    default:\n      return 'Main';\n  }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = ɵDeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n  insertRootElement(rootElementId) {}\n  removeAllRootElements() {}\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\nconst RETHROW_APPLICATION_ERRORS = new InjectionToken('rethrow application errors');\nclass TestBedApplicationErrorHandler {\n  constructor() {\n    this.zone = inject$1(NgZone);\n    this.userErrorHandler = inject$1(ErrorHandler);\n    this.whenStableRejectFunctions = new Set();\n  }\n  handleError(e) {\n    try {\n      this.zone.runOutsideAngular(() => this.userErrorHandler.handleError(e));\n    } catch (userError) {\n      e = userError;\n    }\n    // Instead of throwing the error when there are outstanding `fixture.whenStable` promises,\n    // reject those promises with the error. This allows developers to write\n    // expectAsync(fix.whenStable()).toBeRejected();\n    if (this.whenStableRejectFunctions.size > 0) {\n      for (const fn of this.whenStableRejectFunctions.values()) {\n        fn(e);\n      }\n      this.whenStableRejectFunctions.clear();\n    } else {\n      throw e;\n    }\n  }\n  static {\n    this.ɵfac = i0.ɵɵngDeclareFactory({\n      minVersion: \"12.0.0\",\n      version: \"18.2.13\",\n      ngImport: i0,\n      type: TestBedApplicationErrorHandler,\n      deps: [],\n      target: i0.ɵɵFactoryTarget.Injectable\n    });\n  }\n  static {\n    this.ɵprov = i0.ɵɵngDeclareInjectable({\n      minVersion: \"12.0.0\",\n      version: \"18.2.13\",\n      ngImport: i0,\n      type: TestBedApplicationErrorHandler\n    });\n  }\n}\ni0.ɵɵngDeclareClassMetadata({\n  minVersion: \"12.0.0\",\n  version: \"18.2.13\",\n  ngImport: i0,\n  type: TestBedApplicationErrorHandler,\n  decorators: [{\n    type: Injectable\n  }]\n});\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n  /** @nodoc */\n  constructor(componentRef) {\n    this.componentRef = componentRef;\n    this._isDestroyed = false;\n    /** @internal */\n    this._noZoneOptionIsSet = inject$1(ComponentFixtureNoNgZone, {\n      optional: true\n    });\n    /** @internal */\n    this._ngZone = this._noZoneOptionIsSet ? new ɵNoopNgZone() : inject$1(NgZone);\n    /** @internal */\n    this._effectRunner = inject$1(ɵEffectScheduler);\n    // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n    // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n    // This is a crazy way of doing things but hey, it's the world we live in.\n    // The zoneless scheduler should instead do this more imperatively by attaching\n    // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n    // behavior.\n    /** @internal */\n    this._appRef = inject$1(ApplicationRef);\n    /** @internal */\n    this._testAppRef = this._appRef;\n    this.pendingTasks = inject$1(ɵPendingTasks);\n    this.appErrorHandler = inject$1(TestBedApplicationErrorHandler);\n    // TODO(atscott): Remove this from public API\n    this.ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n  }\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges() {\n    this.changeDetectorRef.checkNoChanges();\n  }\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  isStable() {\n    return !this.pendingTasks.hasPendingTasks.value;\n  }\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  whenStable() {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    }\n    return new Promise((resolve, reject) => {\n      this.appErrorHandler.whenStableRejectFunctions.add(reject);\n      this._appRef.whenStable().then(() => {\n        this.appErrorHandler.whenStableRejectFunctions.delete(reject);\n        resolve(true);\n      });\n    });\n  }\n  /**\n   * Retrieves all defer block fixtures in the component fixture.\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    const lView = this.componentRef.hostView['_lView'];\n    ɵgetDeferBlocks(lView, deferBlocks);\n    const deferBlockFixtures = [];\n    for (const block of deferBlocks) {\n      deferBlockFixtures.push(new DeferBlockFixture(block, this));\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n  _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer;\n  }\n  /**\n   * Get a promise that resolves when the ui state is stable following animations.\n   */\n  whenRenderingDone() {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n  /**\n   * Trigger component destruction.\n   */\n  destroy() {\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      this._isDestroyed = true;\n    }\n  }\n}\n/**\n * ComponentFixture behavior that actually attaches the component to the application to ensure\n * behaviors between fixture and application do not diverge. `detectChanges` is disabled by default\n * (instead, tests should wait for the scheduler to detect changes), `whenStable` is directly the\n * `ApplicationRef.isStable`, and `autoDetectChanges` cannot be disabled.\n */\nclass ScheduledComponentFixture extends ComponentFixture {\n  constructor() {\n    super(...arguments);\n    this._autoDetect = inject$1(ComponentFixtureAutoDetect, {\n      optional: true\n    }) ?? true;\n  }\n  initialize() {\n    if (this._autoDetect) {\n      this._appRef.attachView(this.componentRef.hostView);\n    }\n  }\n  detectChanges(checkNoChanges = true) {\n    if (!checkNoChanges) {\n      throw new Error('Cannot disable `checkNoChanges` in this configuration. ' + 'Use `fixture.componentRef.hostView.changeDetectorRef.detectChanges()` instead.');\n    }\n    this._effectRunner.flush();\n    this._appRef.tick();\n    this._effectRunner.flush();\n  }\n  autoDetectChanges(autoDetect = true) {\n    if (!autoDetect) {\n      throw new Error('Cannot disable autoDetect after it has been enabled when using the zoneless scheduler. ' + 'To disable autoDetect, add `{provide: ComponentFixtureAutoDetect, useValue: false}` to the TestBed providers.');\n    } else if (!this._autoDetect) {\n      this._autoDetect = autoDetect;\n      this._appRef.attachView(this.componentRef.hostView);\n    }\n    this.detectChanges();\n  }\n}\n/**\n * ComponentFixture behavior that attempts to act as a \"mini application\".\n */\nclass PseudoApplicationComponentFixture extends ComponentFixture {\n  constructor() {\n    super(...arguments);\n    this._subscriptions = new Subscription();\n    this._autoDetect = inject$1(ComponentFixtureAutoDetect, {\n      optional: true\n    }) ?? false;\n    this.afterTickSubscription = undefined;\n    this.beforeRenderSubscription = undefined;\n  }\n  initialize() {\n    if (this._autoDetect) {\n      this.subscribeToAppRefEvents();\n    }\n    this.componentRef.hostView.onDestroy(() => {\n      this.unsubscribeFromAppRefEvents();\n    });\n    // Create subscriptions outside the NgZone so that the callbacks run outside\n    // of NgZone.\n    this._ngZone.runOutsideAngular(() => {\n      this._subscriptions.add(this._ngZone.onError.subscribe({\n        next: error => {\n          throw error;\n        }\n      }));\n    });\n  }\n  detectChanges(checkNoChanges = true) {\n    this._effectRunner.flush();\n    // Run the change detection inside the NgZone so that any async tasks as part of the change\n    // detection are captured by the zone and can be waited for in isStable.\n    this._ngZone.run(() => {\n      this.changeDetectorRef.detectChanges();\n      if (checkNoChanges) {\n        this.checkNoChanges();\n      }\n    });\n    // Run any effects that were created/dirtied during change detection. Such effects might become\n    // dirty in response to input signals changing.\n    this._effectRunner.flush();\n  }\n  autoDetectChanges(autoDetect = true) {\n    if (this._noZoneOptionIsSet) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n    }\n    if (autoDetect !== this._autoDetect) {\n      if (autoDetect) {\n        this.subscribeToAppRefEvents();\n      } else {\n        this.unsubscribeFromAppRefEvents();\n      }\n    }\n    this._autoDetect = autoDetect;\n    this.detectChanges();\n  }\n  subscribeToAppRefEvents() {\n    this._ngZone.runOutsideAngular(() => {\n      this.afterTickSubscription = this._testAppRef.afterTick.subscribe(() => {\n        this.checkNoChanges();\n      });\n      this.beforeRenderSubscription = this._testAppRef.beforeRender.subscribe(isFirstPass => {\n        try {\n          ɵdetectChangesInViewIfRequired(this.componentRef.hostView._lView, this.componentRef.hostView.notifyErrorHandler, isFirstPass, false /** zoneless enabled */);\n        } catch (e) {\n          // If an error occurred during change detection, remove the test view from the application\n          // ref tracking. Note that this isn't exactly desirable but done this way because of how\n          // things used to work with `autoDetect` and uncaught errors. Ideally we would surface\n          // this error to the error handler instead and continue refreshing the view like\n          // what would happen in the application.\n          this.unsubscribeFromAppRefEvents();\n          throw e;\n        }\n      });\n      this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n    });\n  }\n  unsubscribeFromAppRefEvents() {\n    this.afterTickSubscription?.unsubscribe();\n    this.beforeRenderSubscription?.unsubscribe();\n    this.afterTickSubscription = undefined;\n    this.beforeRenderSubscription = undefined;\n    this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n  }\n  destroy() {\n    this.unsubscribeFromAppRefEvents();\n    this._subscriptions.unsubscribe();\n    super.destroy();\n  }\n}\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n  if (fakeAsyncTestModule) {\n    fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n * @param options\n *   - flush: When true, will drain the macrotask queue after the test function completes.\n *     When false, will throw an exception at the end of the function if there are pending timers.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn, options) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.fakeAsync(fn, options);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n  processNewMacroTasksSynchronously: true\n}) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.tick(millis, tickOptions);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flush(maxTurns);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.discardPeriodicTasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flushMicrotasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n  constructor() {\n    this._references = new Map();\n  }\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata(metadataClass, oldMetadata, override) {\n    const props = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach(prop => props[prop] = oldMetadata[prop]);\n    }\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${ɵstringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(props);\n  }\n}\nfunction removeMetadata(metadata, remove, references) {\n  const removeObjects = new Set();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (Array.isArray(removeValue)) {\n      removeValue.forEach(value => {\n        removeObjects.add(_propHashKey(prop, value, references));\n      });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (Array.isArray(propValue)) {\n      metadata[prop] = propValue.filter(value => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\nfunction addMetadata(metadata, add) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && Array.isArray(propValue)) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\nfunction setMetadata(metadata, set) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\nfunction _propHashKey(propName, propValue, references) {\n  let nextObjectId = 0;\n  const objectIds = new Map();\n  const replacer = (key, value) => {\n    if (value !== null && typeof value === 'object') {\n      if (objectIds.has(value)) {\n        return objectIds.get(value);\n      }\n      // Record an id for this object such that any later references use the object's id instead\n      // of the object itself, in order to break cyclic pointers in objects.\n      objectIds.set(value, `ɵobj#${nextObjectId++}`);\n      // The first time an object is seen the object itself is serialized.\n      return value;\n    } else if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${ɵstringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\nfunction _valueProps(obj) {\n  const props = [];\n  // regular public props\n  Object.keys(obj).forEach(prop => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach(protoProp => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\nconst reflection = new ɵReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n  constructor() {\n    this.overrides = new Map();\n    this.resolved = new Map();\n  }\n  addOverride(type, override) {\n    const overrides = this.overrides.get(type) || [];\n    overrides.push(override);\n    this.overrides.set(type, overrides);\n    this.resolved.delete(type);\n  }\n  setOverrides(overrides) {\n    this.overrides.clear();\n    overrides.forEach(([type, override]) => {\n      this.addOverride(type, override);\n    });\n  }\n  getAnnotation(type) {\n    const annotations = reflection.annotations(type);\n    // Try to find the nearest known Type annotation and make sure that this annotation is an\n    // instance of the type we are looking for, so we can use it for resolution. Note: there might\n    // be multiple known annotations found due to the fact that Components can extend Directives (so\n    // both Directive and Component annotations would be present), so we always check if the known\n    // annotation has the right type.\n    for (let i = annotations.length - 1; i >= 0; i--) {\n      const annotation = annotations[i];\n      const isKnownType = annotation instanceof Directive || annotation instanceof Component || annotation instanceof Pipe || annotation instanceof NgModule;\n      if (isKnownType) {\n        return annotation instanceof this.type ? annotation : null;\n      }\n    }\n    return null;\n  }\n  resolve(type) {\n    let resolved = this.resolved.get(type) || null;\n    if (!resolved) {\n      resolved = this.getAnnotation(type);\n      if (resolved) {\n        const overrides = this.overrides.get(type);\n        if (overrides) {\n          const overrider = new MetadataOverrider();\n          overrides.forEach(override => {\n            resolved = overrider.overrideMetadata(this.type, resolved, override);\n          });\n        }\n      }\n      this.resolved.set(type, resolved);\n    }\n    return resolved;\n  }\n}\nclass DirectiveResolver extends OverrideResolver {\n  get type() {\n    return Directive;\n  }\n}\nclass ComponentResolver extends OverrideResolver {\n  get type() {\n    return Component;\n  }\n}\nclass PipeResolver extends OverrideResolver {\n  get type() {\n    return Pipe;\n  }\n}\nclass NgModuleResolver extends OverrideResolver {\n  get type() {\n    return NgModule;\n  }\n}\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n  TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n  TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n  return value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE;\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n  types.forEach(type => {\n    if (!ɵgetAsyncClassMetadataFn(type)) {\n      const component = resolver.resolve(type);\n      if (component && component.standalone) {\n        throw new Error(ɵgenerateStandaloneInDeclarationsError(type, location));\n      }\n    }\n  });\n}\nclass TestBedCompiler {\n  constructor(platform, additionalModuleTypes) {\n    this.platform = platform;\n    this.additionalModuleTypes = additionalModuleTypes;\n    this.originalComponentResolutionQueue = null;\n    // Testing module configuration\n    this.declarations = [];\n    this.imports = [];\n    this.providers = [];\n    this.schemas = [];\n    // Queues of components/directives/pipes that should be recompiled.\n    this.pendingComponents = new Set();\n    this.pendingDirectives = new Set();\n    this.pendingPipes = new Set();\n    // Set of components with async metadata, i.e. components with `@defer` blocks\n    // in their templates.\n    this.componentsWithAsyncMetadata = new Set();\n    // Keep track of all components and directives, so we can patch Providers onto defs later.\n    this.seenComponents = new Set();\n    this.seenDirectives = new Set();\n    // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n    this.overriddenModules = new Set();\n    // Store resolved styles for Components that have template overrides present and `styleUrls`\n    // defined at the same time.\n    this.existingComponentStyles = new Map();\n    this.resolvers = initResolvers();\n    // Map of component type to an NgModule that declares it.\n    //\n    // There are a couple special cases:\n    // - for standalone components, the module scope value is `null`\n    // - when a component is declared in `TestBed.configureTestingModule()` call or\n    //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n    //   we use a special value from the `TestingModuleOverride` enum.\n    this.componentToModuleScope = new Map();\n    // Map that keeps initial version of component/directive/pipe defs in case\n    // we compile a Type again, thus overriding respective static fields. This is\n    // required to make sure we restore defs to their initial states between test runs.\n    // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n    // NgModule), store all of them in a map.\n    this.initialNgDefs = new Map();\n    // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n    // defs in case TestBed makes changes to the originals.\n    this.defCleanupOps = [];\n    this._injector = null;\n    this.compilerProviders = null;\n    this.providerOverrides = [];\n    this.rootProviderOverrides = [];\n    // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n    // module's provider list.\n    this.providerOverridesByModule = new Map();\n    this.providerOverridesByToken = new Map();\n    this.scopesWithOverriddenProviders = new Set();\n    this.testModuleRef = null;\n    this.deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    class DynamicTestModule {}\n    this.testModuleType = DynamicTestModule;\n  }\n  setCompilerProviders(providers) {\n    this.compilerProviders = providers;\n    this._injector = null;\n  }\n  configureTestingModule(moduleDef) {\n    // Enqueue any compilation tasks for the directly declared component.\n    if (moduleDef.declarations !== undefined) {\n      // Verify that there are no standalone components\n      assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n      this.declarations.push(...moduleDef.declarations);\n    }\n    // Enqueue any compilation tasks for imported modules.\n    if (moduleDef.imports !== undefined) {\n      this.queueTypesFromModulesArray(moduleDef.imports);\n      this.imports.push(...moduleDef.imports);\n    }\n    if (moduleDef.providers !== undefined) {\n      this.providers.push(...moduleDef.providers);\n    }\n    this.providers.push({\n      provide: RETHROW_APPLICATION_ERRORS,\n      useValue: moduleDef._rethrowApplicationTickErrors ?? false\n    });\n    if (moduleDef.schemas !== undefined) {\n      this.schemas.push(...moduleDef.schemas);\n    }\n    this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n  }\n  overrideModule(ngModule, override) {\n    if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n      ɵdepsTracker.clearScopeCacheFor(ngModule);\n    }\n    this.overriddenModules.add(ngModule);\n    // Compile the module right away.\n    this.resolvers.module.addOverride(ngModule, override);\n    const metadata = this.resolvers.module.resolve(ngModule);\n    if (metadata === null) {\n      throw invalidTypeError(ngModule.name, 'NgModule');\n    }\n    this.recompileNgModule(ngModule, metadata);\n    // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n    // new declarations or imported modules. Ingest any possible new types and add them to the\n    // current queue.\n    this.queueTypesFromModulesArray([ngModule]);\n  }\n  overrideComponent(component, override) {\n    this.verifyNoStandaloneFlagOverrides(component, override);\n    this.resolvers.component.addOverride(component, override);\n    this.pendingComponents.add(component);\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(component);\n  }\n  overrideDirective(directive, override) {\n    this.verifyNoStandaloneFlagOverrides(directive, override);\n    this.resolvers.directive.addOverride(directive, override);\n    this.pendingDirectives.add(directive);\n  }\n  overridePipe(pipe, override) {\n    this.verifyNoStandaloneFlagOverrides(pipe, override);\n    this.resolvers.pipe.addOverride(pipe, override);\n    this.pendingPipes.add(pipe);\n  }\n  verifyNoStandaloneFlagOverrides(type, override) {\n    if (override.add?.hasOwnProperty('standalone') || override.set?.hasOwnProperty('standalone') || override.remove?.hasOwnProperty('standalone')) {\n      throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` + `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n    }\n  }\n  overrideProvider(token, provider) {\n    let providerDef;\n    if (provider.useFactory !== undefined) {\n      providerDef = {\n        provide: token,\n        useFactory: provider.useFactory,\n        deps: provider.deps || [],\n        multi: provider.multi\n      };\n    } else if (provider.useValue !== undefined) {\n      providerDef = {\n        provide: token,\n        useValue: provider.useValue,\n        multi: provider.multi\n      };\n    } else {\n      providerDef = {\n        provide: token\n      };\n    }\n    const injectableDef = typeof token !== 'string' ? ɵgetInjectableDef(token) : null;\n    const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n    const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n    overridesBucket.push(providerDef);\n    // Keep overrides grouped by token as well for fast lookups using token\n    this.providerOverridesByToken.set(token, providerDef);\n    if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n      const existingOverrides = this.providerOverridesByModule.get(providedIn);\n      if (existingOverrides !== undefined) {\n        existingOverrides.push(providerDef);\n      } else {\n        this.providerOverridesByModule.set(providedIn, [providerDef]);\n      }\n    }\n  }\n  overrideTemplateUsingTestingModule(type, template) {\n    const def = type[ɵNG_COMP_DEF];\n    const hasStyleUrls = () => {\n      const metadata = this.resolvers.component.resolve(type);\n      return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n    };\n    const overrideStyleUrls = !!def && !ɵisComponentDefPendingResolution(type) && hasStyleUrls();\n    // In Ivy, compiling a component does not require knowing the module providing the\n    // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n    // overrideComponent. Important: overriding template requires full Component re-compilation,\n    // which may fail in case styleUrls are also present (thus Component is considered as required\n    // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n    // preserve current styles available on Component def and restore styles back once compilation\n    // is complete.\n    const override = overrideStyleUrls ? {\n      template,\n      styles: [],\n      styleUrls: [],\n      styleUrl: undefined\n    } : {\n      template\n    };\n    this.overrideComponent(type, {\n      set: override\n    });\n    if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n      this.existingComponentStyles.set(type, def.styles);\n    }\n    // Set the component's scope to be the testing module.\n    this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n  }\n  resolvePendingComponentsWithAsyncMetadata() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.componentsWithAsyncMetadata.size === 0) return;\n      const promises = [];\n      for (const component of _this2.componentsWithAsyncMetadata) {\n        const asyncMetadataFn = ɵgetAsyncClassMetadataFn(component);\n        if (asyncMetadataFn) {\n          promises.push(asyncMetadataFn());\n        }\n      }\n      _this2.componentsWithAsyncMetadata.clear();\n      const resolvedDeps = yield Promise.all(promises);\n      const flatResolvedDeps = resolvedDeps.flat(2);\n      _this2.queueTypesFromModulesArray(flatResolvedDeps);\n      // Loaded standalone components might contain imports of NgModules\n      // with providers, make sure we override providers there too.\n      for (const component of flatResolvedDeps) {\n        _this2.applyProviderOverridesInScope(component);\n      }\n    })();\n  }\n  compileComponents() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.clearComponentResolutionQueue();\n      // Wait for all async metadata for components that were\n      // overridden, we need resolved metadata to perform an override\n      // and re-compile a component.\n      yield _this3.resolvePendingComponentsWithAsyncMetadata();\n      // Verify that there were no standalone components present in the `declarations` field\n      // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n      // to the logic in the `configureTestingModule` function, since at this point we have\n      // all async metadata resolved.\n      assertNoStandaloneComponents(_this3.declarations, _this3.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      // Run compilers for all queued types.\n      let needsAsyncResources = _this3.compileTypesSync();\n      // compileComponents() should not be async unless it needs to be.\n      if (needsAsyncResources) {\n        let resourceLoader;\n        let resolver = url => {\n          if (!resourceLoader) {\n            resourceLoader = _this3.injector.get(ResourceLoader);\n          }\n          return Promise.resolve(resourceLoader.get(url));\n        };\n        yield ɵresolveComponentResources(resolver);\n      }\n    })();\n  }\n  finalize() {\n    // One last compile\n    this.compileTypesSync();\n    // Create the testing module itself.\n    this.compileTestModule();\n    this.applyTransitiveScopes();\n    this.applyProviderOverrides();\n    // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n    // Components have `styleUrls` fields defined and template override was requested.\n    this.patchComponentsWithExistingStyles();\n    // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n    // every component.\n    this.componentToModuleScope.clear();\n    const parentInjector = this.platform.injector;\n    this.testModuleRef = new ɵRender3NgModuleRef(this.testModuleType, parentInjector, []);\n    // ApplicationInitStatus.runInitializers() is marked @internal to core.\n    // Cast it to any before accessing it.\n    this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n    // Set locale ID after running app initializers, since locale information might be updated while\n    // running initializers. This is also consistent with the execution order while bootstrapping an\n    // app (see `packages/core/src/application_ref.ts` file).\n    const localeId = this.testModuleRef.injector.get(LOCALE_ID, ɵDEFAULT_LOCALE_ID);\n    ɵsetLocaleId(localeId);\n    return this.testModuleRef;\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleSync(moduleType) {\n    this.queueTypesFromModulesArray([moduleType]);\n    this.compileTypesSync();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleAsync(moduleType) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.queueTypesFromModulesArray([moduleType]);\n      yield _this4.compileComponents();\n      _this4.applyProviderOverrides();\n      _this4.applyProviderOverridesInScope(moduleType);\n      _this4.applyTransitiveScopes();\n    })();\n  }\n  /**\n   * @internal\n   */\n  _getModuleResolver() {\n    return this.resolvers.module;\n  }\n  /**\n   * @internal\n   */\n  _getComponentFactories(moduleType) {\n    return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n      const componentDef = declaration.ɵcmp;\n      componentDef && factories.push(new ɵRender3ComponentFactory(componentDef, this.testModuleRef));\n      return factories;\n    }, []);\n  }\n  compileTypesSync() {\n    // Compile all queued components, directives, pipes.\n    let needsAsyncResources = false;\n    this.pendingComponents.forEach(declaration => {\n      if (ɵgetAsyncClassMetadataFn(declaration)) {\n        throw new Error(`Component '${declaration.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n      }\n      needsAsyncResources = needsAsyncResources || ɵisComponentDefPendingResolution(declaration);\n      const metadata = this.resolvers.component.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Component');\n      }\n      this.maybeStoreNgDef(ɵNG_COMP_DEF, declaration);\n      if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        ɵdepsTracker.clearScopeCacheFor(declaration);\n      }\n      ɵcompileComponent(declaration, metadata);\n    });\n    this.pendingComponents.clear();\n    this.pendingDirectives.forEach(declaration => {\n      const metadata = this.resolvers.directive.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Directive');\n      }\n      this.maybeStoreNgDef(ɵNG_DIR_DEF, declaration);\n      ɵcompileDirective(declaration, metadata);\n    });\n    this.pendingDirectives.clear();\n    this.pendingPipes.forEach(declaration => {\n      const metadata = this.resolvers.pipe.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Pipe');\n      }\n      this.maybeStoreNgDef(ɵNG_PIPE_DEF, declaration);\n      ɵcompilePipe(declaration, metadata);\n    });\n    this.pendingPipes.clear();\n    return needsAsyncResources;\n  }\n  applyTransitiveScopes() {\n    if (this.overriddenModules.size > 0) {\n      // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n      // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n      // collect all affected modules and reset scopes to force their re-calculation.\n      const testingModuleDef = this.testModuleType[ɵNG_MOD_DEF];\n      const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n      if (affectedModules.size > 0) {\n        affectedModules.forEach(moduleType => {\n          if (!ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            this.storeFieldOfDefOnType(moduleType, ɵNG_MOD_DEF, 'transitiveCompileScopes');\n            moduleType[ɵNG_MOD_DEF].transitiveCompileScopes = null;\n          } else {\n            ɵdepsTracker.clearScopeCacheFor(moduleType);\n          }\n        });\n      }\n    }\n    const moduleToScope = new Map();\n    const getScopeOfModule = moduleType => {\n      if (!moduleToScope.has(moduleType)) {\n        const isTestingModule = isTestingModuleOverride(moduleType);\n        const realType = isTestingModule ? this.testModuleType : moduleType;\n        moduleToScope.set(moduleType, ɵtransitiveScopesFor(realType));\n      }\n      return moduleToScope.get(moduleType);\n    };\n    this.componentToModuleScope.forEach((moduleType, componentType) => {\n      if (moduleType !== null) {\n        const moduleScope = getScopeOfModule(moduleType);\n        this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'directiveDefs');\n        this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'pipeDefs');\n        ɵpatchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n      }\n      // `tView` that is stored on component def contains information about directives and pipes\n      // that are in the scope of this component. Patching component scope will cause `tView` to be\n      // changed. Store original `tView` before patching scope, so the `tView` (including scope\n      // information) is restored back to its previous/original state before running next test.\n      // Resetting `tView` is also needed for cases when we apply provider overrides and those\n      // providers are defined on component's level, in which case they may end up included into\n      // `tView.blueprint`.\n      this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'tView');\n    });\n    this.componentToModuleScope.clear();\n  }\n  applyProviderOverrides() {\n    const maybeApplyOverrides = field => type => {\n      const resolver = field === ɵNG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n      const metadata = resolver.resolve(type);\n      if (this.hasProviderOverrides(metadata.providers)) {\n        this.patchDefWithProviderOverrides(type, field);\n      }\n    };\n    this.seenComponents.forEach(maybeApplyOverrides(ɵNG_COMP_DEF));\n    this.seenDirectives.forEach(maybeApplyOverrides(ɵNG_DIR_DEF));\n    this.seenComponents.clear();\n    this.seenDirectives.clear();\n  }\n  /**\n   * Applies provider overrides to a given type (either an NgModule or a standalone component)\n   * and all imported NgModules and standalone components recursively.\n   */\n  applyProviderOverridesInScope(type) {\n    const hasScope = isStandaloneComponent(type) || isNgModule(type);\n    // The function can be re-entered recursively while inspecting dependencies\n    // of an NgModule or a standalone component. Exit early if we come across a\n    // type that can not have a scope (directive or pipe) or the type is already\n    // processed earlier.\n    if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n      return;\n    }\n    this.scopesWithOverriddenProviders.add(type);\n    // NOTE: the line below triggers JIT compilation of the module injector,\n    // which also invokes verification of the NgModule semantics, which produces\n    // detailed error messages. The fact that the code relies on this line being\n    // present here is suspicious and should be refactored in a way that the line\n    // below can be moved (for ex. after an early exit check below).\n    const injectorDef = type[ɵNG_INJ_DEF];\n    // No provider overrides, exit early.\n    if (this.providerOverridesByToken.size === 0) return;\n    if (isStandaloneComponent(type)) {\n      // Visit all component dependencies and override providers there.\n      const def = getComponentDef(type);\n      const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n      for (const dependency of dependencies) {\n        this.applyProviderOverridesInScope(dependency);\n      }\n    } else {\n      const providers = [...injectorDef.providers, ...(this.providerOverridesByModule.get(type) || [])];\n      if (this.hasProviderOverrides(providers)) {\n        this.maybeStoreNgDef(ɵNG_INJ_DEF, type);\n        this.storeFieldOfDefOnType(type, ɵNG_INJ_DEF, 'providers');\n        injectorDef.providers = this.getOverriddenProviders(providers);\n      }\n      // Apply provider overrides to imported modules recursively\n      const moduleDef = type[ɵNG_MOD_DEF];\n      const imports = maybeUnwrapFn(moduleDef.imports);\n      for (const importedModule of imports) {\n        this.applyProviderOverridesInScope(importedModule);\n      }\n      // Also override the providers on any ModuleWithProviders imports since those don't appear in\n      // the moduleDef.\n      for (const importedModule of flatten(injectorDef.imports)) {\n        if (isModuleWithProviders(importedModule)) {\n          this.defCleanupOps.push({\n            object: importedModule,\n            fieldName: 'providers',\n            originalValue: importedModule.providers\n          });\n          importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n        }\n      }\n    }\n  }\n  patchComponentsWithExistingStyles() {\n    this.existingComponentStyles.forEach((styles, type) => type[ɵNG_COMP_DEF].styles = styles);\n    this.existingComponentStyles.clear();\n  }\n  queueTypeArray(arr, moduleType) {\n    for (const value of arr) {\n      if (Array.isArray(value)) {\n        this.queueTypeArray(value, moduleType);\n      } else {\n        this.queueType(value, moduleType);\n      }\n    }\n  }\n  recompileNgModule(ngModule, metadata) {\n    // Cache the initial ngModuleDef as it will be overwritten.\n    this.maybeStoreNgDef(ɵNG_MOD_DEF, ngModule);\n    this.maybeStoreNgDef(ɵNG_INJ_DEF, ngModule);\n    ɵcompileNgModuleDefs(ngModule, metadata);\n  }\n  maybeRegisterComponentWithAsyncMetadata(type) {\n    const asyncMetadataFn = ɵgetAsyncClassMetadataFn(type);\n    if (asyncMetadataFn) {\n      this.componentsWithAsyncMetadata.add(type);\n    }\n  }\n  queueType(type, moduleType) {\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(type);\n    const component = this.resolvers.component.resolve(type);\n    if (component) {\n      // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n      // missing. That might happen in case a class without any Angular decorators extends another\n      // class where Component/Directive/Pipe decorator is defined.\n      if (ɵisComponentDefPendingResolution(type) || !type.hasOwnProperty(ɵNG_COMP_DEF)) {\n        this.pendingComponents.add(type);\n      }\n      this.seenComponents.add(type);\n      // Keep track of the module which declares this component, so later the component's scope\n      // can be set correctly. If the component has already been recorded here, then one of several\n      // cases is true:\n      // * the module containing the component was imported multiple times (common).\n      // * the component is declared in multiple modules (which is an error).\n      // * the component was in 'declarations' of the testing module, and also in an imported module\n      //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n      // * overrideTemplateUsingTestingModule was called for the component in which case the module\n      //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n      //\n      // If the component was previously in the testing module's 'declarations' (meaning the\n      // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n      // real module, which was imported. This pattern is understood to mean that the component\n      // should use its original scope, but that the testing module should also contain the\n      // component in its scope.\n      if (!this.componentToModuleScope.has(type) || this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION) {\n        this.componentToModuleScope.set(type, moduleType);\n      }\n      return;\n    }\n    const directive = this.resolvers.directive.resolve(type);\n    if (directive) {\n      if (!type.hasOwnProperty(ɵNG_DIR_DEF)) {\n        this.pendingDirectives.add(type);\n      }\n      this.seenDirectives.add(type);\n      return;\n    }\n    const pipe = this.resolvers.pipe.resolve(type);\n    if (pipe && !type.hasOwnProperty(ɵNG_PIPE_DEF)) {\n      this.pendingPipes.add(type);\n      return;\n    }\n  }\n  queueTypesFromModulesArray(arr) {\n    // Because we may encounter the same NgModule or a standalone Component while processing\n    // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n    // can skip ones that have already been seen encountered. In some test setups, this caching\n    // resulted in 10X runtime improvement.\n    const processedDefs = new Set();\n    const queueTypesFromModulesArrayRecur = arr => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          queueTypesFromModulesArrayRecur(value);\n        } else if (hasNgModuleDef(value)) {\n          const def = value.ɵmod;\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          // Look through declarations, imports, and exports, and queue\n          // everything found there.\n          this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n        } else if (isModuleWithProviders(value)) {\n          queueTypesFromModulesArrayRecur([value.ngModule]);\n        } else if (isStandaloneComponent(value)) {\n          this.queueType(value, null);\n          const def = getComponentDef(value);\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n          dependencies.forEach(dependency => {\n            // Note: in AOT, the `dependencies` might also contain regular\n            // (NgModule-based) Component, Directive and Pipes, so we handle\n            // them separately and proceed with recursive process for standalone\n            // Components and NgModules only.\n            if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n              queueTypesFromModulesArrayRecur([dependency]);\n            } else {\n              this.queueType(dependency, null);\n            }\n          });\n        }\n      }\n    };\n    queueTypesFromModulesArrayRecur(arr);\n  }\n  // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n  // that import (even transitively) an overridden one. For all affected modules we need to\n  // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n  // of this function is to collect all affected modules in a set for further processing. Example:\n  // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n  // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n  // invalidated with the override.\n  collectModulesAffectedByOverrides(arr) {\n    const seenModules = new Set();\n    const affectedModules = new Set();\n    const calcAffectedModulesRecur = (arr, path) => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          // If the value is an array, just flatten it (by invoking this function recursively),\n          // keeping \"path\" the same.\n          calcAffectedModulesRecur(value, path);\n        } else if (hasNgModuleDef(value)) {\n          if (seenModules.has(value)) {\n            // If we've seen this module before and it's included into \"affected modules\" list, mark\n            // the whole path that leads to that module as affected, but do not descend into its\n            // imports, since we already examined them before.\n            if (affectedModules.has(value)) {\n              path.forEach(item => affectedModules.add(item));\n            }\n            continue;\n          }\n          seenModules.add(value);\n          if (this.overriddenModules.has(value)) {\n            path.forEach(item => affectedModules.add(item));\n          }\n          // Examine module imports recursively to look for overridden modules.\n          const moduleDef = value[ɵNG_MOD_DEF];\n          calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n        }\n      }\n    };\n    calcAffectedModulesRecur(arr, []);\n    return affectedModules;\n  }\n  /**\n   * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n   * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n   * an NgModule). If there is a def in a set already, don't override it, since\n   * an original one should be restored at the end of a test.\n   */\n  maybeStoreNgDef(prop, type) {\n    if (!this.initialNgDefs.has(type)) {\n      this.initialNgDefs.set(type, new Map());\n    }\n    const currentDefs = this.initialNgDefs.get(type);\n    if (!currentDefs.has(prop)) {\n      const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n      currentDefs.set(prop, currentDef);\n    }\n  }\n  storeFieldOfDefOnType(type, defField, fieldName) {\n    const def = type[defField];\n    const originalValue = def[fieldName];\n    this.defCleanupOps.push({\n      object: def,\n      fieldName,\n      originalValue\n    });\n  }\n  /**\n   * Clears current components resolution queue, but stores the state of the queue, so we can\n   * restore it later. Clearing the queue is required before we try to compile components (via\n   * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n   */\n  clearComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue === null) {\n      this.originalComponentResolutionQueue = new Map();\n    }\n    ɵclearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n  }\n  /*\n   * Restores component resolution queue to the previously saved state. This operation is performed\n   * as a part of restoring the state after completion of the current set of tests (that might\n   * potentially mutate the state).\n   */\n  restoreComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue !== null) {\n      ɵrestoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n      this.originalComponentResolutionQueue = null;\n    }\n  }\n  restoreOriginalState() {\n    // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n    // case there were multiple overrides for the same field).\n    forEachRight(this.defCleanupOps, op => {\n      op.object[op.fieldName] = op.originalValue;\n    });\n    // Restore initial component/directive/pipe defs\n    this.initialNgDefs.forEach((defs, type) => {\n      if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        ɵdepsTracker.clearScopeCacheFor(type);\n      }\n      defs.forEach((descriptor, prop) => {\n        if (!descriptor) {\n          // Delete operations are generally undesirable since they have performance\n          // implications on objects they were applied to. In this particular case, situations\n          // where this code is invoked should be quite rare to cause any noticeable impact,\n          // since it's applied only to some test cases (for example when class with no\n          // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n          // class to restore its original state (before applying overrides and running tests).\n          delete type[prop];\n        } else {\n          Object.defineProperty(type, prop, descriptor);\n        }\n      });\n    });\n    this.initialNgDefs.clear();\n    this.scopesWithOverriddenProviders.clear();\n    this.restoreComponentResolutionQueue();\n    // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n    ɵsetLocaleId(ɵDEFAULT_LOCALE_ID);\n  }\n  compileTestModule() {\n    class RootScopeModule {}\n    ɵcompileNgModuleDefs(RootScopeModule, {\n      providers: [...this.rootProviderOverrides, ɵinternalProvideZoneChangeDetection({}), TestBedApplicationErrorHandler, {\n        provide: ɵINTERNAL_APPLICATION_ERROR_HANDLER,\n        useFactory: () => {\n          if (inject$1(ɵZONELESS_ENABLED) || inject$1(RETHROW_APPLICATION_ERRORS, {\n            optional: true\n          })) {\n            const handler = inject$1(TestBedApplicationErrorHandler);\n            return e => {\n              handler.handleError(e);\n            };\n          } else {\n            const userErrorHandler = inject$1(ErrorHandler);\n            const ngZone = inject$1(NgZone);\n            return e => ngZone.runOutsideAngular(() => userErrorHandler.handleError(e));\n          }\n        }\n      }, {\n        provide: ɵChangeDetectionScheduler,\n        useExisting: ɵChangeDetectionSchedulerImpl\n      }]\n    });\n    const providers = [{\n      provide: Compiler,\n      useFactory: () => new R3TestCompiler(this)\n    }, {\n      provide: ɵDEFER_BLOCK_CONFIG,\n      useValue: {\n        behavior: this.deferBlockBehavior\n      }\n    }, ...this.providers, ...this.providerOverrides];\n    const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n    ɵcompileNgModuleDefs(this.testModuleType, {\n      declarations: this.declarations,\n      imports,\n      schemas: this.schemas,\n      providers\n    }, /* allowDuplicateDeclarationsInRoot */true);\n    this.applyProviderOverridesInScope(this.testModuleType);\n  }\n  get injector() {\n    if (this._injector !== null) {\n      return this._injector;\n    }\n    const providers = [];\n    const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS);\n    compilerOptions.forEach(opts => {\n      if (opts.providers) {\n        providers.push(opts.providers);\n      }\n    });\n    if (this.compilerProviders !== null) {\n      providers.push(...this.compilerProviders);\n    }\n    this._injector = Injector.create({\n      providers,\n      parent: this.platform.injector\n    });\n    return this._injector;\n  }\n  // get overrides for a specific provider (if any)\n  getSingleProviderOverrides(provider) {\n    const token = getProviderToken(provider);\n    return this.providerOverridesByToken.get(token) || null;\n  }\n  getProviderOverrides(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    // There are two flattening operations here. The inner flattenProviders() operates on the\n    // metadata's providers and applies a mapping function which retrieves overrides for each\n    // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n    // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n    // providers array and contaminate any error messages that might be generated.\n    return flatten(flattenProviders(providers, provider => this.getSingleProviderOverrides(provider) || []));\n  }\n  getOverriddenProviders(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    const flattenedProviders = flattenProviders(providers);\n    const overrides = this.getProviderOverrides(flattenedProviders);\n    const overriddenProviders = [...flattenedProviders, ...overrides];\n    const final = [];\n    const seenOverriddenProviders = new Set();\n    // We iterate through the list of providers in reverse order to make sure provider overrides\n    // take precedence over the values defined in provider list. We also filter out all providers\n    // that have overrides, keeping overridden values only. This is needed, since presence of a\n    // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n    forEachRight(overriddenProviders, provider => {\n      const token = getProviderToken(provider);\n      if (this.providerOverridesByToken.has(token)) {\n        if (!seenOverriddenProviders.has(token)) {\n          seenOverriddenProviders.add(token);\n          // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n          // make sure that provided override takes highest precedence and is not combined with\n          // other instances of the same multi provider.\n          final.unshift({\n            ...provider,\n            multi: false\n          });\n        }\n      } else {\n        final.unshift(provider);\n      }\n    });\n    return final;\n  }\n  hasProviderOverrides(providers) {\n    return this.getProviderOverrides(providers).length > 0;\n  }\n  patchDefWithProviderOverrides(declaration, field) {\n    const def = declaration[field];\n    if (def && def.providersResolver) {\n      this.maybeStoreNgDef(field, declaration);\n      const resolver = def.providersResolver;\n      const processProvidersFn = providers => this.getOverriddenProviders(providers);\n      this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n      def.providersResolver = ngDef => resolver(ngDef, processProvidersFn);\n    }\n  }\n}\nfunction initResolvers() {\n  return {\n    module: new NgModuleResolver(),\n    component: new ComponentResolver(),\n    directive: new DirectiveResolver(),\n    pipe: new PipeResolver()\n  };\n}\nfunction isStandaloneComponent(value) {\n  const def = getComponentDef(value);\n  return !!def?.standalone;\n}\nfunction getComponentDef(value) {\n  return value.ɵcmp ?? null;\n}\nfunction hasNgModuleDef(value) {\n  return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n  return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n  return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n  const out = [];\n  values.forEach(value => {\n    if (Array.isArray(value)) {\n      out.push(...flatten(value));\n    } else {\n      out.push(value);\n    }\n  });\n  return out;\n}\nfunction identityFn(value) {\n  return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n  const out = [];\n  for (let provider of providers) {\n    if (ɵisEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      out.push(...flattenProviders(provider, mapFn));\n    } else {\n      out.push(mapFn(provider));\n    }\n  }\n  return out;\n}\nfunction getProviderField(provider, field) {\n  return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n  return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n  return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n  for (let idx = values.length - 1; idx >= 0; idx--) {\n    fn(values[idx], idx);\n  }\n}\nfunction invalidTypeError(name, expectedType) {\n  return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n  constructor(testBed) {\n    this.testBed = testBed;\n  }\n  compileModuleSync(moduleType) {\n    this.testBed._compileNgModuleSync(moduleType);\n    return new ɵNgModuleFactory(moduleType);\n  }\n  compileModuleAsync(moduleType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5.testBed._compileNgModuleAsync(moduleType);\n      return new ɵNgModuleFactory(moduleType);\n    })();\n  }\n  compileModuleAndAllComponentsSync(moduleType) {\n    const ngModuleFactory = this.compileModuleSync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n  compileModuleAndAllComponentsAsync(moduleType) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const ngModuleFactory = yield _this6.compileModuleAsync(moduleType);\n      const componentFactories = _this6.testBed._getComponentFactories(moduleType);\n      return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    })();\n  }\n  clearCache() {}\n  clearCacheFor(type) {}\n  getModuleId(moduleType) {\n    const meta = this.testBed._getModuleResolver().resolve(moduleType);\n    return meta && meta.id || undefined;\n  }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n  return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n  constructor() {\n    /**\n     * Defer block behavior option that specifies whether defer blocks will be triggered manually\n     * or set to play through.\n     */\n    this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Properties\n    this.platform = null;\n    this.ngModule = null;\n    this._compiler = null;\n    this._testModuleRef = null;\n    this._activeFixtures = [];\n    /**\n     * Internal-only flag to indicate whether a module\n     * scoping queue has been checked and flushed already.\n     * @nodoc\n     */\n    this.globalCompilationChecked = false;\n  }\n  static {\n    this._INSTANCE = null;\n  }\n  static get INSTANCE() {\n    return TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl();\n  }\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  static initTestEnvironment(ngModule, platform, options) {\n    const testBed = TestBedImpl.INSTANCE;\n    testBed.initTestEnvironment(ngModule, platform, options);\n    return testBed;\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  static resetTestEnvironment() {\n    TestBedImpl.INSTANCE.resetTestEnvironment();\n  }\n  static configureCompiler(config) {\n    return TestBedImpl.INSTANCE.configureCompiler(config);\n  }\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef) {\n    return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n  }\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents() {\n    return TestBedImpl.INSTANCE.compileComponents();\n  }\n  static overrideModule(ngModule, override) {\n    return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n  }\n  static overrideComponent(component, override) {\n    return TestBedImpl.INSTANCE.overrideComponent(component, override);\n  }\n  static overrideDirective(directive, override) {\n    return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n  }\n  static overridePipe(pipe, override) {\n    return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n  }\n  static overrideTemplate(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n  }\n  /**\n   * Overrides the template of the given component, compiling the template\n   * in the context of the TestingModule.\n   *\n   * Note: This works for JIT and AOTed components as well.\n   */\n  static overrideTemplateUsingTestingModule(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n  }\n  static overrideProvider(token, provider) {\n    return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n  }\n  static inject(token, notFoundValue, flags) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, ɵconvertToBitFlags(flags));\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n  }\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link EnvironmentInjector#runInContext}\n   */\n  static runInInjectionContext(fn) {\n    return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n  }\n  static createComponent(component) {\n    return TestBedImpl.INSTANCE.createComponent(component);\n  }\n  static resetTestingModule() {\n    return TestBedImpl.INSTANCE.resetTestingModule();\n  }\n  static execute(tokens, fn, context) {\n    return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n  }\n  static get platform() {\n    return TestBedImpl.INSTANCE.platform;\n  }\n  static get ngModule() {\n    return TestBedImpl.INSTANCE.ngModule;\n  }\n  static flushEffects() {\n    return TestBedImpl.INSTANCE.flushEffects();\n  }\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  initTestEnvironment(ngModule, platform, options) {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n    TestBedImpl._environmentTeardownOptions = options?.teardown;\n    TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n    TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n    this.platform = platform;\n    this.ngModule = ngModule;\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n    // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n    // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n    // completely.\n    ɵsetAllowDuplicateNgModuleIdsForTest(true);\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  resetTestEnvironment() {\n    this.resetTestingModule();\n    this._compiler = null;\n    this.platform = null;\n    this.ngModule = null;\n    TestBedImpl._environmentTeardownOptions = undefined;\n    ɵsetAllowDuplicateNgModuleIdsForTest(false);\n  }\n  resetTestingModule() {\n    this.checkGlobalCompilationFinished();\n    ɵresetCompiledComponents();\n    if (this._compiler !== null) {\n      this.compiler.restoreOriginalState();\n    }\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // Restore the previous value of the \"error on unknown elements\" option\n    ɵsetUnknownElementStrictMode(this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    // Restore the previous value of the \"error on unknown properties\" option\n    ɵsetUnknownPropertyStrictMode(this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n    // We have to chain a couple of try/finally blocks, because each step can\n    // throw errors and we don't want it to interrupt the next step and we also\n    // want an error to be thrown at the end.\n    try {\n      this.destroyActiveFixtures();\n    } finally {\n      try {\n        if (this.shouldTearDownTestingModule()) {\n          this.tearDownTestingModule();\n        }\n      } finally {\n        this._testModuleRef = null;\n        this._instanceTeardownOptions = undefined;\n        this._instanceErrorOnUnknownElementsOption = undefined;\n        this._instanceErrorOnUnknownPropertiesOption = undefined;\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n      }\n    }\n    return this;\n  }\n  configureCompiler(config) {\n    if (config.useJit != null) {\n      throw new Error('JIT compiler is not configurable via TestBed APIs.');\n    }\n    if (config.providers !== undefined) {\n      this.compiler.setCompilerProviders(config.providers);\n    }\n    return this;\n  }\n  configureTestingModule(moduleDef) {\n    this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n    // Trigger module scoping queue flush before executing other TestBed operations in a test.\n    // This is needed for the first test invocation to ensure that globally declared modules have\n    // their components scoped properly. See the `checkGlobalCompilationFinished` function\n    // description for additional info.\n    this.checkGlobalCompilationFinished();\n    // Always re-assign the options, even if they're undefined.\n    // This ensures that we don't carry them between tests.\n    this._instanceTeardownOptions = moduleDef.teardown;\n    this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n    this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n    this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Store the current value of the strict mode option,\n    // so we can restore it later\n    this._previousErrorOnUnknownElementsOption = ɵgetUnknownElementStrictMode();\n    ɵsetUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n    this._previousErrorOnUnknownPropertiesOption = ɵgetUnknownPropertyStrictMode();\n    ɵsetUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n    this.compiler.configureTestingModule(moduleDef);\n    return this;\n  }\n  compileComponents() {\n    return this.compiler.compileComponents();\n  }\n  inject(token, notFoundValue, flags) {\n    if (token === TestBed) {\n      return this;\n    }\n    const UNDEFINED = {};\n    const result = this.testModuleRef.injector.get(token, UNDEFINED, ɵconvertToBitFlags(flags));\n    return result === UNDEFINED ? this.compiler.injector.get(token, notFoundValue, flags) : result;\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return this.inject(token, notFoundValue, flags);\n  }\n  runInInjectionContext(fn) {\n    return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n  }\n  execute(tokens, fn, context) {\n    const params = tokens.map(t => this.inject(t));\n    return fn.apply(context, params);\n  }\n  overrideModule(ngModule, override) {\n    this.assertNotInstantiated('overrideModule', 'override module metadata');\n    this.compiler.overrideModule(ngModule, override);\n    return this;\n  }\n  overrideComponent(component, override) {\n    this.assertNotInstantiated('overrideComponent', 'override component metadata');\n    this.compiler.overrideComponent(component, override);\n    return this;\n  }\n  overrideTemplateUsingTestingModule(component, template) {\n    this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n    this.compiler.overrideTemplateUsingTestingModule(component, template);\n    return this;\n  }\n  overrideDirective(directive, override) {\n    this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this.compiler.overrideDirective(directive, override);\n    return this;\n  }\n  overridePipe(pipe, override) {\n    this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this.compiler.overridePipe(pipe, override);\n    return this;\n  }\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token, provider) {\n    this.assertNotInstantiated('overrideProvider', 'override provider');\n    this.compiler.overrideProvider(token, provider);\n    return this;\n  }\n  overrideTemplate(component, template) {\n    return this.overrideComponent(component, {\n      set: {\n        template,\n        templateUrl: null\n      }\n    });\n  }\n  createComponent(type) {\n    const testComponentRenderer = this.inject(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n    if (ɵgetAsyncClassMetadataFn(type)) {\n      throw new Error(`Component '${type.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n    }\n    const componentDef = type.ɵcmp;\n    if (!componentDef) {\n      throw new Error(`It looks like '${ɵstringify(type)}' has not been compiled.`);\n    }\n    const componentFactory = new ɵRender3ComponentFactory(componentDef);\n    const initComponent = () => {\n      const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n      return this.runInInjectionContext(() => {\n        const isZoneless = this.inject(ɵZONELESS_ENABLED);\n        const fixture = isZoneless ? new ScheduledComponentFixture(componentRef) : new PseudoApplicationComponentFixture(componentRef);\n        fixture.initialize();\n        return fixture;\n      });\n    };\n    const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n    const ngZone = noNgZone ? null : this.inject(NgZone, null);\n    const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get compiler() {\n    if (this._compiler === null) {\n      throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n    }\n    return this._compiler;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get testModuleRef() {\n    if (this._testModuleRef === null) {\n      this._testModuleRef = this.compiler.finalize();\n    }\n    return this._testModuleRef;\n  }\n  assertNotInstantiated(methodName, methodDescription) {\n    if (this._testModuleRef !== null) {\n      throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` + `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n  /**\n   * Check whether the module scoping queue should be flushed, and flush it if needed.\n   *\n   * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n   * in-progress module compilation. This creates a potential hazard - the very first time the\n   * TestBed is initialized (or if it's reset without being initialized), there may be pending\n   * compilations of modules declared in global scope. These compilations should be finished.\n   *\n   * To ensure that globally declared modules have their components scoped properly, this function\n   * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n   * to any other operations, the scoping queue is flushed.\n   */\n  checkGlobalCompilationFinished() {\n    // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n    // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n    if (!this.globalCompilationChecked && this._testModuleRef === null) {\n      ɵflushModuleScopingQueueAsMuchAsPossible();\n    }\n    this.globalCompilationChecked = true;\n  }\n  destroyActiveFixtures() {\n    let errorCount = 0;\n    this._activeFixtures.forEach(fixture => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        errorCount++;\n        console.error('Error during cleanup of component', {\n          component: fixture.componentInstance,\n          stacktrace: e\n        });\n      }\n    });\n    this._activeFixtures = [];\n    if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n      throw Error(`${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` + `threw errors during cleanup`);\n    }\n  }\n  shouldRethrowTeardownErrors() {\n    const instanceOptions = this._instanceTeardownOptions;\n    const environmentOptions = TestBedImpl._environmentTeardownOptions;\n    // If the new teardown behavior hasn't been configured, preserve the old behavior.\n    if (!instanceOptions && !environmentOptions) {\n      return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n    }\n    // Otherwise use the configured behavior or default to rethrowing.\n    return instanceOptions?.rethrowErrors ?? environmentOptions?.rethrowErrors ?? this.shouldTearDownTestingModule();\n  }\n  shouldThrowErrorOnUnknownElements() {\n    // Check if a configuration has been provided to throw when an unknown element is found\n    return this._instanceErrorOnUnknownElementsOption ?? TestBedImpl._environmentErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT;\n  }\n  shouldThrowErrorOnUnknownProperties() {\n    // Check if a configuration has been provided to throw when an unknown property is found\n    return this._instanceErrorOnUnknownPropertiesOption ?? TestBedImpl._environmentErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT;\n  }\n  shouldTearDownTestingModule() {\n    return this._instanceTeardownOptions?.destroyAfterEach ?? TestBedImpl._environmentTeardownOptions?.destroyAfterEach ?? TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n  }\n  getDeferBlockBehavior() {\n    return this._instanceDeferBlockBehavior;\n  }\n  tearDownTestingModule() {\n    // If the module ref has already been destroyed, we won't be able to get a test renderer.\n    if (this._testModuleRef === null) {\n      return;\n    }\n    // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n    // last step, but the injector will be destroyed as a part of the module ref destruction.\n    const testRenderer = this.inject(TestComponentRenderer);\n    try {\n      this._testModuleRef.destroy();\n    } catch (e) {\n      if (this.shouldRethrowTeardownErrors()) {\n        throw e;\n      } else {\n        console.error('Error during cleanup of a testing module', {\n          component: this._testModuleRef.instance,\n          stacktrace: e\n        });\n      }\n    } finally {\n      testRenderer.removeAllRootElements?.();\n    }\n  }\n  /**\n   * Execute any pending effects.\n   *\n   * @developerPreview\n   */\n  flushEffects() {\n    this.inject(ɵEffectScheduler).flush();\n  }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n  const testBed = TestBedImpl.INSTANCE;\n  // Not using an arrow function to preserve context passed from call site\n  return function () {\n    return testBed.execute(tokens, fn, this);\n  };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n  constructor(_moduleDef) {\n    this._moduleDef = _moduleDef;\n  }\n  _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      TestBedImpl.configureTestingModule(moduleDef);\n    }\n  }\n  inject(tokens, fn) {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\nfunction withModule(moduleDef, fn) {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      const testBed = TestBedImpl.INSTANCE;\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n  return () => {\n    const testBed = TestBedImpl.INSTANCE;\n    if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n      testBed.resetTestingModule();\n      resetFakeAsyncZoneIfExists();\n    }\n  };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, MetadataOverrider as ɵMetadataOverrider };", "map": {"version": 3, "names": ["i0", "ɵDeferBlockState", "ɵtriggerResourceLoading", "ɵrenderDeferBlockState", "ɵCONTAINER_HEADER_OFFSET", "ɵgetDeferBlocks", "ɵDeferBlockBehavior", "InjectionToken", "inject", "inject$1", "NgZone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Injectable", "ɵNoopNgZone", "ɵEffectScheduler", "ApplicationRef", "ɵPendingTasks", "getDebugNode", "RendererFactory2", "ɵdetectChangesInViewIfRequired", "ɵstringify", "ɵReflectionCapabilities", "Directive", "Component", "<PERSON><PERSON>", "NgModule", "ɵgetAsyncClassMetadataFn", "ɵgenerateStandaloneInDeclarationsError", "ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT", "ɵdepsTracker", "ɵgetInjectableDef", "resolveForwardRef", "ɵNG_COMP_DEF", "ɵisComponentDefPendingResolution", "ɵresolveComponentResources", "ɵRender3NgModuleRef", "ApplicationInitStatus", "LOCALE_ID", "ɵDEFAULT_LOCALE_ID", "ɵsetLocaleId", "ɵRender3ComponentFactory", "ɵcompileComponent", "ɵNG_DIR_DEF", "ɵcompileDirective", "ɵNG_PIPE_DEF", "ɵcompilePipe", "ɵNG_MOD_DEF", "ɵtransitiveScopesFor", "ɵpatchComponentDefWithScope", "ɵNG_INJ_DEF", "ɵcompileNgModuleDefs", "ɵclearResolutionOfComponentResourcesQueue", "ɵrestoreComponentResolutionQueue", "ɵinternalProvideZoneChangeDetection", "ɵINTERNAL_APPLICATION_ERROR_HANDLER", "ɵZONELESS_ENABLED", "ɵChangeDetectionScheduler", "ɵChangeDetectionSchedulerImpl", "Compiler", "ɵDEFER_BLOCK_CONFIG", "COMPILER_OPTIONS", "Injector", "ɵisEnvironmentProviders", "ɵNgModuleFactory", "ModuleWithComponentFactories", "ɵconvertToBitFlags", "InjectFlags", "ɵsetAllowDuplicateNgModuleIdsForTest", "ɵresetCompiledComponents", "ɵsetUnknownElementStrictMode", "ɵsetUnknownPropertyStrictMode", "ɵgetUnknownElementStrictMode", "ɵgetUnknownPropertyStrictMode", "runInInjectionContext", "EnvironmentInjector", "ɵflushModuleScopingQueueAsMuchAsPossible", "DeferBlockBehavior", "DeferBlockState", "Subscription", "Resource<PERSON><PERSON>der", "waitForAsync", "fn", "_Zone", "Zone", "Promise", "reject", "asyncTest", "__symbol__", "DeferBlockFixture", "constructor", "block", "componentFixture", "render", "state", "_this", "_asyncToGenerator", "hasStateTemplate", "stateAsString", "getDeferBlockStateNameFromEnum", "Error", "toLowerCase", "Complete", "tDetails", "lView", "tNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uling", "lContainer", "detectChanges", "getDeferBlocks", "deferBlocks", "deferBlockFixtures", "length", "push", "resolve", "Placeholder", "placeholderTmplIndex", "Loading", "loadingTmplIndex", "errorTmplIndex", "TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT", "THROW_ON_UNKNOWN_ELEMENTS_DEFAULT", "THROW_ON_UNKNOWN_PROPERTIES_DEFAULT", "DEFER_BLOCK_DEFAULT_BEHAVIOR", "Playthrough", "TestComponent<PERSON><PERSON><PERSON>", "insertRootElement", "rootElementId", "removeAllRootElements", "ComponentFixtureAutoDetect", "ComponentFixtureNoNgZone", "RETHROW_APPLICATION_ERRORS", "TestBedApplicationErrorHandler", "zone", "userErrorHandler", "whenStableRejectFunctions", "Set", "handleError", "e", "runOutsideAngular", "userError", "size", "values", "clear", "ɵfac", "ɵɵngDeclareFactory", "minVersion", "version", "ngImport", "type", "deps", "target", "ɵɵFactoryTarget", "ɵprov", "ɵɵngDeclareInjectable", "ɵɵngDeclareClassMetadata", "decorators", "ComponentFixture", "componentRef", "_isDestroyed", "_noZoneOptionIsSet", "optional", "_ngZone", "_effectRunner", "_appRef", "_testAppRef", "pendingTasks", "appError<PERSON><PERSON><PERSON>", "ngZone", "changeDetectorRef", "elementRef", "location", "debugElement", "nativeElement", "componentInstance", "instance", "checkNoChanges", "isStable", "hasPendingTasks", "value", "whenStable", "add", "then", "delete", "<PERSON><PERSON><PERSON><PERSON>", "_get<PERSON><PERSON><PERSON>", "_renderer", "undefined", "injector", "get", "whenRenderingDone", "renderer", "destroy", "ScheduledComponentFixture", "arguments", "_autoDetect", "initialize", "attachView", "flush", "tick", "autoDetectChanges", "autoDetect", "PseudoApplicationComponentFixture", "_subscriptions", "afterTickSubscription", "beforeRenderSubscription", "subscribeToAppRefEvents", "onDestroy", "unsubscribeFromAppRefEvents", "onError", "subscribe", "next", "error", "run", "afterTick", "beforeRender", "isFirstPass", "_l<PERSON>iew", "notify<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "externalTestViews", "unsubscribe", "fakeAsyncTestModule", "fakeAsyncTestModuleNotLoadedErrorMessage", "resetFakeAsyncZone", "resetFakeAsyncZoneIfExists", "fakeAsync", "options", "millis", "tickOptions", "processNewMacroTasksSynchronously", "maxTurns", "discardPeriodicTasks", "flushMicrotasks", "_nextReferenceId", "MetadataOverrider", "_references", "Map", "overrideMetadata", "metadataClass", "oldMetadata", "override", "props", "_valueProps", "for<PERSON>ach", "prop", "set", "remove", "setMetadata", "removeMetadata", "addMetadata", "metadata", "references", "removeObjects", "removeValue", "Array", "isArray", "_propH<PERSON><PERSON><PERSON>", "propValue", "filter", "has", "addValue", "concat", "propName", "nextObjectId", "objectIds", "replacer", "key", "_serializeReference", "JSON", "stringify", "ref", "id", "obj", "Object", "keys", "startsWith", "proto", "getPrototypeOf", "protoProp", "desc", "getOwnPropertyDescriptor", "reflection", "OverrideResolver", "overrides", "resolved", "addOverride", "setOverrides", "getAnnotation", "annotations", "i", "annotation", "isKnownType", "overrider", "DirectiveResolver", "ComponentResolver", "PipeResolver", "NgModuleResolver", "TestingModuleOverride", "isTestingModuleOverride", "DECLARATION", "OVERRIDE_TEMPLATE", "assertNoStandaloneComponents", "types", "resolver", "component", "standalone", "TestBedCompiler", "platform", "additionalModuleTypes", "originalComponentResolutionQueue", "declarations", "imports", "providers", "schemas", "pendingComponents", "pendingDirectives", "pending<PERSON><PERSON>es", "componentsWithAsyncMetadata", "seenComponents", "seenDirectives", "overriddenModules", "existingComponentStyles", "resolvers", "initResolvers", "componentToModuleScope", "initialNgDefs", "defCleanupOps", "_injector", "compilerProviders", "providerOverrides", "rootProviderOverrides", "providerOverridesByModule", "providerOverridesByToken", "scopesWithOverriddenProviders", "testModuleRef", "deferBlock<PERSON><PERSON><PERSON>or", "DynamicTestModule", "testModuleType", "setCompilerProviders", "configureTestingModule", "moduleDef", "queueTypeArray", "queueTypesFromModulesArray", "provide", "useValue", "_rethrowApplicationTickErrors", "overrideModule", "ngModule", "clearScopeCacheFor", "module", "invalidTypeError", "name", "recompileNgModule", "overrideComponent", "verifyNoStandaloneFlagOverrides", "maybeRegisterComponentWithAsyncMetadata", "overrideDirective", "directive", "overridePipe", "pipe", "hasOwnProperty", "override<PERSON><PERSON><PERSON>", "token", "provider", "providerDef", "useFactory", "multi", "injectableDef", "providedIn", "overridesBucket", "existingOverrides", "overrideTemplateUsingTestingModule", "template", "def", "hasStyleUrls", "styleUrl", "styleUrls", "overrideStyleUrls", "styles", "resolvePendingComponentsWithAsyncMetadata", "_this2", "promises", "asyncMetadataFn", "resolvedDeps", "all", "flatResolvedDeps", "flat", "applyProviderOverridesInScope", "compileComponents", "_this3", "clearComponentResolutionQueue", "needsAsyncResources", "compileTypesSync", "resourceLoader", "url", "finalize", "compileTestModule", "applyTransitiveScopes", "applyProviderOverrides", "patchComponentsWithExistingStyles", "parentInjector", "runInitializers", "localeId", "_compileNgModuleSync", "moduleType", "_compileNgModuleAsync", "_this4", "_getModuleResolver", "_getComponentFactories", "maybeUnwrapFn", "ɵmod", "reduce", "factories", "declaration", "componentDef", "ɵcmp", "maybeStoreNgDef", "testingModuleDef", "affectedModules", "collectModulesAffectedByOverrides", "storeFieldOfDefOnType", "transitiveCompileScopes", "moduleToScope", "getScopeOfModule", "isTestingModule", "realType", "componentType", "moduleScope", "getComponentDef", "maybeApplyOverrides", "field", "hasProviderOverrides", "patchDefWithProviderOverrides", "hasScope", "isStandaloneComponent", "isNgModule", "injectorDef", "dependencies", "dependency", "getOverriddenProviders", "importedModule", "flatten", "isModuleWithProviders", "object", "fieldName", "originalValue", "arr", "queueType", "processedDefs", "queueTypesFromModulesArrayRecur", "hasNgModuleDef", "exports", "seenModules", "calcAffectedModulesRecur", "path", "item", "currentDefs", "currentDef", "defField", "restoreComponentResolutionQueue", "restoreOriginalState", "forEachRight", "op", "defs", "descriptor", "defineProperty", "RootScopeModule", "handler", "useExisting", "R3TestCompiler", "behavior", "compilerOptions", "opts", "create", "parent", "getSingleProviderOverrides", "getProviderToken", "getProviderOverrides", "flattenProviders", "flattenedProviders", "overriddenProviders", "final", "seenOverriddenProviders", "unshift", "providersResolver", "processProvidersFn", "ngDef", "maybeFn", "Function", "out", "identityFn", "mapFn", "ɵproviders", "getProviderField", "idx", "expectedType", "testBed", "compileModuleSync", "compileModuleAsync", "_this5", "compileModuleAndAllComponentsSync", "ngModuleFactory", "componentFactories", "compileModuleAndAllComponentsAsync", "_this6", "clearCache", "clearCacheFor", "getModuleId", "meta", "_nextRootElementId", "getTestBed", "TestBedImpl", "INSTANCE", "_instanceDeferBlockBehavior", "_compiler", "_testModuleRef", "_activeFixtures", "globalCompilationChecked", "_INSTANCE", "initTestEnvironment", "resetTestEnvironment", "configureCompiler", "config", "overrideTemplate", "notFoundValue", "flags", "THROW_IF_NOT_FOUND", "<PERSON><PERSON><PERSON>", "createComponent", "resetTestingModule", "execute", "tokens", "context", "flushEffects", "_environmentTeardownOptions", "teardown", "_environmentErrorOnUnknownElementsOption", "errorOnUnknownElements", "_environmentErrorOnUnknownPropertiesOption", "errorOnUnknownProperties", "checkGlobalCompilationFinished", "compiler", "_previousErrorOnUnknownElementsOption", "_previousErrorOnUnknownPropertiesOption", "destroyActiveFixtures", "shouldTearDownTestingModule", "tearDownTestingModule", "_instanceTeardownOptions", "_instanceErrorOnUnknownElementsOption", "_instanceErrorOnUnknownPropertiesOption", "useJit", "assertNotInstantiated", "shouldThrowErrorOnUnknownElements", "shouldThrowErrorOnUnknownProperties", "TestBed", "UNDEFINED", "result", "params", "map", "t", "apply", "templateUrl", "testComponent<PERSON><PERSON><PERSON>", "rootElId", "componentFactory", "initComponent", "NULL", "isZoneless", "fixture", "noNgZone", "methodName", "methodDescription", "errorCount", "console", "stacktrace", "shouldRethrowTeardownErrors", "instanceOptions", "environmentOptions", "rethrowErrors", "destroyAfterEach", "getDeferBlockBehavior", "<PERSON><PERSON><PERSON><PERSON>", "InjectSetupWrapper", "_moduleDef", "_addModule", "self", "call", "withModule", "globalThis", "beforeEach", "getCleanupHook", "after<PERSON>ach", "expectedTeardownValue", "__core_private_testing_placeholder__", "ɵMetadataOverrider"], "sources": ["D:/ProjectNewrepo/Communctionscb/ENCollect.FE.Sleek/node_modules/@angular/core/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵDeferBlockState, ɵtriggerResourceLoading, ɵrenderDeferBlockState, ɵCONTAINER_HEADER_OFFSET, ɵgetDeferBlocks, ɵDeferBlockBehavior, InjectionToken, inject as inject$1, <PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>and<PERSON>, Injectable, ɵNoopNgZone, ɵEffectScheduler, ApplicationRef, ɵPendingTasks, getDebugNode, RendererFactory2, ɵdetectChangesInViewIfRequired, ɵstringify, ɵReflectionCapabilities, Directive, Component, Pipe, NgModule, ɵgetAsyncClassMetadataFn, ɵgenerateStandaloneInDeclarationsError, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker, ɵgetInjectableDef, resolveForwardRef, ɵNG_COMP_DEF, ɵisComponentDefPendingResolution, ɵresolveComponentResources, ɵRender3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID, ɵsetLocaleId, ɵRender3ComponentFactory, ɵcompileComponent, ɵNG_DIR_DEF, ɵcompileDirective, ɵNG_PIPE_DEF, ɵcompilePipe, ɵNG_MOD_DEF, ɵtransitiveScopesFor, ɵpatchComponentDefWithScope, ɵNG_INJ_DEF, ɵcompileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue, ɵinternalProvideZoneChangeDetection, ɵINTERNAL_APPLICATION_ERROR_HANDLER, ɵZONELESS_ENABLED, ɵChangeDetectionScheduler, ɵChangeDetectionSchedulerImpl, Compiler, ɵDEFER_BLOCK_CONFIG, COMPILER_OPTIONS, Injector, ɵisEnvironmentProviders, ɵNgModuleFactory, ModuleWithComponentFactories, ɵconvertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents, ɵsetUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode, runInInjectionContext, EnvironmentInjector, ɵflushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n    const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n    if (!_Zone) {\n        return function () {\n            return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js');\n        };\n    }\n    const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n    if (typeof asyncTest === 'function') {\n        return asyncTest(fn);\n    }\n    return function () {\n        return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' +\n            'Please make sure that your environment includes zone.js/testing');\n    };\n}\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n */\nclass DeferBlockFixture {\n    /** @nodoc */\n    constructor(block, componentFixture) {\n        this.block = block;\n        this.componentFixture = componentFixture;\n    }\n    /**\n     * Renders the specified state of the defer fixture.\n     * @param state the defer state to render\n     */\n    async render(state) {\n        if (!hasStateTemplate(state, this.block)) {\n            const stateAsString = getDeferBlockStateNameFromEnum(state);\n            throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` +\n                `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n        }\n        if (state === ɵDeferBlockState.Complete) {\n            await ɵtriggerResourceLoading(this.block.tDetails, this.block.lView, this.block.tNode);\n        }\n        // If the `render` method is used explicitly - skip timer-based scheduling for\n        // `@placeholder` and `@loading` blocks and render them immediately.\n        const skipTimerScheduling = true;\n        ɵrenderDeferBlockState(state, this.block.tNode, this.block.lContainer, skipTimerScheduling);\n        this.componentFixture.detectChanges();\n    }\n    /**\n     * Retrieves all nested child defer block fixtures\n     * in a given defer block.\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        // An LContainer that represents a defer block has at most 1 view, which is\n        // located right after an LContainer header. Get a hold of that view and inspect\n        // it for nested defer blocks.\n        const deferBlockFixtures = [];\n        if (this.block.lContainer.length >= ɵCONTAINER_HEADER_OFFSET) {\n            const lView = this.block.lContainer[ɵCONTAINER_HEADER_OFFSET];\n            ɵgetDeferBlocks(lView, deferBlocks);\n            for (const block of deferBlocks) {\n                deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n            }\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n}\nfunction hasStateTemplate(state, block) {\n    switch (state) {\n        case ɵDeferBlockState.Placeholder:\n            return block.tDetails.placeholderTmplIndex !== null;\n        case ɵDeferBlockState.Loading:\n            return block.tDetails.loadingTmplIndex !== null;\n        case ɵDeferBlockState.Error:\n            return block.tDetails.errorTmplIndex !== null;\n        case ɵDeferBlockState.Complete:\n            return true;\n        default:\n            return false;\n    }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n    switch (state) {\n        case ɵDeferBlockState.Placeholder:\n            return 'Placeholder';\n        case ɵDeferBlockState.Loading:\n            return 'Loading';\n        case ɵDeferBlockState.Error:\n            return 'Error';\n        default:\n            return 'Main';\n    }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = ɵDeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n    insertRootElement(rootElementId) { }\n    removeAllRootElements() { }\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\n\nconst RETHROW_APPLICATION_ERRORS = new InjectionToken('rethrow application errors');\nclass TestBedApplicationErrorHandler {\n    constructor() {\n        this.zone = inject$1(NgZone);\n        this.userErrorHandler = inject$1(ErrorHandler);\n        this.whenStableRejectFunctions = new Set();\n    }\n    handleError(e) {\n        try {\n            this.zone.runOutsideAngular(() => this.userErrorHandler.handleError(e));\n        }\n        catch (userError) {\n            e = userError;\n        }\n        // Instead of throwing the error when there are outstanding `fixture.whenStable` promises,\n        // reject those promises with the error. This allows developers to write\n        // expectAsync(fix.whenStable()).toBeRejected();\n        if (this.whenStableRejectFunctions.size > 0) {\n            for (const fn of this.whenStableRejectFunctions.values()) {\n                fn(e);\n            }\n            this.whenStableRejectFunctions.clear();\n        }\n        else {\n            throw e;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: TestBedApplicationErrorHandler, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: TestBedApplicationErrorHandler }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: TestBedApplicationErrorHandler, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n    /** @nodoc */\n    constructor(componentRef) {\n        this.componentRef = componentRef;\n        this._isDestroyed = false;\n        /** @internal */\n        this._noZoneOptionIsSet = inject$1(ComponentFixtureNoNgZone, { optional: true });\n        /** @internal */\n        this._ngZone = this._noZoneOptionIsSet ? new ɵNoopNgZone() : inject$1(NgZone);\n        /** @internal */\n        this._effectRunner = inject$1(ɵEffectScheduler);\n        // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n        // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n        // This is a crazy way of doing things but hey, it's the world we live in.\n        // The zoneless scheduler should instead do this more imperatively by attaching\n        // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n        // behavior.\n        /** @internal */\n        this._appRef = inject$1(ApplicationRef);\n        /** @internal */\n        this._testAppRef = this._appRef;\n        this.pendingTasks = inject$1(ɵPendingTasks);\n        this.appErrorHandler = inject$1(TestBedApplicationErrorHandler);\n        // TODO(atscott): Remove this from public API\n        this.ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n        this.changeDetectorRef = componentRef.changeDetectorRef;\n        this.elementRef = componentRef.location;\n        this.debugElement = getDebugNode(this.elementRef.nativeElement);\n        this.componentInstance = componentRef.instance;\n        this.nativeElement = this.elementRef.nativeElement;\n        this.componentRef = componentRef;\n    }\n    /**\n     * Do a change detection run to make sure there were no changes.\n     */\n    checkNoChanges() {\n        this.changeDetectorRef.checkNoChanges();\n    }\n    /**\n     * Return whether the fixture is currently stable or has async tasks that have not been completed\n     * yet.\n     */\n    isStable() {\n        return !this.pendingTasks.hasPendingTasks.value;\n    }\n    /**\n     * Get a promise that resolves when the fixture is stable.\n     *\n     * This can be used to resume testing after events have triggered asynchronous activity or\n     * asynchronous change detection.\n     */\n    whenStable() {\n        if (this.isStable()) {\n            return Promise.resolve(false);\n        }\n        return new Promise((resolve, reject) => {\n            this.appErrorHandler.whenStableRejectFunctions.add(reject);\n            this._appRef.whenStable().then(() => {\n                this.appErrorHandler.whenStableRejectFunctions.delete(reject);\n                resolve(true);\n            });\n        });\n    }\n    /**\n     * Retrieves all defer block fixtures in the component fixture.\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        const lView = this.componentRef.hostView['_lView'];\n        ɵgetDeferBlocks(lView, deferBlocks);\n        const deferBlockFixtures = [];\n        for (const block of deferBlocks) {\n            deferBlockFixtures.push(new DeferBlockFixture(block, this));\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n    _getRenderer() {\n        if (this._renderer === undefined) {\n            this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n        }\n        return this._renderer;\n    }\n    /**\n     * Get a promise that resolves when the ui state is stable following animations.\n     */\n    whenRenderingDone() {\n        const renderer = this._getRenderer();\n        if (renderer && renderer.whenRenderingDone) {\n            return renderer.whenRenderingDone();\n        }\n        return this.whenStable();\n    }\n    /**\n     * Trigger component destruction.\n     */\n    destroy() {\n        if (!this._isDestroyed) {\n            this.componentRef.destroy();\n            this._isDestroyed = true;\n        }\n    }\n}\n/**\n * ComponentFixture behavior that actually attaches the component to the application to ensure\n * behaviors between fixture and application do not diverge. `detectChanges` is disabled by default\n * (instead, tests should wait for the scheduler to detect changes), `whenStable` is directly the\n * `ApplicationRef.isStable`, and `autoDetectChanges` cannot be disabled.\n */\nclass ScheduledComponentFixture extends ComponentFixture {\n    constructor() {\n        super(...arguments);\n        this._autoDetect = inject$1(ComponentFixtureAutoDetect, { optional: true }) ?? true;\n    }\n    initialize() {\n        if (this._autoDetect) {\n            this._appRef.attachView(this.componentRef.hostView);\n        }\n    }\n    detectChanges(checkNoChanges = true) {\n        if (!checkNoChanges) {\n            throw new Error('Cannot disable `checkNoChanges` in this configuration. ' +\n                'Use `fixture.componentRef.hostView.changeDetectorRef.detectChanges()` instead.');\n        }\n        this._effectRunner.flush();\n        this._appRef.tick();\n        this._effectRunner.flush();\n    }\n    autoDetectChanges(autoDetect = true) {\n        if (!autoDetect) {\n            throw new Error('Cannot disable autoDetect after it has been enabled when using the zoneless scheduler. ' +\n                'To disable autoDetect, add `{provide: ComponentFixtureAutoDetect, useValue: false}` to the TestBed providers.');\n        }\n        else if (!this._autoDetect) {\n            this._autoDetect = autoDetect;\n            this._appRef.attachView(this.componentRef.hostView);\n        }\n        this.detectChanges();\n    }\n}\n/**\n * ComponentFixture behavior that attempts to act as a \"mini application\".\n */\nclass PseudoApplicationComponentFixture extends ComponentFixture {\n    constructor() {\n        super(...arguments);\n        this._subscriptions = new Subscription();\n        this._autoDetect = inject$1(ComponentFixtureAutoDetect, { optional: true }) ?? false;\n        this.afterTickSubscription = undefined;\n        this.beforeRenderSubscription = undefined;\n    }\n    initialize() {\n        if (this._autoDetect) {\n            this.subscribeToAppRefEvents();\n        }\n        this.componentRef.hostView.onDestroy(() => {\n            this.unsubscribeFromAppRefEvents();\n        });\n        // Create subscriptions outside the NgZone so that the callbacks run outside\n        // of NgZone.\n        this._ngZone.runOutsideAngular(() => {\n            this._subscriptions.add(this._ngZone.onError.subscribe({\n                next: (error) => {\n                    throw error;\n                },\n            }));\n        });\n    }\n    detectChanges(checkNoChanges = true) {\n        this._effectRunner.flush();\n        // Run the change detection inside the NgZone so that any async tasks as part of the change\n        // detection are captured by the zone and can be waited for in isStable.\n        this._ngZone.run(() => {\n            this.changeDetectorRef.detectChanges();\n            if (checkNoChanges) {\n                this.checkNoChanges();\n            }\n        });\n        // Run any effects that were created/dirtied during change detection. Such effects might become\n        // dirty in response to input signals changing.\n        this._effectRunner.flush();\n    }\n    autoDetectChanges(autoDetect = true) {\n        if (this._noZoneOptionIsSet) {\n            throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n        }\n        if (autoDetect !== this._autoDetect) {\n            if (autoDetect) {\n                this.subscribeToAppRefEvents();\n            }\n            else {\n                this.unsubscribeFromAppRefEvents();\n            }\n        }\n        this._autoDetect = autoDetect;\n        this.detectChanges();\n    }\n    subscribeToAppRefEvents() {\n        this._ngZone.runOutsideAngular(() => {\n            this.afterTickSubscription = this._testAppRef.afterTick.subscribe(() => {\n                this.checkNoChanges();\n            });\n            this.beforeRenderSubscription = this._testAppRef.beforeRender.subscribe((isFirstPass) => {\n                try {\n                    ɵdetectChangesInViewIfRequired(this.componentRef.hostView._lView, this.componentRef.hostView.notifyErrorHandler, isFirstPass, false /** zoneless enabled */);\n                }\n                catch (e) {\n                    // If an error occurred during change detection, remove the test view from the application\n                    // ref tracking. Note that this isn't exactly desirable but done this way because of how\n                    // things used to work with `autoDetect` and uncaught errors. Ideally we would surface\n                    // this error to the error handler instead and continue refreshing the view like\n                    // what would happen in the application.\n                    this.unsubscribeFromAppRefEvents();\n                    throw e;\n                }\n            });\n            this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n        });\n    }\n    unsubscribeFromAppRefEvents() {\n        this.afterTickSubscription?.unsubscribe();\n        this.beforeRenderSubscription?.unsubscribe();\n        this.afterTickSubscription = undefined;\n        this.beforeRenderSubscription = undefined;\n        this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n    }\n    destroy() {\n        this.unsubscribeFromAppRefEvents();\n        this._subscriptions.unsubscribe();\n        super.destroy();\n    }\n}\n\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n    if (fakeAsyncTestModule) {\n        fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n * @param options\n *   - flush: When true, will drain the macrotask queue after the test function completes.\n *     When false, will throw an exception at the end of the function if there are pending timers.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn, options) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.fakeAsync(fn, options);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n    processNewMacroTasksSynchronously: true,\n}) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.tick(millis, tickOptions);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flush(maxTurns);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.discardPeriodicTasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flushMicrotasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n    constructor() {\n        this._references = new Map();\n    }\n    /**\n     * Creates a new instance for the given metadata class\n     * based on an old instance and overrides.\n     */\n    overrideMetadata(metadataClass, oldMetadata, override) {\n        const props = {};\n        if (oldMetadata) {\n            _valueProps(oldMetadata).forEach((prop) => (props[prop] = oldMetadata[prop]));\n        }\n        if (override.set) {\n            if (override.remove || override.add) {\n                throw new Error(`Cannot set and add/remove ${ɵstringify(metadataClass)} at the same time!`);\n            }\n            setMetadata(props, override.set);\n        }\n        if (override.remove) {\n            removeMetadata(props, override.remove, this._references);\n        }\n        if (override.add) {\n            addMetadata(props, override.add);\n        }\n        return new metadataClass(props);\n    }\n}\nfunction removeMetadata(metadata, remove, references) {\n    const removeObjects = new Set();\n    for (const prop in remove) {\n        const removeValue = remove[prop];\n        if (Array.isArray(removeValue)) {\n            removeValue.forEach((value) => {\n                removeObjects.add(_propHashKey(prop, value, references));\n            });\n        }\n        else {\n            removeObjects.add(_propHashKey(prop, removeValue, references));\n        }\n    }\n    for (const prop in metadata) {\n        const propValue = metadata[prop];\n        if (Array.isArray(propValue)) {\n            metadata[prop] = propValue.filter((value) => !removeObjects.has(_propHashKey(prop, value, references)));\n        }\n        else {\n            if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n                metadata[prop] = undefined;\n            }\n        }\n    }\n}\nfunction addMetadata(metadata, add) {\n    for (const prop in add) {\n        const addValue = add[prop];\n        const propValue = metadata[prop];\n        if (propValue != null && Array.isArray(propValue)) {\n            metadata[prop] = propValue.concat(addValue);\n        }\n        else {\n            metadata[prop] = addValue;\n        }\n    }\n}\nfunction setMetadata(metadata, set) {\n    for (const prop in set) {\n        metadata[prop] = set[prop];\n    }\n}\nfunction _propHashKey(propName, propValue, references) {\n    let nextObjectId = 0;\n    const objectIds = new Map();\n    const replacer = (key, value) => {\n        if (value !== null && typeof value === 'object') {\n            if (objectIds.has(value)) {\n                return objectIds.get(value);\n            }\n            // Record an id for this object such that any later references use the object's id instead\n            // of the object itself, in order to break cyclic pointers in objects.\n            objectIds.set(value, `ɵobj#${nextObjectId++}`);\n            // The first time an object is seen the object itself is serialized.\n            return value;\n        }\n        else if (typeof value === 'function') {\n            value = _serializeReference(value, references);\n        }\n        return value;\n    };\n    return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n    let id = references.get(ref);\n    if (!id) {\n        id = `${ɵstringify(ref)}${_nextReferenceId++}`;\n        references.set(ref, id);\n    }\n    return id;\n}\nfunction _valueProps(obj) {\n    const props = [];\n    // regular public props\n    Object.keys(obj).forEach((prop) => {\n        if (!prop.startsWith('_')) {\n            props.push(prop);\n        }\n    });\n    // getters\n    let proto = obj;\n    while ((proto = Object.getPrototypeOf(proto))) {\n        Object.keys(proto).forEach((protoProp) => {\n            const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n            if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n                props.push(protoProp);\n            }\n        });\n    }\n    return props;\n}\n\nconst reflection = new ɵReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n    constructor() {\n        this.overrides = new Map();\n        this.resolved = new Map();\n    }\n    addOverride(type, override) {\n        const overrides = this.overrides.get(type) || [];\n        overrides.push(override);\n        this.overrides.set(type, overrides);\n        this.resolved.delete(type);\n    }\n    setOverrides(overrides) {\n        this.overrides.clear();\n        overrides.forEach(([type, override]) => {\n            this.addOverride(type, override);\n        });\n    }\n    getAnnotation(type) {\n        const annotations = reflection.annotations(type);\n        // Try to find the nearest known Type annotation and make sure that this annotation is an\n        // instance of the type we are looking for, so we can use it for resolution. Note: there might\n        // be multiple known annotations found due to the fact that Components can extend Directives (so\n        // both Directive and Component annotations would be present), so we always check if the known\n        // annotation has the right type.\n        for (let i = annotations.length - 1; i >= 0; i--) {\n            const annotation = annotations[i];\n            const isKnownType = annotation instanceof Directive ||\n                annotation instanceof Component ||\n                annotation instanceof Pipe ||\n                annotation instanceof NgModule;\n            if (isKnownType) {\n                return annotation instanceof this.type ? annotation : null;\n            }\n        }\n        return null;\n    }\n    resolve(type) {\n        let resolved = this.resolved.get(type) || null;\n        if (!resolved) {\n            resolved = this.getAnnotation(type);\n            if (resolved) {\n                const overrides = this.overrides.get(type);\n                if (overrides) {\n                    const overrider = new MetadataOverrider();\n                    overrides.forEach((override) => {\n                        resolved = overrider.overrideMetadata(this.type, resolved, override);\n                    });\n                }\n            }\n            this.resolved.set(type, resolved);\n        }\n        return resolved;\n    }\n}\nclass DirectiveResolver extends OverrideResolver {\n    get type() {\n        return Directive;\n    }\n}\nclass ComponentResolver extends OverrideResolver {\n    get type() {\n        return Component;\n    }\n}\nclass PipeResolver extends OverrideResolver {\n    get type() {\n        return Pipe;\n    }\n}\nclass NgModuleResolver extends OverrideResolver {\n    get type() {\n        return NgModule;\n    }\n}\n\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n    TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n    TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n    return (value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE);\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n    types.forEach((type) => {\n        if (!ɵgetAsyncClassMetadataFn(type)) {\n            const component = resolver.resolve(type);\n            if (component && component.standalone) {\n                throw new Error(ɵgenerateStandaloneInDeclarationsError(type, location));\n            }\n        }\n    });\n}\nclass TestBedCompiler {\n    constructor(platform, additionalModuleTypes) {\n        this.platform = platform;\n        this.additionalModuleTypes = additionalModuleTypes;\n        this.originalComponentResolutionQueue = null;\n        // Testing module configuration\n        this.declarations = [];\n        this.imports = [];\n        this.providers = [];\n        this.schemas = [];\n        // Queues of components/directives/pipes that should be recompiled.\n        this.pendingComponents = new Set();\n        this.pendingDirectives = new Set();\n        this.pendingPipes = new Set();\n        // Set of components with async metadata, i.e. components with `@defer` blocks\n        // in their templates.\n        this.componentsWithAsyncMetadata = new Set();\n        // Keep track of all components and directives, so we can patch Providers onto defs later.\n        this.seenComponents = new Set();\n        this.seenDirectives = new Set();\n        // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n        this.overriddenModules = new Set();\n        // Store resolved styles for Components that have template overrides present and `styleUrls`\n        // defined at the same time.\n        this.existingComponentStyles = new Map();\n        this.resolvers = initResolvers();\n        // Map of component type to an NgModule that declares it.\n        //\n        // There are a couple special cases:\n        // - for standalone components, the module scope value is `null`\n        // - when a component is declared in `TestBed.configureTestingModule()` call or\n        //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n        //   we use a special value from the `TestingModuleOverride` enum.\n        this.componentToModuleScope = new Map();\n        // Map that keeps initial version of component/directive/pipe defs in case\n        // we compile a Type again, thus overriding respective static fields. This is\n        // required to make sure we restore defs to their initial states between test runs.\n        // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n        // NgModule), store all of them in a map.\n        this.initialNgDefs = new Map();\n        // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n        // defs in case TestBed makes changes to the originals.\n        this.defCleanupOps = [];\n        this._injector = null;\n        this.compilerProviders = null;\n        this.providerOverrides = [];\n        this.rootProviderOverrides = [];\n        // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n        // module's provider list.\n        this.providerOverridesByModule = new Map();\n        this.providerOverridesByToken = new Map();\n        this.scopesWithOverriddenProviders = new Set();\n        this.testModuleRef = null;\n        this.deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        class DynamicTestModule {\n        }\n        this.testModuleType = DynamicTestModule;\n    }\n    setCompilerProviders(providers) {\n        this.compilerProviders = providers;\n        this._injector = null;\n    }\n    configureTestingModule(moduleDef) {\n        // Enqueue any compilation tasks for the directly declared component.\n        if (moduleDef.declarations !== undefined) {\n            // Verify that there are no standalone components\n            assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n            this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n            this.declarations.push(...moduleDef.declarations);\n        }\n        // Enqueue any compilation tasks for imported modules.\n        if (moduleDef.imports !== undefined) {\n            this.queueTypesFromModulesArray(moduleDef.imports);\n            this.imports.push(...moduleDef.imports);\n        }\n        if (moduleDef.providers !== undefined) {\n            this.providers.push(...moduleDef.providers);\n        }\n        this.providers.push({\n            provide: RETHROW_APPLICATION_ERRORS,\n            useValue: moduleDef._rethrowApplicationTickErrors ?? false,\n        });\n        if (moduleDef.schemas !== undefined) {\n            this.schemas.push(...moduleDef.schemas);\n        }\n        this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    }\n    overrideModule(ngModule, override) {\n        if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            ɵdepsTracker.clearScopeCacheFor(ngModule);\n        }\n        this.overriddenModules.add(ngModule);\n        // Compile the module right away.\n        this.resolvers.module.addOverride(ngModule, override);\n        const metadata = this.resolvers.module.resolve(ngModule);\n        if (metadata === null) {\n            throw invalidTypeError(ngModule.name, 'NgModule');\n        }\n        this.recompileNgModule(ngModule, metadata);\n        // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n        // new declarations or imported modules. Ingest any possible new types and add them to the\n        // current queue.\n        this.queueTypesFromModulesArray([ngModule]);\n    }\n    overrideComponent(component, override) {\n        this.verifyNoStandaloneFlagOverrides(component, override);\n        this.resolvers.component.addOverride(component, override);\n        this.pendingComponents.add(component);\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(component);\n    }\n    overrideDirective(directive, override) {\n        this.verifyNoStandaloneFlagOverrides(directive, override);\n        this.resolvers.directive.addOverride(directive, override);\n        this.pendingDirectives.add(directive);\n    }\n    overridePipe(pipe, override) {\n        this.verifyNoStandaloneFlagOverrides(pipe, override);\n        this.resolvers.pipe.addOverride(pipe, override);\n        this.pendingPipes.add(pipe);\n    }\n    verifyNoStandaloneFlagOverrides(type, override) {\n        if (override.add?.hasOwnProperty('standalone') ||\n            override.set?.hasOwnProperty('standalone') ||\n            override.remove?.hasOwnProperty('standalone')) {\n            throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` +\n                `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n        }\n    }\n    overrideProvider(token, provider) {\n        let providerDef;\n        if (provider.useFactory !== undefined) {\n            providerDef = {\n                provide: token,\n                useFactory: provider.useFactory,\n                deps: provider.deps || [],\n                multi: provider.multi,\n            };\n        }\n        else if (provider.useValue !== undefined) {\n            providerDef = { provide: token, useValue: provider.useValue, multi: provider.multi };\n        }\n        else {\n            providerDef = { provide: token };\n        }\n        const injectableDef = typeof token !== 'string' ? ɵgetInjectableDef(token) : null;\n        const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n        const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n        overridesBucket.push(providerDef);\n        // Keep overrides grouped by token as well for fast lookups using token\n        this.providerOverridesByToken.set(token, providerDef);\n        if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n            const existingOverrides = this.providerOverridesByModule.get(providedIn);\n            if (existingOverrides !== undefined) {\n                existingOverrides.push(providerDef);\n            }\n            else {\n                this.providerOverridesByModule.set(providedIn, [providerDef]);\n            }\n        }\n    }\n    overrideTemplateUsingTestingModule(type, template) {\n        const def = type[ɵNG_COMP_DEF];\n        const hasStyleUrls = () => {\n            const metadata = this.resolvers.component.resolve(type);\n            return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n        };\n        const overrideStyleUrls = !!def && !ɵisComponentDefPendingResolution(type) && hasStyleUrls();\n        // In Ivy, compiling a component does not require knowing the module providing the\n        // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n        // overrideComponent. Important: overriding template requires full Component re-compilation,\n        // which may fail in case styleUrls are also present (thus Component is considered as required\n        // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n        // preserve current styles available on Component def and restore styles back once compilation\n        // is complete.\n        const override = overrideStyleUrls\n            ? { template, styles: [], styleUrls: [], styleUrl: undefined }\n            : { template };\n        this.overrideComponent(type, { set: override });\n        if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n            this.existingComponentStyles.set(type, def.styles);\n        }\n        // Set the component's scope to be the testing module.\n        this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n    }\n    async resolvePendingComponentsWithAsyncMetadata() {\n        if (this.componentsWithAsyncMetadata.size === 0)\n            return;\n        const promises = [];\n        for (const component of this.componentsWithAsyncMetadata) {\n            const asyncMetadataFn = ɵgetAsyncClassMetadataFn(component);\n            if (asyncMetadataFn) {\n                promises.push(asyncMetadataFn());\n            }\n        }\n        this.componentsWithAsyncMetadata.clear();\n        const resolvedDeps = await Promise.all(promises);\n        const flatResolvedDeps = resolvedDeps.flat(2);\n        this.queueTypesFromModulesArray(flatResolvedDeps);\n        // Loaded standalone components might contain imports of NgModules\n        // with providers, make sure we override providers there too.\n        for (const component of flatResolvedDeps) {\n            this.applyProviderOverridesInScope(component);\n        }\n    }\n    async compileComponents() {\n        this.clearComponentResolutionQueue();\n        // Wait for all async metadata for components that were\n        // overridden, we need resolved metadata to perform an override\n        // and re-compile a component.\n        await this.resolvePendingComponentsWithAsyncMetadata();\n        // Verify that there were no standalone components present in the `declarations` field\n        // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n        // to the logic in the `configureTestingModule` function, since at this point we have\n        // all async metadata resolved.\n        assertNoStandaloneComponents(this.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n        // Run compilers for all queued types.\n        let needsAsyncResources = this.compileTypesSync();\n        // compileComponents() should not be async unless it needs to be.\n        if (needsAsyncResources) {\n            let resourceLoader;\n            let resolver = (url) => {\n                if (!resourceLoader) {\n                    resourceLoader = this.injector.get(ResourceLoader);\n                }\n                return Promise.resolve(resourceLoader.get(url));\n            };\n            await ɵresolveComponentResources(resolver);\n        }\n    }\n    finalize() {\n        // One last compile\n        this.compileTypesSync();\n        // Create the testing module itself.\n        this.compileTestModule();\n        this.applyTransitiveScopes();\n        this.applyProviderOverrides();\n        // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n        // Components have `styleUrls` fields defined and template override was requested.\n        this.patchComponentsWithExistingStyles();\n        // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n        // every component.\n        this.componentToModuleScope.clear();\n        const parentInjector = this.platform.injector;\n        this.testModuleRef = new ɵRender3NgModuleRef(this.testModuleType, parentInjector, []);\n        // ApplicationInitStatus.runInitializers() is marked @internal to core.\n        // Cast it to any before accessing it.\n        this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n        // Set locale ID after running app initializers, since locale information might be updated while\n        // running initializers. This is also consistent with the execution order while bootstrapping an\n        // app (see `packages/core/src/application_ref.ts` file).\n        const localeId = this.testModuleRef.injector.get(LOCALE_ID, ɵDEFAULT_LOCALE_ID);\n        ɵsetLocaleId(localeId);\n        return this.testModuleRef;\n    }\n    /**\n     * @internal\n     */\n    _compileNgModuleSync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        this.compileTypesSync();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    async _compileNgModuleAsync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        await this.compileComponents();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    _getModuleResolver() {\n        return this.resolvers.module;\n    }\n    /**\n     * @internal\n     */\n    _getComponentFactories(moduleType) {\n        return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n            const componentDef = declaration.ɵcmp;\n            componentDef && factories.push(new ɵRender3ComponentFactory(componentDef, this.testModuleRef));\n            return factories;\n        }, []);\n    }\n    compileTypesSync() {\n        // Compile all queued components, directives, pipes.\n        let needsAsyncResources = false;\n        this.pendingComponents.forEach((declaration) => {\n            if (ɵgetAsyncClassMetadataFn(declaration)) {\n                throw new Error(`Component '${declaration.name}' has unresolved metadata. ` +\n                    `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n            }\n            needsAsyncResources = needsAsyncResources || ɵisComponentDefPendingResolution(declaration);\n            const metadata = this.resolvers.component.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Component');\n            }\n            this.maybeStoreNgDef(ɵNG_COMP_DEF, declaration);\n            if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                ɵdepsTracker.clearScopeCacheFor(declaration);\n            }\n            ɵcompileComponent(declaration, metadata);\n        });\n        this.pendingComponents.clear();\n        this.pendingDirectives.forEach((declaration) => {\n            const metadata = this.resolvers.directive.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Directive');\n            }\n            this.maybeStoreNgDef(ɵNG_DIR_DEF, declaration);\n            ɵcompileDirective(declaration, metadata);\n        });\n        this.pendingDirectives.clear();\n        this.pendingPipes.forEach((declaration) => {\n            const metadata = this.resolvers.pipe.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Pipe');\n            }\n            this.maybeStoreNgDef(ɵNG_PIPE_DEF, declaration);\n            ɵcompilePipe(declaration, metadata);\n        });\n        this.pendingPipes.clear();\n        return needsAsyncResources;\n    }\n    applyTransitiveScopes() {\n        if (this.overriddenModules.size > 0) {\n            // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n            // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n            // collect all affected modules and reset scopes to force their re-calculation.\n            const testingModuleDef = this.testModuleType[ɵNG_MOD_DEF];\n            const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n            if (affectedModules.size > 0) {\n                affectedModules.forEach((moduleType) => {\n                    if (!ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                        this.storeFieldOfDefOnType(moduleType, ɵNG_MOD_DEF, 'transitiveCompileScopes');\n                        moduleType[ɵNG_MOD_DEF].transitiveCompileScopes = null;\n                    }\n                    else {\n                        ɵdepsTracker.clearScopeCacheFor(moduleType);\n                    }\n                });\n            }\n        }\n        const moduleToScope = new Map();\n        const getScopeOfModule = (moduleType) => {\n            if (!moduleToScope.has(moduleType)) {\n                const isTestingModule = isTestingModuleOverride(moduleType);\n                const realType = isTestingModule ? this.testModuleType : moduleType;\n                moduleToScope.set(moduleType, ɵtransitiveScopesFor(realType));\n            }\n            return moduleToScope.get(moduleType);\n        };\n        this.componentToModuleScope.forEach((moduleType, componentType) => {\n            if (moduleType !== null) {\n                const moduleScope = getScopeOfModule(moduleType);\n                this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'directiveDefs');\n                this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'pipeDefs');\n                ɵpatchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n            }\n            // `tView` that is stored on component def contains information about directives and pipes\n            // that are in the scope of this component. Patching component scope will cause `tView` to be\n            // changed. Store original `tView` before patching scope, so the `tView` (including scope\n            // information) is restored back to its previous/original state before running next test.\n            // Resetting `tView` is also needed for cases when we apply provider overrides and those\n            // providers are defined on component's level, in which case they may end up included into\n            // `tView.blueprint`.\n            this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'tView');\n        });\n        this.componentToModuleScope.clear();\n    }\n    applyProviderOverrides() {\n        const maybeApplyOverrides = (field) => (type) => {\n            const resolver = field === ɵNG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n            const metadata = resolver.resolve(type);\n            if (this.hasProviderOverrides(metadata.providers)) {\n                this.patchDefWithProviderOverrides(type, field);\n            }\n        };\n        this.seenComponents.forEach(maybeApplyOverrides(ɵNG_COMP_DEF));\n        this.seenDirectives.forEach(maybeApplyOverrides(ɵNG_DIR_DEF));\n        this.seenComponents.clear();\n        this.seenDirectives.clear();\n    }\n    /**\n     * Applies provider overrides to a given type (either an NgModule or a standalone component)\n     * and all imported NgModules and standalone components recursively.\n     */\n    applyProviderOverridesInScope(type) {\n        const hasScope = isStandaloneComponent(type) || isNgModule(type);\n        // The function can be re-entered recursively while inspecting dependencies\n        // of an NgModule or a standalone component. Exit early if we come across a\n        // type that can not have a scope (directive or pipe) or the type is already\n        // processed earlier.\n        if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n            return;\n        }\n        this.scopesWithOverriddenProviders.add(type);\n        // NOTE: the line below triggers JIT compilation of the module injector,\n        // which also invokes verification of the NgModule semantics, which produces\n        // detailed error messages. The fact that the code relies on this line being\n        // present here is suspicious and should be refactored in a way that the line\n        // below can be moved (for ex. after an early exit check below).\n        const injectorDef = type[ɵNG_INJ_DEF];\n        // No provider overrides, exit early.\n        if (this.providerOverridesByToken.size === 0)\n            return;\n        if (isStandaloneComponent(type)) {\n            // Visit all component dependencies and override providers there.\n            const def = getComponentDef(type);\n            const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n            for (const dependency of dependencies) {\n                this.applyProviderOverridesInScope(dependency);\n            }\n        }\n        else {\n            const providers = [\n                ...injectorDef.providers,\n                ...(this.providerOverridesByModule.get(type) || []),\n            ];\n            if (this.hasProviderOverrides(providers)) {\n                this.maybeStoreNgDef(ɵNG_INJ_DEF, type);\n                this.storeFieldOfDefOnType(type, ɵNG_INJ_DEF, 'providers');\n                injectorDef.providers = this.getOverriddenProviders(providers);\n            }\n            // Apply provider overrides to imported modules recursively\n            const moduleDef = type[ɵNG_MOD_DEF];\n            const imports = maybeUnwrapFn(moduleDef.imports);\n            for (const importedModule of imports) {\n                this.applyProviderOverridesInScope(importedModule);\n            }\n            // Also override the providers on any ModuleWithProviders imports since those don't appear in\n            // the moduleDef.\n            for (const importedModule of flatten(injectorDef.imports)) {\n                if (isModuleWithProviders(importedModule)) {\n                    this.defCleanupOps.push({\n                        object: importedModule,\n                        fieldName: 'providers',\n                        originalValue: importedModule.providers,\n                    });\n                    importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n                }\n            }\n        }\n    }\n    patchComponentsWithExistingStyles() {\n        this.existingComponentStyles.forEach((styles, type) => (type[ɵNG_COMP_DEF].styles = styles));\n        this.existingComponentStyles.clear();\n    }\n    queueTypeArray(arr, moduleType) {\n        for (const value of arr) {\n            if (Array.isArray(value)) {\n                this.queueTypeArray(value, moduleType);\n            }\n            else {\n                this.queueType(value, moduleType);\n            }\n        }\n    }\n    recompileNgModule(ngModule, metadata) {\n        // Cache the initial ngModuleDef as it will be overwritten.\n        this.maybeStoreNgDef(ɵNG_MOD_DEF, ngModule);\n        this.maybeStoreNgDef(ɵNG_INJ_DEF, ngModule);\n        ɵcompileNgModuleDefs(ngModule, metadata);\n    }\n    maybeRegisterComponentWithAsyncMetadata(type) {\n        const asyncMetadataFn = ɵgetAsyncClassMetadataFn(type);\n        if (asyncMetadataFn) {\n            this.componentsWithAsyncMetadata.add(type);\n        }\n    }\n    queueType(type, moduleType) {\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(type);\n        const component = this.resolvers.component.resolve(type);\n        if (component) {\n            // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n            // missing. That might happen in case a class without any Angular decorators extends another\n            // class where Component/Directive/Pipe decorator is defined.\n            if (ɵisComponentDefPendingResolution(type) || !type.hasOwnProperty(ɵNG_COMP_DEF)) {\n                this.pendingComponents.add(type);\n            }\n            this.seenComponents.add(type);\n            // Keep track of the module which declares this component, so later the component's scope\n            // can be set correctly. If the component has already been recorded here, then one of several\n            // cases is true:\n            // * the module containing the component was imported multiple times (common).\n            // * the component is declared in multiple modules (which is an error).\n            // * the component was in 'declarations' of the testing module, and also in an imported module\n            //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n            // * overrideTemplateUsingTestingModule was called for the component in which case the module\n            //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n            //\n            // If the component was previously in the testing module's 'declarations' (meaning the\n            // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n            // real module, which was imported. This pattern is understood to mean that the component\n            // should use its original scope, but that the testing module should also contain the\n            // component in its scope.\n            if (!this.componentToModuleScope.has(type) ||\n                this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION) {\n                this.componentToModuleScope.set(type, moduleType);\n            }\n            return;\n        }\n        const directive = this.resolvers.directive.resolve(type);\n        if (directive) {\n            if (!type.hasOwnProperty(ɵNG_DIR_DEF)) {\n                this.pendingDirectives.add(type);\n            }\n            this.seenDirectives.add(type);\n            return;\n        }\n        const pipe = this.resolvers.pipe.resolve(type);\n        if (pipe && !type.hasOwnProperty(ɵNG_PIPE_DEF)) {\n            this.pendingPipes.add(type);\n            return;\n        }\n    }\n    queueTypesFromModulesArray(arr) {\n        // Because we may encounter the same NgModule or a standalone Component while processing\n        // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n        // can skip ones that have already been seen encountered. In some test setups, this caching\n        // resulted in 10X runtime improvement.\n        const processedDefs = new Set();\n        const queueTypesFromModulesArrayRecur = (arr) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    queueTypesFromModulesArrayRecur(value);\n                }\n                else if (hasNgModuleDef(value)) {\n                    const def = value.ɵmod;\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    // Look through declarations, imports, and exports, and queue\n                    // everything found there.\n                    this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n                }\n                else if (isModuleWithProviders(value)) {\n                    queueTypesFromModulesArrayRecur([value.ngModule]);\n                }\n                else if (isStandaloneComponent(value)) {\n                    this.queueType(value, null);\n                    const def = getComponentDef(value);\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n                    dependencies.forEach((dependency) => {\n                        // Note: in AOT, the `dependencies` might also contain regular\n                        // (NgModule-based) Component, Directive and Pipes, so we handle\n                        // them separately and proceed with recursive process for standalone\n                        // Components and NgModules only.\n                        if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n                            queueTypesFromModulesArrayRecur([dependency]);\n                        }\n                        else {\n                            this.queueType(dependency, null);\n                        }\n                    });\n                }\n            }\n        };\n        queueTypesFromModulesArrayRecur(arr);\n    }\n    // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n    // that import (even transitively) an overridden one. For all affected modules we need to\n    // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n    // of this function is to collect all affected modules in a set for further processing. Example:\n    // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n    // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n    // invalidated with the override.\n    collectModulesAffectedByOverrides(arr) {\n        const seenModules = new Set();\n        const affectedModules = new Set();\n        const calcAffectedModulesRecur = (arr, path) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    // If the value is an array, just flatten it (by invoking this function recursively),\n                    // keeping \"path\" the same.\n                    calcAffectedModulesRecur(value, path);\n                }\n                else if (hasNgModuleDef(value)) {\n                    if (seenModules.has(value)) {\n                        // If we've seen this module before and it's included into \"affected modules\" list, mark\n                        // the whole path that leads to that module as affected, but do not descend into its\n                        // imports, since we already examined them before.\n                        if (affectedModules.has(value)) {\n                            path.forEach((item) => affectedModules.add(item));\n                        }\n                        continue;\n                    }\n                    seenModules.add(value);\n                    if (this.overriddenModules.has(value)) {\n                        path.forEach((item) => affectedModules.add(item));\n                    }\n                    // Examine module imports recursively to look for overridden modules.\n                    const moduleDef = value[ɵNG_MOD_DEF];\n                    calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n                }\n            }\n        };\n        calcAffectedModulesRecur(arr, []);\n        return affectedModules;\n    }\n    /**\n     * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n     * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n     * an NgModule). If there is a def in a set already, don't override it, since\n     * an original one should be restored at the end of a test.\n     */\n    maybeStoreNgDef(prop, type) {\n        if (!this.initialNgDefs.has(type)) {\n            this.initialNgDefs.set(type, new Map());\n        }\n        const currentDefs = this.initialNgDefs.get(type);\n        if (!currentDefs.has(prop)) {\n            const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n            currentDefs.set(prop, currentDef);\n        }\n    }\n    storeFieldOfDefOnType(type, defField, fieldName) {\n        const def = type[defField];\n        const originalValue = def[fieldName];\n        this.defCleanupOps.push({ object: def, fieldName, originalValue });\n    }\n    /**\n     * Clears current components resolution queue, but stores the state of the queue, so we can\n     * restore it later. Clearing the queue is required before we try to compile components (via\n     * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n     */\n    clearComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue === null) {\n            this.originalComponentResolutionQueue = new Map();\n        }\n        ɵclearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n    }\n    /*\n     * Restores component resolution queue to the previously saved state. This operation is performed\n     * as a part of restoring the state after completion of the current set of tests (that might\n     * potentially mutate the state).\n     */\n    restoreComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue !== null) {\n            ɵrestoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n            this.originalComponentResolutionQueue = null;\n        }\n    }\n    restoreOriginalState() {\n        // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n        // case there were multiple overrides for the same field).\n        forEachRight(this.defCleanupOps, (op) => {\n            op.object[op.fieldName] = op.originalValue;\n        });\n        // Restore initial component/directive/pipe defs\n        this.initialNgDefs.forEach((defs, type) => {\n            if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                ɵdepsTracker.clearScopeCacheFor(type);\n            }\n            defs.forEach((descriptor, prop) => {\n                if (!descriptor) {\n                    // Delete operations are generally undesirable since they have performance\n                    // implications on objects they were applied to. In this particular case, situations\n                    // where this code is invoked should be quite rare to cause any noticeable impact,\n                    // since it's applied only to some test cases (for example when class with no\n                    // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n                    // class to restore its original state (before applying overrides and running tests).\n                    delete type[prop];\n                }\n                else {\n                    Object.defineProperty(type, prop, descriptor);\n                }\n            });\n        });\n        this.initialNgDefs.clear();\n        this.scopesWithOverriddenProviders.clear();\n        this.restoreComponentResolutionQueue();\n        // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n        ɵsetLocaleId(ɵDEFAULT_LOCALE_ID);\n    }\n    compileTestModule() {\n        class RootScopeModule {\n        }\n        ɵcompileNgModuleDefs(RootScopeModule, {\n            providers: [\n                ...this.rootProviderOverrides,\n                ɵinternalProvideZoneChangeDetection({}),\n                TestBedApplicationErrorHandler,\n                {\n                    provide: ɵINTERNAL_APPLICATION_ERROR_HANDLER,\n                    useFactory: () => {\n                        if (inject$1(ɵZONELESS_ENABLED) || inject$1(RETHROW_APPLICATION_ERRORS, { optional: true })) {\n                            const handler = inject$1(TestBedApplicationErrorHandler);\n                            return (e) => {\n                                handler.handleError(e);\n                            };\n                        }\n                        else {\n                            const userErrorHandler = inject$1(ErrorHandler);\n                            const ngZone = inject$1(NgZone);\n                            return (e) => ngZone.runOutsideAngular(() => userErrorHandler.handleError(e));\n                        }\n                    },\n                },\n                { provide: ɵChangeDetectionScheduler, useExisting: ɵChangeDetectionSchedulerImpl },\n            ],\n        });\n        const providers = [\n            { provide: Compiler, useFactory: () => new R3TestCompiler(this) },\n            { provide: ɵDEFER_BLOCK_CONFIG, useValue: { behavior: this.deferBlockBehavior } },\n            ...this.providers,\n            ...this.providerOverrides,\n        ];\n        const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n        ɵcompileNgModuleDefs(this.testModuleType, {\n            declarations: this.declarations,\n            imports,\n            schemas: this.schemas,\n            providers,\n        }, \n        /* allowDuplicateDeclarationsInRoot */ true);\n        this.applyProviderOverridesInScope(this.testModuleType);\n    }\n    get injector() {\n        if (this._injector !== null) {\n            return this._injector;\n        }\n        const providers = [];\n        const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS);\n        compilerOptions.forEach((opts) => {\n            if (opts.providers) {\n                providers.push(opts.providers);\n            }\n        });\n        if (this.compilerProviders !== null) {\n            providers.push(...this.compilerProviders);\n        }\n        this._injector = Injector.create({ providers, parent: this.platform.injector });\n        return this._injector;\n    }\n    // get overrides for a specific provider (if any)\n    getSingleProviderOverrides(provider) {\n        const token = getProviderToken(provider);\n        return this.providerOverridesByToken.get(token) || null;\n    }\n    getProviderOverrides(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        // There are two flattening operations here. The inner flattenProviders() operates on the\n        // metadata's providers and applies a mapping function which retrieves overrides for each\n        // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n        // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n        // providers array and contaminate any error messages that might be generated.\n        return flatten(flattenProviders(providers, (provider) => this.getSingleProviderOverrides(provider) || []));\n    }\n    getOverriddenProviders(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        const flattenedProviders = flattenProviders(providers);\n        const overrides = this.getProviderOverrides(flattenedProviders);\n        const overriddenProviders = [...flattenedProviders, ...overrides];\n        const final = [];\n        const seenOverriddenProviders = new Set();\n        // We iterate through the list of providers in reverse order to make sure provider overrides\n        // take precedence over the values defined in provider list. We also filter out all providers\n        // that have overrides, keeping overridden values only. This is needed, since presence of a\n        // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n        forEachRight(overriddenProviders, (provider) => {\n            const token = getProviderToken(provider);\n            if (this.providerOverridesByToken.has(token)) {\n                if (!seenOverriddenProviders.has(token)) {\n                    seenOverriddenProviders.add(token);\n                    // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n                    // make sure that provided override takes highest precedence and is not combined with\n                    // other instances of the same multi provider.\n                    final.unshift({ ...provider, multi: false });\n                }\n            }\n            else {\n                final.unshift(provider);\n            }\n        });\n        return final;\n    }\n    hasProviderOverrides(providers) {\n        return this.getProviderOverrides(providers).length > 0;\n    }\n    patchDefWithProviderOverrides(declaration, field) {\n        const def = declaration[field];\n        if (def && def.providersResolver) {\n            this.maybeStoreNgDef(field, declaration);\n            const resolver = def.providersResolver;\n            const processProvidersFn = (providers) => this.getOverriddenProviders(providers);\n            this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n            def.providersResolver = (ngDef) => resolver(ngDef, processProvidersFn);\n        }\n    }\n}\nfunction initResolvers() {\n    return {\n        module: new NgModuleResolver(),\n        component: new ComponentResolver(),\n        directive: new DirectiveResolver(),\n        pipe: new PipeResolver(),\n    };\n}\nfunction isStandaloneComponent(value) {\n    const def = getComponentDef(value);\n    return !!def?.standalone;\n}\nfunction getComponentDef(value) {\n    return value.ɵcmp ?? null;\n}\nfunction hasNgModuleDef(value) {\n    return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n    return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n    return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n    const out = [];\n    values.forEach((value) => {\n        if (Array.isArray(value)) {\n            out.push(...flatten(value));\n        }\n        else {\n            out.push(value);\n        }\n    });\n    return out;\n}\nfunction identityFn(value) {\n    return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n    const out = [];\n    for (let provider of providers) {\n        if (ɵisEnvironmentProviders(provider)) {\n            provider = provider.ɵproviders;\n        }\n        if (Array.isArray(provider)) {\n            out.push(...flattenProviders(provider, mapFn));\n        }\n        else {\n            out.push(mapFn(provider));\n        }\n    }\n    return out;\n}\nfunction getProviderField(provider, field) {\n    return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n    return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n    return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n    for (let idx = values.length - 1; idx >= 0; idx--) {\n        fn(values[idx], idx);\n    }\n}\nfunction invalidTypeError(name, expectedType) {\n    return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n    constructor(testBed) {\n        this.testBed = testBed;\n    }\n    compileModuleSync(moduleType) {\n        this.testBed._compileNgModuleSync(moduleType);\n        return new ɵNgModuleFactory(moduleType);\n    }\n    async compileModuleAsync(moduleType) {\n        await this.testBed._compileNgModuleAsync(moduleType);\n        return new ɵNgModuleFactory(moduleType);\n    }\n    compileModuleAndAllComponentsSync(moduleType) {\n        const ngModuleFactory = this.compileModuleSync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    async compileModuleAndAllComponentsAsync(moduleType) {\n        const ngModuleFactory = await this.compileModuleAsync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    clearCache() { }\n    clearCacheFor(type) { }\n    getModuleId(moduleType) {\n        const meta = this.testBed._getModuleResolver().resolve(moduleType);\n        return (meta && meta.id) || undefined;\n    }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n    return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n    constructor() {\n        /**\n         * Defer block behavior option that specifies whether defer blocks will be triggered manually\n         * or set to play through.\n         */\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        // Properties\n        this.platform = null;\n        this.ngModule = null;\n        this._compiler = null;\n        this._testModuleRef = null;\n        this._activeFixtures = [];\n        /**\n         * Internal-only flag to indicate whether a module\n         * scoping queue has been checked and flushed already.\n         * @nodoc\n         */\n        this.globalCompilationChecked = false;\n    }\n    static { this._INSTANCE = null; }\n    static get INSTANCE() {\n        return (TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl());\n    }\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    static initTestEnvironment(ngModule, platform, options) {\n        const testBed = TestBedImpl.INSTANCE;\n        testBed.initTestEnvironment(ngModule, platform, options);\n        return testBed;\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    static resetTestEnvironment() {\n        TestBedImpl.INSTANCE.resetTestEnvironment();\n    }\n    static configureCompiler(config) {\n        return TestBedImpl.INSTANCE.configureCompiler(config);\n    }\n    /**\n     * Allows overriding default providers, directives, pipes, modules of the test injector,\n     * which are defined in test_injector.js\n     */\n    static configureTestingModule(moduleDef) {\n        return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n    }\n    /**\n     * Compile components with a `templateUrl` for the test's NgModule.\n     * It is necessary to call this function\n     * as fetching urls is asynchronous.\n     */\n    static compileComponents() {\n        return TestBedImpl.INSTANCE.compileComponents();\n    }\n    static overrideModule(ngModule, override) {\n        return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n    }\n    static overrideComponent(component, override) {\n        return TestBedImpl.INSTANCE.overrideComponent(component, override);\n    }\n    static overrideDirective(directive, override) {\n        return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n    }\n    static overridePipe(pipe, override) {\n        return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n    }\n    static overrideTemplate(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n    }\n    /**\n     * Overrides the template of the given component, compiling the template\n     * in the context of the TestingModule.\n     *\n     * Note: This works for JIT and AOTed components as well.\n     */\n    static overrideTemplateUsingTestingModule(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n    }\n    static overrideProvider(token, provider) {\n        return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n    }\n    static inject(token, notFoundValue, flags) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, ɵconvertToBitFlags(flags));\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n    }\n    /**\n     * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n     *\n     * @see {@link EnvironmentInjector#runInContext}\n     */\n    static runInInjectionContext(fn) {\n        return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n    }\n    static createComponent(component) {\n        return TestBedImpl.INSTANCE.createComponent(component);\n    }\n    static resetTestingModule() {\n        return TestBedImpl.INSTANCE.resetTestingModule();\n    }\n    static execute(tokens, fn, context) {\n        return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n    }\n    static get platform() {\n        return TestBedImpl.INSTANCE.platform;\n    }\n    static get ngModule() {\n        return TestBedImpl.INSTANCE.ngModule;\n    }\n    static flushEffects() {\n        return TestBedImpl.INSTANCE.flushEffects();\n    }\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    initTestEnvironment(ngModule, platform, options) {\n        if (this.platform || this.ngModule) {\n            throw new Error('Cannot set base providers because it has already been called');\n        }\n        TestBedImpl._environmentTeardownOptions = options?.teardown;\n        TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n        TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n        this.platform = platform;\n        this.ngModule = ngModule;\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n        // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n        // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n        // completely.\n        ɵsetAllowDuplicateNgModuleIdsForTest(true);\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    resetTestEnvironment() {\n        this.resetTestingModule();\n        this._compiler = null;\n        this.platform = null;\n        this.ngModule = null;\n        TestBedImpl._environmentTeardownOptions = undefined;\n        ɵsetAllowDuplicateNgModuleIdsForTest(false);\n    }\n    resetTestingModule() {\n        this.checkGlobalCompilationFinished();\n        ɵresetCompiledComponents();\n        if (this._compiler !== null) {\n            this.compiler.restoreOriginalState();\n        }\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // Restore the previous value of the \"error on unknown elements\" option\n        ɵsetUnknownElementStrictMode(this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n        // Restore the previous value of the \"error on unknown properties\" option\n        ɵsetUnknownPropertyStrictMode(this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n        // We have to chain a couple of try/finally blocks, because each step can\n        // throw errors and we don't want it to interrupt the next step and we also\n        // want an error to be thrown at the end.\n        try {\n            this.destroyActiveFixtures();\n        }\n        finally {\n            try {\n                if (this.shouldTearDownTestingModule()) {\n                    this.tearDownTestingModule();\n                }\n            }\n            finally {\n                this._testModuleRef = null;\n                this._instanceTeardownOptions = undefined;\n                this._instanceErrorOnUnknownElementsOption = undefined;\n                this._instanceErrorOnUnknownPropertiesOption = undefined;\n                this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n            }\n        }\n        return this;\n    }\n    configureCompiler(config) {\n        if (config.useJit != null) {\n            throw new Error('JIT compiler is not configurable via TestBed APIs.');\n        }\n        if (config.providers !== undefined) {\n            this.compiler.setCompilerProviders(config.providers);\n        }\n        return this;\n    }\n    configureTestingModule(moduleDef) {\n        this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n        // Trigger module scoping queue flush before executing other TestBed operations in a test.\n        // This is needed for the first test invocation to ensure that globally declared modules have\n        // their components scoped properly. See the `checkGlobalCompilationFinished` function\n        // description for additional info.\n        this.checkGlobalCompilationFinished();\n        // Always re-assign the options, even if they're undefined.\n        // This ensures that we don't carry them between tests.\n        this._instanceTeardownOptions = moduleDef.teardown;\n        this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n        this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n        this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        // Store the current value of the strict mode option,\n        // so we can restore it later\n        this._previousErrorOnUnknownElementsOption = ɵgetUnknownElementStrictMode();\n        ɵsetUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n        this._previousErrorOnUnknownPropertiesOption = ɵgetUnknownPropertyStrictMode();\n        ɵsetUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n        this.compiler.configureTestingModule(moduleDef);\n        return this;\n    }\n    compileComponents() {\n        return this.compiler.compileComponents();\n    }\n    inject(token, notFoundValue, flags) {\n        if (token === TestBed) {\n            return this;\n        }\n        const UNDEFINED = {};\n        const result = this.testModuleRef.injector.get(token, UNDEFINED, ɵconvertToBitFlags(flags));\n        return result === UNDEFINED\n            ? this.compiler.injector.get(token, notFoundValue, flags)\n            : result;\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return this.inject(token, notFoundValue, flags);\n    }\n    runInInjectionContext(fn) {\n        return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n    }\n    execute(tokens, fn, context) {\n        const params = tokens.map((t) => this.inject(t));\n        return fn.apply(context, params);\n    }\n    overrideModule(ngModule, override) {\n        this.assertNotInstantiated('overrideModule', 'override module metadata');\n        this.compiler.overrideModule(ngModule, override);\n        return this;\n    }\n    overrideComponent(component, override) {\n        this.assertNotInstantiated('overrideComponent', 'override component metadata');\n        this.compiler.overrideComponent(component, override);\n        return this;\n    }\n    overrideTemplateUsingTestingModule(component, template) {\n        this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n        this.compiler.overrideTemplateUsingTestingModule(component, template);\n        return this;\n    }\n    overrideDirective(directive, override) {\n        this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n        this.compiler.overrideDirective(directive, override);\n        return this;\n    }\n    overridePipe(pipe, override) {\n        this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n        this.compiler.overridePipe(pipe, override);\n        return this;\n    }\n    /**\n     * Overwrites all providers for the given token with the given provider definition.\n     */\n    overrideProvider(token, provider) {\n        this.assertNotInstantiated('overrideProvider', 'override provider');\n        this.compiler.overrideProvider(token, provider);\n        return this;\n    }\n    overrideTemplate(component, template) {\n        return this.overrideComponent(component, { set: { template, templateUrl: null } });\n    }\n    createComponent(type) {\n        const testComponentRenderer = this.inject(TestComponentRenderer);\n        const rootElId = `root${_nextRootElementId++}`;\n        testComponentRenderer.insertRootElement(rootElId);\n        if (ɵgetAsyncClassMetadataFn(type)) {\n            throw new Error(`Component '${type.name}' has unresolved metadata. ` +\n                `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n        }\n        const componentDef = type.ɵcmp;\n        if (!componentDef) {\n            throw new Error(`It looks like '${ɵstringify(type)}' has not been compiled.`);\n        }\n        const componentFactory = new ɵRender3ComponentFactory(componentDef);\n        const initComponent = () => {\n            const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n            return this.runInInjectionContext(() => {\n                const isZoneless = this.inject(ɵZONELESS_ENABLED);\n                const fixture = isZoneless\n                    ? new ScheduledComponentFixture(componentRef)\n                    : new PseudoApplicationComponentFixture(componentRef);\n                fixture.initialize();\n                return fixture;\n            });\n        };\n        const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n        const ngZone = noNgZone ? null : this.inject(NgZone, null);\n        const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n        this._activeFixtures.push(fixture);\n        return fixture;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get compiler() {\n        if (this._compiler === null) {\n            throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n        }\n        return this._compiler;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get testModuleRef() {\n        if (this._testModuleRef === null) {\n            this._testModuleRef = this.compiler.finalize();\n        }\n        return this._testModuleRef;\n    }\n    assertNotInstantiated(methodName, methodDescription) {\n        if (this._testModuleRef !== null) {\n            throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` +\n                `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n        }\n    }\n    /**\n     * Check whether the module scoping queue should be flushed, and flush it if needed.\n     *\n     * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n     * in-progress module compilation. This creates a potential hazard - the very first time the\n     * TestBed is initialized (or if it's reset without being initialized), there may be pending\n     * compilations of modules declared in global scope. These compilations should be finished.\n     *\n     * To ensure that globally declared modules have their components scoped properly, this function\n     * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n     * to any other operations, the scoping queue is flushed.\n     */\n    checkGlobalCompilationFinished() {\n        // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n        // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n        if (!this.globalCompilationChecked && this._testModuleRef === null) {\n            ɵflushModuleScopingQueueAsMuchAsPossible();\n        }\n        this.globalCompilationChecked = true;\n    }\n    destroyActiveFixtures() {\n        let errorCount = 0;\n        this._activeFixtures.forEach((fixture) => {\n            try {\n                fixture.destroy();\n            }\n            catch (e) {\n                errorCount++;\n                console.error('Error during cleanup of component', {\n                    component: fixture.componentInstance,\n                    stacktrace: e,\n                });\n            }\n        });\n        this._activeFixtures = [];\n        if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n            throw Error(`${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` +\n                `threw errors during cleanup`);\n        }\n    }\n    shouldRethrowTeardownErrors() {\n        const instanceOptions = this._instanceTeardownOptions;\n        const environmentOptions = TestBedImpl._environmentTeardownOptions;\n        // If the new teardown behavior hasn't been configured, preserve the old behavior.\n        if (!instanceOptions && !environmentOptions) {\n            return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n        }\n        // Otherwise use the configured behavior or default to rethrowing.\n        return (instanceOptions?.rethrowErrors ??\n            environmentOptions?.rethrowErrors ??\n            this.shouldTearDownTestingModule());\n    }\n    shouldThrowErrorOnUnknownElements() {\n        // Check if a configuration has been provided to throw when an unknown element is found\n        return (this._instanceErrorOnUnknownElementsOption ??\n            TestBedImpl._environmentErrorOnUnknownElementsOption ??\n            THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    }\n    shouldThrowErrorOnUnknownProperties() {\n        // Check if a configuration has been provided to throw when an unknown property is found\n        return (this._instanceErrorOnUnknownPropertiesOption ??\n            TestBedImpl._environmentErrorOnUnknownPropertiesOption ??\n            THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n    }\n    shouldTearDownTestingModule() {\n        return (this._instanceTeardownOptions?.destroyAfterEach ??\n            TestBedImpl._environmentTeardownOptions?.destroyAfterEach ??\n            TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT);\n    }\n    getDeferBlockBehavior() {\n        return this._instanceDeferBlockBehavior;\n    }\n    tearDownTestingModule() {\n        // If the module ref has already been destroyed, we won't be able to get a test renderer.\n        if (this._testModuleRef === null) {\n            return;\n        }\n        // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n        // last step, but the injector will be destroyed as a part of the module ref destruction.\n        const testRenderer = this.inject(TestComponentRenderer);\n        try {\n            this._testModuleRef.destroy();\n        }\n        catch (e) {\n            if (this.shouldRethrowTeardownErrors()) {\n                throw e;\n            }\n            else {\n                console.error('Error during cleanup of a testing module', {\n                    component: this._testModuleRef.instance,\n                    stacktrace: e,\n                });\n            }\n        }\n        finally {\n            testRenderer.removeAllRootElements?.();\n        }\n    }\n    /**\n     * Execute any pending effects.\n     *\n     * @developerPreview\n     */\n    flushEffects() {\n        this.inject(ɵEffectScheduler).flush();\n    }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n    const testBed = TestBedImpl.INSTANCE;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n        return testBed.execute(tokens, fn, this);\n    };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n    constructor(_moduleDef) {\n        this._moduleDef = _moduleDef;\n    }\n    _addModule() {\n        const moduleDef = this._moduleDef();\n        if (moduleDef) {\n            TestBedImpl.configureTestingModule(moduleDef);\n        }\n    }\n    inject(tokens, fn) {\n        const self = this;\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            self._addModule();\n            return inject(tokens, fn).call(this);\n        };\n    }\n}\nfunction withModule(moduleDef, fn) {\n    if (fn) {\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            const testBed = TestBedImpl.INSTANCE;\n            if (moduleDef) {\n                testBed.configureTestingModule(moduleDef);\n            }\n            return fn.apply(this);\n        };\n    }\n    return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n    return () => {\n        const testBed = TestBedImpl.INSTANCE;\n        if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n            testBed.resetTestingModule();\n            resetFakeAsyncZoneIfExists();\n        }\n    };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, MetadataOverrider as ɵMetadataOverrider };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,gBAAgB,EAAEC,uBAAuB,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,8BAA8B,EAAEC,UAAU,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,wBAAwB,EAAEC,sCAAsC,EAAEC,iCAAiC,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,gCAAgC,EAAEC,0BAA0B,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,2BAA2B,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,yCAAyC,EAAEC,gCAAgC,EAAEC,mCAAmC,EAAEC,mCAAmC,EAAEC,iBAAiB,EAAEC,yBAAyB,EAAEC,6BAA6B,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAEC,4BAA4B,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,oCAAoC,EAAEC,wBAAwB,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,wCAAwC,QAAQ,eAAe;AACnlD,SAASrE,mBAAmB,IAAIsE,kBAAkB,EAAE3E,gBAAgB,IAAI4E,eAAe,QAAQ,eAAe;AAC9G,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,cAAc,QAAQ,mBAAmB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,EAAE,EAAE;EACtB,MAAMC,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;EACvD,IAAI,CAACD,KAAK,EAAE;IACR,OAAO,YAAY;MACf,OAAOE,OAAO,CAACC,MAAM,CAAC,4EAA4E,GAC9F,yDAAyD,CAAC;IAClE,CAAC;EACL;EACA,MAAMC,SAAS,GAAGJ,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,WAAW,CAAC,CAAC;EAC/D,IAAI,OAAOD,SAAS,KAAK,UAAU,EAAE;IACjC,OAAOA,SAAS,CAACL,EAAE,CAAC;EACxB;EACA,OAAO,YAAY;IACf,OAAOG,OAAO,CAACC,MAAM,CAAC,gFAAgF,GAClG,iEAAiE,CAAC;EAC1E,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,CAAC;EACpB;EACAC,WAAWA,CAACC,KAAK,EAAEC,gBAAgB,EAAE;IACjC,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;AACA;EACUC,MAAMA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI,CAACC,gBAAgB,CAACH,KAAK,EAAEC,KAAI,CAACJ,KAAK,CAAC,EAAE;QACtC,MAAMO,aAAa,GAAGC,8BAA8B,CAACL,KAAK,CAAC;QAC3D,MAAM,IAAIM,KAAK,CAAC,6CAA6CF,aAAa,YAAY,GAClF,qBAAqBA,aAAa,CAACG,WAAW,CAAC,CAAC,+BAA+B,CAAC;MACxF;MACA,IAAIP,KAAK,KAAK5F,gBAAgB,CAACoG,QAAQ,EAAE;QACrC,MAAMnG,uBAAuB,CAAC4F,KAAI,CAACJ,KAAK,CAACY,QAAQ,EAAER,KAAI,CAACJ,KAAK,CAACa,KAAK,EAAET,KAAI,CAACJ,KAAK,CAACc,KAAK,CAAC;MAC1F;MACA;MACA;MACA,MAAMC,mBAAmB,GAAG,IAAI;MAChCtG,sBAAsB,CAAC0F,KAAK,EAAEC,KAAI,CAACJ,KAAK,CAACc,KAAK,EAAEV,KAAI,CAACJ,KAAK,CAACgB,UAAU,EAAED,mBAAmB,CAAC;MAC3FX,KAAI,CAACH,gBAAgB,CAACgB,aAAa,CAAC,CAAC;IAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB;IACA;IACA;IACA,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACpB,KAAK,CAACgB,UAAU,CAACK,MAAM,IAAI3G,wBAAwB,EAAE;MAC1D,MAAMmG,KAAK,GAAG,IAAI,CAACb,KAAK,CAACgB,UAAU,CAACtG,wBAAwB,CAAC;MAC7DC,eAAe,CAACkG,KAAK,EAAEM,WAAW,CAAC;MACnC,KAAK,MAAMnB,KAAK,IAAImB,WAAW,EAAE;QAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIxB,iBAAiB,CAACE,KAAK,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAChF;IACJ;IACA,OAAOP,OAAO,CAAC6B,OAAO,CAACH,kBAAkB,CAAC;EAC9C;AACJ;AACA,SAASd,gBAAgBA,CAACH,KAAK,EAAEH,KAAK,EAAE;EACpC,QAAQG,KAAK;IACT,KAAK5F,gBAAgB,CAACiH,WAAW;MAC7B,OAAOxB,KAAK,CAACY,QAAQ,CAACa,oBAAoB,KAAK,IAAI;IACvD,KAAKlH,gBAAgB,CAACmH,OAAO;MACzB,OAAO1B,KAAK,CAACY,QAAQ,CAACe,gBAAgB,KAAK,IAAI;IACnD,KAAKpH,gBAAgB,CAACkG,KAAK;MACvB,OAAOT,KAAK,CAACY,QAAQ,CAACgB,cAAc,KAAK,IAAI;IACjD,KAAKrH,gBAAgB,CAACoG,QAAQ;MAC1B,OAAO,IAAI;IACf;MACI,OAAO,KAAK;EACpB;AACJ;AACA,SAASH,8BAA8BA,CAACL,KAAK,EAAE;EAC3C,QAAQA,KAAK;IACT,KAAK5F,gBAAgB,CAACiH,WAAW;MAC7B,OAAO,aAAa;IACxB,KAAKjH,gBAAgB,CAACmH,OAAO;MACzB,OAAO,SAAS;IACpB,KAAKnH,gBAAgB,CAACkG,KAAK;MACvB,OAAO,OAAO;IAClB;MACI,OAAO,MAAM;EACrB;AACJ;;AAEA;AACA,MAAMoB,0CAA0C,GAAG,IAAI;AACvD;AACA,MAAMC,iCAAiC,GAAG,KAAK;AAC/C;AACA,MAAMC,mCAAmC,GAAG,KAAK;AACjD;AACA,MAAMC,4BAA4B,GAAGpH,mBAAmB,CAACqH,WAAW;AACpE;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBC,iBAAiBA,CAACC,aAAa,EAAE,CAAE;EACnCC,qBAAqBA,CAAA,EAAG,CAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAIzH,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA;AACA;AACA,MAAM0H,wBAAwB,GAAG,IAAI1H,cAAc,CAAC,0BAA0B,CAAC;AAE/E,MAAM2H,0BAA0B,GAAG,IAAI3H,cAAc,CAAC,4BAA4B,CAAC;AACnF,MAAM4H,8BAA8B,CAAC;EACjC1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2C,IAAI,GAAG3H,QAAQ,CAACC,MAAM,CAAC;IAC5B,IAAI,CAAC2H,gBAAgB,GAAG5H,QAAQ,CAACE,YAAY,CAAC;IAC9C,IAAI,CAAC2H,yBAAyB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9C;EACAC,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI;MACA,IAAI,CAACL,IAAI,CAACM,iBAAiB,CAAC,MAAM,IAAI,CAACL,gBAAgB,CAACG,WAAW,CAACC,CAAC,CAAC,CAAC;IAC3E,CAAC,CACD,OAAOE,SAAS,EAAE;MACdF,CAAC,GAAGE,SAAS;IACjB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACL,yBAAyB,CAACM,IAAI,GAAG,CAAC,EAAE;MACzC,KAAK,MAAM3D,EAAE,IAAI,IAAI,CAACqD,yBAAyB,CAACO,MAAM,CAAC,CAAC,EAAE;QACtD5D,EAAE,CAACwD,CAAC,CAAC;MACT;MACA,IAAI,CAACH,yBAAyB,CAACQ,KAAK,CAAC,CAAC;IAC1C,CAAC,MACI;MACD,MAAML,CAAC;IACX;EACJ;EACA;IAAS,IAAI,CAACM,IAAI,GAAG/I,EAAE,CAACgJ,kBAAkB,CAAC;MAAEC,UAAU,EAAE,QAAQ;MAAEC,OAAO,EAAE,SAAS;MAAEC,QAAQ,EAAEnJ,EAAE;MAAEoJ,IAAI,EAAEjB,8BAA8B;MAAEkB,IAAI,EAAE,EAAE;MAAEC,MAAM,EAAEtJ,EAAE,CAACuJ,eAAe,CAAC3I;IAAW,CAAC,CAAC;EAAE;EAC/L;IAAS,IAAI,CAAC4I,KAAK,GAAGxJ,EAAE,CAACyJ,qBAAqB,CAAC;MAAER,UAAU,EAAE,QAAQ;MAAEC,OAAO,EAAE,SAAS;MAAEC,QAAQ,EAAEnJ,EAAE;MAAEoJ,IAAI,EAAEjB;IAA+B,CAAC,CAAC;EAAE;AACtJ;AACAnI,EAAE,CAAC0J,wBAAwB,CAAC;EAAET,UAAU,EAAE,QAAQ;EAAEC,OAAO,EAAE,SAAS;EAAEC,QAAQ,EAAEnJ,EAAE;EAAEoJ,IAAI,EAAEjB,8BAA8B;EAAEwB,UAAU,EAAE,CAAC;IAC7HP,IAAI,EAAExI;EACV,CAAC;AAAE,CAAC,CAAC;;AAEb;AACA;AACA;AACA;AACA;AACA,MAAMgJ,gBAAgB,CAAC;EACnB;EACAnE,WAAWA,CAACoE,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,kBAAkB,GAAGtJ,QAAQ,CAACwH,wBAAwB,EAAE;MAAE+B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChF;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACF,kBAAkB,GAAG,IAAIlJ,WAAW,CAAC,CAAC,GAAGJ,QAAQ,CAACC,MAAM,CAAC;IAC7E;IACA,IAAI,CAACwJ,aAAa,GAAGzJ,QAAQ,CAACK,gBAAgB,CAAC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACqJ,OAAO,GAAG1J,QAAQ,CAACM,cAAc,CAAC;IACvC;IACA,IAAI,CAACqJ,WAAW,GAAG,IAAI,CAACD,OAAO;IAC/B,IAAI,CAACE,YAAY,GAAG5J,QAAQ,CAACO,aAAa,CAAC;IAC3C,IAAI,CAACsJ,eAAe,GAAG7J,QAAQ,CAAC0H,8BAA8B,CAAC;IAC/D;IACA,IAAI,CAACoC,MAAM,GAAG,IAAI,CAACR,kBAAkB,GAAG,IAAI,GAAG,IAAI,CAACE,OAAO;IAC3D,IAAI,CAACO,iBAAiB,GAAGX,YAAY,CAACW,iBAAiB;IACvD,IAAI,CAACC,UAAU,GAAGZ,YAAY,CAACa,QAAQ;IACvC,IAAI,CAACC,YAAY,GAAG1J,YAAY,CAAC,IAAI,CAACwJ,UAAU,CAACG,aAAa,CAAC;IAC/D,IAAI,CAACC,iBAAiB,GAAGhB,YAAY,CAACiB,QAAQ;IAC9C,IAAI,CAACF,aAAa,GAAG,IAAI,CAACH,UAAU,CAACG,aAAa;IAClD,IAAI,CAACf,YAAY,GAAGA,YAAY;EACpC;EACA;AACJ;AACA;EACIkB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACP,iBAAiB,CAACO,cAAc,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACX,YAAY,CAACY,eAAe,CAACC,KAAK;EACnD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACH,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAO5F,OAAO,CAAC6B,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAI7B,OAAO,CAAC,CAAC6B,OAAO,EAAE5B,MAAM,KAAK;MACpC,IAAI,CAACiF,eAAe,CAAChC,yBAAyB,CAAC8C,GAAG,CAAC/F,MAAM,CAAC;MAC1D,IAAI,CAAC8E,OAAO,CAACgB,UAAU,CAAC,CAAC,CAACE,IAAI,CAAC,MAAM;QACjC,IAAI,CAACf,eAAe,CAAChC,yBAAyB,CAACgD,MAAM,CAACjG,MAAM,CAAC;QAC7D4B,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIL,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMN,KAAK,GAAG,IAAI,CAACsD,YAAY,CAAC0B,QAAQ,CAAC,QAAQ,CAAC;IAClDlL,eAAe,CAACkG,KAAK,EAAEM,WAAW,CAAC;IACnC,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,KAAK,MAAMpB,KAAK,IAAImB,WAAW,EAAE;MAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIxB,iBAAiB,CAACE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/D;IACA,OAAON,OAAO,CAAC6B,OAAO,CAACH,kBAAkB,CAAC;EAC9C;EACA0E,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,SAAS,KAAKC,SAAS,EAAE;MAC9B,IAAI,CAACD,SAAS,GAAG,IAAI,CAAC5B,YAAY,CAAC8B,QAAQ,CAACC,GAAG,CAAC1K,gBAAgB,EAAE,IAAI,CAAC;IAC3E;IACA,OAAO,IAAI,CAACuK,SAAS;EACzB;EACA;AACJ;AACA;EACII,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAAC,CAAC;IACpC,IAAIM,QAAQ,IAAIA,QAAQ,CAACD,iBAAiB,EAAE;MACxC,OAAOC,QAAQ,CAACD,iBAAiB,CAAC,CAAC;IACvC;IACA,OAAO,IAAI,CAACV,UAAU,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIY,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACjC,YAAY,EAAE;MACpB,IAAI,CAACD,YAAY,CAACkC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACjC,YAAY,GAAG,IAAI;IAC5B;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkC,yBAAyB,SAASpC,gBAAgB,CAAC;EACrDnE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGwG,SAAS,CAAC;IACnB,IAAI,CAACC,WAAW,GAAGzL,QAAQ,CAACuH,0BAA0B,EAAE;MAAEgC,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,IAAI;EACvF;EACAmC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACD,WAAW,EAAE;MAClB,IAAI,CAAC/B,OAAO,CAACiC,UAAU,CAAC,IAAI,CAACvC,YAAY,CAAC0B,QAAQ,CAAC;IACvD;EACJ;EACA5E,aAAaA,CAACoE,cAAc,GAAG,IAAI,EAAE;IACjC,IAAI,CAACA,cAAc,EAAE;MACjB,MAAM,IAAI5E,KAAK,CAAC,yDAAyD,GACrE,gFAAgF,CAAC;IACzF;IACA,IAAI,CAAC+D,aAAa,CAACmC,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAClC,OAAO,CAACmC,IAAI,CAAC,CAAC;IACnB,IAAI,CAACpC,aAAa,CAACmC,KAAK,CAAC,CAAC;EAC9B;EACAE,iBAAiBA,CAACC,UAAU,GAAG,IAAI,EAAE;IACjC,IAAI,CAACA,UAAU,EAAE;MACb,MAAM,IAAIrG,KAAK,CAAC,yFAAyF,GACrG,+GAA+G,CAAC;IACxH,CAAC,MACI,IAAI,CAAC,IAAI,CAAC+F,WAAW,EAAE;MACxB,IAAI,CAACA,WAAW,GAAGM,UAAU;MAC7B,IAAI,CAACrC,OAAO,CAACiC,UAAU,CAAC,IAAI,CAACvC,YAAY,CAAC0B,QAAQ,CAAC;IACvD;IACA,IAAI,CAAC5E,aAAa,CAAC,CAAC;EACxB;AACJ;AACA;AACA;AACA;AACA,MAAM8F,iCAAiC,SAAS7C,gBAAgB,CAAC;EAC7DnE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGwG,SAAS,CAAC;IACnB,IAAI,CAACS,cAAc,GAAG,IAAI5H,YAAY,CAAC,CAAC;IACxC,IAAI,CAACoH,WAAW,GAAGzL,QAAQ,CAACuH,0BAA0B,EAAE;MAAEgC,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,KAAK;IACpF,IAAI,CAAC2C,qBAAqB,GAAGjB,SAAS;IACtC,IAAI,CAACkB,wBAAwB,GAAGlB,SAAS;EAC7C;EACAS,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACD,WAAW,EAAE;MAClB,IAAI,CAACW,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,CAAChD,YAAY,CAAC0B,QAAQ,CAACuB,SAAS,CAAC,MAAM;MACvC,IAAI,CAACC,2BAA2B,CAAC,CAAC;IACtC,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAAC9C,OAAO,CAACvB,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACgE,cAAc,CAACtB,GAAG,CAAC,IAAI,CAACnB,OAAO,CAAC+C,OAAO,CAACC,SAAS,CAAC;QACnDC,IAAI,EAAGC,KAAK,IAAK;UACb,MAAMA,KAAK;QACf;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACAxG,aAAaA,CAACoE,cAAc,GAAG,IAAI,EAAE;IACjC,IAAI,CAACb,aAAa,CAACmC,KAAK,CAAC,CAAC;IAC1B;IACA;IACA,IAAI,CAACpC,OAAO,CAACmD,GAAG,CAAC,MAAM;MACnB,IAAI,CAAC5C,iBAAiB,CAAC7D,aAAa,CAAC,CAAC;MACtC,IAAIoE,cAAc,EAAE;QAChB,IAAI,CAACA,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACb,aAAa,CAACmC,KAAK,CAAC,CAAC;EAC9B;EACAE,iBAAiBA,CAACC,UAAU,GAAG,IAAI,EAAE;IACjC,IAAI,IAAI,CAACzC,kBAAkB,EAAE;MACzB,MAAM,IAAI5D,KAAK,CAAC,qEAAqE,CAAC;IAC1F;IACA,IAAIqG,UAAU,KAAK,IAAI,CAACN,WAAW,EAAE;MACjC,IAAIM,UAAU,EAAE;QACZ,IAAI,CAACK,uBAAuB,CAAC,CAAC;MAClC,CAAC,MACI;QACD,IAAI,CAACE,2BAA2B,CAAC,CAAC;MACtC;IACJ;IACA,IAAI,CAACb,WAAW,GAAGM,UAAU;IAC7B,IAAI,CAAC7F,aAAa,CAAC,CAAC;EACxB;EACAkG,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC5C,OAAO,CAACvB,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACiE,qBAAqB,GAAG,IAAI,CAACvC,WAAW,CAACiD,SAAS,CAACJ,SAAS,CAAC,MAAM;QACpE,IAAI,CAAClC,cAAc,CAAC,CAAC;MACzB,CAAC,CAAC;MACF,IAAI,CAAC6B,wBAAwB,GAAG,IAAI,CAACxC,WAAW,CAACkD,YAAY,CAACL,SAAS,CAAEM,WAAW,IAAK;QACrF,IAAI;UACApM,8BAA8B,CAAC,IAAI,CAAC0I,YAAY,CAAC0B,QAAQ,CAACiC,MAAM,EAAE,IAAI,CAAC3D,YAAY,CAAC0B,QAAQ,CAACkC,kBAAkB,EAAEF,WAAW,EAAE,KAAK,CAAC,uBAAuB,CAAC;QAChK,CAAC,CACD,OAAO9E,CAAC,EAAE;UACN;UACA;UACA;UACA;UACA;UACA,IAAI,CAACsE,2BAA2B,CAAC,CAAC;UAClC,MAAMtE,CAAC;QACX;MACJ,CAAC,CAAC;MACF,IAAI,CAAC2B,WAAW,CAACsD,iBAAiB,CAACtC,GAAG,CAAC,IAAI,CAACvB,YAAY,CAAC0B,QAAQ,CAAC;IACtE,CAAC,CAAC;EACN;EACAwB,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAACJ,qBAAqB,EAAEgB,WAAW,CAAC,CAAC;IACzC,IAAI,CAACf,wBAAwB,EAAEe,WAAW,CAAC,CAAC;IAC5C,IAAI,CAAChB,qBAAqB,GAAGjB,SAAS;IACtC,IAAI,CAACkB,wBAAwB,GAAGlB,SAAS;IACzC,IAAI,CAACtB,WAAW,CAACsD,iBAAiB,CAACpC,MAAM,CAAC,IAAI,CAACzB,YAAY,CAAC0B,QAAQ,CAAC;EACzE;EACAQ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACgB,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACL,cAAc,CAACiB,WAAW,CAAC,CAAC;IACjC,KAAK,CAAC5B,OAAO,CAAC,CAAC;EACnB;AACJ;AAEA,MAAM7G,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;AACvD,MAAMyI,mBAAmB,GAAG1I,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;AAC7E,MAAMsI,wCAAwC,GAAG;AACjD,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EACnD;EACA,MAAM,IAAI3H,KAAK,CAAC0H,wCAAwC,CAAC;AAC7D;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,IAAIH,mBAAmB,EAAE;IACrBA,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAAC/I,EAAE,EAAEgJ,OAAO,EAAE;EAC5B,IAAIL,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACI,SAAS,CAAC/I,EAAE,EAAEgJ,OAAO,CAAC;EACrD;EACA,MAAM,IAAI9H,KAAK,CAAC0H,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASvB,IAAIA,CAAC4B,MAAM,GAAG,CAAC,EAAEC,WAAW,GAAG;EACpCC,iCAAiC,EAAE;AACvC,CAAC,EAAE;EACC,IAAIR,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACtB,IAAI,CAAC4B,MAAM,EAAEC,WAAW,CAAC;EACxD;EACA,MAAM,IAAIhI,KAAK,CAAC0H,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxB,KAAKA,CAACgC,QAAQ,EAAE;EACrB,IAAIT,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACvB,KAAK,CAACgC,QAAQ,CAAC;EAC9C;EACA,MAAM,IAAIlI,KAAK,CAAC0H,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,oBAAoBA,CAAA,EAAG;EAC5B,IAAIV,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACU,oBAAoB,CAAC,CAAC;EACrD;EACA,MAAM,IAAInI,KAAK,CAAC0H,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,eAAeA,CAAA,EAAG;EACvB,IAAIX,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACW,eAAe,CAAC,CAAC;EAChD;EACA,MAAM,IAAIpI,KAAK,CAAC0H,wCAAwC,CAAC;AAC7D;AAEA,IAAIW,gBAAgB,GAAG,CAAC;AACxB,MAAMC,iBAAiB,CAAC;EACpBhJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiJ,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC;EACA;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IACnD,MAAMC,KAAK,GAAG,CAAC,CAAC;IAChB,IAAIF,WAAW,EAAE;MACbG,WAAW,CAACH,WAAW,CAAC,CAACI,OAAO,CAAEC,IAAI,IAAMH,KAAK,CAACG,IAAI,CAAC,GAAGL,WAAW,CAACK,IAAI,CAAE,CAAC;IACjF;IACA,IAAIJ,QAAQ,CAACK,GAAG,EAAE;MACd,IAAIL,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAAC3D,GAAG,EAAE;QACjC,MAAM,IAAIjF,KAAK,CAAC,6BAA6B/E,UAAU,CAACyN,aAAa,CAAC,oBAAoB,CAAC;MAC/F;MACAS,WAAW,CAACN,KAAK,EAAED,QAAQ,CAACK,GAAG,CAAC;IACpC;IACA,IAAIL,QAAQ,CAACM,MAAM,EAAE;MACjBE,cAAc,CAACP,KAAK,EAAED,QAAQ,CAACM,MAAM,EAAE,IAAI,CAACX,WAAW,CAAC;IAC5D;IACA,IAAIK,QAAQ,CAAC3D,GAAG,EAAE;MACdoE,WAAW,CAACR,KAAK,EAAED,QAAQ,CAAC3D,GAAG,CAAC;IACpC;IACA,OAAO,IAAIyD,aAAa,CAACG,KAAK,CAAC;EACnC;AACJ;AACA,SAASO,cAAcA,CAACE,QAAQ,EAAEJ,MAAM,EAAEK,UAAU,EAAE;EAClD,MAAMC,aAAa,GAAG,IAAIpH,GAAG,CAAC,CAAC;EAC/B,KAAK,MAAM4G,IAAI,IAAIE,MAAM,EAAE;IACvB,MAAMO,WAAW,GAAGP,MAAM,CAACF,IAAI,CAAC;IAChC,IAAIU,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC5BA,WAAW,CAACV,OAAO,CAAEhE,KAAK,IAAK;QAC3ByE,aAAa,CAACvE,GAAG,CAAC2E,YAAY,CAACZ,IAAI,EAAEjE,KAAK,EAAEwE,UAAU,CAAC,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,MACI;MACDC,aAAa,CAACvE,GAAG,CAAC2E,YAAY,CAACZ,IAAI,EAAES,WAAW,EAAEF,UAAU,CAAC,CAAC;IAClE;EACJ;EACA,KAAK,MAAMP,IAAI,IAAIM,QAAQ,EAAE;IACzB,MAAMO,SAAS,GAAGP,QAAQ,CAACN,IAAI,CAAC;IAChC,IAAIU,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC1BP,QAAQ,CAACN,IAAI,CAAC,GAAGa,SAAS,CAACC,MAAM,CAAE/E,KAAK,IAAK,CAACyE,aAAa,CAACO,GAAG,CAACH,YAAY,CAACZ,IAAI,EAAEjE,KAAK,EAAEwE,UAAU,CAAC,CAAC,CAAC;IAC3G,CAAC,MACI;MACD,IAAIC,aAAa,CAACO,GAAG,CAACH,YAAY,CAACZ,IAAI,EAAEa,SAAS,EAAEN,UAAU,CAAC,CAAC,EAAE;QAC9DD,QAAQ,CAACN,IAAI,CAAC,GAAGzD,SAAS;MAC9B;IACJ;EACJ;AACJ;AACA,SAAS8D,WAAWA,CAACC,QAAQ,EAAErE,GAAG,EAAE;EAChC,KAAK,MAAM+D,IAAI,IAAI/D,GAAG,EAAE;IACpB,MAAM+E,QAAQ,GAAG/E,GAAG,CAAC+D,IAAI,CAAC;IAC1B,MAAMa,SAAS,GAAGP,QAAQ,CAACN,IAAI,CAAC;IAChC,IAAIa,SAAS,IAAI,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC/CP,QAAQ,CAACN,IAAI,CAAC,GAAGa,SAAS,CAACI,MAAM,CAACD,QAAQ,CAAC;IAC/C,CAAC,MACI;MACDV,QAAQ,CAACN,IAAI,CAAC,GAAGgB,QAAQ;IAC7B;EACJ;AACJ;AACA,SAASb,WAAWA,CAACG,QAAQ,EAAEL,GAAG,EAAE;EAChC,KAAK,MAAMD,IAAI,IAAIC,GAAG,EAAE;IACpBK,QAAQ,CAACN,IAAI,CAAC,GAAGC,GAAG,CAACD,IAAI,CAAC;EAC9B;AACJ;AACA,SAASY,YAAYA,CAACM,QAAQ,EAAEL,SAAS,EAAEN,UAAU,EAAE;EACnD,IAAIY,YAAY,GAAG,CAAC;EACpB,MAAMC,SAAS,GAAG,IAAI5B,GAAG,CAAC,CAAC;EAC3B,MAAM6B,QAAQ,GAAGA,CAACC,GAAG,EAAEvF,KAAK,KAAK;IAC7B,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7C,IAAIqF,SAAS,CAACL,GAAG,CAAChF,KAAK,CAAC,EAAE;QACtB,OAAOqF,SAAS,CAAC3E,GAAG,CAACV,KAAK,CAAC;MAC/B;MACA;MACA;MACAqF,SAAS,CAACnB,GAAG,CAAClE,KAAK,EAAE,QAAQoF,YAAY,EAAE,EAAE,CAAC;MAC9C;MACA,OAAOpF,KAAK;IAChB,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAClCA,KAAK,GAAGwF,mBAAmB,CAACxF,KAAK,EAAEwE,UAAU,CAAC;IAClD;IACA,OAAOxE,KAAK;EAChB,CAAC;EACD,OAAO,GAAGmF,QAAQ,IAAIM,IAAI,CAACC,SAAS,CAACZ,SAAS,EAAEQ,QAAQ,CAAC,EAAE;AAC/D;AACA,SAASE,mBAAmBA,CAACG,GAAG,EAAEnB,UAAU,EAAE;EAC1C,IAAIoB,EAAE,GAAGpB,UAAU,CAAC9D,GAAG,CAACiF,GAAG,CAAC;EAC5B,IAAI,CAACC,EAAE,EAAE;IACLA,EAAE,GAAG,GAAG1P,UAAU,CAACyP,GAAG,CAAC,GAAGrC,gBAAgB,EAAE,EAAE;IAC9CkB,UAAU,CAACN,GAAG,CAACyB,GAAG,EAAEC,EAAE,CAAC;EAC3B;EACA,OAAOA,EAAE;AACb;AACA,SAAS7B,WAAWA,CAAC8B,GAAG,EAAE;EACtB,MAAM/B,KAAK,GAAG,EAAE;EAChB;EACAgC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC7B,OAAO,CAAEC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,CAAC+B,UAAU,CAAC,GAAG,CAAC,EAAE;MACvBlC,KAAK,CAAChI,IAAI,CAACmI,IAAI,CAAC;IACpB;EACJ,CAAC,CAAC;EACF;EACA,IAAIgC,KAAK,GAAGJ,GAAG;EACf,OAAQI,KAAK,GAAGH,MAAM,CAACI,cAAc,CAACD,KAAK,CAAC,EAAG;IAC3CH,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAACjC,OAAO,CAAEmC,SAAS,IAAK;MACtC,MAAMC,IAAI,GAAGN,MAAM,CAACO,wBAAwB,CAACJ,KAAK,EAAEE,SAAS,CAAC;MAC9D,IAAI,CAACA,SAAS,CAACH,UAAU,CAAC,GAAG,CAAC,IAAII,IAAI,IAAI,KAAK,IAAIA,IAAI,EAAE;QACrDtC,KAAK,CAAChI,IAAI,CAACqK,SAAS,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA,OAAOrC,KAAK;AAChB;AAEA,MAAMwC,UAAU,GAAG,IAAInQ,uBAAuB,CAAC,CAAC;AAChD;AACA;AACA;AACA,MAAMoQ,gBAAgB,CAAC;EACnBhM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiM,SAAS,GAAG,IAAI/C,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACgD,QAAQ,GAAG,IAAIhD,GAAG,CAAC,CAAC;EAC7B;EACAiD,WAAWA,CAACxI,IAAI,EAAE2F,QAAQ,EAAE;IACxB,MAAM2C,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC9F,GAAG,CAACxC,IAAI,CAAC,IAAI,EAAE;IAChDsI,SAAS,CAAC1K,IAAI,CAAC+H,QAAQ,CAAC;IACxB,IAAI,CAAC2C,SAAS,CAACtC,GAAG,CAAChG,IAAI,EAAEsI,SAAS,CAAC;IACnC,IAAI,CAACC,QAAQ,CAACrG,MAAM,CAAClC,IAAI,CAAC;EAC9B;EACAyI,YAAYA,CAACH,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,CAAC5I,KAAK,CAAC,CAAC;IACtB4I,SAAS,CAACxC,OAAO,CAAC,CAAC,CAAC9F,IAAI,EAAE2F,QAAQ,CAAC,KAAK;MACpC,IAAI,CAAC6C,WAAW,CAACxI,IAAI,EAAE2F,QAAQ,CAAC;IACpC,CAAC,CAAC;EACN;EACA+C,aAAaA,CAAC1I,IAAI,EAAE;IAChB,MAAM2I,WAAW,GAAGP,UAAU,CAACO,WAAW,CAAC3I,IAAI,CAAC;IAChD;IACA;IACA;IACA;IACA;IACA,KAAK,IAAI4I,CAAC,GAAGD,WAAW,CAAChL,MAAM,GAAG,CAAC,EAAEiL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,MAAMC,UAAU,GAAGF,WAAW,CAACC,CAAC,CAAC;MACjC,MAAME,WAAW,GAAGD,UAAU,YAAY3Q,SAAS,IAC/C2Q,UAAU,YAAY1Q,SAAS,IAC/B0Q,UAAU,YAAYzQ,IAAI,IAC1ByQ,UAAU,YAAYxQ,QAAQ;MAClC,IAAIyQ,WAAW,EAAE;QACb,OAAOD,UAAU,YAAY,IAAI,CAAC7I,IAAI,GAAG6I,UAAU,GAAG,IAAI;MAC9D;IACJ;IACA,OAAO,IAAI;EACf;EACAhL,OAAOA,CAACmC,IAAI,EAAE;IACV,IAAIuI,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC/F,GAAG,CAACxC,IAAI,CAAC,IAAI,IAAI;IAC9C,IAAI,CAACuI,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAI,CAACG,aAAa,CAAC1I,IAAI,CAAC;MACnC,IAAIuI,QAAQ,EAAE;QACV,MAAMD,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC9F,GAAG,CAACxC,IAAI,CAAC;QAC1C,IAAIsI,SAAS,EAAE;UACX,MAAMS,SAAS,GAAG,IAAI1D,iBAAiB,CAAC,CAAC;UACzCiD,SAAS,CAACxC,OAAO,CAAEH,QAAQ,IAAK;YAC5B4C,QAAQ,GAAGQ,SAAS,CAACvD,gBAAgB,CAAC,IAAI,CAACxF,IAAI,EAAEuI,QAAQ,EAAE5C,QAAQ,CAAC;UACxE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC4C,QAAQ,CAACvC,GAAG,CAAChG,IAAI,EAAEuI,QAAQ,CAAC;IACrC;IACA,OAAOA,QAAQ;EACnB;AACJ;AACA,MAAMS,iBAAiB,SAASX,gBAAgB,CAAC;EAC7C,IAAIrI,IAAIA,CAAA,EAAG;IACP,OAAO9H,SAAS;EACpB;AACJ;AACA,MAAM+Q,iBAAiB,SAASZ,gBAAgB,CAAC;EAC7C,IAAIrI,IAAIA,CAAA,EAAG;IACP,OAAO7H,SAAS;EACpB;AACJ;AACA,MAAM+Q,YAAY,SAASb,gBAAgB,CAAC;EACxC,IAAIrI,IAAIA,CAAA,EAAG;IACP,OAAO5H,IAAI;EACf;AACJ;AACA,MAAM+Q,gBAAgB,SAASd,gBAAgB,CAAC;EAC5C,IAAIrI,IAAIA,CAAA,EAAG;IACP,OAAO3H,QAAQ;EACnB;AACJ;AAEA,IAAI+Q,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/EA,qBAAqB,CAACA,qBAAqB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;AAC/F,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,SAASC,uBAAuBA,CAACvH,KAAK,EAAE;EACpC,OAAQA,KAAK,KAAKsH,qBAAqB,CAACE,WAAW,IAAIxH,KAAK,KAAKsH,qBAAqB,CAACG,iBAAiB;AAC5G;AACA,SAASC,4BAA4BA,CAACC,KAAK,EAAEC,QAAQ,EAAEpI,QAAQ,EAAE;EAC7DmI,KAAK,CAAC3D,OAAO,CAAE9F,IAAI,IAAK;IACpB,IAAI,CAAC1H,wBAAwB,CAAC0H,IAAI,CAAC,EAAE;MACjC,MAAM2J,SAAS,GAAGD,QAAQ,CAAC7L,OAAO,CAACmC,IAAI,CAAC;MACxC,IAAI2J,SAAS,IAAIA,SAAS,CAACC,UAAU,EAAE;QACnC,MAAM,IAAI7M,KAAK,CAACxE,sCAAsC,CAACyH,IAAI,EAAEsB,QAAQ,CAAC,CAAC;MAC3E;IACJ;EACJ,CAAC,CAAC;AACN;AACA,MAAMuI,eAAe,CAAC;EAClBxN,WAAWA,CAACyN,QAAQ,EAAEC,qBAAqB,EAAE;IACzC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,gCAAgC,GAAG,IAAI;IAC5C;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIlL,GAAG,CAAC,CAAC;IAClC,IAAI,CAACmL,iBAAiB,GAAG,IAAInL,GAAG,CAAC,CAAC;IAClC,IAAI,CAACoL,YAAY,GAAG,IAAIpL,GAAG,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACqL,2BAA2B,GAAG,IAAIrL,GAAG,CAAC,CAAC;IAC5C;IACA,IAAI,CAACsL,cAAc,GAAG,IAAItL,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACuL,cAAc,GAAG,IAAIvL,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACwL,iBAAiB,GAAG,IAAIxL,GAAG,CAAC,CAAC;IAClC;IACA;IACA,IAAI,CAACyL,uBAAuB,GAAG,IAAIrF,GAAG,CAAC,CAAC;IACxC,IAAI,CAACsF,SAAS,GAAGC,aAAa,CAAC,CAAC;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAIxF,GAAG,CAAC,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA,IAAI,CAACyF,aAAa,GAAG,IAAIzF,GAAG,CAAC,CAAC;IAC9B;IACA;IACA,IAAI,CAAC0F,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B;IACA;IACA,IAAI,CAACC,yBAAyB,GAAG,IAAI/F,GAAG,CAAC,CAAC;IAC1C,IAAI,CAACgG,wBAAwB,GAAG,IAAIhG,GAAG,CAAC,CAAC;IACzC,IAAI,CAACiG,6BAA6B,GAAG,IAAIrM,GAAG,CAAC,CAAC;IAC9C,IAAI,CAACsM,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,kBAAkB,GAAGpN,4BAA4B;IACtD,MAAMqN,iBAAiB,CAAC;IAExB,IAAI,CAACC,cAAc,GAAGD,iBAAiB;EAC3C;EACAE,oBAAoBA,CAAC1B,SAAS,EAAE;IAC5B,IAAI,CAACgB,iBAAiB,GAAGhB,SAAS;IAClC,IAAI,CAACe,SAAS,GAAG,IAAI;EACzB;EACAY,sBAAsBA,CAACC,SAAS,EAAE;IAC9B;IACA,IAAIA,SAAS,CAAC9B,YAAY,KAAK3H,SAAS,EAAE;MACtC;MACAkH,4BAA4B,CAACuC,SAAS,CAAC9B,YAAY,EAAE,IAAI,CAACY,SAAS,CAAClB,SAAS,EAAE,uCAAuC,CAAC;MACvH,IAAI,CAACqC,cAAc,CAACD,SAAS,CAAC9B,YAAY,EAAEb,qBAAqB,CAACE,WAAW,CAAC;MAC9E,IAAI,CAACW,YAAY,CAACrM,IAAI,CAAC,GAAGmO,SAAS,CAAC9B,YAAY,CAAC;IACrD;IACA;IACA,IAAI8B,SAAS,CAAC7B,OAAO,KAAK5H,SAAS,EAAE;MACjC,IAAI,CAAC2J,0BAA0B,CAACF,SAAS,CAAC7B,OAAO,CAAC;MAClD,IAAI,CAACA,OAAO,CAACtM,IAAI,CAAC,GAAGmO,SAAS,CAAC7B,OAAO,CAAC;IAC3C;IACA,IAAI6B,SAAS,CAAC5B,SAAS,KAAK7H,SAAS,EAAE;MACnC,IAAI,CAAC6H,SAAS,CAACvM,IAAI,CAAC,GAAGmO,SAAS,CAAC5B,SAAS,CAAC;IAC/C;IACA,IAAI,CAACA,SAAS,CAACvM,IAAI,CAAC;MAChBsO,OAAO,EAAEpN,0BAA0B;MACnCqN,QAAQ,EAAEJ,SAAS,CAACK,6BAA6B,IAAI;IACzD,CAAC,CAAC;IACF,IAAIL,SAAS,CAAC3B,OAAO,KAAK9H,SAAS,EAAE;MACjC,IAAI,CAAC8H,OAAO,CAACxM,IAAI,CAAC,GAAGmO,SAAS,CAAC3B,OAAO,CAAC;IAC3C;IACA,IAAI,CAACsB,kBAAkB,GAAGK,SAAS,CAACL,kBAAkB,IAAIpN,4BAA4B;EAC1F;EACA+N,cAAcA,CAACC,QAAQ,EAAE3G,QAAQ,EAAE;IAC/B,IAAInN,iCAAiC,EAAE;MACnCC,YAAY,CAAC8T,kBAAkB,CAACD,QAAQ,CAAC;IAC7C;IACA,IAAI,CAAC3B,iBAAiB,CAAC3I,GAAG,CAACsK,QAAQ,CAAC;IACpC;IACA,IAAI,CAACzB,SAAS,CAAC2B,MAAM,CAAChE,WAAW,CAAC8D,QAAQ,EAAE3G,QAAQ,CAAC;IACrD,MAAMU,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAAC2B,MAAM,CAAC3O,OAAO,CAACyO,QAAQ,CAAC;IACxD,IAAIjG,QAAQ,KAAK,IAAI,EAAE;MACnB,MAAMoG,gBAAgB,CAACH,QAAQ,CAACI,IAAI,EAAE,UAAU,CAAC;IACrD;IACA,IAAI,CAACC,iBAAiB,CAACL,QAAQ,EAAEjG,QAAQ,CAAC;IAC1C;IACA;IACA;IACA,IAAI,CAAC4F,0BAA0B,CAAC,CAACK,QAAQ,CAAC,CAAC;EAC/C;EACAM,iBAAiBA,CAACjD,SAAS,EAAEhE,QAAQ,EAAE;IACnC,IAAI,CAACkH,+BAA+B,CAAClD,SAAS,EAAEhE,QAAQ,CAAC;IACzD,IAAI,CAACkF,SAAS,CAAClB,SAAS,CAACnB,WAAW,CAACmB,SAAS,EAAEhE,QAAQ,CAAC;IACzD,IAAI,CAAC0E,iBAAiB,CAACrI,GAAG,CAAC2H,SAAS,CAAC;IACrC;IACA;IACA,IAAI,CAACmD,uCAAuC,CAACnD,SAAS,CAAC;EAC3D;EACAoD,iBAAiBA,CAACC,SAAS,EAAErH,QAAQ,EAAE;IACnC,IAAI,CAACkH,+BAA+B,CAACG,SAAS,EAAErH,QAAQ,CAAC;IACzD,IAAI,CAACkF,SAAS,CAACmC,SAAS,CAACxE,WAAW,CAACwE,SAAS,EAAErH,QAAQ,CAAC;IACzD,IAAI,CAAC2E,iBAAiB,CAACtI,GAAG,CAACgL,SAAS,CAAC;EACzC;EACAC,YAAYA,CAACC,IAAI,EAAEvH,QAAQ,EAAE;IACzB,IAAI,CAACkH,+BAA+B,CAACK,IAAI,EAAEvH,QAAQ,CAAC;IACpD,IAAI,CAACkF,SAAS,CAACqC,IAAI,CAAC1E,WAAW,CAAC0E,IAAI,EAAEvH,QAAQ,CAAC;IAC/C,IAAI,CAAC4E,YAAY,CAACvI,GAAG,CAACkL,IAAI,CAAC;EAC/B;EACAL,+BAA+BA,CAAC7M,IAAI,EAAE2F,QAAQ,EAAE;IAC5C,IAAIA,QAAQ,CAAC3D,GAAG,EAAEmL,cAAc,CAAC,YAAY,CAAC,IAC1CxH,QAAQ,CAACK,GAAG,EAAEmH,cAAc,CAAC,YAAY,CAAC,IAC1CxH,QAAQ,CAACM,MAAM,EAAEkH,cAAc,CAAC,YAAY,CAAC,EAAE;MAC/C,MAAM,IAAIpQ,KAAK,CAAC,uBAAuBiD,IAAI,CAAC0M,IAAI,sCAAsC,GAClF,0EAA0E,CAAC;IACnF;EACJ;EACAU,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC9B,IAAIC,WAAW;IACf,IAAID,QAAQ,CAACE,UAAU,KAAKlL,SAAS,EAAE;MACnCiL,WAAW,GAAG;QACVrB,OAAO,EAAEmB,KAAK;QACdG,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/BvN,IAAI,EAAEqN,QAAQ,CAACrN,IAAI,IAAI,EAAE;QACzBwN,KAAK,EAAEH,QAAQ,CAACG;MACpB,CAAC;IACL,CAAC,MACI,IAAIH,QAAQ,CAACnB,QAAQ,KAAK7J,SAAS,EAAE;MACtCiL,WAAW,GAAG;QAAErB,OAAO,EAAEmB,KAAK;QAAElB,QAAQ,EAAEmB,QAAQ,CAACnB,QAAQ;QAAEsB,KAAK,EAAEH,QAAQ,CAACG;MAAM,CAAC;IACxF,CAAC,MACI;MACDF,WAAW,GAAG;QAAErB,OAAO,EAAEmB;MAAM,CAAC;IACpC;IACA,MAAMK,aAAa,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAG3U,iBAAiB,CAAC2U,KAAK,CAAC,GAAG,IAAI;IACjF,MAAMM,UAAU,GAAGD,aAAa,KAAK,IAAI,GAAG,IAAI,GAAG/U,iBAAiB,CAAC+U,aAAa,CAACC,UAAU,CAAC;IAC9F,MAAMC,eAAe,GAAGD,UAAU,KAAK,MAAM,GAAG,IAAI,CAACtC,qBAAqB,GAAG,IAAI,CAACD,iBAAiB;IACnGwC,eAAe,CAAChQ,IAAI,CAAC2P,WAAW,CAAC;IACjC;IACA,IAAI,CAAChC,wBAAwB,CAACvF,GAAG,CAACqH,KAAK,EAAEE,WAAW,CAAC;IACrD,IAAIG,aAAa,KAAK,IAAI,IAAIC,UAAU,KAAK,IAAI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACjF,MAAME,iBAAiB,GAAG,IAAI,CAACvC,yBAAyB,CAAC9I,GAAG,CAACmL,UAAU,CAAC;MACxE,IAAIE,iBAAiB,KAAKvL,SAAS,EAAE;QACjCuL,iBAAiB,CAACjQ,IAAI,CAAC2P,WAAW,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAACjC,yBAAyB,CAACtF,GAAG,CAAC2H,UAAU,EAAE,CAACJ,WAAW,CAAC,CAAC;MACjE;IACJ;EACJ;EACAO,kCAAkCA,CAAC9N,IAAI,EAAE+N,QAAQ,EAAE;IAC/C,MAAMC,GAAG,GAAGhO,IAAI,CAACpH,YAAY,CAAC;IAC9B,MAAMqV,YAAY,GAAGA,CAAA,KAAM;MACvB,MAAM5H,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAAClB,SAAS,CAAC9L,OAAO,CAACmC,IAAI,CAAC;MACvD,OAAO,CAAC,CAACqG,QAAQ,CAAC6H,QAAQ,IAAI,CAAC,CAAC7H,QAAQ,CAAC8H,SAAS,EAAExQ,MAAM;IAC9D,CAAC;IACD,MAAMyQ,iBAAiB,GAAG,CAAC,CAACJ,GAAG,IAAI,CAACnV,gCAAgC,CAACmH,IAAI,CAAC,IAAIiO,YAAY,CAAC,CAAC;IAC5F;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMtI,QAAQ,GAAGyI,iBAAiB,GAC5B;MAAEL,QAAQ;MAAEM,MAAM,EAAE,EAAE;MAAEF,SAAS,EAAE,EAAE;MAAED,QAAQ,EAAE5L;IAAU,CAAC,GAC5D;MAAEyL;IAAS,CAAC;IAClB,IAAI,CAACnB,iBAAiB,CAAC5M,IAAI,EAAE;MAAEgG,GAAG,EAAEL;IAAS,CAAC,CAAC;IAC/C,IAAIyI,iBAAiB,IAAIJ,GAAG,CAACK,MAAM,IAAIL,GAAG,CAACK,MAAM,CAAC1Q,MAAM,GAAG,CAAC,EAAE;MAC1D,IAAI,CAACiN,uBAAuB,CAAC5E,GAAG,CAAChG,IAAI,EAAEgO,GAAG,CAACK,MAAM,CAAC;IACtD;IACA;IACA,IAAI,CAACtD,sBAAsB,CAAC/E,GAAG,CAAChG,IAAI,EAAEoJ,qBAAqB,CAACG,iBAAiB,CAAC;EAClF;EACM+E,yCAAyCA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA5R,iBAAA;MAC9C,IAAI4R,MAAI,CAAC/D,2BAA2B,CAAChL,IAAI,KAAK,CAAC,EAC3C;MACJ,MAAMgP,QAAQ,GAAG,EAAE;MACnB,KAAK,MAAM7E,SAAS,IAAI4E,MAAI,CAAC/D,2BAA2B,EAAE;QACtD,MAAMiE,eAAe,GAAGnW,wBAAwB,CAACqR,SAAS,CAAC;QAC3D,IAAI8E,eAAe,EAAE;UACjBD,QAAQ,CAAC5Q,IAAI,CAAC6Q,eAAe,CAAC,CAAC,CAAC;QACpC;MACJ;MACAF,MAAI,CAAC/D,2BAA2B,CAAC9K,KAAK,CAAC,CAAC;MACxC,MAAMgP,YAAY,SAAS1S,OAAO,CAAC2S,GAAG,CAACH,QAAQ,CAAC;MAChD,MAAMI,gBAAgB,GAAGF,YAAY,CAACG,IAAI,CAAC,CAAC,CAAC;MAC7CN,MAAI,CAACtC,0BAA0B,CAAC2C,gBAAgB,CAAC;MACjD;MACA;MACA,KAAK,MAAMjF,SAAS,IAAIiF,gBAAgB,EAAE;QACtCL,MAAI,CAACO,6BAA6B,CAACnF,SAAS,CAAC;MACjD;IAAC;EACL;EACMoF,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAArS,iBAAA;MACtBqS,MAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC;MACA;MACA;MACA,MAAMD,MAAI,CAACV,yCAAyC,CAAC,CAAC;MACtD;MACA;MACA;MACA;MACA9E,4BAA4B,CAACwF,MAAI,CAAC/E,YAAY,EAAE+E,MAAI,CAACnE,SAAS,CAAClB,SAAS,EAAE,uCAAuC,CAAC;MAClH;MACA,IAAIuF,mBAAmB,GAAGF,MAAI,CAACG,gBAAgB,CAAC,CAAC;MACjD;MACA,IAAID,mBAAmB,EAAE;QACrB,IAAIE,cAAc;QAClB,IAAI1F,QAAQ,GAAI2F,GAAG,IAAK;UACpB,IAAI,CAACD,cAAc,EAAE;YACjBA,cAAc,GAAGJ,MAAI,CAACzM,QAAQ,CAACC,GAAG,CAAC7G,cAAc,CAAC;UACtD;UACA,OAAOK,OAAO,CAAC6B,OAAO,CAACuR,cAAc,CAAC5M,GAAG,CAAC6M,GAAG,CAAC,CAAC;QACnD,CAAC;QACD,MAAMvW,0BAA0B,CAAC4Q,QAAQ,CAAC;MAC9C;IAAC;EACL;EACA4F,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAACH,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,iCAAiC,CAAC,CAAC;IACxC;IACA;IACA,IAAI,CAAC3E,sBAAsB,CAACrL,KAAK,CAAC,CAAC;IACnC,MAAMiQ,cAAc,GAAG,IAAI,CAAC7F,QAAQ,CAACvH,QAAQ;IAC7C,IAAI,CAACkJ,aAAa,GAAG,IAAI1S,mBAAmB,CAAC,IAAI,CAAC6S,cAAc,EAAE+D,cAAc,EAAE,EAAE,CAAC;IACrF;IACA;IACA,IAAI,CAAClE,aAAa,CAAClJ,QAAQ,CAACC,GAAG,CAACxJ,qBAAqB,CAAC,CAAC4W,eAAe,CAAC,CAAC;IACxE;IACA;IACA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACpE,aAAa,CAAClJ,QAAQ,CAACC,GAAG,CAACvJ,SAAS,EAAEC,kBAAkB,CAAC;IAC/EC,YAAY,CAAC0W,QAAQ,CAAC;IACtB,OAAO,IAAI,CAACpE,aAAa;EAC7B;EACA;AACJ;AACA;EACIqE,oBAAoBA,CAACC,UAAU,EAAE;IAC7B,IAAI,CAAC9D,0BAA0B,CAAC,CAAC8D,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACZ,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACM,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACX,6BAA6B,CAACiB,UAAU,CAAC;IAC9C,IAAI,CAACP,qBAAqB,CAAC,CAAC;EAChC;EACA;AACJ;AACA;EACUQ,qBAAqBA,CAACD,UAAU,EAAE;IAAA,IAAAE,MAAA;IAAA,OAAAtT,iBAAA;MACpCsT,MAAI,CAAChE,0BAA0B,CAAC,CAAC8D,UAAU,CAAC,CAAC;MAC7C,MAAME,MAAI,CAAClB,iBAAiB,CAAC,CAAC;MAC9BkB,MAAI,CAACR,sBAAsB,CAAC,CAAC;MAC7BQ,MAAI,CAACnB,6BAA6B,CAACiB,UAAU,CAAC;MAC9CE,MAAI,CAACT,qBAAqB,CAAC,CAAC;IAAC;EACjC;EACA;AACJ;AACA;EACIU,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACrF,SAAS,CAAC2B,MAAM;EAChC;EACA;AACJ;AACA;EACI2D,sBAAsBA,CAACJ,UAAU,EAAE;IAC/B,OAAOK,aAAa,CAACL,UAAU,CAACM,IAAI,CAACpG,YAAY,CAAC,CAACqG,MAAM,CAAC,CAACC,SAAS,EAAEC,WAAW,KAAK;MAClF,MAAMC,YAAY,GAAGD,WAAW,CAACE,IAAI;MACrCD,YAAY,IAAIF,SAAS,CAAC3S,IAAI,CAAC,IAAIxE,wBAAwB,CAACqX,YAAY,EAAE,IAAI,CAAChF,aAAa,CAAC,CAAC;MAC9F,OAAO8E,SAAS;IACpB,CAAC,EAAE,EAAE,CAAC;EACV;EACApB,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAID,mBAAmB,GAAG,KAAK;IAC/B,IAAI,CAAC7E,iBAAiB,CAACvE,OAAO,CAAE0K,WAAW,IAAK;MAC5C,IAAIlY,wBAAwB,CAACkY,WAAW,CAAC,EAAE;QACvC,MAAM,IAAIzT,KAAK,CAAC,cAAcyT,WAAW,CAAC9D,IAAI,6BAA6B,GACvE,6EAA6E,CAAC;MACtF;MACAwC,mBAAmB,GAAGA,mBAAmB,IAAIrW,gCAAgC,CAAC2X,WAAW,CAAC;MAC1F,MAAMnK,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAAClB,SAAS,CAAC9L,OAAO,CAAC2S,WAAW,CAAC;MAC9D,IAAInK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMoG,gBAAgB,CAAC+D,WAAW,CAAC9D,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACiE,eAAe,CAAC/X,YAAY,EAAE4X,WAAW,CAAC;MAC/C,IAAIhY,iCAAiC,EAAE;QACnCC,YAAY,CAAC8T,kBAAkB,CAACiE,WAAW,CAAC;MAChD;MACAnX,iBAAiB,CAACmX,WAAW,EAAEnK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACgE,iBAAiB,CAAC3K,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC4K,iBAAiB,CAACxE,OAAO,CAAE0K,WAAW,IAAK;MAC5C,MAAMnK,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAACmC,SAAS,CAACnP,OAAO,CAAC2S,WAAW,CAAC;MAC9D,IAAInK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMoG,gBAAgB,CAAC+D,WAAW,CAAC9D,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACiE,eAAe,CAACrX,WAAW,EAAEkX,WAAW,CAAC;MAC9CjX,iBAAiB,CAACiX,WAAW,EAAEnK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACiE,iBAAiB,CAAC5K,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC6K,YAAY,CAACzE,OAAO,CAAE0K,WAAW,IAAK;MACvC,MAAMnK,QAAQ,GAAG,IAAI,CAACwE,SAAS,CAACqC,IAAI,CAACrP,OAAO,CAAC2S,WAAW,CAAC;MACzD,IAAInK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMoG,gBAAgB,CAAC+D,WAAW,CAAC9D,IAAI,EAAE,MAAM,CAAC;MACpD;MACA,IAAI,CAACiE,eAAe,CAACnX,YAAY,EAAEgX,WAAW,CAAC;MAC/C/W,YAAY,CAAC+W,WAAW,EAAEnK,QAAQ,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAACkE,YAAY,CAAC7K,KAAK,CAAC,CAAC;IACzB,OAAOwP,mBAAmB;EAC9B;EACAM,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC7E,iBAAiB,CAACnL,IAAI,GAAG,CAAC,EAAE;MACjC;MACA;MACA;MACA,MAAMoR,gBAAgB,GAAG,IAAI,CAAChF,cAAc,CAAClS,WAAW,CAAC;MACzD,MAAMmX,eAAe,GAAG,IAAI,CAACC,iCAAiC,CAACF,gBAAgB,CAAC1G,OAAO,CAAC;MACxF,IAAI2G,eAAe,CAACrR,IAAI,GAAG,CAAC,EAAE;QAC1BqR,eAAe,CAAC/K,OAAO,CAAEiK,UAAU,IAAK;UACpC,IAAI,CAACvX,iCAAiC,EAAE;YACpC,IAAI,CAACuY,qBAAqB,CAAChB,UAAU,EAAErW,WAAW,EAAE,yBAAyB,CAAC;YAC9EqW,UAAU,CAACrW,WAAW,CAAC,CAACsX,uBAAuB,GAAG,IAAI;UAC1D,CAAC,MACI;YACDvY,YAAY,CAAC8T,kBAAkB,CAACwD,UAAU,CAAC;UAC/C;QACJ,CAAC,CAAC;MACN;IACJ;IACA,MAAMkB,aAAa,GAAG,IAAI1L,GAAG,CAAC,CAAC;IAC/B,MAAM2L,gBAAgB,GAAInB,UAAU,IAAK;MACrC,IAAI,CAACkB,aAAa,CAACnK,GAAG,CAACiJ,UAAU,CAAC,EAAE;QAChC,MAAMoB,eAAe,GAAG9H,uBAAuB,CAAC0G,UAAU,CAAC;QAC3D,MAAMqB,QAAQ,GAAGD,eAAe,GAAG,IAAI,CAACvF,cAAc,GAAGmE,UAAU;QACnEkB,aAAa,CAACjL,GAAG,CAAC+J,UAAU,EAAEpW,oBAAoB,CAACyX,QAAQ,CAAC,CAAC;MACjE;MACA,OAAOH,aAAa,CAACzO,GAAG,CAACuN,UAAU,CAAC;IACxC,CAAC;IACD,IAAI,CAAChF,sBAAsB,CAACjF,OAAO,CAAC,CAACiK,UAAU,EAAEsB,aAAa,KAAK;MAC/D,IAAItB,UAAU,KAAK,IAAI,EAAE;QACrB,MAAMuB,WAAW,GAAGJ,gBAAgB,CAACnB,UAAU,CAAC;QAChD,IAAI,CAACgB,qBAAqB,CAACM,aAAa,EAAEzY,YAAY,EAAE,eAAe,CAAC;QACxE,IAAI,CAACmY,qBAAqB,CAACM,aAAa,EAAEzY,YAAY,EAAE,UAAU,CAAC;QACnEgB,2BAA2B,CAAC2X,eAAe,CAACF,aAAa,CAAC,EAAEC,WAAW,CAAC;MAC5E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACP,qBAAqB,CAACM,aAAa,EAAEzY,YAAY,EAAE,OAAO,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAACmS,sBAAsB,CAACrL,KAAK,CAAC,CAAC;EACvC;EACA+P,sBAAsBA,CAAA,EAAG;IACrB,MAAM+B,mBAAmB,GAAIC,KAAK,IAAMzR,IAAI,IAAK;MAC7C,MAAM0J,QAAQ,GAAG+H,KAAK,KAAK7Y,YAAY,GAAG,IAAI,CAACiS,SAAS,CAAClB,SAAS,GAAG,IAAI,CAACkB,SAAS,CAACmC,SAAS;MAC7F,MAAM3G,QAAQ,GAAGqD,QAAQ,CAAC7L,OAAO,CAACmC,IAAI,CAAC;MACvC,IAAI,IAAI,CAAC0R,oBAAoB,CAACrL,QAAQ,CAAC8D,SAAS,CAAC,EAAE;QAC/C,IAAI,CAACwH,6BAA6B,CAAC3R,IAAI,EAAEyR,KAAK,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAAChH,cAAc,CAAC3E,OAAO,CAAC0L,mBAAmB,CAAC5Y,YAAY,CAAC,CAAC;IAC9D,IAAI,CAAC8R,cAAc,CAAC5E,OAAO,CAAC0L,mBAAmB,CAAClY,WAAW,CAAC,CAAC;IAC7D,IAAI,CAACmR,cAAc,CAAC/K,KAAK,CAAC,CAAC;IAC3B,IAAI,CAACgL,cAAc,CAAChL,KAAK,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIoP,6BAA6BA,CAAC9O,IAAI,EAAE;IAChC,MAAM4R,QAAQ,GAAGC,qBAAqB,CAAC7R,IAAI,CAAC,IAAI8R,UAAU,CAAC9R,IAAI,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAAC4R,QAAQ,IAAI,IAAI,CAACpG,6BAA6B,CAAC1E,GAAG,CAAC9G,IAAI,CAAC,EAAE;MAC3D;IACJ;IACA,IAAI,CAACwL,6BAA6B,CAACxJ,GAAG,CAAChC,IAAI,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA,MAAM+R,WAAW,GAAG/R,IAAI,CAACnG,WAAW,CAAC;IACrC;IACA,IAAI,IAAI,CAAC0R,wBAAwB,CAAC/L,IAAI,KAAK,CAAC,EACxC;IACJ,IAAIqS,qBAAqB,CAAC7R,IAAI,CAAC,EAAE;MAC7B;MACA,MAAMgO,GAAG,GAAGuD,eAAe,CAACvR,IAAI,CAAC;MACjC,MAAMgS,YAAY,GAAG5B,aAAa,CAACpC,GAAG,CAACgE,YAAY,IAAI,EAAE,CAAC;MAC1D,KAAK,MAAMC,UAAU,IAAID,YAAY,EAAE;QACnC,IAAI,CAAClD,6BAA6B,CAACmD,UAAU,CAAC;MAClD;IACJ,CAAC,MACI;MACD,MAAM9H,SAAS,GAAG,CACd,GAAG4H,WAAW,CAAC5H,SAAS,EACxB,IAAI,IAAI,CAACmB,yBAAyB,CAAC9I,GAAG,CAACxC,IAAI,CAAC,IAAI,EAAE,CAAC,CACtD;MACD,IAAI,IAAI,CAAC0R,oBAAoB,CAACvH,SAAS,CAAC,EAAE;QACtC,IAAI,CAACwG,eAAe,CAAC9W,WAAW,EAAEmG,IAAI,CAAC;QACvC,IAAI,CAAC+Q,qBAAqB,CAAC/Q,IAAI,EAAEnG,WAAW,EAAE,WAAW,CAAC;QAC1DkY,WAAW,CAAC5H,SAAS,GAAG,IAAI,CAAC+H,sBAAsB,CAAC/H,SAAS,CAAC;MAClE;MACA;MACA,MAAM4B,SAAS,GAAG/L,IAAI,CAACtG,WAAW,CAAC;MACnC,MAAMwQ,OAAO,GAAGkG,aAAa,CAACrE,SAAS,CAAC7B,OAAO,CAAC;MAChD,KAAK,MAAMiI,cAAc,IAAIjI,OAAO,EAAE;QAClC,IAAI,CAAC4E,6BAA6B,CAACqD,cAAc,CAAC;MACtD;MACA;MACA;MACA,KAAK,MAAMA,cAAc,IAAIC,OAAO,CAACL,WAAW,CAAC7H,OAAO,CAAC,EAAE;QACvD,IAAImI,qBAAqB,CAACF,cAAc,CAAC,EAAE;UACvC,IAAI,CAAClH,aAAa,CAACrN,IAAI,CAAC;YACpB0U,MAAM,EAAEH,cAAc;YACtBI,SAAS,EAAE,WAAW;YACtBC,aAAa,EAAEL,cAAc,CAAChI;UAClC,CAAC,CAAC;UACFgI,cAAc,CAAChI,SAAS,GAAG,IAAI,CAAC+H,sBAAsB,CAACC,cAAc,CAAChI,SAAS,CAAC;QACpF;MACJ;IACJ;EACJ;EACAuF,iCAAiCA,CAAA,EAAG;IAChC,IAAI,CAAC9E,uBAAuB,CAAC9E,OAAO,CAAC,CAACuI,MAAM,EAAErO,IAAI,KAAMA,IAAI,CAACpH,YAAY,CAAC,CAACyV,MAAM,GAAGA,MAAO,CAAC;IAC5F,IAAI,CAACzD,uBAAuB,CAAClL,KAAK,CAAC,CAAC;EACxC;EACAsM,cAAcA,CAACyG,GAAG,EAAE1C,UAAU,EAAE;IAC5B,KAAK,MAAMjO,KAAK,IAAI2Q,GAAG,EAAE;MACrB,IAAIhM,KAAK,CAACC,OAAO,CAAC5E,KAAK,CAAC,EAAE;QACtB,IAAI,CAACkK,cAAc,CAAClK,KAAK,EAAEiO,UAAU,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAAC2C,SAAS,CAAC5Q,KAAK,EAAEiO,UAAU,CAAC;MACrC;IACJ;EACJ;EACApD,iBAAiBA,CAACL,QAAQ,EAAEjG,QAAQ,EAAE;IAClC;IACA,IAAI,CAACsK,eAAe,CAACjX,WAAW,EAAE4S,QAAQ,CAAC;IAC3C,IAAI,CAACqE,eAAe,CAAC9W,WAAW,EAAEyS,QAAQ,CAAC;IAC3CxS,oBAAoB,CAACwS,QAAQ,EAAEjG,QAAQ,CAAC;EAC5C;EACAyG,uCAAuCA,CAAC9M,IAAI,EAAE;IAC1C,MAAMyO,eAAe,GAAGnW,wBAAwB,CAAC0H,IAAI,CAAC;IACtD,IAAIyO,eAAe,EAAE;MACjB,IAAI,CAACjE,2BAA2B,CAACxI,GAAG,CAAChC,IAAI,CAAC;IAC9C;EACJ;EACA0S,SAASA,CAAC1S,IAAI,EAAE+P,UAAU,EAAE;IACxB;IACA;IACA,IAAI,CAACjD,uCAAuC,CAAC9M,IAAI,CAAC;IAClD,MAAM2J,SAAS,GAAG,IAAI,CAACkB,SAAS,CAAClB,SAAS,CAAC9L,OAAO,CAACmC,IAAI,CAAC;IACxD,IAAI2J,SAAS,EAAE;MACX;MACA;MACA;MACA,IAAI9Q,gCAAgC,CAACmH,IAAI,CAAC,IAAI,CAACA,IAAI,CAACmN,cAAc,CAACvU,YAAY,CAAC,EAAE;QAC9E,IAAI,CAACyR,iBAAiB,CAACrI,GAAG,CAAChC,IAAI,CAAC;MACpC;MACA,IAAI,CAACyK,cAAc,CAACzI,GAAG,CAAChC,IAAI,CAAC;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAAC+K,sBAAsB,CAACjE,GAAG,CAAC9G,IAAI,CAAC,IACtC,IAAI,CAAC+K,sBAAsB,CAACvI,GAAG,CAACxC,IAAI,CAAC,KAAKoJ,qBAAqB,CAACE,WAAW,EAAE;QAC7E,IAAI,CAACyB,sBAAsB,CAAC/E,GAAG,CAAChG,IAAI,EAAE+P,UAAU,CAAC;MACrD;MACA;IACJ;IACA,MAAM/C,SAAS,GAAG,IAAI,CAACnC,SAAS,CAACmC,SAAS,CAACnP,OAAO,CAACmC,IAAI,CAAC;IACxD,IAAIgN,SAAS,EAAE;MACX,IAAI,CAAChN,IAAI,CAACmN,cAAc,CAAC7T,WAAW,CAAC,EAAE;QACnC,IAAI,CAACgR,iBAAiB,CAACtI,GAAG,CAAChC,IAAI,CAAC;MACpC;MACA,IAAI,CAAC0K,cAAc,CAAC1I,GAAG,CAAChC,IAAI,CAAC;MAC7B;IACJ;IACA,MAAMkN,IAAI,GAAG,IAAI,CAACrC,SAAS,CAACqC,IAAI,CAACrP,OAAO,CAACmC,IAAI,CAAC;IAC9C,IAAIkN,IAAI,IAAI,CAAClN,IAAI,CAACmN,cAAc,CAAC3T,YAAY,CAAC,EAAE;MAC5C,IAAI,CAAC+Q,YAAY,CAACvI,GAAG,CAAChC,IAAI,CAAC;MAC3B;IACJ;EACJ;EACAiM,0BAA0BA,CAACwG,GAAG,EAAE;IAC5B;IACA;IACA;IACA;IACA,MAAME,aAAa,GAAG,IAAIxT,GAAG,CAAC,CAAC;IAC/B,MAAMyT,+BAA+B,GAAIH,GAAG,IAAK;MAC7C,KAAK,MAAM3Q,KAAK,IAAI2Q,GAAG,EAAE;QACrB,IAAIhM,KAAK,CAACC,OAAO,CAAC5E,KAAK,CAAC,EAAE;UACtB8Q,+BAA+B,CAAC9Q,KAAK,CAAC;QAC1C,CAAC,MACI,IAAI+Q,cAAc,CAAC/Q,KAAK,CAAC,EAAE;UAC5B,MAAMkM,GAAG,GAAGlM,KAAK,CAACuO,IAAI;UACtB,IAAIsC,aAAa,CAAC7L,GAAG,CAACkH,GAAG,CAAC,EAAE;YACxB;UACJ;UACA2E,aAAa,CAAC3Q,GAAG,CAACgM,GAAG,CAAC;UACtB;UACA;UACA,IAAI,CAAChC,cAAc,CAACoE,aAAa,CAACpC,GAAG,CAAC/D,YAAY,CAAC,EAAEnI,KAAK,CAAC;UAC3D8Q,+BAA+B,CAACxC,aAAa,CAACpC,GAAG,CAAC9D,OAAO,CAAC,CAAC;UAC3D0I,+BAA+B,CAACxC,aAAa,CAACpC,GAAG,CAAC8E,OAAO,CAAC,CAAC;QAC/D,CAAC,MACI,IAAIT,qBAAqB,CAACvQ,KAAK,CAAC,EAAE;UACnC8Q,+BAA+B,CAAC,CAAC9Q,KAAK,CAACwK,QAAQ,CAAC,CAAC;QACrD,CAAC,MACI,IAAIuF,qBAAqB,CAAC/P,KAAK,CAAC,EAAE;UACnC,IAAI,CAAC4Q,SAAS,CAAC5Q,KAAK,EAAE,IAAI,CAAC;UAC3B,MAAMkM,GAAG,GAAGuD,eAAe,CAACzP,KAAK,CAAC;UAClC,IAAI6Q,aAAa,CAAC7L,GAAG,CAACkH,GAAG,CAAC,EAAE;YACxB;UACJ;UACA2E,aAAa,CAAC3Q,GAAG,CAACgM,GAAG,CAAC;UACtB,MAAMgE,YAAY,GAAG5B,aAAa,CAACpC,GAAG,CAACgE,YAAY,IAAI,EAAE,CAAC;UAC1DA,YAAY,CAAClM,OAAO,CAAEmM,UAAU,IAAK;YACjC;YACA;YACA;YACA;YACA,IAAIJ,qBAAqB,CAACI,UAAU,CAAC,IAAIY,cAAc,CAACZ,UAAU,CAAC,EAAE;cACjEW,+BAA+B,CAAC,CAACX,UAAU,CAAC,CAAC;YACjD,CAAC,MACI;cACD,IAAI,CAACS,SAAS,CAACT,UAAU,EAAE,IAAI,CAAC;YACpC;UACJ,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;IACDW,+BAA+B,CAACH,GAAG,CAAC;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA3B,iCAAiCA,CAAC2B,GAAG,EAAE;IACnC,MAAMM,WAAW,GAAG,IAAI5T,GAAG,CAAC,CAAC;IAC7B,MAAM0R,eAAe,GAAG,IAAI1R,GAAG,CAAC,CAAC;IACjC,MAAM6T,wBAAwB,GAAGA,CAACP,GAAG,EAAEQ,IAAI,KAAK;MAC5C,KAAK,MAAMnR,KAAK,IAAI2Q,GAAG,EAAE;QACrB,IAAIhM,KAAK,CAACC,OAAO,CAAC5E,KAAK,CAAC,EAAE;UACtB;UACA;UACAkR,wBAAwB,CAAClR,KAAK,EAAEmR,IAAI,CAAC;QACzC,CAAC,MACI,IAAIJ,cAAc,CAAC/Q,KAAK,CAAC,EAAE;UAC5B,IAAIiR,WAAW,CAACjM,GAAG,CAAChF,KAAK,CAAC,EAAE;YACxB;YACA;YACA;YACA,IAAI+O,eAAe,CAAC/J,GAAG,CAAChF,KAAK,CAAC,EAAE;cAC5BmR,IAAI,CAACnN,OAAO,CAAEoN,IAAI,IAAKrC,eAAe,CAAC7O,GAAG,CAACkR,IAAI,CAAC,CAAC;YACrD;YACA;UACJ;UACAH,WAAW,CAAC/Q,GAAG,CAACF,KAAK,CAAC;UACtB,IAAI,IAAI,CAAC6I,iBAAiB,CAAC7D,GAAG,CAAChF,KAAK,CAAC,EAAE;YACnCmR,IAAI,CAACnN,OAAO,CAAEoN,IAAI,IAAKrC,eAAe,CAAC7O,GAAG,CAACkR,IAAI,CAAC,CAAC;UACrD;UACA;UACA,MAAMnH,SAAS,GAAGjK,KAAK,CAACpI,WAAW,CAAC;UACpCsZ,wBAAwB,CAAC5C,aAAa,CAACrE,SAAS,CAAC7B,OAAO,CAAC,EAAE+I,IAAI,CAACjM,MAAM,CAAClF,KAAK,CAAC,CAAC;QAClF;MACJ;IACJ,CAAC;IACDkR,wBAAwB,CAACP,GAAG,EAAE,EAAE,CAAC;IACjC,OAAO5B,eAAe;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,eAAeA,CAAC5K,IAAI,EAAE/F,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAACgL,aAAa,CAAClE,GAAG,CAAC9G,IAAI,CAAC,EAAE;MAC/B,IAAI,CAACgL,aAAa,CAAChF,GAAG,CAAChG,IAAI,EAAE,IAAIuF,GAAG,CAAC,CAAC,CAAC;IAC3C;IACA,MAAM4N,WAAW,GAAG,IAAI,CAACnI,aAAa,CAACxI,GAAG,CAACxC,IAAI,CAAC;IAChD,IAAI,CAACmT,WAAW,CAACrM,GAAG,CAACf,IAAI,CAAC,EAAE;MACxB,MAAMqN,UAAU,GAAGxL,MAAM,CAACO,wBAAwB,CAACnI,IAAI,EAAE+F,IAAI,CAAC;MAC9DoN,WAAW,CAACnN,GAAG,CAACD,IAAI,EAAEqN,UAAU,CAAC;IACrC;EACJ;EACArC,qBAAqBA,CAAC/Q,IAAI,EAAEqT,QAAQ,EAAEd,SAAS,EAAE;IAC7C,MAAMvE,GAAG,GAAGhO,IAAI,CAACqT,QAAQ,CAAC;IAC1B,MAAMb,aAAa,GAAGxE,GAAG,CAACuE,SAAS,CAAC;IACpC,IAAI,CAACtH,aAAa,CAACrN,IAAI,CAAC;MAAE0U,MAAM,EAAEtE,GAAG;MAAEuE,SAAS;MAAEC;IAAc,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;AACA;EACIvD,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACjF,gCAAgC,KAAK,IAAI,EAAE;MAChD,IAAI,CAACA,gCAAgC,GAAG,IAAIzE,GAAG,CAAC,CAAC;IACrD;IACAxL,yCAAyC,CAAC,CAAC,CAAC+L,OAAO,CAAC,CAAChE,KAAK,EAAEuF,GAAG,KAAK,IAAI,CAAC2C,gCAAgC,CAAChE,GAAG,CAACqB,GAAG,EAAEvF,KAAK,CAAC,CAAC;EAC9H;EACA;AACJ;AACA;AACA;AACA;EACIwR,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACtJ,gCAAgC,KAAK,IAAI,EAAE;MAChDhQ,gCAAgC,CAAC,IAAI,CAACgQ,gCAAgC,CAAC;MACvE,IAAI,CAACA,gCAAgC,GAAG,IAAI;IAChD;EACJ;EACAuJ,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACAC,YAAY,CAAC,IAAI,CAACvI,aAAa,EAAGwI,EAAE,IAAK;MACrCA,EAAE,CAACnB,MAAM,CAACmB,EAAE,CAAClB,SAAS,CAAC,GAAGkB,EAAE,CAACjB,aAAa;IAC9C,CAAC,CAAC;IACF;IACA,IAAI,CAACxH,aAAa,CAAClF,OAAO,CAAC,CAAC4N,IAAI,EAAE1T,IAAI,KAAK;MACvC,IAAIxH,iCAAiC,EAAE;QACnCC,YAAY,CAAC8T,kBAAkB,CAACvM,IAAI,CAAC;MACzC;MACA0T,IAAI,CAAC5N,OAAO,CAAC,CAAC6N,UAAU,EAAE5N,IAAI,KAAK;QAC/B,IAAI,CAAC4N,UAAU,EAAE;UACb;UACA;UACA;UACA;UACA;UACA;UACA,OAAO3T,IAAI,CAAC+F,IAAI,CAAC;QACrB,CAAC,MACI;UACD6B,MAAM,CAACgM,cAAc,CAAC5T,IAAI,EAAE+F,IAAI,EAAE4N,UAAU,CAAC;QACjD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAC3I,aAAa,CAACtL,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAC8L,6BAA6B,CAAC9L,KAAK,CAAC,CAAC;IAC1C,IAAI,CAAC4T,+BAA+B,CAAC,CAAC;IACtC;IACAna,YAAY,CAACD,kBAAkB,CAAC;EACpC;EACAqW,iBAAiBA,CAAA,EAAG;IAChB,MAAMsE,eAAe,CAAC;IAEtB/Z,oBAAoB,CAAC+Z,eAAe,EAAE;MAClC1J,SAAS,EAAE,CACP,GAAG,IAAI,CAACkB,qBAAqB,EAC7BpR,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvC8E,8BAA8B,EAC9B;QACImN,OAAO,EAAEhS,mCAAmC;QAC5CsT,UAAU,EAAEA,CAAA,KAAM;UACd,IAAInW,QAAQ,CAAC8C,iBAAiB,CAAC,IAAI9C,QAAQ,CAACyH,0BAA0B,EAAE;YAAE8B,QAAQ,EAAE;UAAK,CAAC,CAAC,EAAE;YACzF,MAAMkT,OAAO,GAAGzc,QAAQ,CAAC0H,8BAA8B,CAAC;YACxD,OAAQM,CAAC,IAAK;cACVyU,OAAO,CAAC1U,WAAW,CAACC,CAAC,CAAC;YAC1B,CAAC;UACL,CAAC,MACI;YACD,MAAMJ,gBAAgB,GAAG5H,QAAQ,CAACE,YAAY,CAAC;YAC/C,MAAM4J,MAAM,GAAG9J,QAAQ,CAACC,MAAM,CAAC;YAC/B,OAAQ+H,CAAC,IAAK8B,MAAM,CAAC7B,iBAAiB,CAAC,MAAML,gBAAgB,CAACG,WAAW,CAACC,CAAC,CAAC,CAAC;UACjF;QACJ;MACJ,CAAC,EACD;QAAE6M,OAAO,EAAE9R,yBAAyB;QAAE2Z,WAAW,EAAE1Z;MAA8B,CAAC;IAE1F,CAAC,CAAC;IACF,MAAM8P,SAAS,GAAG,CACd;MAAE+B,OAAO,EAAE5R,QAAQ;MAAEkT,UAAU,EAAEA,CAAA,KAAM,IAAIwG,cAAc,CAAC,IAAI;IAAE,CAAC,EACjE;MAAE9H,OAAO,EAAE3R,mBAAmB;MAAE4R,QAAQ,EAAE;QAAE8H,QAAQ,EAAE,IAAI,CAACvI;MAAmB;IAAE,CAAC,EACjF,GAAG,IAAI,CAACvB,SAAS,EACjB,GAAG,IAAI,CAACiB,iBAAiB,CAC5B;IACD,MAAMlB,OAAO,GAAG,CAAC2J,eAAe,EAAE,IAAI,CAAC9J,qBAAqB,EAAE,IAAI,CAACG,OAAO,IAAI,EAAE,CAAC;IACjFpQ,oBAAoB,CAAC,IAAI,CAAC8R,cAAc,EAAE;MACtC3B,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,OAAO;MACPE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBD;IACJ,CAAC,EACD,sCAAuC,IAAI,CAAC;IAC5C,IAAI,CAAC2E,6BAA6B,CAAC,IAAI,CAAClD,cAAc,CAAC;EAC3D;EACA,IAAIrJ,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC2I,SAAS,KAAK,IAAI,EAAE;MACzB,OAAO,IAAI,CAACA,SAAS;IACzB;IACA,MAAMf,SAAS,GAAG,EAAE;IACpB,MAAM+J,eAAe,GAAG,IAAI,CAACpK,QAAQ,CAACvH,QAAQ,CAACC,GAAG,CAAChI,gBAAgB,CAAC;IACpE0Z,eAAe,CAACpO,OAAO,CAAEqO,IAAI,IAAK;MAC9B,IAAIA,IAAI,CAAChK,SAAS,EAAE;QAChBA,SAAS,CAACvM,IAAI,CAACuW,IAAI,CAAChK,SAAS,CAAC;MAClC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACgB,iBAAiB,KAAK,IAAI,EAAE;MACjChB,SAAS,CAACvM,IAAI,CAAC,GAAG,IAAI,CAACuN,iBAAiB,CAAC;IAC7C;IACA,IAAI,CAACD,SAAS,GAAGzQ,QAAQ,CAAC2Z,MAAM,CAAC;MAAEjK,SAAS;MAAEkK,MAAM,EAAE,IAAI,CAACvK,QAAQ,CAACvH;IAAS,CAAC,CAAC;IAC/E,OAAO,IAAI,CAAC2I,SAAS;EACzB;EACA;EACAoJ,0BAA0BA,CAAChH,QAAQ,EAAE;IACjC,MAAMD,KAAK,GAAGkH,gBAAgB,CAACjH,QAAQ,CAAC;IACxC,OAAO,IAAI,CAAC/B,wBAAwB,CAAC/I,GAAG,CAAC6K,KAAK,CAAC,IAAI,IAAI;EAC3D;EACAmH,oBAAoBA,CAACrK,SAAS,EAAE;IAC5B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAACxM,MAAM,IAAI,IAAI,CAAC4N,wBAAwB,CAAC/L,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb;IACA;IACA;IACA;IACA;IACA,OAAO4S,OAAO,CAACqC,gBAAgB,CAACtK,SAAS,EAAGmD,QAAQ,IAAK,IAAI,CAACgH,0BAA0B,CAAChH,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;EAC9G;EACA4E,sBAAsBA,CAAC/H,SAAS,EAAE;IAC9B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAACxM,MAAM,IAAI,IAAI,CAAC4N,wBAAwB,CAAC/L,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb,MAAMkV,kBAAkB,GAAGD,gBAAgB,CAACtK,SAAS,CAAC;IACtD,MAAM7B,SAAS,GAAG,IAAI,CAACkM,oBAAoB,CAACE,kBAAkB,CAAC;IAC/D,MAAMC,mBAAmB,GAAG,CAAC,GAAGD,kBAAkB,EAAE,GAAGpM,SAAS,CAAC;IACjE,MAAMsM,KAAK,GAAG,EAAE;IAChB,MAAMC,uBAAuB,GAAG,IAAI1V,GAAG,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACAqU,YAAY,CAACmB,mBAAmB,EAAGrH,QAAQ,IAAK;MAC5C,MAAMD,KAAK,GAAGkH,gBAAgB,CAACjH,QAAQ,CAAC;MACxC,IAAI,IAAI,CAAC/B,wBAAwB,CAACzE,GAAG,CAACuG,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACwH,uBAAuB,CAAC/N,GAAG,CAACuG,KAAK,CAAC,EAAE;UACrCwH,uBAAuB,CAAC7S,GAAG,CAACqL,KAAK,CAAC;UAClC;UACA;UACA;UACAuH,KAAK,CAACE,OAAO,CAAC;YAAE,GAAGxH,QAAQ;YAAEG,KAAK,EAAE;UAAM,CAAC,CAAC;QAChD;MACJ,CAAC,MACI;QACDmH,KAAK,CAACE,OAAO,CAACxH,QAAQ,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,OAAOsH,KAAK;EAChB;EACAlD,oBAAoBA,CAACvH,SAAS,EAAE;IAC5B,OAAO,IAAI,CAACqK,oBAAoB,CAACrK,SAAS,CAAC,CAACxM,MAAM,GAAG,CAAC;EAC1D;EACAgU,6BAA6BA,CAACnB,WAAW,EAAEiB,KAAK,EAAE;IAC9C,MAAMzD,GAAG,GAAGwC,WAAW,CAACiB,KAAK,CAAC;IAC9B,IAAIzD,GAAG,IAAIA,GAAG,CAAC+G,iBAAiB,EAAE;MAC9B,IAAI,CAACpE,eAAe,CAACc,KAAK,EAAEjB,WAAW,CAAC;MACxC,MAAM9G,QAAQ,GAAGsE,GAAG,CAAC+G,iBAAiB;MACtC,MAAMC,kBAAkB,GAAI7K,SAAS,IAAK,IAAI,CAAC+H,sBAAsB,CAAC/H,SAAS,CAAC;MAChF,IAAI,CAAC4G,qBAAqB,CAACP,WAAW,EAAEiB,KAAK,EAAE,mBAAmB,CAAC;MACnEzD,GAAG,CAAC+G,iBAAiB,GAAIE,KAAK,IAAKvL,QAAQ,CAACuL,KAAK,EAAED,kBAAkB,CAAC;IAC1E;EACJ;AACJ;AACA,SAASlK,aAAaA,CAAA,EAAG;EACrB,OAAO;IACH0B,MAAM,EAAE,IAAIrD,gBAAgB,CAAC,CAAC;IAC9BQ,SAAS,EAAE,IAAIV,iBAAiB,CAAC,CAAC;IAClC+D,SAAS,EAAE,IAAIhE,iBAAiB,CAAC,CAAC;IAClCkE,IAAI,EAAE,IAAIhE,YAAY,CAAC;EAC3B,CAAC;AACL;AACA,SAAS2I,qBAAqBA,CAAC/P,KAAK,EAAE;EAClC,MAAMkM,GAAG,GAAGuD,eAAe,CAACzP,KAAK,CAAC;EAClC,OAAO,CAAC,CAACkM,GAAG,EAAEpE,UAAU;AAC5B;AACA,SAAS2H,eAAeA,CAACzP,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAAC4O,IAAI,IAAI,IAAI;AAC7B;AACA,SAASmC,cAAcA,CAAC/Q,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACqL,cAAc,CAAC,MAAM,CAAC;AACvC;AACA,SAAS2E,UAAUA,CAAChQ,KAAK,EAAE;EACvB,OAAO+Q,cAAc,CAAC/Q,KAAK,CAAC;AAChC;AACA,SAASsO,aAAaA,CAAC8E,OAAO,EAAE;EAC5B,OAAOA,OAAO,YAAYC,QAAQ,GAAGD,OAAO,CAAC,CAAC,GAAGA,OAAO;AAC5D;AACA,SAAS9C,OAAOA,CAAC3S,MAAM,EAAE;EACrB,MAAM2V,GAAG,GAAG,EAAE;EACd3V,MAAM,CAACqG,OAAO,CAAEhE,KAAK,IAAK;IACtB,IAAI2E,KAAK,CAACC,OAAO,CAAC5E,KAAK,CAAC,EAAE;MACtBsT,GAAG,CAACxX,IAAI,CAAC,GAAGwU,OAAO,CAACtQ,KAAK,CAAC,CAAC;IAC/B,CAAC,MACI;MACDsT,GAAG,CAACxX,IAAI,CAACkE,KAAK,CAAC;IACnB;EACJ,CAAC,CAAC;EACF,OAAOsT,GAAG;AACd;AACA,SAASC,UAAUA,CAACvT,KAAK,EAAE;EACvB,OAAOA,KAAK;AAChB;AACA,SAAS2S,gBAAgBA,CAACtK,SAAS,EAAEmL,KAAK,GAAGD,UAAU,EAAE;EACrD,MAAMD,GAAG,GAAG,EAAE;EACd,KAAK,IAAI9H,QAAQ,IAAInD,SAAS,EAAE;IAC5B,IAAIzP,uBAAuB,CAAC4S,QAAQ,CAAC,EAAE;MACnCA,QAAQ,GAAGA,QAAQ,CAACiI,UAAU;IAClC;IACA,IAAI9O,KAAK,CAACC,OAAO,CAAC4G,QAAQ,CAAC,EAAE;MACzB8H,GAAG,CAACxX,IAAI,CAAC,GAAG6W,gBAAgB,CAACnH,QAAQ,EAAEgI,KAAK,CAAC,CAAC;IAClD,CAAC,MACI;MACDF,GAAG,CAACxX,IAAI,CAAC0X,KAAK,CAAChI,QAAQ,CAAC,CAAC;IAC7B;EACJ;EACA,OAAO8H,GAAG;AACd;AACA,SAASI,gBAAgBA,CAAClI,QAAQ,EAAEmE,KAAK,EAAE;EACvC,OAAOnE,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACmE,KAAK,CAAC;AACtE;AACA,SAAS8C,gBAAgBA,CAACjH,QAAQ,EAAE;EAChC,OAAOkI,gBAAgB,CAAClI,QAAQ,EAAE,SAAS,CAAC,IAAIA,QAAQ;AAC5D;AACA,SAAS+E,qBAAqBA,CAACvQ,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACqL,cAAc,CAAC,UAAU,CAAC;AAC3C;AACA,SAASqG,YAAYA,CAAC/T,MAAM,EAAE5D,EAAE,EAAE;EAC9B,KAAK,IAAI4Z,GAAG,GAAGhW,MAAM,CAAC9B,MAAM,GAAG,CAAC,EAAE8X,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IAC/C5Z,EAAE,CAAC4D,MAAM,CAACgW,GAAG,CAAC,EAAEA,GAAG,CAAC;EACxB;AACJ;AACA,SAAShJ,gBAAgBA,CAACC,IAAI,EAAEgJ,YAAY,EAAE;EAC1C,OAAO,IAAI3Y,KAAK,CAAC,GAAG2P,IAAI,wBAAwBgJ,YAAY,oCAAoC,CAAC;AACrG;AACA,MAAM1B,cAAc,CAAC;EACjB3X,WAAWA,CAACsZ,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAC,iBAAiBA,CAAC7F,UAAU,EAAE;IAC1B,IAAI,CAAC4F,OAAO,CAAC7F,oBAAoB,CAACC,UAAU,CAAC;IAC7C,OAAO,IAAIpV,gBAAgB,CAACoV,UAAU,CAAC;EAC3C;EACM8F,kBAAkBA,CAAC9F,UAAU,EAAE;IAAA,IAAA+F,MAAA;IAAA,OAAAnZ,iBAAA;MACjC,MAAMmZ,MAAI,CAACH,OAAO,CAAC3F,qBAAqB,CAACD,UAAU,CAAC;MACpD,OAAO,IAAIpV,gBAAgB,CAACoV,UAAU,CAAC;IAAC;EAC5C;EACAgG,iCAAiCA,CAAChG,UAAU,EAAE;IAC1C,MAAMiG,eAAe,GAAG,IAAI,CAACJ,iBAAiB,CAAC7F,UAAU,CAAC;IAC1D,MAAMkG,kBAAkB,GAAG,IAAI,CAACN,OAAO,CAACxF,sBAAsB,CAACJ,UAAU,CAAC;IAC1E,OAAO,IAAInV,4BAA4B,CAACob,eAAe,EAAEC,kBAAkB,CAAC;EAChF;EACMC,kCAAkCA,CAACnG,UAAU,EAAE;IAAA,IAAAoG,MAAA;IAAA,OAAAxZ,iBAAA;MACjD,MAAMqZ,eAAe,SAASG,MAAI,CAACN,kBAAkB,CAAC9F,UAAU,CAAC;MACjE,MAAMkG,kBAAkB,GAAGE,MAAI,CAACR,OAAO,CAACxF,sBAAsB,CAACJ,UAAU,CAAC;MAC1E,OAAO,IAAInV,4BAA4B,CAACob,eAAe,EAAEC,kBAAkB,CAAC;IAAC;EACjF;EACAG,UAAUA,CAAA,EAAG,CAAE;EACfC,aAAaA,CAACrW,IAAI,EAAE,CAAE;EACtBsW,WAAWA,CAACvG,UAAU,EAAE;IACpB,MAAMwG,IAAI,GAAG,IAAI,CAACZ,OAAO,CAACzF,kBAAkB,CAAC,CAAC,CAACrS,OAAO,CAACkS,UAAU,CAAC;IAClE,OAAQwG,IAAI,IAAIA,IAAI,CAAC7O,EAAE,IAAKpF,SAAS;EACzC;AACJ;;AAEA;AACA,IAAIkU,kBAAkB,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EAClB,OAAOC,WAAW,CAACC,QAAQ;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EACdra,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACua,2BAA2B,GAAGtY,4BAA4B;IAC/D;IACA,IAAI,CAACwL,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACwC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACuK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,KAAK;EACzC;EACA;IAAS,IAAI,CAACC,SAAS,GAAG,IAAI;EAAE;EAChC,WAAWN,QAAQA,CAAA,EAAG;IAClB,OAAQD,WAAW,CAACO,SAAS,GAAGP,WAAW,CAACO,SAAS,IAAI,IAAIP,WAAW,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOQ,mBAAmBA,CAAC5K,QAAQ,EAAExC,QAAQ,EAAEjF,OAAO,EAAE;IACpD,MAAM8Q,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpChB,OAAO,CAACuB,mBAAmB,CAAC5K,QAAQ,EAAExC,QAAQ,EAAEjF,OAAO,CAAC;IACxD,OAAO8Q,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOwB,oBAAoBA,CAAA,EAAG;IAC1BT,WAAW,CAACC,QAAQ,CAACQ,oBAAoB,CAAC,CAAC;EAC/C;EACA,OAAOC,iBAAiBA,CAACC,MAAM,EAAE;IAC7B,OAAOX,WAAW,CAACC,QAAQ,CAACS,iBAAiB,CAACC,MAAM,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,OAAOvL,sBAAsBA,CAACC,SAAS,EAAE;IACrC,OAAO2K,WAAW,CAACC,QAAQ,CAAC7K,sBAAsB,CAACC,SAAS,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOgD,iBAAiBA,CAAA,EAAG;IACvB,OAAO2H,WAAW,CAACC,QAAQ,CAAC5H,iBAAiB,CAAC,CAAC;EACnD;EACA,OAAO1C,cAAcA,CAACC,QAAQ,EAAE3G,QAAQ,EAAE;IACtC,OAAO+Q,WAAW,CAACC,QAAQ,CAACtK,cAAc,CAACC,QAAQ,EAAE3G,QAAQ,CAAC;EAClE;EACA,OAAOiH,iBAAiBA,CAACjD,SAAS,EAAEhE,QAAQ,EAAE;IAC1C,OAAO+Q,WAAW,CAACC,QAAQ,CAAC/J,iBAAiB,CAACjD,SAAS,EAAEhE,QAAQ,CAAC;EACtE;EACA,OAAOoH,iBAAiBA,CAACC,SAAS,EAAErH,QAAQ,EAAE;IAC1C,OAAO+Q,WAAW,CAACC,QAAQ,CAAC5J,iBAAiB,CAACC,SAAS,EAAErH,QAAQ,CAAC;EACtE;EACA,OAAOsH,YAAYA,CAACC,IAAI,EAAEvH,QAAQ,EAAE;IAChC,OAAO+Q,WAAW,CAACC,QAAQ,CAAC1J,YAAY,CAACC,IAAI,EAAEvH,QAAQ,CAAC;EAC5D;EACA,OAAO2R,gBAAgBA,CAAC3N,SAAS,EAAEoE,QAAQ,EAAE;IACzC,OAAO2I,WAAW,CAACC,QAAQ,CAACW,gBAAgB,CAAC3N,SAAS,EAAEoE,QAAQ,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOD,kCAAkCA,CAACnE,SAAS,EAAEoE,QAAQ,EAAE;IAC3D,OAAO2I,WAAW,CAACC,QAAQ,CAAC7I,kCAAkC,CAACnE,SAAS,EAAEoE,QAAQ,CAAC;EACvF;EACA,OAAOX,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACrC,OAAOoJ,WAAW,CAACC,QAAQ,CAACvJ,gBAAgB,CAACC,KAAK,EAAEC,QAAQ,CAAC;EACjE;EACA,OAAOlW,MAAMA,CAACiW,KAAK,EAAEkK,aAAa,EAAEC,KAAK,EAAE;IACvC,OAAOd,WAAW,CAACC,QAAQ,CAACvf,MAAM,CAACiW,KAAK,EAAEkK,aAAa,EAAE1c,kBAAkB,CAAC2c,KAAK,CAAC,CAAC;EACvF;EACA;EACA,OAAOhV,GAAGA,CAAC6K,KAAK,EAAEkK,aAAa,GAAG9c,QAAQ,CAACgd,kBAAkB,EAAED,KAAK,GAAG1c,WAAW,CAAC4c,OAAO,EAAE;IACxF,OAAOhB,WAAW,CAACC,QAAQ,CAACvf,MAAM,CAACiW,KAAK,EAAEkK,aAAa,EAAEC,KAAK,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOnc,qBAAqBA,CAACQ,EAAE,EAAE;IAC7B,OAAO6a,WAAW,CAACC,QAAQ,CAACtb,qBAAqB,CAACQ,EAAE,CAAC;EACzD;EACA,OAAO8b,eAAeA,CAAChO,SAAS,EAAE;IAC9B,OAAO+M,WAAW,CAACC,QAAQ,CAACgB,eAAe,CAAChO,SAAS,CAAC;EAC1D;EACA,OAAOiO,kBAAkBA,CAAA,EAAG;IACxB,OAAOlB,WAAW,CAACC,QAAQ,CAACiB,kBAAkB,CAAC,CAAC;EACpD;EACA,OAAOC,OAAOA,CAACC,MAAM,EAAEjc,EAAE,EAAEkc,OAAO,EAAE;IAChC,OAAOrB,WAAW,CAACC,QAAQ,CAACkB,OAAO,CAACC,MAAM,EAAEjc,EAAE,EAAEkc,OAAO,CAAC;EAC5D;EACA,WAAWjO,QAAQA,CAAA,EAAG;IAClB,OAAO4M,WAAW,CAACC,QAAQ,CAAC7M,QAAQ;EACxC;EACA,WAAWwC,QAAQA,CAAA,EAAG;IAClB,OAAOoK,WAAW,CAACC,QAAQ,CAACrK,QAAQ;EACxC;EACA,OAAO0L,YAAYA,CAAA,EAAG;IAClB,OAAOtB,WAAW,CAACC,QAAQ,CAACqB,YAAY,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACId,mBAAmBA,CAAC5K,QAAQ,EAAExC,QAAQ,EAAEjF,OAAO,EAAE;IAC7C,IAAI,IAAI,CAACiF,QAAQ,IAAI,IAAI,CAACwC,QAAQ,EAAE;MAChC,MAAM,IAAIvP,KAAK,CAAC,8DAA8D,CAAC;IACnF;IACA2Z,WAAW,CAACuB,2BAA2B,GAAGpT,OAAO,EAAEqT,QAAQ;IAC3DxB,WAAW,CAACyB,wCAAwC,GAAGtT,OAAO,EAAEuT,sBAAsB;IACtF1B,WAAW,CAAC2B,0CAA0C,GAAGxT,OAAO,EAAEyT,wBAAwB;IAC1F,IAAI,CAACxO,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACwC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuK,SAAS,GAAG,IAAIhN,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACwC,QAAQ,CAAC;IAClE;IACA;IACA;IACA;IACAvR,oCAAoC,CAAC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIoc,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACS,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC/M,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACwC,QAAQ,GAAG,IAAI;IACpBoK,WAAW,CAACuB,2BAA2B,GAAG3V,SAAS;IACnDvH,oCAAoC,CAAC,KAAK,CAAC;EAC/C;EACA6c,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACW,8BAA8B,CAAC,CAAC;IACrCvd,wBAAwB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAAC6b,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAAC2B,QAAQ,CAACjF,oBAAoB,CAAC,CAAC;IACxC;IACA,IAAI,CAACsD,SAAS,GAAG,IAAIhN,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACwC,QAAQ,CAAC;IAClE;IACArR,4BAA4B,CAAC,IAAI,CAACwd,qCAAqC,IAAIra,iCAAiC,CAAC;IAC7G;IACAlD,6BAA6B,CAAC,IAAI,CAACwd,uCAAuC,IAAIra,mCAAmC,CAAC;IAClH;IACA;IACA;IACA,IAAI;MACA,IAAI,CAACsa,qBAAqB,CAAC,CAAC;IAChC,CAAC,SACO;MACJ,IAAI;QACA,IAAI,IAAI,CAACC,2BAA2B,CAAC,CAAC,EAAE;UACpC,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAChC;MACJ,CAAC,SACO;QACJ,IAAI,CAAC/B,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACgC,wBAAwB,GAAGxW,SAAS;QACzC,IAAI,CAACyW,qCAAqC,GAAGzW,SAAS;QACtD,IAAI,CAAC0W,uCAAuC,GAAG1W,SAAS;QACxD,IAAI,CAACsU,2BAA2B,GAAGtY,4BAA4B;MACnE;IACJ;IACA,OAAO,IAAI;EACf;EACA8Y,iBAAiBA,CAACC,MAAM,EAAE;IACtB,IAAIA,MAAM,CAAC4B,MAAM,IAAI,IAAI,EAAE;MACvB,MAAM,IAAIlc,KAAK,CAAC,oDAAoD,CAAC;IACzE;IACA,IAAIsa,MAAM,CAAClN,SAAS,KAAK7H,SAAS,EAAE;MAChC,IAAI,CAACkW,QAAQ,CAAC3M,oBAAoB,CAACwL,MAAM,CAAClN,SAAS,CAAC;IACxD;IACA,OAAO,IAAI;EACf;EACA2B,sBAAsBA,CAACC,SAAS,EAAE;IAC9B,IAAI,CAACmN,qBAAqB,CAAC,gCAAgC,EAAE,2BAA2B,CAAC;IACzF;IACA;IACA;IACA;IACA,IAAI,CAACX,8BAA8B,CAAC,CAAC;IACrC;IACA;IACA,IAAI,CAACO,wBAAwB,GAAG/M,SAAS,CAACmM,QAAQ;IAClD,IAAI,CAACa,qCAAqC,GAAGhN,SAAS,CAACqM,sBAAsB;IAC7E,IAAI,CAACY,uCAAuC,GAAGjN,SAAS,CAACuM,wBAAwB;IACjF,IAAI,CAAC1B,2BAA2B,GAAG7K,SAAS,CAACL,kBAAkB,IAAIpN,4BAA4B;IAC/F;IACA;IACA,IAAI,CAACma,qCAAqC,GAAGtd,4BAA4B,CAAC,CAAC;IAC3EF,4BAA4B,CAAC,IAAI,CAACke,iCAAiC,CAAC,CAAC,CAAC;IACtE,IAAI,CAACT,uCAAuC,GAAGtd,6BAA6B,CAAC,CAAC;IAC9EF,6BAA6B,CAAC,IAAI,CAACke,mCAAmC,CAAC,CAAC,CAAC;IACzE,IAAI,CAACZ,QAAQ,CAAC1M,sBAAsB,CAACC,SAAS,CAAC;IAC/C,OAAO,IAAI;EACf;EACAgD,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACyJ,QAAQ,CAACzJ,iBAAiB,CAAC,CAAC;EAC5C;EACA3X,MAAMA,CAACiW,KAAK,EAAEkK,aAAa,EAAEC,KAAK,EAAE;IAChC,IAAInK,KAAK,KAAKgM,OAAO,EAAE;MACnB,OAAO,IAAI;IACf;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,MAAM,GAAG,IAAI,CAAC9N,aAAa,CAAClJ,QAAQ,CAACC,GAAG,CAAC6K,KAAK,EAAEiM,SAAS,EAAEze,kBAAkB,CAAC2c,KAAK,CAAC,CAAC;IAC3F,OAAO+B,MAAM,KAAKD,SAAS,GACrB,IAAI,CAACd,QAAQ,CAACjW,QAAQ,CAACC,GAAG,CAAC6K,KAAK,EAAEkK,aAAa,EAAEC,KAAK,CAAC,GACvD+B,MAAM;EAChB;EACA;EACA/W,GAAGA,CAAC6K,KAAK,EAAEkK,aAAa,GAAG9c,QAAQ,CAACgd,kBAAkB,EAAED,KAAK,GAAG1c,WAAW,CAAC4c,OAAO,EAAE;IACjF,OAAO,IAAI,CAACtgB,MAAM,CAACiW,KAAK,EAAEkK,aAAa,EAAEC,KAAK,CAAC;EACnD;EACAnc,qBAAqBA,CAACQ,EAAE,EAAE;IACtB,OAAOR,qBAAqB,CAAC,IAAI,CAACjE,MAAM,CAACkE,mBAAmB,CAAC,EAAEO,EAAE,CAAC;EACtE;EACAgc,OAAOA,CAACC,MAAM,EAAEjc,EAAE,EAAEkc,OAAO,EAAE;IACzB,MAAMyB,MAAM,GAAG1B,MAAM,CAAC2B,GAAG,CAAEC,CAAC,IAAK,IAAI,CAACtiB,MAAM,CAACsiB,CAAC,CAAC,CAAC;IAChD,OAAO7d,EAAE,CAAC8d,KAAK,CAAC5B,OAAO,EAAEyB,MAAM,CAAC;EACpC;EACAnN,cAAcA,CAACC,QAAQ,EAAE3G,QAAQ,EAAE;IAC/B,IAAI,CAACuT,qBAAqB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC;IACxE,IAAI,CAACV,QAAQ,CAACnM,cAAc,CAACC,QAAQ,EAAE3G,QAAQ,CAAC;IAChD,OAAO,IAAI;EACf;EACAiH,iBAAiBA,CAACjD,SAAS,EAAEhE,QAAQ,EAAE;IACnC,IAAI,CAACuT,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACV,QAAQ,CAAC5L,iBAAiB,CAACjD,SAAS,EAAEhE,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAmI,kCAAkCA,CAACnE,SAAS,EAAEoE,QAAQ,EAAE;IACpD,IAAI,CAACmL,qBAAqB,CAAC,4CAA4C,EAAE,6EAA6E,CAAC;IACvJ,IAAI,CAACV,QAAQ,CAAC1K,kCAAkC,CAACnE,SAAS,EAAEoE,QAAQ,CAAC;IACrE,OAAO,IAAI;EACf;EACAhB,iBAAiBA,CAACC,SAAS,EAAErH,QAAQ,EAAE;IACnC,IAAI,CAACuT,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACV,QAAQ,CAACzL,iBAAiB,CAACC,SAAS,EAAErH,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAsH,YAAYA,CAACC,IAAI,EAAEvH,QAAQ,EAAE;IACzB,IAAI,CAACuT,qBAAqB,CAAC,cAAc,EAAE,wBAAwB,CAAC;IACpE,IAAI,CAACV,QAAQ,CAACvL,YAAY,CAACC,IAAI,EAAEvH,QAAQ,CAAC;IAC1C,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIyH,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC9B,IAAI,CAAC4L,qBAAqB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;IACnE,IAAI,CAACV,QAAQ,CAACpL,gBAAgB,CAACC,KAAK,EAAEC,QAAQ,CAAC;IAC/C,OAAO,IAAI;EACf;EACAgK,gBAAgBA,CAAC3N,SAAS,EAAEoE,QAAQ,EAAE;IAClC,OAAO,IAAI,CAACnB,iBAAiB,CAACjD,SAAS,EAAE;MAAE3D,GAAG,EAAE;QAAE+H,QAAQ;QAAE6L,WAAW,EAAE;MAAK;IAAE,CAAC,CAAC;EACtF;EACAjC,eAAeA,CAAC3X,IAAI,EAAE;IAClB,MAAM6Z,qBAAqB,GAAG,IAAI,CAACziB,MAAM,CAACoH,qBAAqB,CAAC;IAChE,MAAMsb,QAAQ,GAAG,OAAOtD,kBAAkB,EAAE,EAAE;IAC9CqD,qBAAqB,CAACpb,iBAAiB,CAACqb,QAAQ,CAAC;IACjD,IAAIxhB,wBAAwB,CAAC0H,IAAI,CAAC,EAAE;MAChC,MAAM,IAAIjD,KAAK,CAAC,cAAciD,IAAI,CAAC0M,IAAI,6BAA6B,GAChE,6EAA6E,CAAC;IACtF;IACA,MAAM+D,YAAY,GAAGzQ,IAAI,CAAC0Q,IAAI;IAC9B,IAAI,CAACD,YAAY,EAAE;MACf,MAAM,IAAI1T,KAAK,CAAC,kBAAkB/E,UAAU,CAACgI,IAAI,CAAC,0BAA0B,CAAC;IACjF;IACA,MAAM+Z,gBAAgB,GAAG,IAAI3gB,wBAAwB,CAACqX,YAAY,CAAC;IACnE,MAAMuJ,aAAa,GAAGA,CAAA,KAAM;MACxB,MAAMvZ,YAAY,GAAGsZ,gBAAgB,CAAC3F,MAAM,CAAC3Z,QAAQ,CAACwf,IAAI,EAAE,EAAE,EAAE,IAAIH,QAAQ,EAAE,EAAE,IAAI,CAACrO,aAAa,CAAC;MACnG,OAAO,IAAI,CAACpQ,qBAAqB,CAAC,MAAM;QACpC,MAAM6e,UAAU,GAAG,IAAI,CAAC9iB,MAAM,CAAC+C,iBAAiB,CAAC;QACjD,MAAMggB,OAAO,GAAGD,UAAU,GACpB,IAAItX,yBAAyB,CAACnC,YAAY,CAAC,GAC3C,IAAI4C,iCAAiC,CAAC5C,YAAY,CAAC;QACzD0Z,OAAO,CAACpX,UAAU,CAAC,CAAC;QACpB,OAAOoX,OAAO;MAClB,CAAC,CAAC;IACN,CAAC;IACD,MAAMC,QAAQ,GAAG,IAAI,CAAChjB,MAAM,CAACyH,wBAAwB,EAAE,KAAK,CAAC;IAC7D,MAAMsC,MAAM,GAAGiZ,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAChjB,MAAM,CAACE,MAAM,EAAE,IAAI,CAAC;IAC1D,MAAM6iB,OAAO,GAAGhZ,MAAM,GAAGA,MAAM,CAAC6C,GAAG,CAACgW,aAAa,CAAC,GAAGA,aAAa,CAAC,CAAC;IACpE,IAAI,CAACjD,eAAe,CAACnZ,IAAI,CAACuc,OAAO,CAAC;IAClC,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAI3B,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC3B,SAAS,KAAK,IAAI,EAAE;MACzB,MAAM,IAAI9Z,KAAK,CAAC,kDAAkD,CAAC;IACvE;IACA,OAAO,IAAI,CAAC8Z,SAAS;EACzB;EACA;AACJ;AACA;AACA;EACI,IAAIpL,aAAaA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACqL,cAAc,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC0B,QAAQ,CAAClJ,QAAQ,CAAC,CAAC;IAClD;IACA,OAAO,IAAI,CAACwH,cAAc;EAC9B;EACAoC,qBAAqBA,CAACmB,UAAU,EAAEC,iBAAiB,EAAE;IACjD,IAAI,IAAI,CAACxD,cAAc,KAAK,IAAI,EAAE;MAC9B,MAAM,IAAI/Z,KAAK,CAAC,UAAUud,iBAAiB,uDAAuD,GAC9F,mDAAmDD,UAAU,KAAK,CAAC;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9B,8BAA8BA,CAAA,EAAG;IAC7B;IACA;IACA,IAAI,CAAC,IAAI,CAACvB,wBAAwB,IAAI,IAAI,CAACF,cAAc,KAAK,IAAI,EAAE;MAChEvb,wCAAwC,CAAC,CAAC;IAC9C;IACA,IAAI,CAACyb,wBAAwB,GAAG,IAAI;EACxC;EACA2B,qBAAqBA,CAAA,EAAG;IACpB,IAAI4B,UAAU,GAAG,CAAC;IAClB,IAAI,CAACxD,eAAe,CAACjR,OAAO,CAAEqU,OAAO,IAAK;MACtC,IAAI;QACAA,OAAO,CAACxX,OAAO,CAAC,CAAC;MACrB,CAAC,CACD,OAAOtD,CAAC,EAAE;QACNkb,UAAU,EAAE;QACZC,OAAO,CAACzW,KAAK,CAAC,mCAAmC,EAAE;UAC/C4F,SAAS,EAAEwQ,OAAO,CAAC1Y,iBAAiB;UACpCgZ,UAAU,EAAEpb;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,IAAI,CAAC0X,eAAe,GAAG,EAAE;IACzB,IAAIwD,UAAU,GAAG,CAAC,IAAI,IAAI,CAACG,2BAA2B,CAAC,CAAC,EAAE;MACtD,MAAM3d,KAAK,CAAC,GAAGwd,UAAU,IAAIA,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,YAAY,GAAG,GACzE,6BAA6B,CAAC;IACtC;EACJ;EACAG,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,eAAe,GAAG,IAAI,CAAC7B,wBAAwB;IACrD,MAAM8B,kBAAkB,GAAGlE,WAAW,CAACuB,2BAA2B;IAClE;IACA,IAAI,CAAC0C,eAAe,IAAI,CAACC,kBAAkB,EAAE;MACzC,OAAOzc,0CAA0C;IACrD;IACA;IACA,OAAQwc,eAAe,EAAEE,aAAa,IAClCD,kBAAkB,EAAEC,aAAa,IACjC,IAAI,CAACjC,2BAA2B,CAAC,CAAC;EAC1C;EACAO,iCAAiCA,CAAA,EAAG;IAChC;IACA,OAAQ,IAAI,CAACJ,qCAAqC,IAC9CrC,WAAW,CAACyB,wCAAwC,IACpD/Z,iCAAiC;EACzC;EACAgb,mCAAmCA,CAAA,EAAG;IAClC;IACA,OAAQ,IAAI,CAACJ,uCAAuC,IAChDtC,WAAW,CAAC2B,0CAA0C,IACtDha,mCAAmC;EAC3C;EACAua,2BAA2BA,CAAA,EAAG;IAC1B,OAAQ,IAAI,CAACE,wBAAwB,EAAEgC,gBAAgB,IACnDpE,WAAW,CAACuB,2BAA2B,EAAE6C,gBAAgB,IACzD3c,0CAA0C;EAClD;EACA4c,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACnE,2BAA2B;EAC3C;EACAiC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAAC/B,cAAc,KAAK,IAAI,EAAE;MAC9B;IACJ;IACA;IACA;IACA,MAAMkE,YAAY,GAAG,IAAI,CAAC5jB,MAAM,CAACoH,qBAAqB,CAAC;IACvD,IAAI;MACA,IAAI,CAACsY,cAAc,CAACnU,OAAO,CAAC,CAAC;IACjC,CAAC,CACD,OAAOtD,CAAC,EAAE;MACN,IAAI,IAAI,CAACqb,2BAA2B,CAAC,CAAC,EAAE;QACpC,MAAMrb,CAAC;MACX,CAAC,MACI;QACDmb,OAAO,CAACzW,KAAK,CAAC,0CAA0C,EAAE;UACtD4F,SAAS,EAAE,IAAI,CAACmN,cAAc,CAACpV,QAAQ;UACvC+Y,UAAU,EAAEpb;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,SACO;MACJ2b,YAAY,CAACrc,qBAAqB,GAAG,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIqZ,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC5gB,MAAM,CAACM,gBAAgB,CAAC,CAACuL,KAAK,CAAC,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoW,OAAO,GAAG3C,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStf,MAAMA,CAAC0gB,MAAM,EAAEjc,EAAE,EAAE;EACxB,MAAM8Z,OAAO,GAAGe,WAAW,CAACC,QAAQ;EACpC;EACA,OAAO,YAAY;IACf,OAAOhB,OAAO,CAACkC,OAAO,CAACC,MAAM,EAAEjc,EAAE,EAAE,IAAI,CAAC;EAC5C,CAAC;AACL;AACA;AACA;AACA;AACA,MAAMof,kBAAkB,CAAC;EACrB5e,WAAWA,CAAC6e,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,UAAUA,CAAA,EAAG;IACT,MAAMpP,SAAS,GAAG,IAAI,CAACmP,UAAU,CAAC,CAAC;IACnC,IAAInP,SAAS,EAAE;MACX2K,WAAW,CAAC5K,sBAAsB,CAACC,SAAS,CAAC;IACjD;EACJ;EACA3U,MAAMA,CAAC0gB,MAAM,EAAEjc,EAAE,EAAE;IACf,MAAMuf,IAAI,GAAG,IAAI;IACjB;IACA,OAAO,YAAY;MACfA,IAAI,CAACD,UAAU,CAAC,CAAC;MACjB,OAAO/jB,MAAM,CAAC0gB,MAAM,EAAEjc,EAAE,CAAC,CAACwf,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC;EACL;AACJ;AACA,SAASC,UAAUA,CAACvP,SAAS,EAAElQ,EAAE,EAAE;EAC/B,IAAIA,EAAE,EAAE;IACJ;IACA,OAAO,YAAY;MACf,MAAM8Z,OAAO,GAAGe,WAAW,CAACC,QAAQ;MACpC,IAAI5K,SAAS,EAAE;QACX4J,OAAO,CAAC7J,sBAAsB,CAACC,SAAS,CAAC;MAC7C;MACA,OAAOlQ,EAAE,CAAC8d,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;EACL;EACA,OAAO,IAAIsB,kBAAkB,CAAC,MAAMlP,SAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAwP,UAAU,CAACC,UAAU,GAAGC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACAF,UAAU,CAACG,SAAS,GAAGD,cAAc,CAAC,IAAI,CAAC,CAAC;AAC5C,SAASA,cAAcA,CAACE,qBAAqB,EAAE;EAC3C,OAAO,MAAM;IACT,MAAMhG,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpC,IAAIhB,OAAO,CAACiD,2BAA2B,CAAC,CAAC,KAAK+C,qBAAqB,EAAE;MACjEhG,OAAO,CAACiC,kBAAkB,CAAC,CAAC;MAC5BjT,0BAA0B,CAAC,CAAC;IAChC;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiX,oCAAoC,GAAG,EAAE;;AAE/C;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASpb,gBAAgB,EAAE5B,0BAA0B,EAAEC,wBAAwB,EAAEzC,iBAAiB,EAAE6e,kBAAkB,EAAE5B,OAAO,EAAE7a,qBAAqB,EAAEod,oCAAoC,EAAE1W,oBAAoB,EAAEN,SAAS,EAAE3B,KAAK,EAAEkC,eAAe,EAAEsR,UAAU,EAAErf,MAAM,EAAEsN,kBAAkB,EAAExB,IAAI,EAAEtH,YAAY,EAAE0f,UAAU,EAAEjW,iBAAiB,IAAIwW,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}