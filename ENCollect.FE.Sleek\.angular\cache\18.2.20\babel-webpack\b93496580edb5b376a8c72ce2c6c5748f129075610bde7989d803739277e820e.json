{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { ImgUploadComponent } from './img-upload.component';\ndescribe('ImgUploadComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [ImgUploadComponent]\n    });\n    fixture = TestBed.createComponent(ImgUploadComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "ImgUploadComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\img-upload\\img-upload.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { ImgUploadComponent } from './img-upload.component';\r\n\r\ndescribe('ImgUploadComponent', () => {\r\n  let component: ImgUploadComponent;\r\n  let fixture: ComponentFixture<ImgUploadComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ImgUploadComponent]\r\n    });\r\n    fixture = TestBed.createComponent(ImgUploadComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3DC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,SAA6B;EACjC,IAAIC,OAA6C;EAEjDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,kBAAkB;KAClC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}