{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./upload-legal-allocation-batch.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./upload-legal-allocation-batch.component.css?ngResource\";\nexport class UploadControls {}\nimport { Component, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { ToastrService } from 'ngx-toastr';\nimport { JwtService } from 'src/app/authentication/jwt.service';\nimport { legalService } from '../legal.service';\nimport { ConfirmDialogComponent } from 'src/app/shared/components/confirm-dialog/confirm-dialog.component';\nlet UploadLegalAllocationBatchComponent = class UploadLegalAllocationBatchComponent {\n  constructor(jwtService, toastr, modalService, router, legalService) {\n    this.jwtService = jwtService;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.router = router;\n    this.legalService = legalService;\n    this.breadcrumbData = [{\n      label: \"Legal\",\n      path: \"/encollect/legal/upload-legal-allocation-batch\"\n    }, {\n      label: \"Upload Legal allocation batch\",\n      path: \"/encollect/legal/upload-legal-allocation-batch\"\n    }];\n    this.uploadControls = new UploadControls();\n    this.serverBusy = false;\n    this.dataToCsv = [];\n    this.uploadTemp = false;\n  }\n  ngOnInit() {\n    this.currentUserData = this.jwtService.getUser();\n    this.varInit();\n  }\n  varInit() {\n    this.results = [];\n    this.uploadControls = {\n      template: '',\n      allocationType: false,\n      fileName: ''\n    };\n    this.loader = {\n      isDownload: false,\n      isSubmit: false,\n      product: false,\n      subproduct: false\n    };\n  }\n  fileUploadConfirmation(event, confirmation) {\n    this.uploadTemp = true;\n    this.fileList = event.target.files;\n    var file_extension = this.fileList[0].name.split('.').pop();\n    this.uploadControls.fileName = this.fileList[0].name;\n    if (file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension != \"XLS\") {\n      this.fileList = [];\n      this.uploadTemp = false;\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.info('You can only upload the file with extension xls or xlsx');\n      return;\n    } else {\n      const modalRef = this.modalService.show(ConfirmDialogComponent, {\n        initialState: {\n          type: 'info',\n          message: `Are you sure you want to upload this file\n            ${this.uploadControls.fileName}`,\n          title: \"Confirm File Upload?\",\n          okBtn: \"Ok\",\n          closeBtn: \"Close\"\n        },\n        animated: true,\n        ignoreBackdropClick: true\n      });\n      modalRef.content.onClose.subscribe(res => {\n        if (res.isSuccess) {\n          this.fIleUpload();\n        }\n      });\n    }\n  }\n  fIleUpload() {\n    const file = this.fileList[0];\n    const fd = new FormData();\n    fd.append('file', file);\n    this.modalRef?.hide();\n    this.serverBusy = true;\n    this.legalService.uploadLegalFile(fd).subscribe(data => {\n      this.uploadTemp = false;\n      this.legalAllocation(data);\n    }, err => {\n      this.uploadTemp = false;\n      this.fileUploader.nativeElement.value = null;\n      this.serverBusy = false;\n      this.toastr.error(err);\n    });\n  }\n  legalAllocation(data) {\n    let inputParams = {\n      \"filename\": data[\"name\"]\n    };\n    this.legalService.legalAllocationUpload(inputParams).subscribe(data => {\n      this.fileUploader.nativeElement.value = null;\n      setTimeout(() => {\n        this.toastr.success(\"File Uploaded Successfully. Transaction ID : \" + data.transactionId);\n        this.fileList = [];\n        this.uploadControls = new UploadControls();\n        this.serverBusy = false;\n        this.router.navigateByUrl('/', {\n          skipLocationChange: true\n        }).then(() => this.router.navigate(['encollect/legal/upload-legal-allocation-batch']));\n      }, 3000);\n    }, err => {\n      this.serverBusy = false;\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.error(err);\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: JwtService\n    }, {\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: Router\n    }, {\n      type: legalService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      fileUploader: [{\n        type: ViewChild,\n        args: ['fileUploader']\n      }]\n    };\n  }\n};\nUploadLegalAllocationBatchComponent = __decorate([Component({\n  selector: 'app-upload-legal-allocation-batch',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], UploadLegalAllocationBatchComponent);\nexport { UploadLegalAllocationBatchComponent };", "map": {"version": 3, "names": ["UploadControls", "Component", "ViewChild", "Router", "BsModalService", "ToastrService", "JwtService", "legalService", "ConfirmDialogComponent", "UploadLegalAllocationBatchComponent", "constructor", "jwtService", "toastr", "modalService", "router", "breadcrumbData", "label", "path", "uploadControls", "serverBusy", "dataToCsv", "uploadTemp", "ngOnInit", "currentUserData", "getUser", "varInit", "results", "template", "allocationType", "fileName", "loader", "isDownload", "isSubmit", "product", "subproduct", "fileUploadConfirmation", "event", "confirmation", "fileList", "target", "files", "file_extension", "name", "split", "pop", "fileUploader", "nativeElement", "value", "info", "modalRef", "show", "initialState", "type", "message", "title", "okBtn", "closeBtn", "animated", "ignoreBackdropClick", "content", "onClose", "subscribe", "res", "isSuccess", "fIleUpload", "file", "fd", "FormData", "append", "hide", "uploadLegalFile", "data", "legalAllocation", "err", "error", "inputParams", "legalAllocationUpload", "setTimeout", "success", "transactionId", "navigateByUrl", "skipLocationChange", "then", "navigate", "args", "__decorate", "selector", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\upload-legal-allocation-batch\\upload-legal-allocation-batch.component.ts"], "sourcesContent": ["export class UploadControls{\r\n  template: string;\r\n  allocationType: boolean;\r\n  fileName: string;\r\n}\r\nexport interface Loader{\r\n   isDownload: boolean;\r\n   isSubmit: boolean;\r\n   product: boolean;\r\n   subproduct: boolean;\r\n}\r\nimport { Component, ElementRef, OnInit,ViewChild,TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { JwtService } from 'src/app/authentication/jwt.service';\r\nimport { legalService } from '../legal.service';\r\nimport { ConfirmDialogComponent } from 'src/app/shared/components/confirm-dialog/confirm-dialog.component';\r\n\r\n@Component({\r\n  selector: 'app-upload-legal-allocation-batch',\r\n  templateUrl: './upload-legal-allocation-batch.component.html',\r\n  styleUrls: ['./upload-legal-allocation-batch.component.css']\r\n})\r\nexport class UploadLegalAllocationBatchComponent implements OnInit {\r\n  public breadcrumbData = [\r\n    { label: \"Legal\", path: \"/encollect/legal/upload-legal-allocation-batch\" },\r\n    { label: \"Upload Legal allocation batch\", path: \"/encollect/legal/upload-legal-allocation-batch\" },\r\n  ];\r\n  @ViewChild('fileUploader') fileUploader:ElementRef;\r\n  uploadControls: UploadControls = new UploadControls();\r\n  loader: Loader;\r\n  currentUserData: any;\r\n  results: Array<object>;\r\n  fileList: any;\r\n  modalRef: BsModalRef;\r\n  attachedFile:any;\r\n  serverBusy = false\r\n  dataToCsv = [];\r\n  uploadTemp = false\r\n  constructor(private jwtService: JwtService,public toastr: ToastrService,\r\n    private modalService: BsModalService,private router: Router,private legalService: legalService) { }\r\n\r\n  ngOnInit() {\r\n    this.currentUserData =  this.jwtService.getUser()\r\n     this.varInit()\r\n  }\r\n  varInit(){\r\n    this.results = [];\r\n    this.uploadControls = {\r\n       template: '',\r\n       allocationType: false,\r\n       fileName: ''\r\n    }\r\n    this.loader = {\r\n      isDownload: false,\r\n      isSubmit: false,\r\n      product: false,\r\n      subproduct: false,\r\n    }\r\n }\r\n\r\n fileUploadConfirmation(event,confirmation: TemplateRef<any>) {\r\n      this.uploadTemp = true\r\n      this.fileList = event.target.files;\r\n      var file_extension =  this.fileList[0].name.split('.').pop();\r\n      this.uploadControls.fileName = this.fileList[0].name\r\n      if(file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension !=\"XLS\" ){\r\n        this.fileList=[]\r\n        this.uploadTemp = false\r\n        this.fileUploader.nativeElement.value = null;\r\n        this.toastr.info('You can only upload the file with extension xls or xlsx');\r\n        return;\r\n      }else {\r\n        const modalRef = this.modalService.show(ConfirmDialogComponent, {\r\n          initialState: {\r\n            type: 'info',\r\n            message: `Are you sure you want to upload this file\r\n            ${ this.uploadControls.fileName }`,\r\n            title: \"Confirm File Upload?\",\r\n            okBtn: \"Ok\",\r\n            closeBtn: \"Close\",\r\n          },\r\n          animated: true,\r\n          ignoreBackdropClick: true,\r\n        });\r\n\r\n        modalRef.content.onClose.subscribe((res) => {\r\n          if (res.isSuccess) {\r\n            this.fIleUpload();\r\n          }\r\n        });\r\n      }\r\n  }\r\n\r\n\r\n fIleUpload() {\r\n      const file: File = this.fileList[0];\r\n      const fd: FormData = new FormData();\r\n      fd.append('file', file);\r\n      this.modalRef?.hide()\r\n      this.serverBusy = true\r\n      this.legalService.uploadLegalFile(fd).subscribe(data => {\r\n        this.uploadTemp = false\r\n         this.legalAllocation(data);\r\n      }, err=>{\r\n        this.uploadTemp = false\r\n        this.fileUploader.nativeElement.value = null;\r\n        this.serverBusy = false\r\n        this.toastr.error(err)\r\n      });\r\n  }\r\n\r\n  legalAllocation(data){\r\n    let inputParams = {\r\n    \"filename\": data[\"name\"]\r\n  }\r\n  this.legalService.legalAllocationUpload(inputParams)\r\n    .subscribe(data => {\r\n      this.fileUploader.nativeElement.value = null;\r\n      setTimeout(()=>{\r\n            this.toastr.success(\"File Uploaded Successfully. Transaction ID : \"+ data.transactionId);\r\n            this.fileList = []\r\n            this.uploadControls = new UploadControls();\r\n            this.serverBusy = false\r\n             this.router.navigateByUrl('/', {skipLocationChange: true}).then(()=>\r\n             this.router.navigate(['encollect/legal/upload-legal-allocation-batch']));\r\n       }, 3000);\r\n   },err=>{\r\n    this.serverBusy = false\r\n    this.fileUploader.nativeElement.value = null;\r\n    this.toastr.error(err);\r\n   });\r\n }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;AAW3B,SAASC,SAAS,EAAqBC,SAAS,QAAoB,eAAe;AACnF,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAAqBC,cAAc,QAAQ,qBAAqB;AAChE,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,sBAAsB,QAAQ,mEAAmE;AAOnG,IAAMC,mCAAmC,GAAzC,MAAMA,mCAAmC;EAgB9CC,YAAoBC,UAAsB,EAAQC,MAAqB,EAC7DC,YAA4B,EAASC,MAAc,EAASP,YAA0B;IAD5E,KAAAI,UAAU,GAAVA,UAAU;IAAoB,KAAAC,MAAM,GAANA,MAAM;IAC9C,KAAAC,YAAY,GAAZA,YAAY;IAAyB,KAAAC,MAAM,GAANA,MAAM;IAAiB,KAAAP,YAAY,GAAZA,YAAY;IAhB3E,KAAAQ,cAAc,GAAG,CACtB;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAgD,CAAE,EAC1E;MAAED,KAAK,EAAE,+BAA+B;MAAEC,IAAI,EAAE;IAAgD,CAAE,CACnG;IAED,KAAAC,cAAc,GAAmB,IAAIlB,cAAc,EAAE;IAOrD,KAAAmB,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,UAAU,GAAG,KAAK;EAEkF;EAEpGC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAI,IAAI,CAACZ,UAAU,CAACa,OAAO,EAAE;IAChD,IAAI,CAACC,OAAO,EAAE;EACjB;EACAA,OAAOA,CAAA;IACL,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACR,cAAc,GAAG;MACnBS,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,KAAK;MACrBC,QAAQ,EAAE;KACZ;IACD,IAAI,CAACC,MAAM,GAAG;MACZC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;KACb;EACJ;EAEAC,sBAAsBA,CAACC,KAAK,EAACC,YAA8B;IACtD,IAAI,CAAChB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACiB,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK;IAClC,IAAIC,cAAc,GAAI,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAC5D,IAAI,CAAC1B,cAAc,CAACW,QAAQ,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI;IACpD,IAAGD,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,KAAK,IAAIA,cAAc,IAAG,KAAK,EAAE;MAC5G,IAAI,CAACH,QAAQ,GAAC,EAAE;MAChB,IAAI,CAACjB,UAAU,GAAG,KAAK;MACvB,IAAI,CAACwB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAACnC,MAAM,CAACoC,IAAI,CAAC,yDAAyD,CAAC;MAC3E;IACF,CAAC,MAAK;MACJ,MAAMC,QAAQ,GAAG,IAAI,CAACpC,YAAY,CAACqC,IAAI,CAAC1C,sBAAsB,EAAE;QAC9D2C,YAAY,EAAE;UACZC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE;cACN,IAAI,CAACnC,cAAc,CAACW,QAAS,EAAE;UAClCyB,KAAK,EAAE,sBAAsB;UAC7BC,KAAK,EAAE,IAAI;UACXC,QAAQ,EAAE;SACX;QACDC,QAAQ,EAAE,IAAI;QACdC,mBAAmB,EAAE;OACtB,CAAC;MAEFT,QAAQ,CAACU,OAAO,CAACC,OAAO,CAACC,SAAS,CAAEC,GAAG,IAAI;QACzC,IAAIA,GAAG,CAACC,SAAS,EAAE;UACjB,IAAI,CAACC,UAAU,EAAE;QACnB;MACF,CAAC,CAAC;IACJ;EACJ;EAGDA,UAAUA,CAAA;IACL,MAAMC,IAAI,GAAS,IAAI,CAAC3B,QAAQ,CAAC,CAAC,CAAC;IACnC,MAAM4B,EAAE,GAAa,IAAIC,QAAQ,EAAE;IACnCD,EAAE,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IACvB,IAAI,CAAChB,QAAQ,EAAEoB,IAAI,EAAE;IACrB,IAAI,CAAClD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACZ,YAAY,CAAC+D,eAAe,CAACJ,EAAE,CAAC,CAACL,SAAS,CAACU,IAAI,IAAG;MACrD,IAAI,CAAClD,UAAU,GAAG,KAAK;MACtB,IAAI,CAACmD,eAAe,CAACD,IAAI,CAAC;IAC7B,CAAC,EAAEE,GAAG,IAAE;MACN,IAAI,CAACpD,UAAU,GAAG,KAAK;MACvB,IAAI,CAACwB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAAC5B,UAAU,GAAG,KAAK;MACvB,IAAI,CAACP,MAAM,CAAC8D,KAAK,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC;EACN;EAEAD,eAAeA,CAACD,IAAI;IAClB,IAAII,WAAW,GAAG;MAClB,UAAU,EAAEJ,IAAI,CAAC,MAAM;KACxB;IACD,IAAI,CAAChE,YAAY,CAACqE,qBAAqB,CAACD,WAAW,CAAC,CACjDd,SAAS,CAACU,IAAI,IAAG;MAChB,IAAI,CAAC1B,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C8B,UAAU,CAAC,MAAI;QACT,IAAI,CAACjE,MAAM,CAACkE,OAAO,CAAC,+CAA+C,GAAEP,IAAI,CAACQ,aAAa,CAAC;QACxF,IAAI,CAACzC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACpB,cAAc,GAAG,IAAIlB,cAAc,EAAE;QAC1C,IAAI,CAACmB,UAAU,GAAG,KAAK;QACtB,IAAI,CAACL,MAAM,CAACkE,aAAa,CAAC,GAAG,EAAE;UAACC,kBAAkB,EAAE;QAAI,CAAC,CAAC,CAACC,IAAI,CAAC,MAChE,IAAI,CAACpE,MAAM,CAACqE,QAAQ,CAAC,CAAC,+CAA+C,CAAC,CAAC,CAAC;MAC9E,CAAC,EAAE,IAAI,CAAC;IACZ,CAAC,EAACV,GAAG,IAAE;MACN,IAAI,CAACtD,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC0B,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAACnC,MAAM,CAAC8D,KAAK,CAACD,GAAG,CAAC;IACvB,CAAC,CAAC;EACJ;;;;;;;;;;;;;;;;;cAxGEvE,SAAS;QAAAkF,IAAA,GAAC,cAAc;MAAA;;;;AALd3E,mCAAmC,GAAA4E,UAAA,EAL/CpF,SAAS,CAAC;EACTqF,QAAQ,EAAE,mCAAmC;EAC7C3D,QAAA,EAAA4D,oBAA6D;;CAE9D,CAAC,C,EACW9E,mCAAmC,CA+G/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}