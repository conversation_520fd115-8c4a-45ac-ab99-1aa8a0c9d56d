"use strict";(self.webpackChunkEntiger_Base=self.webpackChunkEntiger_Base||[]).push([[6811],{16811:(la,fn,R)=>{R.r(fn),R.d(fn,{CreateTemplateComponent:()=>Rg});var l=R(54438),w=R(89417),L=R(51188),Zn=R(50779),ei=R(45794),ti=R(75166);let me=(()=>{class i{constructor(){this.template="",this.selectVar=new l.bkB,this.change=new l.bkB,this.element=(0,l.WQX)(l.aKT),this.renderer=(0,l.WQX)(l.sFG)}ngOnChanges(t){t.template&&this.replaceTags()}replaceTags(){const t=(this.template||"")?.replace(/<<(.+?)>>/g,(n,r)=>`<span ${"Var"!==r?'class="assigned"':""}>${r}</span>`)?.replace(/\n/g,"<br>");this.element.nativeElement.innerHTML=t,this.element.nativeElement.querySelectorAll("span").forEach((n,r)=>{this.renderer.listen(n,"click",o=>{const s=n?.innerHTML;this.selectVar.emit({index:r,value:"Var"===s?null:s})})})}onUpdateVariable({index:t,value:n}){let r=-1;const o=this.template.replace(/<<(.+?)>>/g,(s,a,c)=>(r++,r===t?`<<${n}>>`:s));this.change.emit(o)}get hostClass(){return"form-control template-var-config"}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275dir=l.FsC({type:i,selectors:[["","appTemplateVarConfig",""]],hostVars:2,hostBindings:function(n,r){2&n&&l.HbH(r.hostClass)},inputs:{template:"template"},outputs:{selectVar:"selectVar",change:"change"},exportAs:["appTemplateVarConfig"],standalone:!0,features:[l.OA$]})}}return i})(),ni=(()=>{class i{constructor(){this.template="",this.variables=[],this.element=(0,l.WQX)(l.aKT),this.renderer=(0,l.WQX)(l.sFG)}ngOnChanges(t){(t.template||t.variables)&&this.generatePreview()}generatePreview(){let t=this.template||"";t=t.replace(/<<(.+?)>>/g,(n,r)=>{if("Var"===r)return'<span class="unmapped-var" title="This variable needs to be mapped to a database field">[Unmapped Variable]</span>';{const o=this.variables.find(s=>s.name===r);if(o){const s=o.code||o.id;return`<span class="mapped-var" title="Variable: ${o.name} | Response Code: ${s} | ID: ${o.id}">${s}</span>`}return`<span class="mapped-var" title="Variable: ${r} (mapping not found in current field list)">[${r}]</span>`}}),t=t.replace(/\n/g,"<br>"),this.element.nativeElement.innerHTML=t}get hostClass(){return"form-control form-control-readonly template-preview"}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275dir=l.FsC({type:i,selectors:[["","appTemplatePreview",""]],hostVars:2,hostBindings:function(n,r){2&n&&l.HbH(r.hostClass)},inputs:{template:"template",variables:"variables"},exportAs:["appTemplatePreview"],standalone:!0,features:[l.OA$]})}}return i})();var ge=R(63218),re=R(10467),xt=R(21413),aa=R(74402),kr=R(7673),ca=R(33726),da=R(43236),ua=R(56977),fa=R(23386);const ii=i=>{const e=(i=>{const e={};return Object.keys(i).forEach(t=>{i[t]&&"string"==typeof i[t]&&(e[t]=i[t])}),e})(i);return Object.entries(e).map(([t,n])=>`${(i=>i.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`))(t)}:${n}`).join(";")};class pn extends Error{constructor(e){super(e),this.name=this.constructor.name}}const vr=()=>`${Date.now().toString(36)}${Math.random().toString(36).substring(2,7)}`;function X(i){this.content=i}X.prototype={constructor:X,find:function(i){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===i)return e;return-1},get:function(i){var e=this.find(i);return-1==e?void 0:this.content[e+1]},update:function(i,e,t){var n=t&&t!=i?this.remove(t):this,r=n.find(i),o=n.content.slice();return-1==r?o.push(t||i,e):(o[r+1]=e,t&&(o[r]=t)),new X(o)},remove:function(i){var e=this.find(i);if(-1==e)return this;var t=this.content.slice();return t.splice(e,2),new X(t)},addToStart:function(i,e){return new X([i,e].concat(this.remove(i).content))},addToEnd:function(i,e){var t=this.remove(i).content.slice();return t.push(i,e),new X(t)},addBefore:function(i,e,t){var n=this.remove(e),r=n.content.slice(),o=n.find(i);return r.splice(-1==o?r.length:o,0,e,t),new X(r)},forEach:function(i){for(var e=0;e<this.content.length;e+=2)i(this.content[e],this.content[e+1])},prepend:function(i){return(i=X.from(i)).size?new X(i.content.concat(this.subtract(i).content)):this},append:function(i){return(i=X.from(i)).size?new X(this.subtract(i).content.concat(i.content)):this},subtract:function(i){var e=this;i=X.from(i);for(var t=0;t<i.content.length;t+=2)e=e.remove(i.content[t]);return e},toObject:function(){var i={};return this.forEach(function(e,t){i[e]=t}),i},get size(){return this.content.length>>1}},X.from=function(i){if(i instanceof X)return i;var e=[];if(i)for(var t in i)e.push(t,i[t]);return new X(e)};const Tr=X;function Mr(i,e,t){for(let n=0;;n++){if(n==i.childCount||n==e.childCount)return i.childCount==e.childCount?null:t;let r=i.child(n),o=e.child(n);if(r!=o){if(!r.sameMarkup(o))return t;if(r.isText&&r.text!=o.text){for(let s=0;r.text[s]==o.text[s];s++)t++;return t}if(r.content.size||o.content.size){let s=Mr(r.content,o.content,t+1);if(null!=s)return s}t+=r.nodeSize}else t+=r.nodeSize}}function Er(i,e,t,n){for(let r=i.childCount,o=e.childCount;;){if(0==r||0==o)return r==o?null:{a:t,b:n};let s=i.child(--r),a=e.child(--o),c=s.nodeSize;if(s!=a){if(!s.sameMarkup(a))return{a:t,b:n};if(s.isText&&s.text!=a.text){let d=0,u=Math.min(s.text.length,a.text.length);for(;d<u&&s.text[s.text.length-d-1]==a.text[a.text.length-d-1];)d++,t--,n--;return{a:t,b:n}}if(s.content.size||a.content.size){let d=Er(s.content,a.content,t-1,n-1);if(d)return d}t-=c,n-=c}else t-=c,n-=c}}class _{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,r=0,o){for(let s=0,a=0;a<t;s++){let c=this.content[s],d=a+c.nodeSize;if(d>e&&!1!==n(c,r+a,o||null,s)&&c.content.size){let u=a+1;c.nodesBetween(Math.max(0,e-u),Math.min(c.content.size,t-u),n,r+u)}a=d}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let o="",s=!0;return this.nodesBetween(e,t,(a,c)=>{let d=a.isText?a.text.slice(Math.max(e,c)-c,t-c):a.isLeaf?r?"function"==typeof r?r(a):r:a.type.spec.leafText?a.type.spec.leafText(a):"":"";a.isBlock&&(a.isLeaf&&d||a.isTextblock)&&n&&(s?s=!1:o+=n),o+=d},0),o}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),o=1);o<e.content.length;o++)r.push(e.content[o]);return new _(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let o=0,s=0;s<t;o++){let a=this.content[o],c=s+a.nodeSize;c>e&&((s<e||c>t)&&(a=a.isText?a.cut(Math.max(0,e-s),Math.min(a.text.length,t-s)):a.cut(Math.max(0,e-s-1),Math.min(a.content.size,t-s-1))),n.push(a),r+=a.nodeSize),s=c}return new _(n,r)}cutByIndex(e,t){return e==t?_.empty:0==e&&t==this.content.length?this:new _(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),o=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new _(r,o)}addToStart(e){return new _([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new _(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return Mr(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return Er(this,e,t,n)}findIndex(e,t=-1){if(0==e)return hn(0,e);if(e==this.size)return hn(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let s=r+this.child(n).nodeSize;if(s>=e)return s==e||t>0?hn(n+1,s):hn(n,r);r=s}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return _.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new _(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return _.empty;let t,n=0;for(let r=0;r<e.length;r++){let o=e[r];n+=o.nodeSize,r&&o.isText&&e[r-1].sameMarkup(o)?(t||(t=e.slice(0,r)),t[t.length-1]=o.withText(t[t.length-1].text+o.text)):t&&t.push(o)}return new _(t||e,n)}static from(e){if(!e)return _.empty;if(e instanceof _)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new _([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}_.empty=new _([],0);const ri={index:0,offset:0};function hn(i,e){return ri.index=i,ri.offset=e,ri}function mn(i,e){if(i===e)return!0;if(!i||"object"!=typeof i||!e||"object"!=typeof e)return!1;let t=Array.isArray(i);if(Array.isArray(e)!=t)return!1;if(t){if(i.length!=e.length)return!1;for(let n=0;n<i.length;n++)if(!mn(i[n],e[n]))return!1}else{for(let n in i)if(!(n in e)||!mn(i[n],e[n]))return!1;for(let n in e)if(!(n in i))return!1}return!0}let P=(()=>{class i{constructor(t,n){this.type=t,this.attrs=n}addToSet(t){let n,r=!1;for(let o=0;o<t.length;o++){let s=t[o];if(this.eq(s))return t;if(this.type.excludes(s.type))n||(n=t.slice(0,o));else{if(s.type.excludes(this.type))return t;!r&&s.type.rank>this.type.rank&&(n||(n=t.slice(0,o)),n.push(this),r=!0),n&&n.push(s)}}return n||(n=t.slice()),r||n.push(this),n}removeFromSet(t){for(let n=0;n<t.length;n++)if(this.eq(t[n]))return t.slice(0,n).concat(t.slice(n+1));return t}isInSet(t){for(let n=0;n<t.length;n++)if(this.eq(t[n]))return!0;return!1}eq(t){return this==t||this.type==t.type&&mn(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let n in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,n){if(!n)throw new RangeError("Invalid input for Mark.fromJSON");let r=t.marks[n.type];if(!r)throw new RangeError(`There is no mark type ${n.type} in this schema`);let o=r.create(n.attrs);return r.checkAttrs(o.attrs),o}static sameSet(t,n){if(t==n)return!0;if(t.length!=n.length)return!1;for(let r=0;r<t.length;r++)if(!t[r].eq(n[r]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return i.none;if(t instanceof i)return[t];let n=t.slice();return n.sort((r,o)=>r.type.rank-o.type.rank),n}}return i.none=[],i})();class gn extends Error{}class x{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=Nr(this.content,e+this.openStart,t);return n&&new x(n,this.openStart,this.openEnd)}removeBetween(e,t){return new x(Sr(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return x.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw new RangeError("Invalid input for Slice.fromJSON");return new x(_.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let o=e.firstChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.firstChild)n++;for(let o=e.lastChild;o&&!o.isLeaf&&(t||!o.type.spec.isolating);o=o.lastChild)r++;return new x(e,n,r)}}function Sr(i,e,t){let{index:n,offset:r}=i.findIndex(e),o=i.maybeChild(n),{index:s,offset:a}=i.findIndex(t);if(r==e||o.isText){if(a!=t&&!i.child(s).isText)throw new RangeError("Removing non-flat range");return i.cut(0,e).append(i.cut(t))}if(n!=s)throw new RangeError("Removing non-flat range");return i.replaceChild(n,o.copy(Sr(o.content,e-r-1,t-r-1)))}function Nr(i,e,t,n){let{index:r,offset:o}=i.findIndex(e),s=i.maybeChild(r);if(o==e||s.isText)return n&&!n.canReplace(r,r,t)?null:i.cut(0,e).append(t).append(i.cut(e));let a=Nr(s.content,e-o-1,t);return a&&i.replaceChild(r,s.copy(a))}function ya(i,e,t){if(t.openStart>i.depth)throw new gn("Inserted content deeper than insertion position");if(i.depth-t.openStart!=e.depth-t.openEnd)throw new gn("Inconsistent open depths");return Or(i,e,t,0)}function Or(i,e,t,n){let r=i.index(n),o=i.node(n);if(r==e.index(n)&&n<i.depth-t.openStart){let s=Or(i,e,t,n+1);return o.copy(o.content.replaceChild(r,s))}if(t.content.size){if(t.openStart||t.openEnd||i.depth!=n||e.depth!=n){let{start:s,end:a}=function ba(i,e){let t=e.depth-i.openStart,r=e.node(t).copy(i.content);for(let o=t-1;o>=0;o--)r=e.node(o).copy(_.from(r));return{start:r.resolveNoCache(i.openStart+t),end:r.resolveNoCache(r.content.size-i.openEnd-t)}}(t,i);return ot(o,Ir(i,s,a,e,n))}{let s=i.parent,a=s.content;return ot(s,a.cut(0,i.parentOffset).append(t.content).append(a.cut(e.parentOffset)))}}return ot(o,yn(i,e,n))}function Dr(i,e){if(!e.type.compatibleContent(i.type))throw new gn("Cannot join "+e.type.name+" onto "+i.type.name)}function oi(i,e,t){let n=i.node(t);return Dr(n,e.node(t)),n}function rt(i,e){let t=e.length-1;t>=0&&i.isText&&i.sameMarkup(e[t])?e[t]=i.withText(e[t].text+i.text):e.push(i)}function $t(i,e,t,n){let r=(e||i).node(t),o=0,s=e?e.index(t):r.childCount;i&&(o=i.index(t),i.depth>t?o++:i.textOffset&&(rt(i.nodeAfter,n),o++));for(let a=o;a<s;a++)rt(r.child(a),n);e&&e.depth==t&&e.textOffset&&rt(e.nodeBefore,n)}function ot(i,e){return i.type.checkContent(e),i.copy(e)}function Ir(i,e,t,n,r){let o=i.depth>r&&oi(i,e,r+1),s=n.depth>r&&oi(t,n,r+1),a=[];return $t(null,i,r,a),o&&s&&e.index(r)==t.index(r)?(Dr(o,s),rt(ot(o,Ir(i,e,t,n,r+1)),a)):(o&&rt(ot(o,yn(i,e,r+1)),a),$t(e,t,r,a),s&&rt(ot(s,yn(t,n,r+1)),a)),$t(n,null,r,a),new _(a)}function yn(i,e,t){let n=[];return $t(null,i,t,n),i.depth>t&&rt(ot(oi(i,e,t+1),yn(i,e,t+1)),n),$t(e,null,t,n),new _(n)}x.empty=new x(_.empty,0,0);class Ht{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let o=0;o<e;o++)r+=n.child(o).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return P.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let a=n;n=r,r=a}let o=n.marks;for(var s=0;s<o.length;s++)!1===o[s].type.spec.inclusive&&(!r||!o[s].isInSet(r.marks))&&(o=o[s--].removeFromSet(o));return o}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var o=0;o<n.length;o++)!1===n[o].type.spec.inclusive&&(!r||!n[o].isInSet(r.marks))&&(n=n[o--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new bn(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],r=0,o=t;for(let s=e;;){let{index:a,offset:c}=s.content.findIndex(o),d=o-c;if(n.push(s,a,r+c),!d||(s=s.child(a),s.isText))break;o=d-1,r+=c+1}return new Ht(t,n,o)}static resolveCached(e,t){let n=Ar.get(e);if(n)for(let o=0;o<n.elts.length;o++){let s=n.elts[o];if(s.pos==t)return s}else Ar.set(e,n=new _a);let r=n.elts[n.i]=Ht.resolve(e,t);return n.i=(n.i+1)%xa,r}}class _a{constructor(){this.elts=[],this.i=0}}const xa=12,Ar=new WeakMap;class bn{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const Ca=Object.create(null);class we{constructor(e,t,n,r=P.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||_.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&mn(this.attrs,t||e.defaultAttrs||Ca)&&P.sameSet(this.marks,n||P.none)}copy(e=null){return e==this.content?this:new we(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new we(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return x.empty;let r=this.resolve(e),o=this.resolve(t),s=n?0:r.sharedDepth(t),a=r.start(s),d=r.node(s).content.cut(r.pos-a,o.pos-a);return new x(d,r.depth-s,o.depth-s)}replace(e,t,n){return ya(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return Ht.resolveCached(this,e)}resolveNoCache(e){return Ht.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,o=>(n.isInSet(o.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),Rr(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=_.empty,r=0,o=n.childCount){let s=this.contentMatchAt(e).matchFragment(n,r,o),a=s&&s.matchFragment(this.content,t);if(!a||!a.validEnd)return!1;for(let c=r;c<o;c++)if(!this.type.allowsMarks(n.child(c).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let o=this.contentMatchAt(e).matchType(n),s=o&&o.matchFragment(this.content,t);return!!s&&s.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=P.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!P.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=_.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,r,n);return o.type.checkAttrs(o.attrs),o}}we.prototype.text=void 0;class _n extends we{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):Rr(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new _n(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new _n(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function Rr(i,e){for(let t=i.length-1;t>=0;t--)e=i[t].type.name+"("+e+")";return e}class st{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new wa(e,t);if(null==n.next)return st.empty;let r=Fr(n);n.next&&n.err("Unexpected trailing text");let o=function Na(i){let e=Object.create(null);return function t(n){let r=[];n.forEach(s=>{i[s].forEach(({term:a,to:c})=>{if(!a)return;let d;for(let u=0;u<r.length;u++)r[u][0]==a&&(d=r[u][1]);Vr(i,c).forEach(u=>{d||r.push([a,d=[]]),-1==d.indexOf(u)&&d.push(u)})})});let o=e[n.join(",")]=new st(n.indexOf(i.length-1)>-1);for(let s=0;s<r.length;s++){let a=r[s][1].sort(Lr);o.next.push({type:r[s][0],next:e[a.join(",")]||t(a)})}return o}(Vr(i,0))}(function Sa(i){let e=[[]];return r(function o(s,a){if("choice"==s.type)return s.exprs.reduce((c,d)=>c.concat(o(d,a)),[]);if("seq"!=s.type){if("star"==s.type){let c=t();return n(a,c),r(o(s.expr,c),c),[n(c)]}if("plus"==s.type){let c=t();return r(o(s.expr,a),c),r(o(s.expr,c),c),[n(c)]}if("opt"==s.type)return[n(a)].concat(o(s.expr,a));if("range"==s.type){let c=a;for(let d=0;d<s.min;d++){let u=t();r(o(s.expr,c),u),c=u}if(-1==s.max)r(o(s.expr,c),c);else for(let d=s.min;d<s.max;d++){let u=t();n(c,u),r(o(s.expr,c),u),c=u}return[n(c)]}if("name"==s.type)return[n(a,void 0,s.value)];throw new Error("Unknown expr type")}for(let c=0;;c++){let d=o(s.exprs[c],a);if(c==s.exprs.length-1)return d;r(d,a=t())}}(i,0),t()),e;function t(){return e.push([])-1}function n(s,a,c){let d={term:c,to:a};return e[s].push(d),d}function r(s,a){s.forEach(c=>c.to=a)}}(r));return function Oa(i,e){for(let t=0,n=[i];t<n.length;t++){let r=n[t],o=!r.validEnd,s=[];for(let a=0;a<r.next.length;a++){let{type:c,next:d}=r.next[a];s.push(c.name),o&&!(c.isText||c.hasRequiredAttrs())&&(o=!1),-1==n.indexOf(d)&&n.push(d)}o&&e.err("Only non-generatable nodes ("+s.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(o,n),o}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let o=t;r&&o<n;o++)r=r.matchType(e.child(o).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!t.isText&&!t.hasRequiredAttrs())return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function o(s,a){let c=s.matchFragment(e,n);if(c&&(!t||c.validEnd))return _.from(a.map(d=>d.createAndFill()));for(let d=0;d<s.next.length;d++){let{type:u,next:f}=s.next[d];if(!u.isText&&!u.hasRequiredAttrs()&&-1==r.indexOf(f)){r.push(f);let h=o(f,a.concat(u));if(h)return h}}return null}(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),o=r.match;if(o.matchType(e)){let s=[];for(let a=r;a.type;a=a.via)s.push(a.type);return s.reverse()}for(let s=0;s<o.next.length;s++){let{type:a,next:c}=o.next[s];!a.isLeaf&&!a.hasRequiredAttrs()&&!(a.name in t)&&(!r.type||c.validEnd)&&(n.push({match:a.contentMatch,type:a,via:r}),t[a.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((n,r)=>{let o=r+(n.validEnd?"*":" ")+" ";for(let s=0;s<n.next.length;s++)o+=(s?", ":"")+n.next[s].type.name+"->"+e.indexOf(n.next[s].next);return o}).join("\n")}}st.empty=new st(!0);class wa{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function Fr(i){let e=[];do{e.push(ka(i))}while(i.eat("|"));return 1==e.length?e[0]:{type:"choice",exprs:e}}function ka(i){let e=[];do{e.push(va(i))}while(i.next&&")"!=i.next&&"|"!=i.next);return 1==e.length?e[0]:{type:"seq",exprs:e}}function va(i){let e=function Ea(i){if(i.eat("(")){let e=Fr(i);return i.eat(")")||i.err("Missing closing paren"),e}if(!/\W/.test(i.next)){let e=function Ma(i,e){let t=i.nodeTypes,n=t[e];if(n)return[n];let r=[];for(let o in t){let s=t[o];s.isInGroup(e)&&r.push(s)}return 0==r.length&&i.err("No node type or group '"+e+"' found"),r}(i,i.next).map(t=>(null==i.inline?i.inline=t.isInline:i.inline!=t.isInline&&i.err("Mixing inline and block content"),{type:"name",value:t}));return i.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}i.err("Unexpected token '"+i.next+"'")}(i);for(;;)if(i.eat("+"))e={type:"plus",expr:e};else if(i.eat("*"))e={type:"star",expr:e};else if(i.eat("?"))e={type:"opt",expr:e};else{if(!i.eat("{"))break;e=Ta(i,e)}return e}function Pr(i){/\D/.test(i.next)&&i.err("Expected number, got '"+i.next+"'");let e=Number(i.next);return i.pos++,e}function Ta(i,e){let t=Pr(i),n=t;return i.eat(",")&&(n="}"!=i.next?Pr(i):-1),i.eat("}")||i.err("Unclosed braced range"),{type:"range",min:t,max:n,expr:e}}function Lr(i,e){return e-i}function Vr(i,e){let t=[];return function n(r){let o=i[r];if(1==o.length&&!o[0].term)return n(o[0].to);t.push(r);for(let s=0;s<o.length;s++){let{term:a,to:c}=o[s];!a&&-1==t.indexOf(c)&&n(c)}}(e),t.sort(Lr)}function Br(i){let e=Object.create(null);for(let t in i){let n=i[t];if(!n.hasDefault)return null;e[t]=n.default}return e}function zr(i,e){let t=Object.create(null);for(let n in i){let r=e&&e[n];if(void 0===r){let o=i[n];if(!o.hasDefault)throw new RangeError("No value supplied for attribute "+n);r=o.default}t[n]=r}return t}function $r(i,e,t,n){for(let r in e)if(!(r in i))throw new RangeError(`Unsupported attribute ${r} for ${t} of type ${r}`);for(let r in i){let o=i[r];o.validate&&o.validate(e[r])}}function Hr(i,e){let t=Object.create(null);if(e)for(let n in e)t[n]=new Ia(i,n,e[n]);return t}class xn{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=Hr(e,n.attrs),this.defaultAttrs=Br(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==st.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:zr(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new we(this,this.computeAttrs(e),_.from(t),P.setFrom(n))}createChecked(e=null,t,n){return t=_.from(t),this.checkContent(t),new we(this,this.computeAttrs(e),t,P.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=_.from(t)).size){let s=this.contentMatch.fillBefore(t);if(!s)return null;t=s.append(t)}let r=this.contentMatch.matchFragment(t),o=r&&r.fillBefore(_.empty,!0);return o?new we(this,e,t.append(o),P.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){$r(this.attrs,e,"node")}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(null==this.markSet)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:P.none:e}static compile(e,t){let n=Object.create(null);e.forEach((o,s)=>n[o]=new xn(o,t,s));let r=t.spec.topNode||"doc";if(!n[r])throw new RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let o in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}}class Ia{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function Da(i,e,t){let n=t.split("|");return r=>{let o=null===r?"null":typeof r;if(n.indexOf(o)<0)throw new RangeError(`Expected value of type ${n} for attribute ${e} on type ${i}, got ${o}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class Cn{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=Hr(e,r.attrs),this.excluded=null;let o=Br(this.attrs);this.instance=o?new P(this,o):null}create(e=null){return!e&&this.instance?this.instance:new P(this,zr(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((o,s)=>n[o]=new Cn(o,r++,t,s)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){$r(this.attrs,e,"mark")}excludes(e){return this.excluded.indexOf(e)>-1}}function jr(i,e){let t=[];for(let n=0;n<e.length;n++){let r=e[n],o=i.marks[r],s=o;if(o)t.push(o);else for(let a in i.marks){let c=i.marks[a];("_"==r||c.spec.group&&c.spec.group.split(" ").indexOf(r)>-1)&&t.push(s=c)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[n]+"'")}return t}class lt{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(r=>{if(function Ra(i){return null!=i.tag}(r))this.tags.push(r);else if(function Fa(i){return null!=i.style}(r)){let o=/[^=]*/.exec(r.style)[0];n.indexOf(o)<0&&n.push(o),this.styles.push(r)}}),this.normalizeLists=!this.tags.some(r=>{if(!/^(ul|ol)\b/.test(r.tag)||!r.node)return!1;let o=e.nodes[r.node];return o.contentMatch.matchType(o)})}parse(e,t={}){let n=new Ur(this,t,!1);return n.addAll(e,P.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new Ur(this,t,!0);return n.addAll(e,P.none,t.from,t.to),x.maxOpen(n.finish())}matchTag(e,t,n){for(let r=n?this.tags.indexOf(n)+1:0;r<this.tags.length;r++){let o=this.tags[r];if(Va(e,o.tag)&&(void 0===o.namespace||e.namespaceURI==o.namespace)&&(!o.context||t.matchesContext(o.context))){if(o.getAttrs){let s=o.getAttrs(e);if(!1===s)continue;o.attrs=s||void 0}return o}}}matchStyle(e,t,n,r){for(let o=r?this.styles.indexOf(r)+1:0;o<this.styles.length;o++){let s=this.styles[o],a=s.style;if(!(0!=a.indexOf(e)||s.context&&!n.matchesContext(s.context)||a.length>e.length&&(61!=a.charCodeAt(e.length)||a.slice(e.length+1)!=t))){if(s.getAttrs){let c=s.getAttrs(t);if(!1===c)continue;s.attrs=c||void 0}return s}}}static schemaRules(e){let t=[];function n(r){let o=null==r.priority?50:r.priority,s=0;for(;s<t.length;s++){let a=t[s];if((null==a.priority?50:a.priority)<o)break}t.splice(s,0,r)}for(let r in e.marks){let o=e.marks[r].spec.parseDOM;o&&o.forEach(s=>{n(s=Jr(s)),s.mark||s.ignore||s.clearMark||(s.mark=r)})}for(let r in e.nodes){let o=e.nodes[r].spec.parseDOM;o&&o.forEach(s=>{n(s=Jr(s)),s.node||s.ignore||s.mark||(s.node=r)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new lt(e,lt.schemaRules(e)))}}const Gr={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Pa={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Wr={ol:!0,ul:!0};function Xr(i,e,t){return null!=e?(e?1:0)|("full"===e?2:0):i&&"pre"==i.whitespace?3:-5&t}class wn{constructor(e,t,n,r,o,s){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=s,this.content=[],this.activeMarks=P.none,this.match=o||(4&s?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(_.from(e));if(!t){let r,n=this.type.contentMatch;return(r=n.findWrapping(e.type))?(this.match=n,r):null}this.match=this.type.contentMatch.matchFragment(t)}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let r,n=this.content[this.content.length-1];if(n&&n.isText&&(r=/[ \t\r\n\u000c]+$/.exec(n.text))){let o=n;n.text.length==r[0].length?this.content.pop():this.content[this.content.length-1]=o.withText(o.text.slice(0,o.text.length-r[0].length))}}let t=_.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(_.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Gr.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class Ur{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let o,r=t.topNode,s=Xr(null,t.preserveWhitespace,0)|(n?4:0);o=r?new wn(r.type,r.attrs,P.none,!0,t.topMatch||r.type.contentMatch,s):new wn(n?null:e.schema.topNodeType,null,P.none,!0,null,s),this.nodes=[o],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,o=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===o||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(o)n="full"!==o?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let s=r.content[r.content.length-1],a=e.previousSibling;(!s||a&&"BR"==a.nodeName||s.isText&&/[ \t\r\n\u000c]$/.test(s.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,o=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let a,s=e.nodeName.toLowerCase();Wr.hasOwnProperty(s)&&this.parser.normalizeLists&&function La(i){for(let e=i.firstChild,t=null;e;e=e.nextSibling){let n=1==e.nodeType?e.nodeName.toLowerCase():null;n&&Wr.hasOwnProperty(n)&&t?(t.appendChild(e),e=t):"li"==n?t=e:n&&(t=null)}}(e);let c=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(a=this.parser.matchTag(e,this,n));e:if(c?c.ignore:Pa.hasOwnProperty(s))this.findInside(e),this.ignoreFallback(e,t);else if(!c||c.skip||c.closeParent){c&&c.closeParent?this.open=Math.max(0,this.open-1):c&&c.skip.nodeType&&(e=c.skip);let d,u=this.needsBlock;if(Gr.hasOwnProperty(s))o.content.length&&o.content[0].isInline&&this.open&&(this.open--,o=this.top),d=!0,o.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let f=c&&c.skip?t:this.readStyles(e,t);f&&this.addAll(e,f),d&&this.sync(o),this.needsBlock=u}else{let d=this.readStyles(e,t);d&&this.addElementByRule(e,c,d,!1===c.consuming?a:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"==e.nodeName&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let r=0;r<this.parser.matchedStyles.length;r++){let o=this.parser.matchedStyles[r],s=n.getPropertyValue(o);if(s)for(let a;;){let c=this.parser.matchStyle(o,s,this,a);if(!c)break;if(c.ignore)return null;if(t=c.clearMark?t.filter(d=>!c.clearMark(d)):t.concat(this.parser.schema.marks[c.mark].create(c.attrs)),!1!==c.consuming)break;a=c}}return t}addElementByRule(e,t,n,r){let o,s;if(t.node)if(s=this.parser.schema.nodes[t.node],s.isLeaf)this.insertNode(s.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let c=this.enter(s,t.attrs||null,n,t.preserveWhitespace);c&&(o=!0,n=c)}else n=n.concat(this.parser.schema.marks[t.mark].create(t.attrs));let a=this.top;if(s&&s.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(c=>this.insertNode(c,n,!1));else{let c=e;"string"==typeof t.contentElement?c=e.querySelector(t.contentElement):"function"==typeof t.contentElement?c=t.contentElement(e):t.contentElement&&(c=t.contentElement),this.findAround(e,c,!0),this.addAll(c,n),this.findAround(e,c,!1)}o&&this.sync(a)&&this.open--}addAll(e,t,n,r){let o=n||0;for(let s=n?e.childNodes[n]:e.firstChild,a=null==r?null:e.childNodes[r];s!=a;s=s.nextSibling,++o)this.findAtPoint(e,o),this.addDOM(s,t);this.findAtPoint(e,o)}findPlace(e,t,n){let r,o;for(let s=this.open,a=0;s>=0;s--){let c=this.nodes[s],d=c.findWrapping(e);if(d&&(!r||r.length>d.length+a)&&(r=d,o=c,!d.length))break;if(c.solid){if(n)break;a+=2}}if(!r)return null;this.sync(o);for(let s=0;s<r.length;s++)t=this.enterInner(r[s],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let o=this.textblockFromContext();o&&(t=this.enterInner(o,null,t))}let r=this.findPlace(e,t,n);if(r){this.closeExtra();let o=this.top;o.match&&(o.match=o.match.matchType(e.type));let s=P.none;for(let a of r.concat(e.marks))(o.type?o.type.allowsMarkType(a.type):Kr(a.type,e.type))&&(s=a.addToSet(s));return o.content.push(e.mark(s)),!0}return!1}enter(e,t,n,r){let o=this.findPlace(e.create(t),n,!1);return o&&(o=this.enterInner(e,t,n,!0,r)),o}enterInner(e,t,n,r=!1,o){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(e);let a=Xr(e,o,s.options);4&s.options&&0==s.content.length&&(a|=4);let c=P.none;return n=n.filter(d=>!(s.type?s.type.allowsMarkType(d.type):Kr(d.type,e))||(c=d.addToSet(c),!1)),this.nodes.push(new wn(e,t,c,r,null,a)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!(!this.isOpen&&!this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=1)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let r=n.length-1;r>=0;r--)e+=n[r].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),o=(r?0:1)-(n?n.depth+1:0),s=(a,c)=>{for(;a>=0;a--){let d=t[a];if(""==d){if(a==t.length-1||0==a)continue;for(;c>=o;c--)if(s(a-1,c))return!0;return!1}{let u=c>0||0==c&&r?this.nodes[c].type:n&&c>=o?n.node(c-o).type:null;if(!u||u.name!=d&&!u.isInGroup(d))return!1;c--}}return!0};return s(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let n=this.parser.schema.nodes[t];if(n.isTextblock&&n.defaultAttrs)return n}}}function Va(i,e){return(i.matches||i.msMatchesSelector||i.webkitMatchesSelector||i.mozMatchesSelector).call(i,e)}function Jr(i){let e={};for(let t in i)e[t]=i[t];return e}function Kr(i,e){let t=e.schema.nodes;for(let n in t){let r=t[n];if(!r.allowsMarkType(i))continue;let o=[],s=a=>{o.push(a);for(let c=0;c<a.edgeCount;c++){let{type:d,next:u}=a.edge(c);if(d==e||o.indexOf(u)<0&&s(u))return!0}};if(s(r.contentMatch))return!0}}class at{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=li(t).createDocumentFragment());let r=n,o=[];return e.forEach(s=>{if(o.length||s.marks.length){let a=0,c=0;for(;a<o.length&&c<s.marks.length;){let d=s.marks[c];if(this.marks[d.type.name]){if(!d.eq(o[a][0])||!1===d.type.spec.spanning)break;a++,c++}else c++}for(;a<o.length;)r=o.pop()[1];for(;c<s.marks.length;){let d=s.marks[c++],u=this.serializeMark(d,s.isInline,t);u&&(o.push([d,r]),r.appendChild(u.dom),r=u.contentDOM||u.dom)}}r.appendChild(this.serializeNodeInner(s,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=kn(li(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let o=this.serializeMark(e.marks[r],e.isInline,t);o&&((o.contentDOM||o.dom).appendChild(n),n=o.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&kn(li(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return kn(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new at(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Yr(e.nodes);return t.text||(t.text=n=>n.text),t}static marksFromSchema(e){return Yr(e.marks)}}function Yr(i){let e={};for(let t in i){let n=i[t].spec.toDOM;n&&(e[t]=n)}return e}function li(i){return i.document||window.document}const qr=new WeakMap;function kn(i,e,t,n){if("string"==typeof e)return{dom:i.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let o,r=e[0];if("string"!=typeof r)throw new RangeError("Invalid array passed to renderSpec");if(n&&(o=function Ba(i){let e=qr.get(i);return void 0===e&&qr.set(i,e=function za(i){let e=null;return function t(n){if(n&&"object"==typeof n)if(Array.isArray(n))if("string"==typeof n[0])e||(e=[]),e.push(n);else for(let r=0;r<n.length;r++)t(n[r]);else for(let r in n)t(n[r])}(i),e}(i)),e}(n))&&o.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s=r.indexOf(" ");s>0&&(t=r.slice(0,s),r=r.slice(s+1));let a,c=t?i.createElementNS(t,r):i.createElement(r),d=e[1],u=1;if(d&&"object"==typeof d&&null==d.nodeType&&!Array.isArray(d)){u=2;for(let f in d)if(null!=d[f]){let h=f.indexOf(" ");h>0?c.setAttributeNS(f.slice(0,h),f.slice(h+1),d[f]):c.setAttribute(f,d[f])}}for(let f=u;f<e.length;f++){let h=e[f];if(0===h){if(f<e.length-1||f>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:c,contentDOM:c}}{let{dom:p,contentDOM:m}=kn(i,h,t,n);if(c.appendChild(p),m){if(a)throw new RangeError("Multiple content holes");a=m}}}return{dom:c,contentDOM:a}}const Zr=Math.pow(2,16);function $a(i,e){return i+e*Zr}function eo(i){return 65535&i}class ai{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class oe{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&oe.empty)return oe.empty}recover(e){let t=0,n=eo(e);if(!this.inverted)for(let r=0;r<n;r++)t+=this.ranges[3*r+2]-this.ranges[3*r+1];return this.ranges[3*n]+t+function Ha(i){return(i-(65535&i))/Zr}(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let a=0;a<this.ranges.length;a+=3){let c=this.ranges[a]-(this.inverted?r:0);if(c>e)break;let d=this.ranges[a+o],u=this.ranges[a+s],f=c+d;if(e<=f){let p=c+r+((d?e==c?-1:e==f?1:t:t)<0?0:u);if(n)return p;let m=e==(t<0?c:f)?null:$a(a/3,e-c),g=e==c?2:e==f?1:4;return(t<0?e!=c:e!=f)&&(g|=8),new ai(p,g,m)}r+=u-d}return n?e+r:new ai(e+r,0,null)}touches(e,t){let n=0,r=eo(t),o=this.inverted?2:1,s=this.inverted?1:2;for(let a=0;a<this.ranges.length;a+=3){let c=this.ranges[a]-(this.inverted?n:0);if(c>e)break;let d=this.ranges[a+o];if(e<=c+d&&a==3*r)return!0;n+=this.ranges[a+s]-d}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,o=0;r<this.ranges.length;r+=3){let s=this.ranges[r],a=s-(this.inverted?o:0),c=s+(this.inverted?0:o),d=this.ranges[r+t],u=this.ranges[r+n];e(a,a+d,c,c+u),o+=u-d}}invert(){return new oe(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?oe.empty:new oe(e<0?[0,-e,0]:[0,0,e])}}oe.empty=new oe([]);class Wt{constructor(e,t,n=0,r=(e?e.length:0)){this.mirror=t,this.from=n,this.to=r,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new Wt(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let r=e.getMirror(t);this.appendMap(e._maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror)for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new Wt;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let o=this.from;o<this.to;o++){let a=this._maps[o].mapResult(e,t);if(null!=a.recover){let c=this.getMirror(o);if(null!=c&&c>o&&c<this.to){o=c,e=this._maps[c].recover(a.recover);continue}}r|=a.delInfo,e=a.pos}return n?e:new ai(e,r,null)}}const ci=Object.create(null);class U{getMap(){return oe.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=ci[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in ci)throw new RangeError("Duplicate use of step JSON ID "+e);return ci[e]=t,t.prototype.jsonID=e,t}}class V{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new V(e,null)}static fail(e){return new V(null,e)}static fromReplace(e,t,n,r){try{return V.ok(e.replace(t,n,r))}catch(o){if(o instanceof gn)return V.fail(o.message);throw o}}}function di(i,e,t){let n=[];for(let r=0;r<i.childCount;r++){let o=i.child(r);o.content.size&&(o=o.copy(di(o.content,e,o))),o.isInline&&(o=e(o,t,r)),n.push(o)}return _.fromArray(n)}class ze extends U{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),r=n.node(n.sharedDepth(this.to)),o=new x(di(t.content,(s,a)=>s.isAtom&&a.type.allowsMarkType(this.mark.type)?s.mark(this.mark.addToSet(s.marks)):s,r),t.openStart,t.openEnd);return V.fromReplace(e,this.from,this.to,o)}invert(){return new ke(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new ze(t.pos,n.pos,this.mark)}merge(e){return e instanceof ze&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new ze(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new ze(t.from,t.to,e.markFromJSON(t.mark))}}U.jsonID("addMark",ze);class ke extends U{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new x(di(t.content,r=>r.mark(this.mark.removeFromSet(r.marks)),e),t.openStart,t.openEnd);return V.fromReplace(e,this.from,this.to,n)}invert(){return new ze(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new ke(t.pos,n.pos,this.mark)}merge(e){return e instanceof ke&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new ke(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new ke(t.from,t.to,e.markFromJSON(t.mark))}}U.jsonID("removeMark",ke);class $e extends U{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return V.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return V.fromReplace(e,this.pos,this.pos+1,new x(_.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let n=this.mark.addToSet(t.marks);if(n.length==t.marks.length){for(let r=0;r<t.marks.length;r++)if(!t.marks[r].isInSet(n))return new $e(this.pos,t.marks[r]);return new $e(this.pos,this.mark)}}return new ct(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new $e(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new $e(t.pos,e.markFromJSON(t.mark))}}U.jsonID("addNodeMark",$e);class ct extends U{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return V.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return V.fromReplace(e,this.pos,this.pos+1,new x(_.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new $e(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new ct(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new ct(t.pos,e.markFromJSON(t.mark))}}U.jsonID("removeNodeMark",ct);class J extends U{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&ui(e,this.from,this.to)?V.fail("Structure replace would overwrite content"):V.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new oe([this.from,this.to-this.from,this.slice.size])}invert(e){return new J(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new J(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof J)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?x.empty:new x(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new J(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?x.empty:new x(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new J(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new J(t.from,t.to,x.fromJSON(e,t.slice),!!t.structure)}}U.jsonID("replace",J);class H extends U{constructor(e,t,n,r,o,s,a=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=o,this.insert=s,this.structure=a}apply(e){if(this.structure&&(ui(e,this.from,this.gapFrom)||ui(e,this.gapTo,this.to)))return V.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return V.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?V.fromReplace(e,this.from,this.to,n):V.fail("Content does not fit in gap")}getMap(){return new oe([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new H(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),o=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||o>n.pos?null:new H(t.pos,n.pos,r,o,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new H(t.from,t.to,t.gapFrom,t.gapTo,x.fromJSON(e,t.slice),t.insert,!!t.structure)}}function ui(i,e,t){let n=i.resolve(e),r=t-e,o=n.depth;for(;r>0&&o>0&&n.indexAfter(o)==n.node(o).childCount;)o--,r--;if(r>0){let s=n.node(o).maybeChild(n.indexAfter(o));for(;r>0;){if(!s||s.isLeaf)return!0;s=s.firstChild,r--}}return!1}function fi(i,e,t,n=t.contentMatch,r=!0){let o=i.doc.nodeAt(e),s=[],a=e+1;for(let c=0;c<o.childCount;c++){let d=o.child(c),u=a+d.nodeSize,f=n.matchType(d.type);if(f){n=f;for(let h=0;h<d.marks.length;h++)t.allowsMarkType(d.marks[h].type)||i.step(new ke(a,u,d.marks[h]));if(r&&d.isText&&"pre"!=t.whitespace){let h,m,p=/\r?\n|\r/g;for(;h=p.exec(d.text);)m||(m=new x(_.from(t.schema.text(" ",t.allowedMarks(d.marks))),0,0)),s.push(new J(a+h.index,a+h.index+h[0].length,m))}}else s.push(new J(a,u,x.empty));a=u}if(!n.validEnd){let c=n.fillBefore(_.empty,!0);i.replace(a,a,new x(c,0,0))}for(let c=s.length-1;c>=0;c--)i.step(s[c])}function Wa(i,e,t){return(0==e||i.canReplace(e,i.childCount))&&(t==i.childCount||i.canReplace(0,t))}function Xt(i){let t=i.parent.content.cutByIndex(i.startIndex,i.endIndex);for(let n=i.depth;;--n){let r=i.$from.node(n),o=i.$from.index(n),s=i.$to.indexAfter(n);if(n<i.depth&&r.canReplace(o,s,t))return n;if(0==n||r.type.spec.isolating||!Wa(r,o,s))break}return null}function pi(i,e,t=null,n=i){let r=function Ua(i,e){let{parent:t,startIndex:n,endIndex:r}=i,o=t.contentMatchAt(n).findWrapping(e);return o&&t.canReplaceWith(n,r,o.length?o[0]:e)?o:null}(i,e),o=r&&function Ja(i,e){let{parent:t,startIndex:n,endIndex:r}=i,o=t.child(n),s=e.contentMatch.findWrapping(o.type);if(!s)return null;let c=(s.length?s[s.length-1]:e).contentMatch;for(let d=n;c&&d<r;d++)c=c.matchType(t.child(d).type);return c&&c.validEnd?s:null}(n,e);return o?r.map(ro).concat({type:e,attrs:t}).concat(o.map(ro)):null}function ro(i){return{type:i,attrs:null}}function oo(i,e,t,n){e.forEach((r,o)=>{if(r.isText){let s,a=/\r?\n|\r/g;for(;s=a.exec(r.text);){let c=i.mapping.slice(n).map(t+1+o+s.index);i.replaceWith(c,c+1,e.type.schema.linebreakReplacement.create())}}})}function so(i,e,t,n){e.forEach((r,o)=>{if(r.type==r.type.schema.linebreakReplacement){let s=i.mapping.slice(n).map(t+1+o);i.replaceWith(s,s+1,e.type.schema.text("\n"))}})}function Ct(i,e,t=1,n){let r=i.resolve(e),o=r.depth-t,s=n&&n[n.length-1]||r.parent;if(o<0||r.parent.type.spec.isolating||!r.parent.canReplace(r.index(),r.parent.childCount)||!s.type.validContent(r.parent.content.cutByIndex(r.index(),r.parent.childCount)))return!1;for(let d=r.depth-1,u=t-2;d>o;d--,u--){let f=r.node(d),h=r.index(d);if(f.type.spec.isolating)return!1;let p=f.content.cutByIndex(h,f.childCount),m=n&&n[u+1];m&&(p=p.replaceChild(0,m.type.create(m.attrs)));let g=n&&n[u]||f;if(!f.canReplace(h+1,f.childCount)||!g.type.validContent(p))return!1}let a=r.indexAfter(o),c=n&&n[0];return r.node(o).canReplaceWith(a,a,c?c.type:r.node(o+1).type)}function Tn(i,e){let t=i.resolve(e),n=t.index();return function lo(i,e){return!(!i||!e||i.isLeaf||!function ec(i,e){e.content.size||i.type.compatibleContent(e.type);let t=i.contentMatchAt(i.childCount),{linebreakReplacement:n}=i.type.schema;for(let r=0;r<e.childCount;r++){let o=e.child(r);if(t=t.matchType(o.type==n?i.type.schema.nodes.text:o.type),!t||!i.type.allowsMarks(o.marks))return!1}return t.validEnd}(i,e))}(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(n,n+1)}function hi(i,e,t=e,n=x.empty){if(e==t&&!n.size)return null;let r=i.resolve(e),o=i.resolve(t);return ao(r,o,n)?new J(e,t,n):new rc(r,o,n).fit()}function ao(i,e,t){return!t.openStart&&!t.openEnd&&i.start()==e.start()&&i.parent.canReplace(i.index(),e.index(),t.content)}U.jsonID("replaceAround",H);class rc{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=_.empty;for(let r=0;r<=e.depth;r++){let o=e.node(r);this.frontier.push({type:o.type,match:o.contentMatchAt(e.indexAfter(r))})}for(let r=e.depth;r>0;r--)this.placed=_.from(e.node(r).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let d=this.findFittable();d?this.placeNodes(d):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(e<0?this.$to:n.doc.resolve(e));if(!r)return null;let o=this.placed,s=n.depth,a=r.depth;for(;s&&a&&1==o.childCount;)o=o.firstChild.content,s--,a--;let c=new x(o,s,a);return e>-1?new H(n.pos,e,this.$to.pos,this.$to.end(),c,t):c.size||n.pos!=this.$to.pos?new J(n.pos,r.pos,c):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let o=t.firstChild;if(t.childCount>1&&(r=0),o.type.spec.isolating&&r<=n){e=n;break}t=o.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let r,o=null;n?(o=mi(this.unplaced.content,n-1).firstChild,r=o.content):r=this.unplaced.content;let s=r.firstChild;for(let a=this.depth;a>=0;a--){let u,{type:c,match:d}=this.frontier[a],f=null;if(1==t&&(s?d.matchType(s.type)||(f=d.fillBefore(_.from(s),!1)):o&&c.compatibleContent(o.type)))return{sliceDepth:n,frontierDepth:a,parent:o,inject:f};if(2==t&&s&&(u=d.findWrapping(s.type)))return{sliceDepth:n,frontierDepth:a,parent:o,wrap:u};if(o&&d.matchType(o.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=mi(e,t);return!(!r.childCount||r.firstChild.isLeaf||(this.unplaced=new x(e,t+1,Math.max(n,r.size+t>=e.size-n?t+1:0)),0))}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=mi(e,t);if(r.childCount<=1&&t>0){let o=e.size-t<=t+r.size;this.unplaced=new x(Ut(e,t-1,1),t-1,o?t-1:n)}else this.unplaced=new x(Ut(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:r,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let g=0;g<o.length;g++)this.openFrontierNode(o[g]);let s=this.unplaced,a=n?n.content:s.content,c=s.openStart-e,d=0,u=[],{match:f,type:h}=this.frontier[t];if(r){for(let g=0;g<r.childCount;g++)u.push(r.child(g));f=f.matchFragment(r)}let p=a.size+e-(s.content.size-s.openEnd);for(;d<a.childCount;){let g=a.child(d),y=f.matchType(g.type);if(!y)break;d++,(d>1||0==c||g.content.size)&&(f=y,u.push(co(g.mark(h.allowedMarks(g.marks)),1==d?c:0,d==a.childCount?p:-1)))}let m=d==a.childCount;m||(p=-1),this.placed=Jt(this.placed,t,_.from(u)),this.frontier[t].match=f,m&&p<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let g=0,y=a;g<p;g++){let b=y.lastChild;this.frontier.push({type:b.type,match:b.contentMatchAt(b.childCount)}),y=b.content}this.unplaced=m?0==e?x.empty:new x(Ut(s.content,e-1,1),e-1,p<0?s.openEnd:e-1):new x(Ut(s.content,e,d),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let t,e=this.frontier[this.depth];if(!e.type.isTextblock||!gi(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],o=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),s=gi(e,t,r,n,o);if(s){for(let a=t-1;a>=0;a--){let{match:c,type:d}=this.frontier[a],u=gi(e,a,d,c,!0);if(!u||u.childCount)continue e}return{depth:t,fit:s,move:o?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=Jt(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let r=e.node(n),o=r.type.contentMatch.fillBefore(r.content,!0,e.index(n));this.openFrontierNode(r.type,r.attrs,o)}return e}openFrontierNode(e,t=null,n){let r=this.frontier[this.depth];r.match=r.match.matchType(e),this.placed=Jt(this.placed,this.depth,_.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(_.empty,!0);t.childCount&&(this.placed=Jt(this.placed,this.frontier.length,t))}}function Ut(i,e,t){return 0==e?i.cutByIndex(t,i.childCount):i.replaceChild(0,i.firstChild.copy(Ut(i.firstChild.content,e-1,t)))}function Jt(i,e,t){return 0==e?i.append(t):i.replaceChild(i.childCount-1,i.lastChild.copy(Jt(i.lastChild.content,e-1,t)))}function mi(i,e){for(let t=0;t<e;t++)i=i.firstChild.content;return i}function co(i,e,t){if(e<=0)return i;let n=i.content;return e>1&&(n=n.replaceChild(0,co(n.firstChild,e-1,1==n.childCount?t-1:0))),e>0&&(n=i.type.contentMatch.fillBefore(n).append(n),t<=0&&(n=n.append(i.type.contentMatch.matchFragment(n).fillBefore(_.empty,!0)))),i.copy(n)}function gi(i,e,t,n,r){let o=i.node(e),s=r?i.indexAfter(e):i.index(e);if(s==o.childCount&&!t.compatibleContent(o.type))return null;let a=n.fillBefore(o.content,!0,s);return a&&!function oc(i,e,t){for(let n=t;n<e.childCount;n++)if(!i.allowsMarks(e.child(n).marks))return!0;return!1}(t,o.content,s)?a:null}function sc(i){return i.spec.defining||i.spec.definingForContent}function uo(i,e,t,n,r){if(e<t){let o=i.firstChild;i=i.replaceChild(0,o.copy(uo(o.content,e+1,t,n,o)))}if(e>n){let o=r.contentMatchAt(0),s=o.fillBefore(i).append(i);i=s.append(o.matchFragment(s).fillBefore(_.empty,!0))}return i}function fo(i,e){let t=[];for(let r=Math.min(i.depth,e.depth);r>=0;r--){let o=i.start(r);if(o<i.pos-(i.depth-r)||e.end(r)>e.pos+(e.depth-r)||i.node(r).type.spec.isolating||e.node(r).type.spec.isolating)break;(o==e.start(r)||r==i.depth&&r==e.depth&&i.parent.inlineContent&&e.parent.inlineContent&&r&&e.start(r-1)==o-1)&&t.push(r)}return t}class wt extends U{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return V.fail("No node at attribute step's position");let n=Object.create(null);for(let o in t.attrs)n[o]=t.attrs[o];n[this.attr]=this.value;let r=t.type.create(n,null,t.marks);return V.fromReplace(e,this.pos,this.pos+1,new x(_.from(r),0,t.isLeaf?0:1))}getMap(){return oe.empty}invert(e){return new wt(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new wt(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw new RangeError("Invalid input for AttrStep.fromJSON");return new wt(t.pos,t.attr,t.value)}}U.jsonID("attr",wt);class Kt extends U{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let r in e.attrs)t[r]=e.attrs[r];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return V.ok(n)}getMap(){return oe.empty}invert(e){return new Kt(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new Kt(t.attr,t.value)}}U.jsonID("docAttr",Kt);let kt=class extends Error{};kt=function i(e){let t=Error.call(this,e);return t.__proto__=i.prototype,t},(kt.prototype=Object.create(Error.prototype)).constructor=kt,kt.prototype.name="TransformError";class dc{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Wt}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new kt(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=x.empty){let r=hi(this.doc,e,t,n);return r&&this.step(r),this}replaceWith(e,t,n){return this.replace(e,t,new x(_.from(n),0,0))}delete(e,t){return this.replace(e,t,x.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return function lc(i,e,t,n){if(!n.size)return i.deleteRange(e,t);let r=i.doc.resolve(e),o=i.doc.resolve(t);if(ao(r,o,n))return i.step(new J(e,t,n));let s=fo(r,i.doc.resolve(t));0==s[s.length-1]&&s.pop();let a=-(r.depth+1);s.unshift(a);for(let h=r.depth,p=r.pos-1;h>0;h--,p--){let m=r.node(h).type.spec;if(m.defining||m.definingAsContext||m.isolating)break;s.indexOf(h)>-1?a=h:r.before(h)==p&&s.splice(1,0,-h)}let c=s.indexOf(a),d=[],u=n.openStart;for(let h=n.content,p=0;;p++){let m=h.firstChild;if(d.push(m),p==n.openStart)break;h=m.content}for(let h=u-1;h>=0;h--){let p=d[h],m=sc(p.type);if(m&&!p.sameMarkup(r.node(Math.abs(a)-1)))u=h;else if(m||!p.type.isTextblock)break}for(let h=n.openStart;h>=0;h--){let p=(h+u+1)%(n.openStart+1),m=d[p];if(m)for(let g=0;g<s.length;g++){let y=s[(g+c)%s.length],b=!0;y<0&&(b=!1,y=-y);let C=r.node(y-1),k=r.index(y-1);if(C.canReplaceWith(k,k,m.type,m.marks))return i.replace(r.before(y),b?o.after(y):t,new x(uo(n.content,0,n.openStart,p),p,n.openEnd))}}let f=i.steps.length;for(let h=s.length-1;h>=0&&(i.replace(e,t,n),!(i.steps.length>f));h--){let p=s[h];p<0||(e=r.before(p),t=o.after(p))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return function ac(i,e,t,n){if(!n.isInline&&e==t&&i.doc.resolve(e).parent.content.size){let r=function nc(i,e,t){let n=i.resolve(e);if(n.parent.canReplaceWith(n.index(),n.index(),t))return e;if(0==n.parentOffset)for(let r=n.depth-1;r>=0;r--){let o=n.index(r);if(n.node(r).canReplaceWith(o,o,t))return n.before(r+1);if(o>0)return null}if(n.parentOffset==n.parent.content.size)for(let r=n.depth-1;r>=0;r--){let o=n.indexAfter(r);if(n.node(r).canReplaceWith(o,o,t))return n.after(r+1);if(o<n.node(r).childCount)return null}return null}(i.doc,e,n.type);null!=r&&(e=t=r)}i.replaceRange(e,t,new x(_.from(n),0,0))}(this,e,t,n),this}deleteRange(e,t){return function cc(i,e,t){let n=i.doc.resolve(e),r=i.doc.resolve(t),o=fo(n,r);for(let s=0;s<o.length;s++){let a=o[s],c=s==o.length-1;if(c&&0==a||n.node(a).type.contentMatch.validEnd)return i.delete(n.start(a),r.end(a));if(a>0&&(c||n.node(a-1).canReplace(n.index(a-1),r.indexAfter(a-1))))return i.delete(n.before(a),r.after(a))}for(let s=1;s<=n.depth&&s<=r.depth;s++)if(e-n.start(s)==n.depth-s&&t>n.end(s)&&r.end(s)-t!=r.depth-s&&n.start(s-1)==r.start(s-1)&&n.node(s-1).canReplace(n.index(s-1),r.index(s-1)))return i.delete(n.before(s),t);i.delete(e,t)}(this,e,t),this}lift(e,t){return function Xa(i,e,t){let{$from:n,$to:r,depth:o}=e,s=n.before(o+1),a=r.after(o+1),c=s,d=a,u=_.empty,f=0;for(let m=o,g=!1;m>t;m--)g||n.index(m)>0?(g=!0,u=_.from(n.node(m).copy(u)),f++):c--;let h=_.empty,p=0;for(let m=o,g=!1;m>t;m--)g||r.after(m+1)<r.end(m)?(g=!0,h=_.from(r.node(m).copy(h)),p++):d++;i.step(new H(c,d,s,a,new x(u.append(h),f,p),u.size-f,!0))}(this,e,t),this}join(e,t=1){return function tc(i,e,t){let n=null,{linebreakReplacement:r}=i.doc.type.schema,o=i.doc.resolve(e-t),s=o.node().type;if(r&&s.inlineContent){let u="pre"==s.whitespace,f=!!s.contentMatch.matchType(r);u&&!f?n=!1:!u&&f&&(n=!0)}let a=i.steps.length;if(!1===n){let u=i.doc.resolve(e+t);so(i,u.node(),u.before(),a)}s.inlineContent&&fi(i,e+t-1,s,o.node().contentMatchAt(o.index()),null==n);let c=i.mapping.slice(a),d=c.map(e-t);if(i.step(new J(d,c.map(e+t,-1),x.empty,!0)),!0===n){let u=i.doc.resolve(d);oo(i,u.node(),u.before(),i.steps.length)}}(this,e,t),this}wrap(e,t){return function Ka(i,e,t){let n=_.empty;for(let s=t.length-1;s>=0;s--){if(n.size){let a=t[s].type.contentMatch.matchFragment(n);if(!a||!a.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}n=_.from(t[s].type.create(t[s].attrs,n))}let r=e.start,o=e.end;i.step(new H(r,o,r,o,new x(n,0,0),t.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,r=null){return function Ya(i,e,t,n,r){if(!n.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let o=i.steps.length;i.doc.nodesBetween(e,t,(s,a)=>{let c="function"==typeof r?r(s):r;if(s.isTextblock&&!s.hasMarkup(n,c)&&function qa(i,e,t){let n=i.resolve(e),r=n.index();return n.parent.canReplaceWith(r,r+1,t)}(i.doc,i.mapping.slice(o).map(a),n)){let d=null;if(n.schema.linebreakReplacement){let p="pre"==n.whitespace,m=!!n.contentMatch.matchType(n.schema.linebreakReplacement);p&&!m?d=!1:!p&&m&&(d=!0)}!1===d&&so(i,s,a,o),fi(i,i.mapping.slice(o).map(a,1),n,void 0,null===d);let u=i.mapping.slice(o),f=u.map(a,1),h=u.map(a+s.nodeSize,1);return i.step(new H(f,h,f+1,h-1,new x(_.from(n.create(c,null,s.marks)),0,0),1,!0)),!0===d&&oo(i,s,a,o),!1}})}(this,e,t,n,r),this}setNodeMarkup(e,t,n=null,r){return function Qa(i,e,t,n,r){let o=i.doc.nodeAt(e);if(!o)throw new RangeError("No node at given position");t||(t=o.type);let s=t.create(n,null,r||o.marks);if(o.isLeaf)return i.replaceWith(e,e+o.nodeSize,s);if(!t.validContent(o.content))throw new RangeError("Invalid content for node type "+t.name);i.step(new H(e,e+o.nodeSize,e+1,e+o.nodeSize-1,new x(_.from(s),0,0),1,!0))}(this,e,t,n,r),this}setNodeAttribute(e,t,n){return this.step(new wt(e,t,n)),this}setDocAttribute(e,t){return this.step(new Kt(e,t)),this}addNodeMark(e,t){return this.step(new $e(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(t instanceof P)t.isInSet(n.marks)&&this.step(new ct(e,t));else{let o,r=n.marks,s=[];for(;o=t.isInSet(r);)s.push(new ct(e,o)),r=o.removeFromSet(r);for(let a=s.length-1;a>=0;a--)this.step(s[a])}return this}split(e,t=1,n){return function Za(i,e,t=1,n){let r=i.doc.resolve(e),o=_.empty,s=_.empty;for(let a=r.depth,c=r.depth-t,d=t-1;a>c;a--,d--){o=_.from(r.node(a).copy(o));let u=n&&n[d];s=_.from(u?u.type.create(u.attrs,s):r.node(a).copy(s))}i.step(new J(e,e,new x(o.append(s),t,t),!0))}(this,e,t,n),this}addMark(e,t,n){return function ja(i,e,t,n){let s,a,r=[],o=[];i.doc.nodesBetween(e,t,(c,d,u)=>{if(!c.isInline)return;let f=c.marks;if(!n.isInSet(f)&&u.type.allowsMarkType(n.type)){let h=Math.max(d,e),p=Math.min(d+c.nodeSize,t),m=n.addToSet(f);for(let g=0;g<f.length;g++)f[g].isInSet(m)||(s&&s.to==h&&s.mark.eq(f[g])?s.to=p:r.push(s=new ke(h,p,f[g])));a&&a.to==h?a.to=p:o.push(a=new ze(h,p,n))}}),r.forEach(c=>i.step(c)),o.forEach(c=>i.step(c))}(this,e,t,n),this}removeMark(e,t,n){return function Ga(i,e,t,n){let r=[],o=0;i.doc.nodesBetween(e,t,(s,a)=>{if(!s.isInline)return;o++;let c=null;if(n instanceof Cn){let u,d=s.marks;for(;u=n.isInSet(d);)(c||(c=[])).push(u),d=u.removeFromSet(d)}else n?n.isInSet(s.marks)&&(c=[n]):c=s.marks;if(c&&c.length){let d=Math.min(a+s.nodeSize,t);for(let u=0;u<c.length;u++){let h,f=c[u];for(let p=0;p<r.length;p++){let m=r[p];m.step==o-1&&f.eq(r[p].style)&&(h=m)}h?(h.to=d,h.step=o):r.push({style:f,from:Math.max(a,e),to:d,step:o})}}}),r.forEach(s=>i.step(new ke(s.from,s.to,s.style)))}(this,e,t,n),this}clearIncompatible(e,t,n){return fi(this,e,t,n),this}}const yi=Object.create(null);class I{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new bi(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=x.empty){let n=t.content.lastChild,r=null;for(let a=0;a<t.openEnd;a++)r=n,n=n.lastChild;let o=e.steps.length,s=this.ranges;for(let a=0;a<s.length;a++){let{$from:c,$to:d}=s[a],u=e.mapping.slice(o);e.replaceRange(u.map(c.pos),u.map(d.pos),a?x.empty:t),0==a&&mo(e,o,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let o=0;o<r.length;o++){let{$from:s,$to:a}=r[o],c=e.mapping.slice(n),d=c.map(s.pos),u=c.map(a.pos);o?e.deleteRange(d,u):(e.replaceRangeWith(d,u,t),mo(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new A(e):vt(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let o=e.depth-1;o>=0;o--){let s=t<0?vt(e.node(0),e.node(o),e.before(o+1),e.index(o),t,n):vt(e.node(0),e.node(o),e.after(o+1),e.index(o)+1,t,n);if(s)return s}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new se(e.node(0))}static atStart(e){return vt(e,e,0,0,1)||new se(e)}static atEnd(e){return vt(e,e,e.content.size,e.childCount,-1)||new se(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=yi[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in yi)throw new RangeError("Duplicate use of selection JSON ID "+e);return yi[e]=t,t.prototype.jsonID=e,t}getBookmark(){return A.between(this.$anchor,this.$head).getBookmark()}}I.prototype.visible=!0;class bi{constructor(e,t){this.$from=e,this.$to=t}}let po=!1;function ho(i){!po&&!i.parent.inlineContent&&(po=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+i.parent.type.name+")"))}class A extends I{constructor(e,t=e){ho(e),ho(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return I.near(n);let r=e.resolve(t.map(this.anchor));return new A(r.parent.inlineContent?r:n,n)}replace(e,t=x.empty){if(super.replace(e,t),t==x.empty){let n=this.$from.marksAcross(this.$to);n&&e.ensureMarks(n)}}eq(e){return e instanceof A&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Mn(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new A(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if((!n||r)&&(n=r>=0?1:-1),!t.parent.inlineContent){let o=I.findFrom(t,n,!0)||I.findFrom(t,-n,!0);if(!o)return I.near(t,n);t=o.$head}return e.parent.inlineContent||(0==r||(e=(I.findFrom(e,-n,!0)||I.findFrom(e,n,!0)).$anchor).pos<t.pos!=r<0)&&(e=t),new A(e,t)}}I.jsonID("text",A);class Mn{constructor(e,t){this.anchor=e,this.head=t}map(e){return new Mn(e.map(this.anchor),e.map(this.head))}resolve(e){return A.between(e.resolve(this.anchor),e.resolve(this.head))}}class v extends I{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),o=e.resolve(r);return n?I.near(o):new v(o)}content(){return new x(_.from(this.node),0,0)}eq(e){return e instanceof v&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new _i(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new v(e.resolve(t.anchor))}static create(e,t){return new v(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}v.prototype.visible=!1,I.jsonID("node",v);class _i{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new Mn(n,n):new _i(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&v.isSelectable(n)?new v(t):I.near(t)}}class se extends I{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=x.empty){if(t==x.empty){e.delete(0,e.doc.content.size);let n=I.atStart(e.doc);n.eq(e.selection)||e.setSelection(n)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new se(e)}map(e){return new se(e)}eq(e){return e instanceof se}getBookmark(){return uc}}I.jsonID("all",se);const uc={map(){return this},resolve:i=>new se(i)};function vt(i,e,t,n,r,o=!1){if(e.inlineContent)return A.create(i,t);for(let s=n-(r>0?0:1);r>0?s<e.childCount:s>=0;s+=r){let a=e.child(s);if(a.isAtom){if(!o&&v.isSelectable(a))return v.create(i,t-(r<0?a.nodeSize:0))}else{let c=vt(i,a,t+r,r<0?a.childCount:0,r,o);if(c)return c}t+=a.nodeSize*r}return null}function mo(i,e,t){let n=i.steps.length-1;if(n<e)return;let s,r=i.steps[n];(r instanceof J||r instanceof H)&&(i.mapping.maps[n].forEach((a,c,d,u)=>{null==s&&(s=u)}),i.setSelection(I.near(i.doc.resolve(s),t)))}class fc extends dc{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return P.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||P.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=n??t,!e)return this.deleteRange(t,n);let o=this.storedMarks;if(!o){let s=this.doc.resolve(t);o=n==t?s.marks():s.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,o)),this.selection.empty||this.setSelection(I.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function bo(i,e){return e&&i?i.bind(e):i}class Yt{constructor(e,t,n){this.name=e,this.init=bo(t.init,n),this.apply=bo(t.apply,n)}}const pc=[new Yt("doc",{init:i=>i.doc||i.schema.topNodeType.createAndFill(),apply:i=>i.doc}),new Yt("selection",{init:(i,e)=>i.selection||I.atStart(e.doc),apply:i=>i.selection}),new Yt("storedMarks",{init:i=>i.storedMarks||null,apply:(i,e,t,n)=>n.selection.$cursor?i.storedMarks:null}),new Yt("scrollToSelection",{init:()=>0,apply:(i,e)=>i.scrolledIntoView?e+1:e})];class xi{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=pc.slice(),t&&t.forEach(n=>{if(this.pluginsByKey[n.key])throw new RangeError("Adding different instances of a keyed plugin ("+n.key+")");this.plugins.push(n),this.pluginsByKey[n.key]=n,n.spec.state&&this.fields.push(new Yt(n.key,n.spec.state,n))})}}class Tt{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let r=this.config.plugins[n];if(r.spec.filterTransaction&&!r.spec.filterTransaction.call(r,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let o=!1;for(let s=0;s<this.config.plugins.length;s++){let a=this.config.plugins[s];if(a.spec.appendTransaction){let c=r?r[s].n:0,d=r?r[s].state:this,u=c<t.length&&a.spec.appendTransaction.call(a,c?t.slice(c):t,d,n);if(u&&n.filterTransaction(u,s)){if(u.setMeta("appendedTransaction",e),!r){r=[];for(let f=0;f<this.config.plugins.length;f++)r.push(f<s?{state:n,n:t.length}:{state:this,n:0})}t.push(u),n=n.applyInner(u),o=!0}r&&(r[s]={state:n,n:t.length})}}if(!o)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Tt(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let o=n[r];t[o.name]=o.apply(e,this[o.name],this,t)}return t}get tr(){return new fc(this)}static create(e){let t=new xi(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new Tt(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new xi(this.schema,e.plugins),n=t.fields,r=new Tt(t);for(let o=0;o<n.length;o++){let s=n[o].name;r[s]=this.hasOwnProperty(s)?this[s]:n[o].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(n=>n.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],o=r.spec.state;o&&o.toJSON&&(t[n]=o.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let r=new xi(e.schema,e.plugins),o=new Tt(r);return r.fields.forEach(s=>{if("doc"==s.name)o.doc=we.fromJSON(e.schema,t.doc);else if("selection"==s.name)o.selection=I.fromJSON(o.doc,t.selection);else if("storedMarks"==s.name)t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let a in n){let c=n[a],d=c.spec.state;if(c.key==s.name&&d&&d.fromJSON&&Object.prototype.hasOwnProperty.call(t,a))return void(o[s.name]=d.fromJSON.call(c,e,t[a],o))}o[s.name]=s.init(e,o)}}),o}}function _o(i,e,t){for(let n in i){let r=i[n];r instanceof Function?r=r.bind(e):"handleDOMEvents"==n&&(r=_o(r,e,{})),t[n]=r}return t}class ve{constructor(e){this.spec=e,this.props={},e.props&&_o(e.props,this,this.props),this.key=e.key?e.key.key:xo("plugin")}getState(e){return e[this.key]}}const Ci=Object.create(null);function xo(i){return i in Ci?i+"$"+ ++Ci[i]:(Ci[i]=0,i+"$")}class Oe{constructor(e="key"){this.key=xo(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const hc=["ol",0],mc=["ul",0],gc=["li",0],Co={attrs:{order:{default:1,validate:"number"}},parseDOM:[{tag:"ol",getAttrs:i=>({order:i.hasAttribute("start")?+i.getAttribute("start"):1})}],toDOM:i=>1==i.attrs.order?hc:["ol",{start:i.attrs.order},0]},wo={parseDOM:[{tag:"ul"}],toDOM:()=>mc},ko={parseDOM:[{tag:"li"}],toDOM:()=>gc,defining:!0};function vo(i,e){return function(t,n){let{$from:r,$to:o,node:s}=t.selection;if(s&&s.isBlock||r.depth<2||!r.sameParent(o))return!1;let a=r.node(-1);if(a.type!=i)return!1;if(0==r.parent.content.size&&r.node(-1).childCount==r.indexAfter(-1)){if(3==r.depth||r.node(-3).type!=i||r.index(-2)!=r.node(-2).childCount-1)return!1;if(n){let f=_.empty,h=r.index(-1)?1:r.index(-2)?2:3;for(let b=r.depth-h;b>=r.depth-3;b--)f=_.from(r.node(b).copy(f));let p=r.indexAfter(-1)<r.node(-2).childCount?1:r.indexAfter(-2)<r.node(-3).childCount?2:3;f=f.append(_.from(i.createAndFill()));let m=r.before(r.depth-(h-1)),g=t.tr.replace(m,r.after(-p),new x(f,4-h,0)),y=-1;g.doc.nodesBetween(m,g.doc.content.size,(b,C)=>{if(y>-1)return!1;b.isTextblock&&0==b.content.size&&(y=C+1)}),y>-1&&g.setSelection(I.near(g.doc.resolve(y))),n(g.scrollIntoView())}return!0}let c=o.pos==r.end()?a.contentMatchAt(0).defaultType:null,d=t.tr.delete(r.pos,o.pos),u=c?[e?{type:i,attrs:e}:null,{type:c}]:void 0;return!!Ct(d.doc,r.pos,2,u)&&(n&&n(d.split(r.pos,2,u).scrollIntoView()),!0)}}function To(i){return function(e,t){let{$from:n,$to:r}=e.selection,o=n.blockRange(r,s=>s.childCount>0&&s.firstChild.type==i);return!!o&&(!t||(n.node(o.depth-1).type==i?function xc(i,e,t,n){let r=i.tr,o=n.end,s=n.$to.end(n.depth);o<s&&(r.step(new H(o-1,s,o,s,new x(_.from(t.create(null,n.parent.copy())),1,0),1,!0)),n=new bn(r.doc.resolve(n.$from.pos),r.doc.resolve(s),n.depth));const a=Xt(n);if(null==a)return!1;r.lift(n,a);let c=r.doc.resolve(r.mapping.map(o,-1)-1);return Tn(r.doc,c.pos)&&c.nodeBefore.type==c.nodeAfter.type&&r.join(c.pos),e(r.scrollIntoView()),!0}(e,t,i,o):function Cc(i,e,t){let n=i.tr,r=t.parent;for(let p=t.end,m=t.endIndex-1,g=t.startIndex;m>g;m--)p-=r.child(m).nodeSize,n.delete(p-1,p+1);let o=n.doc.resolve(t.start),s=o.nodeAfter;if(n.mapping.map(t.end)!=t.start+o.nodeAfter.nodeSize)return!1;let a=0==t.startIndex,c=t.endIndex==r.childCount,d=o.node(-1),u=o.index(-1);if(!d.canReplace(u+(a?0:1),u+1,s.content.append(c?_.empty:_.from(r))))return!1;let f=o.pos,h=f+s.nodeSize;return n.step(new H(f-(a?1:0),h+(c?1:0),f+1,h-1,new x((a?_.empty:_.from(r.copy(_.empty))).append(c?_.empty:_.from(r.copy(_.empty))),a?0:1,c?0:1),a?0:1)),e(n.scrollIntoView()),!0}(e,t,o)))}}function Mo(i){return function(e,t){let{$from:n,$to:r}=e.selection,o=n.blockRange(r,d=>d.childCount>0&&d.firstChild.type==i);if(!o)return!1;let s=o.startIndex;if(0==s)return!1;let a=o.parent,c=a.child(s-1);if(c.type!=i)return!1;if(t){let d=c.lastChild&&c.lastChild.type==a.type,u=_.from(d?i.create():null),f=new x(_.from(i.create(null,_.from(a.type.create(null,u)))),d?3:1,0),h=o.start,p=o.end;t(e.tr.step(new H(h-(d?3:1),p,h,p,f,1,!0)).scrollIntoView())}return!0}}const wc={link:{attrs:{href:{},title:{default:null},target:{default:"_blank"}},inclusive:!1,parseDOM:[{tag:"a[href]",getAttrs:i=>({href:i.getAttribute("href"),title:i.getAttribute("title"),target:i.getAttribute("target")})}],toDOM(i){const{href:e,title:t,target:n}=i.attrs;return["a",{href:e,title:t,target:n},0]}},em:{parseDOM:[{tag:"i"},{tag:"em"},{style:"font-style=italic"}],toDOM:()=>["em",0]},strong:{parseDOM:[{tag:"strong"},{tag:"b",getAttrs:i=>"normal"!==i.style.fontWeight&&null},{style:"font-weight",getAttrs:i=>/^(?:bold(?:er)?|[5-9]\d{2,})$/.test(i)&&null}],toDOM:()=>["strong",0]},code:{parseDOM:[{tag:"code"}],toDOM:()=>["code",0]},u:{parseDOM:[{tag:"u"},{style:"text-decoration=underline",consuming:!1}],toDOM:()=>["u",0]},s:{parseDOM:[{tag:"s"},{tag:"strike"},{style:"text-decoration=line-through"}],toDOM:()=>["s",0]},text_color:{attrs:{color:{default:null}},parseDOM:[{style:"color",getAttrs:i=>({color:i})}],toDOM(i){const{color:e}=i.attrs;return["span",{style:`color:${e};`},0]}},text_background_color:{attrs:{backgroundColor:{default:null}},parseDOM:[{style:"background-color",getAttrs:i=>({backgroundColor:i})}],toDOM(i){const{backgroundColor:e}=i.attrs;return["span",{style:`background-color:${e};`},0]}},sup:{attrs:{},parseDOM:[{tag:"sup"},{style:"vertical-align=super"}],toDOM:()=>["sup",0]},sub:{attrs:{},parseDOM:[{tag:"sub"},{style:"vertical-align=sub"}],toDOM:()=>["sub",0]}},Fc={doc:{content:"block+"},text:{group:"inline"},paragraph:{content:"inline*",group:"block",attrs:{align:{default:null},indent:{default:null}},parseDOM:[{tag:"p",getAttrs(i){const{textAlign:e}=i.style,t=i.getAttribute("align")||e||null,n=i.getAttribute("data-indent")||null;return{align:t,indent:parseInt(n,10)||null}}}],toDOM(i){const{align:e,indent:t}=i.attrs;return["p",{style:ii({textAlign:"left"!==e?e:null,marginLeft:null!==t?40*t+"px":null})||null,"data-indent":t??null},0]}},blockquote:{content:"block+",group:"block",defining:!0,attrs:{indent:{default:null}},parseDOM:[{tag:"blockquote",getAttrs(i){const e=i.getAttribute("data-indent")||null;return{indent:parseInt(e,10)||null}}}],toDOM(i){const{indent:e}=i.attrs;return["blockquote",{style:ii({marginLeft:null!==e?40*e+"px":null})||null,"data-indent":e??null},0]}},horizontal_rule:{group:"block",parseDOM:[{tag:"hr"}],toDOM:()=>["hr"]},heading:{attrs:{level:{default:1},align:{default:null},indent:{default:null}},content:"inline*",group:"block",defining:!0,parseDOM:[{tag:"h1",getAttrs(i){const{textAlign:e}=i.style,t=i.getAttribute("align")||e||null,n=i.getAttribute("data-indent")||null;return{level:1,align:t,indent:parseInt(n,10)||null}}},{tag:"h2",getAttrs(i){const{textAlign:e}=i.style,t=i.getAttribute("align")||e||null,n=i.getAttribute("data-indent")||null;return{level:2,align:t,indent:parseInt(n,10)||null}}},{tag:"h3",getAttrs(i){const{textAlign:e}=i.style,t=i.getAttribute("align")||e||null,n=i.getAttribute("data-indent")||null;return{level:3,align:t,indent:parseInt(n,10)||null}}},{tag:"h4",getAttrs(i){const{textAlign:e}=i.style,t=i.getAttribute("align")||e||null,n=i.getAttribute("data-indent")||null;return{level:4,align:t,indent:parseInt(n,10)||null}}},{tag:"h5",getAttrs(i){const{textAlign:e}=i.style,t=i.getAttribute("align")||e||null,n=i.getAttribute("data-indent")||null;return{level:5,align:t,indent:parseInt(n,10)||null}}},{tag:"h6",getAttrs(i){const{textAlign:e}=i.style,t=i.getAttribute("align")||e||null,n=i.getAttribute("data-indent")||null;return{level:6,align:t,indent:parseInt(n,10)||null}}}],toDOM(i){const{level:e,align:t,indent:n}=i.attrs;return[`h${e}`,{style:ii({textAlign:"left"!==t?t:null,marginLeft:null!==n?40*n+"px":null})||null,"data-indent":n??null},0]}},hard_break:{inline:!0,group:"inline",selectable:!1,parseDOM:[{tag:"br"}],toDOM:()=>["br"]},code_block:{content:"text*",marks:"",group:"block",code:!0,defining:!0,parseDOM:[{tag:"pre",preserveWhitespace:"full"}],toDOM:()=>["pre",["code",0]]},image:{inline:!0,attrs:{src:{},alt:{default:null},title:{default:null},width:{default:null}},group:"inline",draggable:!0,parseDOM:[{tag:"img[src]",getAttrs:i=>({src:i.getAttribute("src"),title:i.getAttribute("title"),alt:i.getAttribute("alt"),width:i.getAttribute("width")})}],toDOM(i){const{src:e,alt:t,title:n,width:r}=i.attrs;return["img",{src:e,alt:t,title:n,width:r}]}},list_item:{...ko,content:"paragraph block*"},ordered_list:{...Co,content:"list_item+",group:"block"},bullet_list:{...wo,content:"list_item+",group:"block"}},Sn=new class Aa{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let r in e)t[r]=e[r];t.nodes=Tr.from(e.nodes),t.marks=Tr.from(e.marks||{}),this.nodes=xn.compile(this.spec.nodes,this),this.marks=Cn.compile(this.spec.marks,this);let n=Object.create(null);for(let r in this.nodes){if(r in this.marks)throw new RangeError(r+" can not be both a node and a mark");let o=this.nodes[r],s=o.spec.content||"",a=o.spec.marks;if(o.contentMatch=n[s]||(n[s]=st.parse(s,this.nodes)),o.inlineContent=o.contentMatch.inlineContent,o.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!o.isInline||!o.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=o}o.markSet="_"==a?null:a?jr(this,a.split(" ")):""!=a&&o.inlineContent?null:[]}for(let r in this.marks){let o=this.marks[r],s=o.spec.excludes;o.excluded=null==s?[o]:""==s?[]:jr(this,s.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else{if(!(e instanceof xn))throw new RangeError("Invalid node type: "+e);if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new _n(n,n.defaultAttrs,e,P.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return we.fromJSON(this,e)}markFromJSON(e){return P.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}({marks:wc,nodes:Fc}),j=function(i){for(var e=0;;e++)if(!(i=i.previousSibling))return e},Mt=function(i){let e=i.assignedSlot||i.parentNode;return e&&11==e.nodeType?e.host:e};let ki=null;const De=function(i,e,t){let n=ki||(ki=document.createRange());return n.setEnd(i,t??i.nodeValue.length),n.setStart(i,e||0),n},dt=function(i,e,t,n){return t&&(Eo(i,e,t,n,-1)||Eo(i,e,t,n,1))},Lc=/^(img|br|input|textarea|hr)$/i;function Eo(i,e,t,n,r){for(var o;;){if(i==t&&e==n)return!0;if(e==(r<0?0:de(i))){let s=i.parentNode;if(!s||1!=s.nodeType||qt(i)||Lc.test(i.nodeName)||"false"==i.contentEditable)return!1;e=j(i)+(r<0?0:1),i=s}else{if(1!=i.nodeType)return!1;{let s=i.childNodes[e+(r<0?-1:0)];if(1==s.nodeType&&"false"==s.contentEditable){if(null===(o=s.pmViewDesc)||void 0===o||!o.ignoreForSelection)return!1;e+=r}else i=s,e=r<0?de(i):0}}}}function de(i){return 3==i.nodeType?i.nodeValue.length:i.childNodes.length}function qt(i){let e;for(let t=i;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==i||e.contentDOM==i)}const Nn=function(i){return i.focusNode&&dt(i.focusNode,i.focusOffset,i.anchorNode,i.anchorOffset)};function ut(i,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=i,t.key=t.code=e,t}const Te=typeof navigator<"u"?navigator:null,So=typeof document<"u"?document:null,He=Te&&Te.userAgent||"",vi=/Edge\/(\d+)/.exec(He),No=/MSIE \d/.exec(He),Ti=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(He),te=!!(No||Ti||vi),je=No?document.documentMode:Ti?+Ti[1]:vi?+vi[1]:0,ye=!te&&/gecko\/(\d+)/i.test(He);ye&&/Firefox\/(\d+)/.exec(He);const Mi=!te&&/Chrome\/(\d+)/.exec(He),K=!!Mi,Oo=Mi?+Mi[1]:0,q=!te&&!!Te&&/Apple Computer/.test(Te.vendor),Et=q&&(/Mobile\/\w+/.test(He)||!!Te&&Te.maxTouchPoints>2),ue=Et||!!Te&&/Mac/.test(Te.platform),jc=!!Te&&/Win/.test(Te.platform),Ie=/Android \d/.test(He),Qt=!!So&&"webkitFontSmoothing"in So.documentElement.style,Gc=Qt?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Wc(i){let e=i.defaultView&&i.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:i.documentElement.clientWidth,top:0,bottom:i.documentElement.clientHeight}}function Ae(i,e){return"number"==typeof i?i:i[e]}function Xc(i){let e=i.getBoundingClientRect();return{left:e.left,right:e.left+i.clientWidth*(e.width/i.offsetWidth||1),top:e.top,bottom:e.top+i.clientHeight*(e.height/i.offsetHeight||1)}}function Do(i,e,t){let n=i.someProp("scrollThreshold")||0,r=i.someProp("scrollMargin")||5,o=i.dom.ownerDocument;for(let s=t||i.dom;s;){if(1!=s.nodeType){s=Mt(s);continue}let a=s,c=a==o.body,d=c?Wc(o):Xc(a),u=0,f=0;if(e.top<d.top+Ae(n,"top")?f=-(d.top-e.top+Ae(r,"top")):e.bottom>d.bottom-Ae(n,"bottom")&&(f=e.bottom-e.top>d.bottom-d.top?e.top+Ae(r,"top")-d.top:e.bottom-d.bottom+Ae(r,"bottom")),e.left<d.left+Ae(n,"left")?u=-(d.left-e.left+Ae(r,"left")):e.right>d.right-Ae(n,"right")&&(u=e.right-d.right+Ae(r,"right")),u||f)if(c)o.defaultView.scrollBy(u,f);else{let p=a.scrollLeft,m=a.scrollTop;f&&(a.scrollTop+=f),u&&(a.scrollLeft+=u);let g=a.scrollLeft-p,y=a.scrollTop-m;e={left:e.left-g,top:e.top-y,right:e.right-g,bottom:e.bottom-y}}let h=c?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(h))break;s="absolute"==h?s.offsetParent:Mt(s)}}function Io(i){let e=[],t=i.ownerDocument;for(let n=i;n&&(e.push({dom:n,top:n.scrollTop,left:n.scrollLeft}),i!=t);n=Mt(n));return e}function Ao(i,e){for(let t=0;t<i.length;t++){let{dom:n,top:r,left:o}=i[t];n.scrollTop!=r+e&&(n.scrollTop=r+e),n.scrollLeft!=o&&(n.scrollLeft=o)}}let St=null;function Ro(i,e){let t,r,c,d,n=2e8,o=0,s=e.top,a=e.top;for(let u=i.firstChild,f=0;u;u=u.nextSibling,f++){let h;if(1==u.nodeType)h=u.getClientRects();else{if(3!=u.nodeType)continue;h=De(u).getClientRects()}for(let p=0;p<h.length;p++){let m=h[p];if(m.top<=s&&m.bottom>=a){s=Math.max(m.bottom,s),a=Math.min(m.top,a);let g=m.left>e.left?m.left-e.left:m.right<e.left?e.left-m.right:0;if(g<n){t=u,n=g,r=g&&3==t.nodeType?{left:m.right<e.left?m.right:m.left,top:e.top}:e,1==u.nodeType&&g&&(o=f+(e.left>=(m.left+m.right)/2?1:0));continue}}else m.top>e.top&&!c&&m.left<=e.left&&m.right>=e.left&&(c=u,d={left:Math.max(m.left,Math.min(m.right,e.left)),top:m.top});!t&&(e.left>=m.right&&e.top>=m.top||e.left>=m.left&&e.top>=m.bottom)&&(o=f+1)}}return!t&&c&&(t=c,r=d,n=0),t&&3==t.nodeType?function Yc(i,e){let t=i.nodeValue.length,n=document.createRange();for(let r=0;r<t;r++){n.setEnd(i,r+1),n.setStart(i,r);let o=Ge(n,1);if(o.top!=o.bottom&&Ei(e,o))return{node:i,offset:r+(e.left>=(o.left+o.right)/2?1:0)}}return{node:i,offset:0}}(t,r):!t||n&&1==t.nodeType?{node:i,offset:o}:Ro(t,r)}function Ei(i,e){return i.left>=e.left-1&&i.left<=e.right+1&&i.top>=e.top-1&&i.top<=e.bottom+1}function Fo(i,e,t){let n=i.childNodes.length;if(n&&t.top<t.bottom)for(let r=Math.max(0,Math.min(n-1,Math.floor(n*(e.top-t.top)/(t.bottom-t.top))-2)),o=r;;){let s=i.childNodes[o];if(1==s.nodeType){let a=s.getClientRects();for(let c=0;c<a.length;c++){let d=a[c];if(Ei(e,d))return Fo(s,e,d)}}if((o=(o+1)%n)==r)break}return i}function ed(i,e){let n,t=i.dom.ownerDocument,r=0,o=function Hc(i,e,t){if(i.caretPositionFromPoint)try{let n=i.caretPositionFromPoint(e,t);if(n)return{node:n.offsetNode,offset:Math.min(de(n.offsetNode),n.offset)}}catch{}if(i.caretRangeFromPoint){let n=i.caretRangeFromPoint(e,t);if(n)return{node:n.startContainer,offset:Math.min(de(n.startContainer),n.startOffset)}}}(t,e.left,e.top);o&&({node:n,offset:r}=o);let a,s=(i.root.elementFromPoint?i.root:t).elementFromPoint(e.left,e.top);if(!s||!i.dom.contains(1!=s.nodeType?s.parentNode:s)){let d=i.dom.getBoundingClientRect();if(!Ei(e,d)||(s=Fo(i.dom,e,d),!s))return null}if(q)for(let d=s;n&&d;d=Mt(d))d.draggable&&(n=void 0);if(s=function qc(i,e){let t=i.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<i.getBoundingClientRect().left?t:i}(s,e),n){if(ye&&1==n.nodeType&&(r=Math.min(r,n.childNodes.length),r<n.childNodes.length)){let f,u=n.childNodes[r];"IMG"==u.nodeName&&(f=u.getBoundingClientRect()).right<=e.left&&f.bottom>e.top&&r++}let d;Qt&&r&&1==n.nodeType&&1==(d=n.childNodes[r-1]).nodeType&&"false"==d.contentEditable&&d.getBoundingClientRect().top>=e.top&&r--,n==i.dom&&r==n.childNodes.length-1&&1==n.lastChild.nodeType&&e.top>n.lastChild.getBoundingClientRect().bottom?a=i.state.doc.content.size:(0==r||1!=n.nodeType||"BR"!=n.childNodes[r-1].nodeName)&&(a=function Zc(i,e,t,n){let r=-1;for(let o=e,s=!1;o!=i.dom;){let c,a=i.docView.nearestDesc(o,!0);if(!a)return null;if(1==a.dom.nodeType&&(a.node.isBlock&&a.parent||!a.contentDOM)&&((c=a.dom.getBoundingClientRect()).width||c.height)&&(a.node.isBlock&&a.parent&&(!s&&c.left>n.left||c.top>n.top?r=a.posBefore:(!s&&c.right<n.left||c.bottom<n.top)&&(r=a.posAfter),s=!0),!a.contentDOM&&r<0&&!a.node.isText))return(a.node.isBlock?n.top<(c.top+c.bottom)/2:n.left<(c.left+c.right)/2)?a.posBefore:a.posAfter;o=a.dom.parentNode}return r>-1?r:i.docView.posFromDOM(e,t,-1)}(i,n,r,e))}null==a&&(a=function Qc(i,e,t){let{node:n,offset:r}=Ro(e,t),o=-1;if(1==n.nodeType&&!n.firstChild){let s=n.getBoundingClientRect();o=s.left!=s.right&&t.left>(s.left+s.right)/2?1:-1}return i.docView.posFromDOM(n,r,o)}(i,s,e));let c=i.docView.nearestDesc(s,!0);return{pos:a,inside:c?c.posAtStart-c.border:-1}}function Po(i){return i.top<i.bottom||i.left<i.right}function Ge(i,e){let t=i.getClientRects();if(t.length){let n=t[e<0?0:t.length-1];if(Po(n))return n}return Array.prototype.find.call(t,Po)||i.getBoundingClientRect()}const td=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Lo(i,e,t){let{node:n,offset:r,atom:o}=i.docView.domFromPos(e,t<0?-1:1),s=Qt||ye;if(3==n.nodeType){if(!s||!td.test(n.nodeValue)&&(t<0?r:r!=n.nodeValue.length)){let c=r,d=r,u=t<0?1:-1;return t<0&&!r?(d++,u=-1):t>=0&&r==n.nodeValue.length?(c--,u=1):t<0?c--:d++,Zt(Ge(De(n,c,d),u),u<0)}{let c=Ge(De(n,r,r),t);if(ye&&r&&/\s/.test(n.nodeValue[r-1])&&r<n.nodeValue.length){let d=Ge(De(n,r-1,r-1),-1);if(d.top==c.top){let u=Ge(De(n,r,r+1),-1);if(u.top!=c.top)return Zt(u,u.left<d.left)}}return c}}if(!i.state.doc.resolve(e-(o||0)).parent.inlineContent){if(null==o&&r&&(t<0||r==de(n))){let c=n.childNodes[r-1];if(1==c.nodeType)return Si(c.getBoundingClientRect(),!1)}if(null==o&&r<de(n)){let c=n.childNodes[r];if(1==c.nodeType)return Si(c.getBoundingClientRect(),!0)}return Si(n.getBoundingClientRect(),t>=0)}if(null==o&&r&&(t<0||r==de(n))){let c=n.childNodes[r-1],d=3==c.nodeType?De(c,de(c)-(s?0:1)):1!=c.nodeType||"BR"==c.nodeName&&c.nextSibling?null:c;if(d)return Zt(Ge(d,1),!1)}if(null==o&&r<de(n)){let c=n.childNodes[r];for(;c.pmViewDesc&&c.pmViewDesc.ignoreForCoords;)c=c.nextSibling;let d=c?3==c.nodeType?De(c,0,s?0:1):1==c.nodeType?c:null:null;if(d)return Zt(Ge(d,-1),!0)}return Zt(Ge(3==n.nodeType?De(n):n,-t),t>=0)}function Zt(i,e){if(0==i.width)return i;let t=e?i.left:i.right;return{top:i.top,bottom:i.bottom,left:t,right:t}}function Si(i,e){if(0==i.height)return i;let t=e?i.top:i.bottom;return{top:t,bottom:t,left:i.left,right:i.right}}function Vo(i,e,t){let n=i.state,r=i.root.activeElement;n!=e&&i.updateState(e),r!=i.dom&&i.focus();try{return t()}finally{n!=e&&i.updateState(n),r!=i.dom&&r&&r.focus()}}const id=/[\u0590-\u08ac]/;let Bo=null,zo=null,$o=!1;class en{constructor(e,t,n,r){this.parent=e,this.children=t,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let r=this.children[t];if(r==e)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode)){if(n<0){let o,s;if(e==this.contentDOM)o=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;o=e.previousSibling}for(;o&&(!(s=o.pmViewDesc)||s.parent!=this);)o=o.previousSibling;return o?this.posBeforeChild(s)+s.size:this.posAtStart}{let o,s;if(e==this.contentDOM)o=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;o=e.nextSibling}for(;o&&(!(s=o.pmViewDesc)||s.parent!=this);)o=o.nextSibling;return o?this.posBeforeChild(s):this.posAtEnd}}let r;if(e==this.dom&&this.contentDOM)r=t>j(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(let o=e;;o=o.parentNode){if(o==this.dom){r=!1;break}if(o.previousSibling)break}if(null==r&&t==e.childNodes.length)for(let o=e;;o=o.parentNode){if(o==this.dom){r=!0;break}if(o.nextSibling)break}}return r??n>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,r=e;r;r=r.parentNode){let s,o=this.getDesc(r);if(o&&(!t||o.node)){if(!n||!(s=o.nodeDOM)||(1==s.nodeType?s.contains(1==e.nodeType?e:e.parentNode):s==e))return o;n=!1}}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let r=e;r;r=r.parentNode){let o=this.getDesc(r);if(o)return o.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let r=this.children[t],o=n+r.size;if(n==e&&o!=n){for(;!r.border&&r.children.length;)for(let s=0;s<r.children.length;s++){let a=r.children[s];if(a.size){r=a;break}}return r}if(e<o)return r.descAt(e-n-r.border);n=o}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,r=0;for(let o=0;n<this.children.length;n++){let s=this.children[n],a=o+s.size;if(a>e||s instanceof Wo){r=e-o;break}o=a}if(r)return this.children[n].domFromPos(r-this.children[n].border,t);for(let o;n&&!(o=this.children[n-1]).size&&o instanceof jo&&o.side>=0;n--);if(t<=0){let o,s=!0;for(;o=n?this.children[n-1]:null,o&&o.dom.parentNode!=this.contentDOM;n--,s=!1);return o&&t&&s&&!o.border&&!o.domAtom?o.domFromPos(o.size,t):{node:this.contentDOM,offset:o?j(o.dom)+1:0}}{let o,s=!0;for(;o=n<this.children.length?this.children[n]:null,o&&o.dom.parentNode!=this.contentDOM;n++,s=!1);return o&&s&&!o.border&&!o.domAtom?o.domFromPos(0,t):{node:this.contentDOM,offset:o?j(o.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,o=-1;for(let s=n,a=0;;a++){let c=this.children[a],d=s+c.size;if(-1==r&&e<=d){let u=s+c.border;if(e>=u&&t<=d-c.border&&c.node&&c.contentDOM&&this.contentDOM.contains(c.contentDOM))return c.parseRange(e,t,u);e=s;for(let f=a;f>0;f--){let h=this.children[f-1];if(h.size&&h.dom.parentNode==this.contentDOM&&!h.emptyChildAt(1)){r=j(h.dom)+1;break}e-=h.size}-1==r&&(r=0)}if(r>-1&&(d>t||a==this.children.length-1)){t=d;for(let u=a+1;u<this.children.length;u++){let f=this.children[u];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(-1)){o=j(f.dom);break}t+=f.size}-1==o&&(o=this.contentDOM.childNodes.length);break}s=d}return{node:this.contentDOM,from:e,to:t,fromOffset:r,toOffset:o}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(1!=t.nodeType||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,r=!1){let o=Math.min(e,t),s=Math.max(e,t);for(let p=0,m=0;p<this.children.length;p++){let g=this.children[p],y=m+g.size;if(o>m&&s<y)return g.setSelection(e-m-g.border,t-m-g.border,n,r);m=y}let a=this.domFromPos(e,e?-1:1),c=t==e?a:this.domFromPos(t,t?-1:1),d=n.root.getSelection(),u=n.domSelectionRange(),f=!1;if((ye||q)&&e==t){let{node:p,offset:m}=a;if(3==p.nodeType){if(f=!(!m||"\n"!=p.nodeValue[m-1]),f&&m==p.nodeValue.length)for(let y,g=p;g;g=g.parentNode){if(y=g.nextSibling){"BR"==y.nodeName&&(a=c={node:y.parentNode,offset:j(y)+1});break}let b=g.pmViewDesc;if(b&&b.node&&b.node.isBlock)break}}else{let g=p.childNodes[m-1];f=g&&("BR"==g.nodeName||"false"==g.contentEditable)}}if(ye&&u.focusNode&&u.focusNode!=c.node&&1==u.focusNode.nodeType){let p=u.focusNode.childNodes[u.focusOffset];p&&"false"==p.contentEditable&&(r=!0)}if(!(r||f&&q)&&dt(a.node,a.offset,u.anchorNode,u.anchorOffset)&&dt(c.node,c.offset,u.focusNode,u.focusOffset))return;let h=!1;if((d.extend||e==t)&&!f){d.collapse(a.node,a.offset);try{e!=t&&d.extend(c.node,c.offset),h=!0}catch{}}if(!h){if(e>t){let m=a;a=c,c=m}let p=document.createRange();p.setEnd(c.node,c.offset),p.setStart(a.node,a.offset),d.removeAllRanges(),d.addRange(p)}}ignoreMutation(e){return!this.contentDOM&&"selection"!=e.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,r=0;r<this.children.length;r++){let o=this.children[r],s=n+o.size;if(n==s?e<=s&&t>=n:e<s&&t>n){let a=n+o.border,c=s-o.border;if(e>=a&&t<=c)return this.dirty=e==n||t==s?2:1,void(e!=a||t!=c||!o.contentLost&&o.dom.parentNode==this.contentDOM?o.markDirty(e-a,t-a):o.dirty=3);o.dirty=o.dom!=o.contentDOM||o.dom.parentNode!=this.contentDOM||o.children.length?3:2}n=s}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=1==e?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}}class jo extends en{constructor(e,t,n,r){let o,s=t.type.toDOM;if("function"==typeof s&&(s=s(n,()=>o?o.parent?o.parent.posBeforeChild(o):void 0:r)),!t.type.spec.raw){if(1!=s.nodeType){let a=document.createElement("span");a.appendChild(s),s=a}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(e,[],s,null),this.widget=t,this.widget=t,o=this}matchesWidget(e){return 0==this.dirty&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class sd extends en{constructor(e,t,n,r){super(e,[],t,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}class pt extends en{constructor(e,t,n,r,o){super(e,[],n,r),this.mark=t,this.spec=o}static create(e,t,n,r){let o=r.nodeViews[t.type.name],s=o&&o(t,r,n);return(!s||!s.dom)&&(s=at.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new pt(e,t,s.dom,s.contentDOM||s.dom,s)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return 3!=this.dirty&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),0!=this.dirty){let n=this.parent;for(;!n.node;)n=n.parent;n.dirty<this.dirty&&(n.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let r=pt.create(this.parent,this.mark,!0,n),o=this.children,s=this.size;t<s&&(o=Oi(o,t,s,n)),e>0&&(o=Oi(o,0,e,n));for(let a=0;a<o.length;a++)o[a].parent=r;return r.children=o,r}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class We extends en{constructor(e,t,n,r,o,s,a,c,d){super(e,[],o,s),this.node=t,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=a}static create(e,t,n,r,o,s){let c,a=o.nodeViews[t.type.name],d=a&&a(t,o,()=>c?c.parent?c.parent.posBeforeChild(c):void 0:s,n,r),u=d&&d.dom,f=d&&d.contentDOM;if(t.isText)if(u){if(3!=u.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else u=document.createTextNode(t.text);else u||({dom:u,contentDOM:f}=at.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs));!f&&!t.isText&&"BR"!=u.nodeName&&(u.hasAttribute("contenteditable")||(u.contentEditable="false"),t.type.spec.draggable&&(u.draggable=!0));let h=u;return u=Jo(u,n,t),d?c=new ld(e,t,n,r,u,f||null,h,d,o,s+1):t.isText?new On(e,t,n,r,u,h,o):new We(e,t,n,r,u,f||null,h,o,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>_.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return 0==this.dirty&&e.eq(this.node)&&Dn(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,r=t,o=e.composing?this.localCompositionInfo(e,t):null,s=o&&o.pos>-1?o:null,a=o&&o.pos<0,c=new cd(this,s&&s.node,e);(function fd(i,e,t,n){let r=e.locals(i),o=0;if(0==r.length){for(let d=0;d<i.childCount;d++){let u=i.child(d);n(u,r,e.forChild(o,u),d),o+=u.nodeSize}return}let s=0,a=[],c=null;for(let d=0;;){let u,f,h,p;for(;s<r.length&&r[s].to==o;){let y=r[s++];y.widget&&(u?(f||(f=[u])).push(y):u=y)}if(u)if(f){f.sort(ud);for(let y=0;y<f.length;y++)t(f[y],d,!!c)}else t(u,d,!!c);if(c)p=-1,h=c,c=null;else{if(!(d<i.childCount))break;p=d,h=i.child(d++)}for(let y=0;y<a.length;y++)a[y].to<=o&&a.splice(y--,1);for(;s<r.length&&r[s].from<=o&&r[s].to>o;)a.push(r[s++]);let m=o+h.nodeSize;if(h.isText){let y=m;s<r.length&&r[s].from<y&&(y=r[s].from);for(let b=0;b<a.length;b++)a[b].to<y&&(y=a[b].to);y<m&&(c=h.cut(y-o),h=h.cut(0,y-o),m=y,p=-1)}else for(;s<r.length&&r[s].to<m;)s++;n(h,h.isInline&&!h.isLeaf?a.filter(y=>!y.inline):a.slice(),e.forChild(o,h),p),o=m}})(this.node,this.innerDeco,(d,u,f)=>{d.spec.marks?c.syncToMarks(d.spec.marks,n,e):d.type.side>=0&&!f&&c.syncToMarks(u==this.node.childCount?P.none:this.node.child(u).marks,n,e),c.placeWidget(d,e,r)},(d,u,f,h)=>{let p;c.syncToMarks(d.marks,n,e),c.findNodeMatch(d,u,f,h)||a&&e.state.selection.from>r&&e.state.selection.to<r+d.nodeSize&&(p=c.findIndexWithChild(o.node))>-1&&c.updateNodeAt(d,u,f,p,e)||c.updateNextNode(d,u,f,e,h,r)||c.addNode(d,u,f,e,r),r+=d.nodeSize}),c.syncToMarks([],n,e),this.node.isTextblock&&c.addTextblockHacks(),c.destroyRest(),(c.changed||2==this.dirty)&&(s&&this.protectLocalComposition(e,s),Xo(this.contentDOM,this.children,e),Et&&function pd(i){if("UL"==i.nodeName||"OL"==i.nodeName){let e=i.style.cssText;i.style.cssText=e+"; list-style: square !important",window.getComputedStyle(i),i.style.cssText=e}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:r}=e.state.selection;if(!(e.state.selection instanceof A)||n<t||r>t+this.node.content.size)return null;let o=e.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(this.node.inlineContent){let s=o.nodeValue,a=function hd(i,e,t,n){for(let r=0,o=0;r<i.childCount&&o<=n;){let s=i.child(r++),a=o;if(o+=s.nodeSize,!s.isText)continue;let c=s.text;for(;r<i.childCount;){let d=i.child(r++);if(o+=d.nodeSize,!d.isText)break;c+=d.text}if(o>=t){if(o>=n&&c.slice(n-e.length-a,n-a)==e)return n-e.length;let d=a<n?c.lastIndexOf(e,n-a-1):-1;if(d>=0&&d+e.length+a>=t)return a+d;if(t==n&&c.length>=n+e.length-a&&c.slice(n-a,n-a+e.length)==e)return n}}return-1}(this.node.content,s,n-t,r-t);return a<0?null:{node:o,pos:a,text:s}}return{node:o,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:r}){if(this.getDesc(t))return;let o=t;for(;o.parentNode!=this.contentDOM;o=o.parentNode){for(;o.previousSibling;)o.parentNode.removeChild(o.previousSibling);for(;o.nextSibling;)o.parentNode.removeChild(o.nextSibling);o.pmViewDesc&&(o.pmViewDesc=void 0)}let s=new sd(this,o,t,r);e.input.compositionNodes.push(s),this.children=Oi(this.children,n,n+r.length,e,s)}update(e,t,n,r){return!(3==this.dirty||!e.sameMarkup(this.node)||(this.updateInner(e,t,n,r),0))}updateInner(e,t,n,r){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(Dn(e,this.outerDeco))return;let t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=Uo(this.dom,this.nodeDOM,Ni(this.outerDeco,this.node,t),Ni(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function Go(i,e,t,n,r){Jo(n,e,i);let o=new We(void 0,i,e,t,n,n,n,r,0);return o.contentDOM&&o.updateChildren(r,0),o}class On extends We{constructor(e,t,n,r,o,s,a){super(e,t,n,r,o,null,s,a,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,r){return!(3==this.dirty||0!=this.dirty&&!this.inParent()||!e.sameMarkup(this.node)||(this.updateOuterDeco(t),(0!=this.dirty||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=e,this.dirty=0,0))}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return"characterData"!=e.type&&"selection"!=e.type}slice(e,t,n){let r=this.node.cut(e,t),o=document.createTextNode(r.text);return new On(this.parent,r,this.outerDeco,this.innerDeco,o,o,n)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(0==e||t==this.nodeDOM.nodeValue.length)&&(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class Wo extends en{parseRule(){return{ignore:!0}}matchesHack(e){return 0==this.dirty&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class ld extends We{constructor(e,t,n,r,o,s,a,c,d,u){super(e,t,n,r,o,s,a,d,u),this.spec=c}update(e,t,n,r){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let o=this.spec.update(e,t,n);return o&&this.updateInner(e,t,n,r),o}return!(!this.contentDOM&&!e.isLeaf)&&super.update(e,t,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,r){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function Xo(i,e,t){let n=i.firstChild,r=!1;for(let o=0;o<e.length;o++){let s=e[o],a=s.dom;if(a.parentNode==i){for(;a!=n;)n=Ko(n),r=!0;n=n.nextSibling}else r=!0,i.insertBefore(a,n);if(s instanceof pt){let c=n?n.previousSibling:i.lastChild;Xo(s.contentDOM,s.children,t),n=c?c.nextSibling:i.firstChild}}for(;n;)n=Ko(n),r=!0;r&&t.trackWrites==i&&(t.trackWrites=null)}const tn=function(i){i&&(this.nodeName=i)};tn.prototype=Object.create(null);const ht=[new tn];function Ni(i,e,t){if(0==i.length)return ht;let n=t?ht[0]:new tn,r=[n];for(let o=0;o<i.length;o++){let s=i[o].type.attrs;if(s){s.nodeName&&r.push(n=new tn(s.nodeName));for(let a in s){let c=s[a];null!=c&&(t&&1==r.length&&r.push(n=new tn(e.isInline?"span":"div")),"class"==a?n.class=(n.class?n.class+" ":"")+c:"style"==a?n.style=(n.style?n.style+";":"")+c:"nodeName"!=a&&(n[a]=c))}}}return r}function Uo(i,e,t,n){if(t==ht&&n==ht)return e;let r=e;for(let o=0;o<n.length;o++){let s=n[o],a=t[o];if(o){let c;a&&a.nodeName==s.nodeName&&r!=i&&(c=r.parentNode)&&c.nodeName.toLowerCase()==s.nodeName||(c=document.createElement(s.nodeName),c.pmIsDeco=!0,c.appendChild(r),a=ht[0]),r=c}ad(r,a||ht[0],s)}return r}function ad(i,e,t){for(let n in e)"class"!=n&&"style"!=n&&"nodeName"!=n&&!(n in t)&&i.removeAttribute(n);for(let n in t)"class"!=n&&"style"!=n&&"nodeName"!=n&&t[n]!=e[n]&&i.setAttribute(n,t[n]);if(e.class!=t.class){let n=e.class?e.class.split(" ").filter(Boolean):[],r=t.class?t.class.split(" ").filter(Boolean):[];for(let o=0;o<n.length;o++)-1==r.indexOf(n[o])&&i.classList.remove(n[o]);for(let o=0;o<r.length;o++)-1==n.indexOf(r[o])&&i.classList.add(r[o]);0==i.classList.length&&i.removeAttribute("class")}if(e.style!=t.style){if(e.style){let r,n=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;r=n.exec(e.style);)i.style.removeProperty(r[1])}t.style&&(i.style.cssText+=t.style)}}function Jo(i,e,t){return Uo(i,i,ht,Ni(e,t,1!=i.nodeType))}function Dn(i,e){if(i.length!=e.length)return!1;for(let t=0;t<i.length;t++)if(!i[t].type.eq(e[t].type))return!1;return!0}function Ko(i){let e=i.nextSibling;return i.parentNode.removeChild(i),e}class cd{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function dd(i,e){let t=e,n=t.children.length,r=i.childCount,o=new Map,s=[];e:for(;r>0;){let a;for(;;)if(n){let d=t.children[n-1];if(!(d instanceof pt)){a=d,n--;break}t=d,n=d.children.length}else{if(t==e)break e;n=t.parent.children.indexOf(t),t=t.parent}let c=a.node;if(c){if(c!=i.child(r-1))break;--r,o.set(a,r),s.push(a)}}return{index:r,matched:o,matches:s.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let r=0,o=this.stack.length>>1,s=Math.min(o,e.length);for(;r<s&&(r==o-1?this.top:this.stack[r+1<<1]).matchesMark(e[r])&&!1!==e[r].type.spec.spanning;)r++;for(;r<o;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),o--;for(;o<e.length;){this.stack.push(this.top,this.index+1);let a=-1;for(let c=this.index;c<Math.min(this.index+3,this.top.children.length);c++){let d=this.top.children[c];if(d.matchesMark(e[o])&&!this.isLocked(d.dom)){a=c;break}}if(a>-1)a>this.index&&(this.changed=!0,this.destroyBetween(this.index,a)),this.top=this.top.children[this.index];else{let c=pt.create(this.top,e[o],t,n);this.top.children.splice(this.index,0,c),this.top=c,this.changed=!0}this.index=0,o++}}findNodeMatch(e,t,n,r){let s,o=-1;if(r>=this.preMatch.index&&(s=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&s.matchesNode(e,t,n))o=this.top.children.indexOf(s,this.index);else for(let a=this.index,c=Math.min(this.top.children.length,a+5);a<c;a++){let d=this.top.children[a];if(d.matchesNode(e,t,n)&&!this.preMatch.matched.has(d)){o=a;break}}return!(o<0||(this.destroyBetween(this.index,o),this.index++,0))}updateNodeAt(e,t,n,r,o){let s=this.top.children[r];return 3==s.dirty&&s.dom==s.contentDOM&&(s.dirty=2),!!s.update(e,t,n,o)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let n=e.pmViewDesc;if(n)for(let r=this.index;r<this.top.children.length;r++)if(this.top.children[r]==n)return r;return-1}e=t}}updateNextNode(e,t,n,r,o,s){for(let a=this.index;a<this.top.children.length;a++){let c=this.top.children[a];if(c instanceof We){let d=this.preMatch.matched.get(c);if(null!=d&&d!=o)return!1;let f,u=c.dom,h=this.isLocked(u)&&!(e.isText&&c.node&&c.node.isText&&c.nodeDOM.nodeValue==e.text&&3!=c.dirty&&Dn(t,c.outerDeco));if(!h&&c.update(e,t,n,r))return this.destroyBetween(this.index,a),c.dom!=u&&(this.changed=!0),this.index++,!0;if(!h&&(f=this.recreateWrapper(c,e,t,n,r,s)))return this.destroyBetween(this.index,a),this.top.children[this.index]=f,f.contentDOM&&(f.dirty=2,f.updateChildren(r,s+1),f.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,r,o,s){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!Dn(n,e.outerDeco)||!r.eq(e.innerDeco))return null;let a=We.create(this.top,t,n,r,o,s);if(a.contentDOM){a.children=e.children,e.children=[];for(let c of a.children)c.parent=a}return e.destroy(),a}addNode(e,t,n,r,o){let s=We.create(this.top,e,t,n,r,o);s.contentDOM&&s.updateChildren(r,o+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(e,t,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(!r||!r.matchesWidget(e)||e!=r.widget&&r.widget.type.toDOM.parentNode){let o=new jo(this.top,e,t,n);this.top.children.splice(this.index++,0,o),this.changed=!0}else this.index++}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof pt;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof On)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((q||K)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");let r=new Wo(this.top,[],n,null);t!=this.top?t.children.push(r):t.children.splice(this.index++,0,r),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||1==e.nodeType&&e.contains(this.lock.parentNode))}}function ud(i,e){return i.type.side-e.type.side}function Oi(i,e,t,n,r){let o=[];for(let s=0,a=0;s<i.length;s++){let c=i[s],d=a,u=a+=c.size;d>=t||u<=e?o.push(c):(d<e&&o.push(c.slice(0,e-d,n)),r&&(o.push(r),r=void 0),u>t&&o.push(c.slice(t-d,c.size,n)))}return o}function Di(i,e=null){let t=i.domSelectionRange(),n=i.state.doc;if(!t.focusNode)return null;let r=i.docView.nearestDesc(t.focusNode),o=r&&0==r.size,s=i.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(s<0)return null;let c,d,a=n.resolve(s);if(Nn(t)){for(c=s;r&&!r.node;)r=r.parent;let f=r.node;if(r&&f.isAtom&&v.isSelectable(f)&&r.parent&&(!f.isInline||!function zc(i,e,t){for(let n=0==e,r=e==de(i);n||r;){if(i==t)return!0;let o=j(i);if(!(i=i.parentNode))return!1;n=n&&0==o,r=r&&o==de(i)}}(t.focusNode,t.focusOffset,r.dom))){let h=r.posBefore;d=new v(s==h?a:n.resolve(h))}}else{if(t instanceof i.dom.ownerDocument.defaultView.Selection&&t.rangeCount>1){let f=s,h=s;for(let p=0;p<t.rangeCount;p++){let m=t.getRangeAt(p);f=Math.min(f,i.docView.posFromDOM(m.startContainer,m.startOffset,1)),h=Math.max(h,i.docView.posFromDOM(m.endContainer,m.endOffset,-1))}if(f<0)return null;[c,s]=h==i.state.selection.anchor?[h,f]:[f,h],a=n.resolve(s)}else c=i.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(c<0)return null}let u=n.resolve(c);return d||(d=Ai(i,u,a,"pointer"==e||i.state.selection.head<a.pos&&!o?1:-1)),d}function Yo(i){return i.editable?i.hasFocus():is(i)&&document.activeElement&&document.activeElement.contains(i.dom)}function Re(i,e=!1){let t=i.state.selection;if(es(i,t),Yo(i)){if(!e&&i.input.mouseDown&&i.input.mouseDown.allowDefault&&K){let n=i.domSelectionRange(),r=i.domObserver.currentSelection;if(n.anchorNode&&r.anchorNode&&dt(n.anchorNode,n.anchorOffset,r.anchorNode,r.anchorOffset))return i.input.mouseDown.delayedSelectionSync=!0,void i.domObserver.setCurSelection()}if(i.domObserver.disconnectSelection(),i.cursorWrapper)!function gd(i){let e=i.domSelection(),t=document.createRange();if(!e)return;let n=i.cursorWrapper.dom,r="IMG"==n.nodeName;r?t.setStart(n.parentNode,j(n)+1):t.setStart(n,0),t.collapse(!0),e.removeAllRanges(),e.addRange(t),!r&&!i.state.selection.visible&&te&&je<=11&&(n.disabled=!0,n.disabled=!1)}(i);else{let o,s,{anchor:n,head:r}=t;qo&&!(t instanceof A)&&(t.$from.parent.inlineContent||(o=Qo(i,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(s=Qo(i,t.to))),i.docView.setSelection(n,r,i,e),qo&&(o&&Zo(o),s&&Zo(s)),t.visible?i.dom.classList.remove("ProseMirror-hideselection"):(i.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function md(i){let e=i.dom.ownerDocument;e.removeEventListener("selectionchange",i.input.hideSelectionGuard);let t=i.domSelectionRange(),n=t.anchorNode,r=t.anchorOffset;e.addEventListener("selectionchange",i.input.hideSelectionGuard=()=>{(t.anchorNode!=n||t.anchorOffset!=r)&&(e.removeEventListener("selectionchange",i.input.hideSelectionGuard),setTimeout(()=>{(!Yo(i)||i.state.selection.visible)&&i.dom.classList.remove("ProseMirror-hideselection")},20))})}(i))}i.domObserver.setCurSelection(),i.domObserver.connectSelection()}}const qo=q||K&&Oo<63;function Qo(i,e){let{node:t,offset:n}=i.docView.domFromPos(e,0),r=n<t.childNodes.length?t.childNodes[n]:null,o=n?t.childNodes[n-1]:null;if(q&&r&&"false"==r.contentEditable)return Ii(r);if(!(r&&"false"!=r.contentEditable||o&&"false"!=o.contentEditable)){if(r)return Ii(r);if(o)return Ii(o)}}function Ii(i){return i.contentEditable="true",q&&i.draggable&&(i.draggable=!1,i.wasDraggable=!0),i}function Zo(i){i.contentEditable="false",i.wasDraggable&&(i.draggable=!0,i.wasDraggable=null)}function es(i,e){if(e instanceof v){let t=i.docView.descAt(e.from);t!=i.lastSelectedViewDesc&&(ts(i),t&&t.selectNode(),i.lastSelectedViewDesc=t)}else ts(i)}function ts(i){i.lastSelectedViewDesc&&(i.lastSelectedViewDesc.parent&&i.lastSelectedViewDesc.deselectNode(),i.lastSelectedViewDesc=void 0)}function Ai(i,e,t,n){return i.someProp("createSelectionBetween",r=>r(i,e,t))||A.between(e,t,n)}function ns(i){return!(i.editable&&!i.hasFocus())&&is(i)}function is(i){let e=i.domSelectionRange();if(!e.anchorNode)return!1;try{return i.dom.contains(3==e.anchorNode.nodeType?e.anchorNode.parentNode:e.anchorNode)&&(i.editable||i.dom.contains(3==e.focusNode.nodeType?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function Ri(i,e){let{$anchor:t,$head:n}=i.selection,r=e>0?t.max(n):t.min(n),o=r.parent.inlineContent?r.depth?i.doc.resolve(e>0?r.after():r.before()):null:r;return o&&I.findFrom(o,e)}function Xe(i,e){return i.dispatch(i.state.tr.setSelection(e).scrollIntoView()),!0}function rs(i,e,t){let n=i.state.selection;if(!(n instanceof A)){if(n instanceof v&&n.node.isInline)return Xe(i,new A(e>0?n.$to:n.$from));{let r=Ri(i.state,e);return!!r&&Xe(i,r)}}if(t.indexOf("s")>-1){let{$head:r}=n,o=r.textOffset?null:e<0?r.nodeBefore:r.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let s=i.state.doc.resolve(r.pos+o.nodeSize*(e<0?-1:1));return Xe(i,new A(n.$anchor,s))}if(!n.empty)return!1;if(i.endOfTextblock(e>0?"forward":"backward")){let r=Ri(i.state,e);return!!(r&&r instanceof v)&&Xe(i,r)}if(!(ue&&t.indexOf("m")>-1)){let s,r=n.$head,o=r.textOffset?null:e<0?r.nodeBefore:r.nodeAfter;if(!o||o.isText)return!1;let a=e<0?r.pos-o.nodeSize:r.pos;return!!(o.isAtom||(s=i.docView.descAt(a))&&!s.contentDOM)&&(v.isSelectable(o)?Xe(i,new v(e<0?i.state.doc.resolve(r.pos-o.nodeSize):r)):!!Qt&&Xe(i,new A(i.state.doc.resolve(e<0?a:a+o.nodeSize))))}}function In(i){return 3==i.nodeType?i.nodeValue.length:i.childNodes.length}function nn(i,e){let t=i.pmViewDesc;return t&&0==t.size&&(e<0||i.nextSibling||"BR"!=i.nodeName)}function Nt(i,e){return e<0?function bd(i){let e=i.domSelectionRange(),t=e.focusNode,n=e.focusOffset;if(!t)return;let r,o,s=!1;for(ye&&1==t.nodeType&&n<In(t)&&nn(t.childNodes[n],-1)&&(s=!0);;)if(n>0){if(1!=t.nodeType)break;{let a=t.childNodes[n-1];if(nn(a,-1))r=t,o=--n;else{if(3!=a.nodeType)break;t=a,n=t.nodeValue.length}}}else{if(ss(t))break;{let a=t.previousSibling;for(;a&&nn(a,-1);)r=t.parentNode,o=j(a),a=a.previousSibling;if(a)t=a,n=In(t);else{if(t=t.parentNode,t==i.dom)break;n=0}}}s?Fi(i,t,n):r&&Fi(i,r,o)}(i):function _d(i){let e=i.domSelectionRange(),t=e.focusNode,n=e.focusOffset;if(!t)return;let o,s,r=In(t);for(;;)if(n<r){if(1!=t.nodeType)break;if(!nn(t.childNodes[n],1))break;o=t,s=++n}else{if(ss(t))break;{let a=t.nextSibling;for(;a&&nn(a,1);)o=a.parentNode,s=j(a)+1,a=a.nextSibling;if(a)t=a,n=0,r=In(t);else{if(t=t.parentNode,t==i.dom)break;n=r=0}}}o&&Fi(i,o,s)}(i)}function ss(i){let e=i.pmViewDesc;return e&&e.node&&e.node.isBlock}function Fi(i,e,t){if(3!=e.nodeType){let o,s;(s=function xd(i,e){for(;i&&e==i.childNodes.length&&!qt(i);)e=j(i)+1,i=i.parentNode;for(;i&&e<i.childNodes.length;){let t=i.childNodes[e];if(3==t.nodeType)return t;if(1==t.nodeType&&"false"==t.contentEditable)break;i=t,e=0}}(e,t))?(e=s,t=0):(o=function Cd(i,e){for(;i&&!e&&!qt(i);)e=j(i),i=i.parentNode;for(;i&&e;){let t=i.childNodes[e-1];if(3==t.nodeType)return t;if(1==t.nodeType&&"false"==t.contentEditable)break;e=(i=t).childNodes.length}}(e,t))&&(e=o,t=o.nodeValue.length)}let n=i.domSelection();if(!n)return;if(Nn(n)){let o=document.createRange();o.setEnd(e,t),o.setStart(e,t),n.removeAllRanges(),n.addRange(o)}else n.extend&&n.extend(e,t);i.domObserver.setCurSelection();let{state:r}=i;setTimeout(()=>{i.state==r&&Re(i)},50)}function ls(i,e){let t=i.state.doc.resolve(e);if(!K&&!jc&&t.parent.inlineContent){let r=i.coordsAtPos(e);if(e>t.start()){let o=i.coordsAtPos(e-1),s=(o.top+o.bottom)/2;if(s>r.top&&s<r.bottom&&Math.abs(o.left-r.left)>1)return o.left<r.left?"ltr":"rtl"}if(e<t.end()){let o=i.coordsAtPos(e+1),s=(o.top+o.bottom)/2;if(s>r.top&&s<r.bottom&&Math.abs(o.left-r.left)>1)return o.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(i.dom).direction?"rtl":"ltr"}function as(i,e,t){let n=i.state.selection;if(n instanceof A&&!n.empty||t.indexOf("s")>-1||ue&&t.indexOf("m")>-1)return!1;let{$from:r,$to:o}=n;if(!r.parent.inlineContent||i.endOfTextblock(e<0?"up":"down")){let s=Ri(i.state,e);if(s&&s instanceof v)return Xe(i,s)}if(!r.parent.inlineContent){let s=e<0?r:o,a=n instanceof se?I.near(s,e):I.findFrom(s,e);return!!a&&Xe(i,a)}return!1}function cs(i,e){if(!(i.state.selection instanceof A))return!0;let{$head:t,$anchor:n,empty:r}=i.state.selection;if(!t.sameParent(n))return!0;if(!r)return!1;if(i.endOfTextblock(e>0?"forward":"backward"))return!0;let o=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(o&&!o.isText){let s=i.state.tr;return e<0?s.delete(t.pos-o.nodeSize,t.pos):s.delete(t.pos,t.pos+o.nodeSize),i.dispatch(s),!0}return!1}function ds(i,e,t){i.domObserver.stop(),e.contentEditable=t,i.domObserver.start()}function Pi(i,e){i.someProp("transformCopied",p=>{e=p(e,i)});let t=[],{content:n,openStart:r,openEnd:o}=e;for(;r>1&&o>1&&1==n.childCount&&1==n.firstChild.childCount;){r--,o--;let p=n.firstChild;t.push(p.type.name,p.attrs!=p.type.defaultAttrs?p.attrs:null),n=p.content}let s=i.someProp("clipboardSerializer")||at.fromSchema(i.state.schema),a=bs(),c=a.createElement("div");c.appendChild(s.serializeFragment(n,{document:a}));let u,d=c.firstChild,f=0;for(;d&&1==d.nodeType&&(u=gs[d.nodeName.toLowerCase()]);){for(let p=u.length-1;p>=0;p--){let m=a.createElement(u[p]);for(;c.firstChild;)m.appendChild(c.firstChild);c.appendChild(m),f++}d=c.firstChild}return d&&1==d.nodeType&&d.setAttribute("data-pm-slice",`${r} ${o}${f?` -${f}`:""} ${JSON.stringify(t)}`),{dom:c,text:i.someProp("clipboardTextSerializer",p=>p(e,i))||e.content.textBetween(0,e.content.size,"\n\n"),slice:e}}function us(i,e,t,n,r){let s,a,o=r.parent.type.spec.code;if(!t&&!e)return null;let c=e&&(n||o||!t);if(c){if(i.someProp("transformPastedText",h=>{e=h(e,o||n,i)}),o)return e?new x(_.from(i.state.schema.text(e.replace(/\r\n?/g,"\n"))),0,0):x.empty;let f=i.someProp("clipboardTextParser",h=>h(e,r,n,i));if(f)a=f;else{let h=r.marks(),{schema:p}=i.state,m=at.fromSchema(p);s=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(g=>{let y=s.appendChild(document.createElement("p"));g&&y.appendChild(m.serializeNode(p.text(g,h)))})}}else i.someProp("transformPastedHTML",f=>{t=f(t,i)}),s=function Sd(i){let e=/^(\s*<meta [^>]*>)*/.exec(i);e&&(i=i.slice(e[0].length));let r,t=bs().createElement("div"),n=/<([a-z][^>\s]+)/i.exec(i);if((r=n&&gs[n[1].toLowerCase()])&&(i=r.map(o=>"<"+o+">").join("")+i+r.map(o=>"</"+o+">").reverse().join("")),t.innerHTML=function Ed(i){let e=window.trustedTypes;return e?(Vi||(Vi=e.defaultPolicy||e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t})),Vi.createHTML(i)):i}(i),r)for(let o=0;o<r.length;o++)t=t.querySelector(r[o])||t;return t}(t),Qt&&function Nd(i){let e=i.querySelectorAll(K?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let n=e[t];1==n.childNodes.length&&"\xa0"==n.textContent&&n.parentNode&&n.parentNode.replaceChild(i.ownerDocument.createTextNode(" "),n)}}(s);let d=s&&s.querySelector("[data-pm-slice]"),u=d&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(d.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let f=+u[3];f>0;f--){let h=s.firstChild;for(;h&&1!=h.nodeType;)h=h.nextSibling;if(!h)break;s=h}if(a||(a=(i.someProp("clipboardParser")||i.someProp("domParser")||lt.fromSchema(i.state.schema)).parseSlice(s,{preserveWhitespace:!(!c&&!u),context:r,ruleFromNode:h=>"BR"!=h.nodeName||h.nextSibling||!h.parentNode||Td.test(h.parentNode.nodeName)?null:{ignore:!0}})),u)a=function Od(i,e){if(!i.size)return i;let n,t=i.content.firstChild.type.schema;try{n=JSON.parse(e)}catch{return i}let{content:r,openStart:o,openEnd:s}=i;for(let a=n.length-2;a>=0;a-=2){let c=t.nodes[n[a]];if(!c||c.hasRequiredAttrs())break;r=_.from(c.create(n[a+1],r)),o++,s++}return new x(r,o,s)}(ms(a,+u[1],+u[2]),u[4]);else if(a=x.maxOpen(function Md(i,e){if(i.childCount<2)return i;for(let t=e.depth;t>=0;t--){let o,r=e.node(t).contentMatchAt(e.index(t)),s=[];if(i.forEach(a=>{if(!s)return;let d,c=r.findWrapping(a.type);if(!c)return s=null;if(d=s.length&&o.length&&ps(c,o,a,s[s.length-1],0))s[s.length-1]=d;else{s.length&&(s[s.length-1]=hs(s[s.length-1],o.length));let u=fs(a,c);s.push(u),r=r.matchType(u.type),o=c}}),s)return _.from(s)}return i}(a.content,r),!0),a.openStart||a.openEnd){let f=0,h=0;for(let p=a.content.firstChild;f<a.openStart&&!p.type.spec.isolating;f++,p=p.firstChild);for(let p=a.content.lastChild;h<a.openEnd&&!p.type.spec.isolating;h++,p=p.lastChild);a=ms(a,f,h)}return i.someProp("transformPasted",f=>{a=f(a,i)}),a}const Td=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function fs(i,e,t=0){for(let n=e.length-1;n>=t;n--)i=e[n].create(null,_.from(i));return i}function ps(i,e,t,n,r){if(r<i.length&&r<e.length&&i[r]==e[r]){let o=ps(i,e,t,n.lastChild,r+1);if(o)return n.copy(n.content.replaceChild(n.childCount-1,o));if(n.contentMatchAt(n.childCount).matchType(r==i.length-1?t.type:i[r+1]))return n.copy(n.content.append(_.from(fs(t,i,r+1))))}}function hs(i,e){if(0==e)return i;let t=i.content.replaceChild(i.childCount-1,hs(i.lastChild,e-1)),n=i.contentMatchAt(i.childCount).fillBefore(_.empty,!0);return i.copy(t.append(n))}function Li(i,e,t,n,r,o){let s=e<0?i.firstChild:i.lastChild,a=s.content;return i.childCount>1&&(o=0),r<n-1&&(a=Li(a,e,t,n,r+1,o)),r>=t&&(a=e<0?s.contentMatchAt(0).fillBefore(a,o<=r).append(a):a.append(s.contentMatchAt(s.childCount).fillBefore(_.empty,!0))),i.replaceChild(e<0?0:i.childCount-1,s.copy(a))}function ms(i,e,t){return e<i.openStart&&(i=new x(Li(i.content,-1,e,i.openStart,0,i.openEnd),e,i.openEnd)),t<i.openEnd&&(i=new x(Li(i.content,1,t,i.openEnd,0,0),i.openStart,t)),i}const gs={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let ys=null;function bs(){return ys||(ys=document.implementation.createHTMLDocument("title"))}let Vi=null;const Q={},Z={},Dd={touchstart:!0,touchmove:!0};class Id{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function Ue(i,e){i.input.lastSelectionOrigin=e,i.input.lastSelectionTime=Date.now()}function Bi(i){i.someProp("handleDOMEvents",e=>{for(let t in e)i.input.eventHandlers[t]||i.dom.addEventListener(t,i.input.eventHandlers[t]=n=>zi(i,n))})}function zi(i,e){return i.someProp("handleDOMEvents",t=>{let n=t[e.type];return!!n&&(n(i,e)||e.defaultPrevented)})}function Fd(i,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=i.dom;t=t.parentNode)if(!t||11==t.nodeType||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function An(i){return{left:i.clientX,top:i.clientY}}function $i(i,e,t,n,r){if(-1==n)return!1;let o=i.state.doc.resolve(n);for(let s=o.depth+1;s>0;s--)if(i.someProp(e,a=>s>o.depth?a(i,t,o.nodeAfter,o.before(s),r,!0):a(i,t,o.node(s),o.before(s),r,!1)))return!0;return!1}function Ot(i,e,t){if(i.focused||i.focus(),i.state.selection.eq(e))return;let n=i.state.tr.setSelection(e);"pointer"==t&&n.setMeta("pointer",!0),i.dispatch(n)}function $d(i,e,t,n){return $i(i,"handleDoubleClickOn",e,t,n)||i.someProp("handleDoubleClick",r=>r(i,e,n))}function Hd(i,e,t,n){return $i(i,"handleTripleClickOn",e,t,n)||i.someProp("handleTripleClick",r=>r(i,e,n))||function jd(i,e,t){if(0!=t.button)return!1;let n=i.state.doc;if(-1==e)return!!n.inlineContent&&(Ot(i,A.create(n,0,n.content.size),"pointer"),!0);let r=n.resolve(e);for(let o=r.depth+1;o>0;o--){let s=o>r.depth?r.nodeAfter:r.node(o),a=r.before(o);if(s.inlineContent)Ot(i,A.create(n,a+1,a+1+s.content.size),"pointer");else{if(!v.isSelectable(s))continue;Ot(i,v.create(n,a),"pointer")}return!0}}(i,t,n)}function Hi(i){return Rn(i)}Z.keydown=(i,e)=>{let t=e;if(i.input.shiftKey=16==t.keyCode||t.shiftKey,!xs(i,t)&&(i.input.lastKeyCode=t.keyCode,i.input.lastKeyCodeTime=Date.now(),!Ie||!K||13!=t.keyCode))if(229!=t.keyCode&&i.domObserver.forceFlush(),!Et||13!=t.keyCode||t.ctrlKey||t.altKey||t.metaKey)i.someProp("handleKeyDown",n=>n(i,t))||function vd(i,e){let t=e.keyCode,n=function kd(i){let e="";return i.ctrlKey&&(e+="c"),i.metaKey&&(e+="m"),i.altKey&&(e+="a"),i.shiftKey&&(e+="s"),e}(e);if(8==t||ue&&72==t&&"c"==n)return cs(i,-1)||Nt(i,-1);if(46==t&&!e.shiftKey||ue&&68==t&&"c"==n)return cs(i,1)||Nt(i,1);if(13==t||27==t)return!0;if(37==t||ue&&66==t&&"c"==n){let r=37==t?"ltr"==ls(i,i.state.selection.from)?-1:1:-1;return rs(i,r,n)||Nt(i,r)}if(39==t||ue&&70==t&&"c"==n){let r=39==t?"ltr"==ls(i,i.state.selection.from)?1:-1:1;return rs(i,r,n)||Nt(i,r)}return 38==t||ue&&80==t&&"c"==n?as(i,-1,n)||Nt(i,-1):40==t||ue&&78==t&&"c"==n?function wd(i){if(!q||i.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=i.domSelectionRange();if(e&&1==e.nodeType&&0==t&&e.firstChild&&"false"==e.firstChild.contentEditable){let n=e.firstChild;ds(i,n,"true"),setTimeout(()=>ds(i,n,"false"),20)}return!1}(i)||as(i,1,n)||Nt(i,1):n==(ue?"m":"c")&&(66==t||73==t||89==t||90==t)}(i,t)?t.preventDefault():Ue(i,"key");else{let n=Date.now();i.input.lastIOSEnter=n,i.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{i.input.lastIOSEnter==n&&(i.someProp("handleKeyDown",r=>r(i,ut(13,"Enter"))),i.input.lastIOSEnter=0)},200)}},Z.keyup=(i,e)=>{16==e.keyCode&&(i.input.shiftKey=!1)},Z.keypress=(i,e)=>{let t=e;if(xs(i,t)||!t.charCode||t.ctrlKey&&!t.altKey||ue&&t.metaKey)return;if(i.someProp("handleKeyPress",r=>r(i,t)))return void t.preventDefault();let n=i.state.selection;if(!(n instanceof A&&n.$from.sameParent(n.$to))){let r=String.fromCharCode(t.charCode),o=()=>i.state.tr.insertText(r).scrollIntoView();!/[\r\n]/.test(r)&&!i.someProp("handleTextInput",s=>s(i,n.$from.pos,n.$to.pos,r,o))&&i.dispatch(o()),t.preventDefault()}};const _s=ue?"metaKey":"ctrlKey";Q.mousedown=(i,e)=>{let t=e;i.input.shiftKey=t.shiftKey;let n=Hi(i),r=Date.now(),o="singleClick";r-i.input.lastClick.time<500&&function Ld(i,e){let t=e.x-i.clientX,n=e.y-i.clientY;return t*t+n*n<100}(t,i.input.lastClick)&&!t[_s]&&i.input.lastClick.button==t.button&&("singleClick"==i.input.lastClick.type?o="doubleClick":"doubleClick"==i.input.lastClick.type&&(o="tripleClick")),i.input.lastClick={time:r,x:t.clientX,y:t.clientY,type:o,button:t.button};let s=i.posAtCoords(An(t));s&&("singleClick"==o?(i.input.mouseDown&&i.input.mouseDown.done(),i.input.mouseDown=new Gd(i,s,t,!!n)):("doubleClick"==o?$d:Hd)(i,s.pos,s.inside,t)?t.preventDefault():Ue(i,"pointer"))};class Gd{constructor(e,t,n,r){let o,s;if(this.view=e,this.pos=t,this.event=n,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[_s],this.allowDefault=n.shiftKey,t.inside>-1)o=e.state.doc.nodeAt(t.inside),s=t.inside;else{let u=e.state.doc.resolve(t.pos);o=u.parent,s=u.depth?u.before():0}const a=r?null:n.target,c=a?e.docView.nearestDesc(a,!0):null;this.target=c&&1==c.dom.nodeType?c.dom:null;let{selection:d}=e.state;(0==n.button&&o.type.spec.draggable&&!1!==o.type.spec.selectable||d instanceof v&&d.from<=s&&d.to>s)&&(this.mightDrag={node:o,pos:s,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!ye||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),Ue(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>Re(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(An(e))),this.updateAllowDefault(e),this.allowDefault||!t?Ue(this.view,"pointer"):function zd(i,e,t,n,r){return $i(i,"handleClickOn",e,t,n)||i.someProp("handleClick",o=>o(i,e,n))||(r?function Bd(i,e){if(-1==e)return!1;let n,r,t=i.state.selection;t instanceof v&&(n=t.node);let o=i.state.doc.resolve(e);for(let s=o.depth+1;s>0;s--){let a=s>o.depth?o.nodeAfter:o.node(s);if(v.isSelectable(a)){r=n&&t.$from.depth>0&&s>=t.$from.depth&&o.before(t.$from.depth+1)==t.$from.pos?o.before(t.$from.depth):o.before(s);break}}return null!=r&&(Ot(i,v.create(i.state.doc,r),"pointer"),!0)}(i,t):function Vd(i,e){if(-1==e)return!1;let t=i.state.doc.resolve(e),n=t.nodeAfter;return!!(n&&n.isAtom&&v.isSelectable(n))&&(Ot(i,new v(t),"pointer"),!0)}(i,t))}(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():0==e.button&&(this.flushed||q&&this.mightDrag&&!this.mightDrag.node.isAtom||K&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(Ot(this.view,I.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):Ue(this.view,"pointer")}move(e){this.updateAllowDefault(e),Ue(this.view,"pointer"),0==e.buttons&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function xs(i,e){return!!i.composing||!!(q&&Math.abs(e.timeStamp-i.input.compositionEndedAt)<500)&&(i.input.compositionEndedAt=-2e8,!0)}Q.touchstart=i=>{i.input.lastTouch=Date.now(),Hi(i),Ue(i,"pointer")},Q.touchmove=i=>{i.input.lastTouch=Date.now(),Ue(i,"pointer")},Q.contextmenu=i=>Hi(i);const Wd=Ie?5e3:-1;function Cs(i,e){clearTimeout(i.input.composingTimeout),e>-1&&(i.input.composingTimeout=setTimeout(()=>Rn(i),e))}function ws(i){for(i.composing&&(i.input.composing=!1,i.input.compositionEndedAt=function Ud(){let i=document.createEvent("Event");return i.initEvent("event",!0,!0),i.timeStamp}());i.input.compositionNodes.length>0;)i.input.compositionNodes.pop().markParentsDirty()}function Rn(i,e=!1){if(!(Ie&&i.domObserver.flushingSoon>=0)){if(i.domObserver.forceFlush(),ws(i),e||i.docView&&i.docView.dirty){let t=Di(i),n=i.state.selection;return t&&!t.eq(n)?i.dispatch(i.state.tr.setSelection(t)):!i.markCursor&&!e||n.$from.node(n.$from.sharedDepth(n.to)).inlineContent?i.updateState(i.state):i.dispatch(i.state.tr.deleteSelection()),!0}return!1}}Z.compositionstart=Z.compositionupdate=i=>{if(!i.composing){i.domObserver.flush();let{state:e}=i,t=e.selection.$to;if(e.selection instanceof A&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(n=>!1===n.type.spec.inclusive)))i.markCursor=i.state.storedMarks||t.marks(),Rn(i,!0),i.markCursor=null;else if(Rn(i,!e.selection.empty),ye&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let n=i.domSelectionRange();for(let r=n.focusNode,o=n.focusOffset;r&&1==r.nodeType&&0!=o;){let s=o<0?r.lastChild:r.childNodes[o-1];if(!s)break;if(3==s.nodeType){let a=i.domSelection();a&&a.collapse(s,s.nodeValue.length);break}r=s,o=-1}}i.input.composing=!0}Cs(i,Wd)},Z.compositionend=(i,e)=>{i.composing&&(i.input.composing=!1,i.input.compositionEndedAt=e.timeStamp,i.input.compositionPendingChanges=i.domObserver.pendingRecords().length?i.input.compositionID:0,i.input.compositionNode=null,i.input.compositionPendingChanges&&Promise.resolve().then(()=>i.domObserver.flush()),i.input.compositionID++,Cs(i,20))};const rn=te&&je<15||Et&&Gc<604;function on(i,e,t,n,r){let o=us(i,e,t,n,i.state.selection.$from);if(i.someProp("handlePaste",c=>c(i,r,o||x.empty)))return!0;if(!o)return!1;let s=function Kd(i){return 0==i.openStart&&0==i.openEnd&&1==i.content.childCount?i.content.firstChild:null}(o),a=s?i.state.tr.replaceSelectionWith(s,n):i.state.tr.replaceSelection(o);return i.dispatch(a.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function ks(i){let e=i.getData("text/plain")||i.getData("Text");if(e)return e;let t=i.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}Q.copy=Z.cut=(i,e)=>{let t=e,n=i.state.selection,r="cut"==t.type;if(n.empty)return;let o=rn?null:t.clipboardData,s=n.content(),{dom:a,text:c}=Pi(i,s);o?(t.preventDefault(),o.clearData(),o.setData("text/html",a.innerHTML),o.setData("text/plain",c)):function Jd(i,e){if(!i.dom.parentNode)return;let t=i.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let n=getSelection(),r=document.createRange();r.selectNodeContents(e),i.dom.blur(),n.removeAllRanges(),n.addRange(r),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),i.focus()},50)}(i,a),r&&i.dispatch(i.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},Z.paste=(i,e)=>{let t=e;if(i.composing&&!Ie)return;let n=rn?null:t.clipboardData,r=i.input.shiftKey&&45!=i.input.lastKeyCode;n&&on(i,ks(n),n.getData("text/html"),r,t)?t.preventDefault():function Yd(i,e){if(!i.dom.parentNode)return;let t=i.input.shiftKey||i.state.selection.$from.parent.type.spec.code,n=i.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(n.contentEditable="true"),n.style.cssText="position: fixed; left: -10000px; top: 10px",n.focus();let r=i.input.shiftKey&&45!=i.input.lastKeyCode;setTimeout(()=>{i.focus(),n.parentNode&&n.parentNode.removeChild(n),t?on(i,n.value,null,r,e):on(i,n.textContent,n.innerHTML,r,e)},50)}(i,t)};class vs{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}const qd=ue?"altKey":"ctrlKey";function Ts(i,e){return i.someProp("dragCopies",n=>!n(e))??!e[qd]}Q.dragstart=(i,e)=>{let t=e,n=i.input.mouseDown;if(n&&n.done(),!t.dataTransfer)return;let s,r=i.state.selection,o=r.empty?null:i.posAtCoords(An(t));if(!(o&&o.pos>=r.from&&o.pos<=(r instanceof v?r.to-1:r.to)))if(n&&n.mightDrag)s=v.create(i.state.doc,n.mightDrag.pos);else if(t.target&&1==t.target.nodeType){let f=i.docView.nearestDesc(t.target,!0);f&&f.node.type.spec.draggable&&f!=i.docView&&(s=v.create(i.state.doc,f.posBefore))}let a=(s||i.state.selection).content(),{dom:c,text:d,slice:u}=Pi(i,a);(!t.dataTransfer.files.length||!K||Oo>120)&&t.dataTransfer.clearData(),t.dataTransfer.setData(rn?"Text":"text/html",c.innerHTML),t.dataTransfer.effectAllowed="copyMove",rn||t.dataTransfer.setData("text/plain",d),i.dragging=new vs(u,Ts(i,t),s)},Q.dragend=i=>{let e=i.dragging;window.setTimeout(()=>{i.dragging==e&&(i.dragging=null)},50)},Z.dragover=Z.dragenter=(i,e)=>e.preventDefault(),Z.drop=(i,e)=>{let t=e,n=i.dragging;if(i.dragging=null,!t.dataTransfer)return;let r=i.posAtCoords(An(t));if(!r)return;let o=i.state.doc.resolve(r.pos),s=n&&n.slice;s?i.someProp("transformPasted",m=>{s=m(s,i)}):s=us(i,ks(t.dataTransfer),rn?null:t.dataTransfer.getData("text/html"),!1,o);let a=!(!n||!Ts(i,t));if(i.someProp("handleDrop",m=>m(i,t,s||x.empty,a)))return void t.preventDefault();if(!s)return;t.preventDefault();let c=s?function ic(i,e,t){let n=i.resolve(e);if(!t.content.size)return e;let r=t.content;for(let o=0;o<t.openStart;o++)r=r.firstChild.content;for(let o=1;o<=(0==t.openStart&&t.size?2:1);o++)for(let s=n.depth;s>=0;s--){let a=s==n.depth?0:n.pos<=(n.start(s+1)+n.end(s+1))/2?-1:1,c=n.index(s)+(a>0?1:0),d=n.node(s),u=!1;if(1==o)u=d.canReplace(c,c,r);else{let f=d.contentMatchAt(c).findWrapping(r.firstChild.type);u=f&&d.canReplaceWith(c,c,f[0])}if(u)return 0==a?n.pos:a<0?n.before(s+1):n.after(s+1)}return null}(i.state.doc,o.pos,s):o.pos;null==c&&(c=o.pos);let d=i.state.tr;if(a){let{node:m}=n;m?m.replace(d):d.deleteSelection()}let u=d.mapping.map(c),f=0==s.openStart&&0==s.openEnd&&1==s.content.childCount,h=d.doc;if(f?d.replaceRangeWith(u,u,s.content.firstChild):d.replaceRange(u,u,s),d.doc.eq(h))return;let p=d.doc.resolve(u);if(f&&v.isSelectable(s.content.firstChild)&&p.nodeAfter&&p.nodeAfter.sameMarkup(s.content.firstChild))d.setSelection(new v(p));else{let m=d.mapping.map(c);d.mapping.maps[d.mapping.maps.length-1].forEach((g,y,b,C)=>m=C),d.setSelection(Ai(i,p,d.doc.resolve(m)))}i.focus(),i.dispatch(d.setMeta("uiEvent","drop"))},Q.focus=i=>{i.input.lastFocus=Date.now(),i.focused||(i.domObserver.stop(),i.dom.classList.add("ProseMirror-focused"),i.domObserver.start(),i.focused=!0,setTimeout(()=>{i.docView&&i.hasFocus()&&!i.domObserver.currentSelection.eq(i.domSelectionRange())&&Re(i)},20))},Q.blur=(i,e)=>{let t=e;i.focused&&(i.domObserver.stop(),i.dom.classList.remove("ProseMirror-focused"),i.domObserver.start(),t.relatedTarget&&i.dom.contains(t.relatedTarget)&&i.domObserver.currentSelection.clear(),i.focused=!1)},Q.beforeinput=(i,e)=>{if(K&&Ie&&"deleteContentBackward"==e.inputType){i.domObserver.flushSoon();let{domChangeCount:n}=i.input;setTimeout(()=>{if(i.input.domChangeCount!=n||(i.dom.blur(),i.focus(),i.someProp("handleKeyDown",o=>o(i,ut(8,"Backspace")))))return;let{$cursor:r}=i.state.selection;r&&r.pos>0&&i.dispatch(i.state.tr.delete(r.pos-1,r.pos).scrollIntoView())},50)}};for(let i in Z)Q[i]=Z[i];function sn(i,e){if(i==e)return!0;for(let t in i)if(i[t]!==e[t])return!1;for(let t in e)if(!(t in i))return!1;return!0}class Fn{constructor(e,t){this.toDOM=e,this.spec=t||mt,this.side=this.spec.side||0}map(e,t,n,r){let{pos:o,deleted:s}=e.mapResult(t.from+r,this.side<0?-1:1);return s?null:new pe(o-n,o-n,this)}valid(){return!0}eq(e){return this==e||e instanceof Fn&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&sn(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class Je{constructor(e,t){this.attrs=e,this.spec=t||mt}map(e,t,n,r){let o=e.map(t.from+r,this.spec.inclusiveStart?-1:1)-n,s=e.map(t.to+r,this.spec.inclusiveEnd?1:-1)-n;return o>=s?null:new pe(o,s,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof Je&&sn(this.attrs,e.attrs)&&sn(this.spec,e.spec)}static is(e){return e.type instanceof Je}destroy(){}}class ji{constructor(e,t){this.attrs=e,this.spec=t||mt}map(e,t,n,r){let o=e.mapResult(t.from+r,1);if(o.deleted)return null;let s=e.mapResult(t.to+r,-1);return s.deleted||s.pos<=o.pos?null:new pe(o.pos-n,s.pos-n,this)}valid(e,t){let o,{index:n,offset:r}=e.content.findIndex(t.from);return r==t.from&&!(o=e.child(n)).isText&&r+o.nodeSize==t.to}eq(e){return this==e||e instanceof ji&&sn(this.attrs,e.attrs)&&sn(this.spec,e.spec)}destroy(){}}class pe{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new pe(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new pe(e,e,new Fn(t,n))}static inline(e,t,n,r){return new pe(e,t,new Je(n,r))}static node(e,t,n,r){return new pe(e,t,new ji(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof Je}get widget(){return this.type instanceof Fn}}const Dt=[],mt={};class B{constructor(e,t){this.local=e.length?e:Dt,this.children=t.length?t:Dt}static create(e,t){return t.length?Pn(t,e,0,mt):Y}find(e,t,n){let r=[];return this.findInner(e??0,t??1e9,r,0,n),r}findInner(e,t,n,r,o){for(let s=0;s<this.local.length;s++){let a=this.local[s];a.from<=t&&a.to>=e&&(!o||o(a.spec))&&n.push(a.copy(a.from+r,a.to+r))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<t&&this.children[s+1]>e){let a=this.children[s]+1;this.children[s+2].findInner(e-a,t-a,n,r+a,o)}}map(e,t,n){return this==Y||0==e.maps.length?this:this.mapInner(e,t,0,0,n||mt)}mapInner(e,t,n,r,o){let s;for(let a=0;a<this.local.length;a++){let c=this.local[a].map(e,n,r);c&&c.type.valid(t,c)?(s||(s=[])).push(c):o.onRemove&&o.onRemove(this.local[a].spec)}return this.children.length?function Qd(i,e,t,n,r,o,s){let a=i.slice();for(let d=0,u=o;d<t.maps.length;d++){let f=0;t.maps[d].forEach((h,p,m,g)=>{let y=g-m-(p-h);for(let b=0;b<a.length;b+=3){let C=a[b+1];if(C<0||h>C+u-f)continue;let k=a[b]+u-f;p>=k?a[b+1]=h<=k?-2:-1:h>=u&&y&&(a[b]+=y,a[b+1]+=y)}f+=y}),u=t.maps[d].map(u,-1)}let c=!1;for(let d=0;d<a.length;d+=3)if(a[d+1]<0){if(-2==a[d+1]){c=!0,a[d+1]=-1;continue}let u=t.map(i[d]+o),f=u-r;if(f<0||f>=n.content.size){c=!0;continue}let p=t.map(i[d+1]+o,-1)-r,{index:m,offset:g}=n.content.findIndex(f),y=n.maybeChild(m);if(y&&g==f&&g+y.nodeSize==p){let b=a[d+2].mapInner(t,y,u+1,i[d]+o+1,s);b!=Y?(a[d]=f,a[d+1]=p,a[d+2]=b):(a[d+1]=-2,c=!0)}else c=!0}if(c){let d=function Zd(i,e,t,n,r,o,s){function a(c,d){for(let u=0;u<c.local.length;u++){let f=c.local[u].map(n,r,d);f?t.push(f):s.onRemove&&s.onRemove(c.local[u].spec)}for(let u=0;u<c.children.length;u+=3)a(c.children[u+2],c.children[u]+d+1)}for(let c=0;c<i.length;c+=3)-1==i[c+1]&&a(i[c+2],e[c]+o+1);return t}(a,i,e,t,r,o,s),u=Pn(d,n,0,s);e=u.local;for(let f=0;f<a.length;f+=3)a[f+1]<0&&(a.splice(f,3),f-=3);for(let f=0,h=0;f<u.children.length;f+=3){let p=u.children[f];for(;h<a.length&&a[h]<p;)h+=3;a.splice(h,0,u.children[f],u.children[f+1],u.children[f+2])}}return new B(e.sort(gt),a)}(this.children,s||[],e,t,n,r,o):s?new B(s.sort(gt),Dt):Y}add(e,t){return t.length?this==Y?B.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let r,o=0;e.forEach((a,c)=>{let u,d=c+n;if(u=Es(t,a,d)){for(r||(r=this.children.slice());o<r.length&&r[o]<c;)o+=3;r[o]==c?r[o+2]=r[o+2].addInner(a,u,d+1):r.splice(o,0,c,c+a.nodeSize,Pn(u,a,d+1,mt)),o+=3}});let s=Ms(o?Ss(t):t,-n);for(let a=0;a<s.length;a++)s[a].type.valid(e,s[a])||s.splice(a--,1);return new B(s.length?this.local.concat(s).sort(gt):this.local,r||this.children)}remove(e){return 0==e.length||this==Y?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,r=this.local;for(let o=0;o<n.length;o+=3){let s,a=n[o]+t,c=n[o+1]+t;for(let f,u=0;u<e.length;u++)(f=e[u])&&f.from>a&&f.to<c&&(e[u]=null,(s||(s=[])).push(f));if(!s)continue;n==this.children&&(n=this.children.slice());let d=n[o+2].removeInner(s,a+1);d!=Y?n[o+2]=d:(n.splice(o,3),o-=3)}if(r.length)for(let s,o=0;o<e.length;o++)if(s=e[o])for(let a=0;a<r.length;a++)r[a].eq(s,t)&&(r==this.local&&(r=this.local.slice()),r.splice(a--,1));return n==this.children&&r==this.local?this:r.length||n.length?new B(r,n):Y}forChild(e,t){if(this==Y)return this;if(t.isLeaf)return B.empty;let n,r;for(let a=0;a<this.children.length;a+=3)if(this.children[a]>=e){this.children[a]==e&&(n=this.children[a+2]);break}let o=e+1,s=o+t.content.size;for(let a=0;a<this.local.length;a++){let c=this.local[a];if(c.from<s&&c.to>o&&c.type instanceof Je){let d=Math.max(o,c.from)-o,u=Math.min(s,c.to)-o;d<u&&(r||(r=[])).push(c.copy(d,u))}}if(r){let a=new B(r.sort(gt),Dt);return n?new Ke([a,n]):a}return n||Y}eq(e){if(this==e)return!0;if(!(e instanceof B)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return Gi(this.localsInner(e))}localsInner(e){if(this==Y)return Dt;if(e.inlineContent||!this.local.some(Je.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof Je||t.push(this.local[n]);return t}forEachSet(e){e(this)}}B.empty=new B([],[]),B.removeOverlap=Gi;const Y=B.empty;class Ke{constructor(e){this.members=e}map(e,t){const n=this.members.map(r=>r.map(e,t,mt));return Ke.from(n)}forChild(e,t){if(t.isLeaf)return B.empty;let n=[];for(let r=0;r<this.members.length;r++){let o=this.members[r].forChild(e,t);o!=Y&&(o instanceof Ke?n=n.concat(o.members):n.push(o))}return Ke.from(n)}eq(e){if(!(e instanceof Ke)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let r=0;r<this.members.length;r++){let o=this.members[r].localsInner(e);if(o.length)if(t){n&&(t=t.slice(),n=!1);for(let s=0;s<o.length;s++)t.push(o[s])}else t=o}return t?Gi(n?t:t.sort(gt)):Dt}static from(e){switch(e.length){case 0:return Y;case 1:return e[0];default:return new Ke(e.every(t=>t instanceof B)?e:e.reduce((t,n)=>t.concat(n instanceof B?n:n.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function Ms(i,e){if(!e||!i.length)return i;let t=[];for(let n=0;n<i.length;n++){let r=i[n];t.push(new pe(r.from+e,r.to+e,r.type))}return t}function Es(i,e,t){if(e.isLeaf)return null;let n=t+e.nodeSize,r=null;for(let s,o=0;o<i.length;o++)(s=i[o])&&s.from>t&&s.to<n&&((r||(r=[])).push(s),i[o]=null);return r}function Ss(i){let e=[];for(let t=0;t<i.length;t++)null!=i[t]&&e.push(i[t]);return e}function Pn(i,e,t,n){let r=[],o=!1;e.forEach((a,c)=>{let d=Es(i,a,c+t);if(d){o=!0;let u=Pn(d,a,t+c+1,n);u!=Y&&r.push(c,c+a.nodeSize,u)}});let s=Ms(o?Ss(i):i,-t).sort(gt);for(let a=0;a<s.length;a++)s[a].type.valid(e,s[a])||(n.onRemove&&n.onRemove(s[a].spec),s.splice(a--,1));return s.length||r.length?new B(s,r):Y}function gt(i,e){return i.from-e.from||i.to-e.to}function Gi(i){let e=i;for(let t=0;t<e.length-1;t++){let n=e[t];if(n.from!=n.to)for(let r=t+1;r<e.length;r++){let o=e[r];if(o.from!=n.from){o.from<n.to&&(e==i&&(e=i.slice()),e[t]=n.copy(n.from,o.from),Ns(e,r,n.copy(o.from,n.to)));break}o.to!=n.to&&(e==i&&(e=i.slice()),e[r]=o.copy(o.from,n.to),Ns(e,r+1,o.copy(n.to,o.to)))}}return e}function Ns(i,e,t){for(;e<i.length&&gt(t,i[e])>0;)e++;i.splice(e,0,t)}function Wi(i){let e=[];return i.someProp("decorations",t=>{let n=t(i.state);n&&n!=Y&&e.push(n)}),i.cursorWrapper&&e.push(B.create(i.state.doc,[i.cursorWrapper.deco])),Ke.from(e)}const eu={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},tu=te&&je<=11;class nu{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class iu{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new nu,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(n=>{for(let r=0;r<n.length;r++)this.queue.push(n[r]);te&&je<=11&&n.some(r=>"childList"==r.type&&r.removedNodes.length||"characterData"==r.type&&r.oldValue.length>r.target.nodeValue.length)?this.flushSoon():this.flush()}),tu&&(this.onCharData=n=>{this.queue.push({target:n.target,type:"characterData",oldValue:n.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,eu)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(ns(this.view)){if(this.suppressingSelectionUpdates)return Re(this.view);if(te&&je<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&dt(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let n,t=new Set;for(let o=e.focusNode;o;o=Mt(o))t.add(o);for(let o=e.anchorNode;o;o=Mt(o))if(t.has(o)){n=o;break}let r=n&&this.view.docView.nearestDesc(n);return r&&r.ignoreMutation({type:"selection",target:3==n.nodeType?n.parentNode:n})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),r=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&ns(e)&&!this.ignoreSelectionChange(n),o=-1,s=-1,a=!1,c=[];if(e.editable)for(let u=0;u<t.length;u++){let f=this.registerMutation(t[u],c);f&&(o=o<0?f.from:Math.min(f.from,o),s=s<0?f.to:Math.max(f.to,s),f.typeOver&&(a=!0))}if(ye&&c.length){let u=c.filter(f=>"BR"==f.nodeName);if(2==u.length){let[f,h]=u;f.parentNode&&f.parentNode.parentNode==h.parentNode?h.remove():f.remove()}else{let{focusNode:f}=this.currentSelection;for(let h of u){let p=h.parentNode;p&&"LI"==p.nodeName&&(!f||su(e,f)!=p)&&h.remove()}}}let d=null;o<0&&r&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&Nn(n)&&(d=Di(e))&&d.eq(I.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,Re(e),this.currentSelection.set(n),e.scrollToSelection()):(o>-1||r)&&(o>-1&&(e.docView.markDirty(o,s),function ru(i){if(!Os.has(i)&&(Os.set(i,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(i.dom).whiteSpace))){if(i.requiresGeckoHackNode=ye,Ds)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Ds=!0}}(e)),this.handleDOMChange(o,s,a,c),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||Re(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(let u=0;u<e.addedNodes.length;u++){let f=e.addedNodes[u];t.push(f),3==f.nodeType&&(this.lastChangedTextNode=f)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let r=e.previousSibling,o=e.nextSibling;if(te&&je<=11&&e.addedNodes.length)for(let u=0;u<e.addedNodes.length;u++){let{previousSibling:f,nextSibling:h}=e.addedNodes[u];(!f||Array.prototype.indexOf.call(e.addedNodes,f)<0)&&(r=f),(!h||Array.prototype.indexOf.call(e.addedNodes,h)<0)&&(o=h)}let s=r&&r.parentNode==e.target?j(r)+1:0,a=n.localPosFromDOM(e.target,s,-1),c=o&&o.parentNode==e.target?j(o):e.target.childNodes.length;return{from:a,to:n.localPosFromDOM(e.target,c,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let Os=new WeakMap,Ds=!1;function Is(i,e){let t=e.startContainer,n=e.startOffset,r=e.endContainer,o=e.endOffset,s=i.domAtPos(i.state.selection.anchor);return dt(s.node,s.offset,r,o)&&([t,n,r,o]=[r,o,t,n]),{anchorNode:t,anchorOffset:n,focusNode:r,focusOffset:o}}function su(i,e){for(let t=e.parentNode;t&&t!=i.dom;t=t.parentNode){let n=i.docView.nearestDesc(t,!0);if(n&&n.node.isBlock)return t}return null}function au(i){let e=i.pmViewDesc;if(e)return e.parseRule();if("BR"==i.nodeName&&i.parentNode){if(q&&/^(ul|ol)$/i.test(i.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(i.parentNode.lastChild==i||q&&/^(tr|table)$/i.test(i.parentNode.nodeName))return{ignore:!0}}else if("IMG"==i.nodeName&&i.getAttribute("mark-placeholder"))return{ignore:!0};return null}const cu=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function As(i,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:Ai(i,e.resolve(t.anchor),e.resolve(t.head))}function Xi(i,e,t){let n=i.depth,r=e?i.end():i.pos;for(;n>0&&(e||i.indexAfter(n)==i.node(n).childCount);)n--,r++,e=!1;if(t){let o=i.node(n).maybeChild(i.indexAfter(n));for(;o&&!o.isLeaf;)o=o.firstChild,r++}return r}function Rs(i){if(2!=i.length)return!1;let e=i.charCodeAt(0),t=i.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class Fs{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Id,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(zs),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):"function"==typeof e?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=Vs(this),Ls(this),this.nodeViews=Bs(this),this.docView=Go(this.state.doc,Ps(this),Wi(this),this.dom,this),this.domObserver=new iu(this,(n,r,o,s)=>function du(i,e,t,n,r){let o=i.input.compositionPendingChanges||(i.composing?i.input.compositionID:0);if(i.input.compositionPendingChanges=0,e<0){let M=i.input.lastSelectionTime>Date.now()-50?i.input.lastSelectionOrigin:null,D=Di(i,M);if(D&&!i.state.selection.eq(D)){if(K&&Ie&&13===i.input.lastKeyCode&&Date.now()-100<i.input.lastKeyCodeTime&&i.someProp("handleKeyDown",z=>z(i,ut(13,"Enter"))))return;let T=i.state.tr.setSelection(D);"pointer"==M?T.setMeta("pointer",!0):"key"==M&&T.scrollIntoView(),o&&T.setMeta("composition",o),i.dispatch(T)}return}let s=i.state.doc.resolve(e),a=s.sharedDepth(t);e=s.before(a+1),t=i.state.doc.resolve(t).after(a+1);let h,p,c=i.state.selection,d=function lu(i,e,t){let d,{node:n,fromOffset:r,toOffset:o,from:s,to:a}=i.docView.parseRange(e,t),c=i.domSelectionRange(),u=c.anchorNode;if(u&&i.dom.contains(1==u.nodeType?u:u.parentNode)&&(d=[{node:u,offset:c.anchorOffset}],Nn(c)||d.push({node:c.focusNode,offset:c.focusOffset})),K&&8===i.input.lastKeyCode)for(let y=o;y>r;y--){let b=n.childNodes[y-1],C=b.pmViewDesc;if("BR"==b.nodeName&&!C){o=y;break}if(!C||C.size)break}let f=i.state.doc,h=i.someProp("domParser")||lt.fromSchema(i.state.schema),p=f.resolve(s),m=null,g=h.parse(n,{topNode:p.parent,topMatch:p.parent.contentMatchAt(p.index()),topOpen:!0,from:r,to:o,preserveWhitespace:"pre"!=p.parent.type.whitespace||"full",findPositions:d,ruleFromNode:au,context:p});if(d&&null!=d[0].pos){let y=d[0].pos,b=d[1]&&d[1].pos;null==b&&(b=y),m={anchor:y+s,head:b+s}}return{doc:g,sel:m,from:s,to:a}}(i,e,t),u=i.state.doc,f=u.slice(d.from,d.to);8===i.input.lastKeyCode&&Date.now()-100<i.input.lastKeyCodeTime?(h=i.state.selection.to,p="end"):(h=i.state.selection.from,p="start"),i.input.lastKeyCode=null;let m=function pu(i,e,t,n,r){let o=i.findDiffStart(e,t);if(null==o)return null;let{a:s,b:a}=i.findDiffEnd(e,t+i.size,t+e.size);if("end"==r&&(n-=s+Math.max(0,o-Math.min(s,a))-o),s<o&&i.size<e.size){let c=n<=o&&n>=s?o-n:0;o-=c,o&&o<e.size&&Rs(e.textBetween(o-1,o+1))&&(o+=c?1:-1),a=o+(a-s),s=o}else if(a<o){let c=n<=o&&n>=a?o-n:0;o-=c,o&&o<i.size&&Rs(i.textBetween(o-1,o+1))&&(o+=c?1:-1),s=o+(s-a),a=o}return{start:o,endA:s,endB:a}}(f.content,d.doc.content,d.from,h,p);if(m&&i.input.domChangeCount++,(Et&&i.input.lastIOSEnter>Date.now()-225||Ie)&&r.some(M=>1==M.nodeType&&!cu.test(M.nodeName))&&(!m||m.endA>=m.endB)&&i.someProp("handleKeyDown",M=>M(i,ut(13,"Enter"))))return void(i.input.lastIOSEnter=0);if(!m){if(!(n&&c instanceof A&&!c.empty&&c.$head.sameParent(c.$anchor))||i.composing||d.sel&&d.sel.anchor!=d.sel.head){if(d.sel){let M=As(i,i.state.doc,d.sel);if(M&&!M.eq(i.state.selection)){let D=i.state.tr.setSelection(M);o&&D.setMeta("composition",o),i.dispatch(D)}}return}m={start:c.from,endA:c.to,endB:c.to}}i.state.selection.from<i.state.selection.to&&m.start==m.endB&&i.state.selection instanceof A&&(m.start>i.state.selection.from&&m.start<=i.state.selection.from+2&&i.state.selection.from>=d.from?m.start=i.state.selection.from:m.endA<i.state.selection.to&&m.endA>=i.state.selection.to-2&&i.state.selection.to<=d.to&&(m.endB+=i.state.selection.to-m.endA,m.endA=i.state.selection.to)),te&&je<=11&&m.endB==m.start+1&&m.endA==m.start&&m.start>d.from&&" \xa0"==d.doc.textBetween(m.start-d.from-1,m.start-d.from+1)&&(m.start--,m.endA--,m.endB--);let k,g=d.doc.resolveNoCache(m.start-d.from),y=d.doc.resolveNoCache(m.endB-d.from),b=u.resolve(m.start),C=g.sameParent(y)&&g.parent.inlineContent&&b.end()>=m.endA;if((Et&&i.input.lastIOSEnter>Date.now()-225&&(!C||r.some(M=>"DIV"==M.nodeName||"P"==M.nodeName))||!C&&g.pos<d.doc.content.size&&(!g.sameParent(y)||!g.parent.inlineContent)&&!/\S/.test(d.doc.textBetween(g.pos,y.pos,"",""))&&(k=I.findFrom(d.doc.resolve(g.pos+1),1,!0))&&k.head>g.pos)&&i.someProp("handleKeyDown",M=>M(i,ut(13,"Enter"))))return void(i.input.lastIOSEnter=0);if(i.state.selection.anchor>m.start&&function fu(i,e,t,n,r){if(t-e<=r.pos-n.pos||Xi(n,!0,!1)<r.pos)return!1;let o=i.resolve(e);if(!n.parent.isTextblock){let a=o.nodeAfter;return null!=a&&t==e+a.nodeSize}if(o.parentOffset<o.parent.content.size||!o.parent.isTextblock)return!1;let s=i.resolve(Xi(o,!0,!0));return!(!s.parent.isTextblock||s.pos>t||Xi(s,!0,!1)<t)&&n.parent.content.cut(n.parentOffset).eq(s.parent.content)}(u,m.start,m.endA,g,y)&&i.someProp("handleKeyDown",M=>M(i,ut(8,"Backspace"))))return void(Ie&&K&&i.domObserver.suppressSelectionUpdates());K&&m.endB==m.start&&(i.input.lastChromeDelete=Date.now()),Ie&&!C&&g.start()!=y.start()&&0==y.parentOffset&&g.depth==y.depth&&d.sel&&d.sel.anchor==d.sel.head&&d.sel.head==m.endA&&(m.endB-=2,y=d.doc.resolveNoCache(m.endB-d.from),setTimeout(()=>{i.someProp("handleKeyDown",function(M){return M(i,ut(13,"Enter"))})},20));let F,E=m.start,S=m.endA,O=M=>{let D=M||i.state.tr.replace(E,S,d.doc.slice(m.start-d.from,m.endB-d.from));if(d.sel){let T=As(i,D.doc,d.sel);T&&!(K&&i.composing&&T.empty&&(m.start!=m.endB||i.input.lastChromeDelete<Date.now()-100)&&(T.head==E||T.head==D.mapping.map(S)-1)||te&&T.empty&&T.head==E)&&D.setSelection(T)}return o&&D.setMeta("composition",o),D.scrollIntoView()};if(C){if(g.pos==y.pos){te&&je<=11&&0==g.parentOffset&&(i.domObserver.suppressSelectionUpdates(),setTimeout(()=>Re(i),20));let M=O(i.state.tr.delete(E,S)),D=u.resolve(m.start).marksAcross(u.resolve(m.endA));D&&M.ensureMarks(D),i.dispatch(M)}else if(m.endA==m.endB&&(F=function uu(i,e){let s,a,c,t=i.firstChild.marks,n=e.firstChild.marks,r=t,o=n;for(let u=0;u<n.length;u++)r=n[u].removeFromSet(r);for(let u=0;u<t.length;u++)o=t[u].removeFromSet(o);if(1==r.length&&0==o.length)a=r[0],s="add",c=u=>u.mark(a.addToSet(u.marks));else{if(0!=r.length||1!=o.length)return null;a=o[0],s="remove",c=u=>u.mark(a.removeFromSet(u.marks))}let d=[];for(let u=0;u<e.childCount;u++)d.push(c(e.child(u)));if(_.from(d).eq(i))return{mark:a,type:s}}(g.parent.content.cut(g.parentOffset,y.parentOffset),b.parent.content.cut(b.parentOffset,m.endA-b.start())))){let M=O(i.state.tr);"add"==F.type?M.addMark(E,S,F.mark):M.removeMark(E,S,F.mark),i.dispatch(M)}else if(g.parent.child(g.index()).isText&&g.index()==y.index()-(y.textOffset?0:1)){let M=g.parent.textBetween(g.parentOffset,y.parentOffset),D=()=>O(i.state.tr.insertText(M,E,S));i.someProp("handleTextInput",T=>T(i,E,S,M,D))||i.dispatch(D())}}else i.dispatch(O())}(this,n,r,o,s)),this.domObserver.start(),function Ad(i){for(let e in Q){let t=Q[e];i.dom.addEventListener(e,i.input.eventHandlers[e]=n=>{Fd(i,n)&&!zi(i,n)&&(i.editable||!(n.type in Z))&&t(i,n)},Dd[e]?{passive:!0}:void 0)}q&&i.dom.addEventListener("input",()=>null),Bi(i)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&Bi(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(zs),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n;let r=this.state,o=!1,s=!1;e.storedMarks&&this.composing&&(ws(this),s=!0),this.state=e;let a=r.plugins!=e.plugins||this._props.plugins!=t.plugins;if(a||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let p=Bs(this);(function mu(i,e){let t=0,n=0;for(let r in i){if(i[r]!=e[r])return!0;t++}for(let r in e)n++;return t!=n})(p,this.nodeViews)&&(this.nodeViews=p,o=!0)}(a||t.handleDOMEvents!=this._props.handleDOMEvents)&&Bi(this),this.editable=Vs(this),Ls(this);let c=Wi(this),d=Ps(this),u=r.plugins==e.plugins||r.doc.eq(e.doc)?e.scrollToSelection>r.scrollToSelection?"to selection":"preserve":"reset",f=o||!this.docView.matchesNode(e.doc,d,c);(f||!e.selection.eq(r.selection))&&(s=!0);let h="preserve"==u&&s&&null==this.dom.style.overflowAnchor&&function Uc(i){let n,r,e=i.dom.getBoundingClientRect(),t=Math.max(0,e.top);for(let o=(e.left+e.right)/2,s=t+1;s<Math.min(innerHeight,e.bottom);s+=5){let a=i.root.elementFromPoint(o,s);if(!a||a==i.dom||!i.dom.contains(a))continue;let c=a.getBoundingClientRect();if(c.top>=t-20){n=a,r=c.top;break}}return{refDOM:n,refTop:r,stack:Io(i.dom)}}(this);if(s){this.domObserver.stop();let p=f&&(te||K)&&!this.composing&&!r.selection.empty&&!e.selection.empty&&function hu(i,e){let t=Math.min(i.$anchor.sharedDepth(i.head),e.$anchor.sharedDepth(e.head));return i.$anchor.start(t)!=e.$anchor.start(t)}(r.selection,e.selection);if(f){let m=K?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function Xd(i){let e=i.domSelectionRange();if(!e.focusNode)return null;let t=function Vc(i,e){for(;;){if(3==i.nodeType&&e)return i;if(1==i.nodeType&&e>0){if("false"==i.contentEditable)return null;e=de(i=i.childNodes[e-1])}else{if(!i.parentNode||qt(i))return null;e=j(i),i=i.parentNode}}}(e.focusNode,e.focusOffset),n=function Bc(i,e){for(;;){if(3==i.nodeType&&e<i.nodeValue.length)return i;if(1==i.nodeType&&e<i.childNodes.length){if("false"==i.contentEditable)return null;i=i.childNodes[e],e=0}else{if(!i.parentNode||qt(i))return null;e=j(i)+1,i=i.parentNode}}}(e.focusNode,e.focusOffset);if(t&&n&&t!=n){let r=n.pmViewDesc,o=i.domObserver.lastChangedTextNode;if(t==o||n==o)return o;if(!r||!r.isText(n.nodeValue))return n;if(i.input.compositionNode==n){let s=t.pmViewDesc;if(s&&s.isText(t.nodeValue))return n}}return t||n}(this)),(o||!this.docView.update(e.doc,d,c,this))&&(this.docView.updateOuterDeco(d),this.docView.destroy(),this.docView=Go(e.doc,d,c,this.dom,this)),m&&!this.trackWrites&&(p=!0)}p||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function yd(i){let e=i.docView.domFromPos(i.state.selection.anchor,0),t=i.domSelectionRange();return dt(e.node,e.offset,t.anchorNode,t.anchorOffset)}(this))?Re(this,p):(es(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(r),!(null===(n=this.dragging)||void 0===n)&&n.node&&!r.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,r),"reset"==u?this.dom.scrollTop=0:"to selection"==u?this.scrollToSelection():h&&function Jc({refDOM:i,refTop:e,stack:t}){let n=i?i.getBoundingClientRect().top:0;Ao(t,0==n?0:n-e)}(h)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(e&&this.dom.contains(1==e.nodeType?e:e.parentNode)&&!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof v){let t=this.docView.domAfterPos(this.state.selection.from);1==t.nodeType&&Do(this,t.getBoundingClientRect(),e)}else Do(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let n=this.directPlugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let n=this.state.plugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,r=-1;if(this.state.doc.nodeAt(n.from)==n.node)r=n.from;else{let o=n.from+(this.state.doc.content.size-t.doc.content.size);(o>0&&this.state.doc.nodeAt(o))==n.node&&(r=o)}this.dragging=new vs(e.slice,e.move,r<0?void 0:v.create(this.state.doc,r))}someProp(e,t){let r,n=this._props&&this._props[e];if(null!=n&&(r=t?t(n):n))return r;for(let s=0;s<this.directPlugins.length;s++){let a=this.directPlugins[s].props[e];if(null!=a&&(r=t?t(a):a))return r}let o=this.state.plugins;if(o)for(let s=0;s<o.length;s++){let a=o[s].props[e];if(null!=a&&(r=t?t(a):a))return r}}hasFocus(){if(te){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if("false"==e.contentEditable)return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function Kc(i){if(i.setActive)return i.setActive();if(St)return i.focus(St);let e=Io(i);i.focus(null==St?{get preventScroll(){return St={preventScroll:!0},!0}}:void 0),St||(St=!1,Ao(e,0))}(this.dom),Re(this),this.domObserver.start()}get root(){let e=this._root;if(null==e)for(let t=this.dom.parentNode;t;t=t.parentNode)if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t;return e||document}updateRoot(){this._root=null}posAtCoords(e){return ed(this,e)}coordsAtPos(e,t=1){return Lo(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let r=this.docView.posFromDOM(e,t,n);if(null==r)throw new RangeError("DOM position not inside the editor");return r}endOfTextblock(e,t){return function od(i,e,t){return Bo==e&&zo==t?$o:(Bo=e,zo=t,$o="up"==t||"down"==t?function nd(i,e,t){let n=e.selection,r="up"==t?n.$from:n.$to;return Vo(i,e,()=>{let{node:o}=i.docView.domFromPos(r.pos,"up"==t?-1:1);for(;;){let a=i.docView.nearestDesc(o,!0);if(!a)break;if(a.node.isBlock){o=a.contentDOM||a.dom;break}o=a.dom.parentNode}let s=Lo(i,r.pos,1);for(let a=o.firstChild;a;a=a.nextSibling){let c;if(1==a.nodeType)c=a.getClientRects();else{if(3!=a.nodeType)continue;c=De(a,0,a.nodeValue.length).getClientRects()}for(let d=0;d<c.length;d++){let u=c[d];if(u.bottom>u.top+1&&("up"==t?s.top-u.top>2*(u.bottom-s.top):u.bottom-s.bottom>2*(s.bottom-u.top)))return!1}}return!0})}(i,e,t):function rd(i,e,t){let{$head:n}=e.selection;if(!n.parent.isTextblock)return!1;let r=n.parentOffset,o=!r,s=r==n.parent.content.size,a=i.domSelection();return a?id.test(n.parent.textContent)&&a.modify?Vo(i,e,()=>{let{focusNode:c,focusOffset:d,anchorNode:u,anchorOffset:f}=i.domSelectionRange(),h=a.caretBidiLevel;a.modify("move",t,"character");let p=n.depth?i.docView.domAfterPos(n.before()):i.dom,{focusNode:m,focusOffset:g}=i.domSelectionRange(),y=m&&!p.contains(1==m.nodeType?m:m.parentNode)||c==m&&d==g;try{a.collapse(u,f),c&&(c!=u||d!=f)&&a.extend&&a.extend(c,d)}catch{}return null!=h&&(a.caretBidiLevel=h),y}):"left"==t||"backward"==t?o:s:n.pos==n.start()||n.pos==n.end()}(i,e,t))}(this,t||this.state,e)}pasteHTML(e,t){return on(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return on(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return Pi(this,e)}destroy(){this.docView&&(function Rd(i){i.domObserver.stop();for(let e in i.input.eventHandlers)i.dom.removeEventListener(e,i.input.eventHandlers[e]);clearTimeout(i.input.composingTimeout),clearTimeout(i.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Wi(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,ki=null)}get isDestroyed(){return null==this.docView}dispatchEvent(e){return function Pd(i,e){!zi(i,e)&&Q[e.type]&&(i.editable||!(e.type in Z))&&Q[e.type](i,e)}(this,e)}domSelectionRange(){let e=this.domSelection();return e?q&&11===this.root.nodeType&&function $c(i){let e=i.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function ou(i,e){if(e.getComposedRanges){let r=e.getComposedRanges(i.root)[0];if(r)return Is(i,r)}let t;function n(r){r.preventDefault(),r.stopImmediatePropagation(),t=r.getTargetRanges()[0]}return i.dom.addEventListener("beforeinput",n,!0),document.execCommand("indent"),i.dom.removeEventListener("beforeinput",n,!0),t?Is(i,t):null}(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function Ps(i){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(i.editable),i.someProp("attributes",t=>{if("function"==typeof t&&(t=t(i.state)),t)for(let n in t)"class"==n?e.class+=" "+t[n]:"style"==n?e.style=(e.style?e.style+";":"")+t[n]:!e[n]&&"contenteditable"!=n&&"nodeName"!=n&&(e[n]=String(t[n]))}),e.translate||(e.translate="no"),[pe.node(0,i.state.doc.content.size,e)]}function Ls(i){if(i.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),i.cursorWrapper={dom:e,deco:pe.widget(i.state.selection.from,e,{raw:!0,marks:i.markCursor})}}else i.cursorWrapper=null}function Vs(i){return!i.someProp("editable",e=>!1===e(i.state))}function Bs(i){let e=Object.create(null);function t(n){for(let r in n)Object.prototype.hasOwnProperty.call(e,r)||(e[r]=n[r])}return i.someProp("nodeViews",t),i.someProp("markViews",t),e}function zs(i){if(i.spec.state||i.spec.filterTransaction||i.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}Fs.prototype.dispatch=function(i){let e=this._props.dispatchTransaction;e?e.call(this,i):this.updateState(this.state.apply(i))};var N=R(60177),gu=R(345);const $s=(i,e)=>!i.selection.empty&&(e&&e(i.tr.deleteSelection().scrollIntoView()),!0);function It(i,e,t=!1){for(let n=i;n;n="start"==e?n.firstChild:n.lastChild){if(n.isTextblock)return!0;if(t&&1!=n.childCount)return!1}return!1}function Ui(i){if(!i.parent.type.spec.isolating)for(let e=i.depth-1;e>=0;e--){if(i.index(e)>0)return i.doc.resolve(i.before(e+1));if(i.node(e).type.spec.isolating)break}return null}function Ji(i){if(!i.parent.type.spec.isolating)for(let e=i.depth-1;e>=0;e--){let t=i.node(e);if(i.index(e)+1<t.childCount)return i.doc.resolve(i.after(e+1));if(t.type.spec.isolating)break}return null}const Ws=(i,e)=>{let{$head:t,$anchor:n}=i.selection;return!(!t.parent.type.spec.code||!t.sameParent(n)||(e&&e(i.tr.insertText("\n").scrollIntoView()),0))};function Ki(i){for(let e=0;e<i.edgeCount;e++){let{type:t}=i.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const Xs=(i,e)=>{let{$head:t,$anchor:n}=i.selection;if(!t.parent.type.spec.code||!t.sameParent(n))return!1;let r=t.node(-1),o=t.indexAfter(-1),s=Ki(r.contentMatchAt(o));if(!s||!r.canReplaceWith(o,o,s))return!1;if(e){let a=t.after(),c=i.tr.replaceWith(a,a,s.createAndFill());c.setSelection(I.near(c.doc.resolve(a),1)),e(c.scrollIntoView())}return!0},Us=(i,e)=>{let t=i.selection,{$from:n,$to:r}=t;if(t instanceof se||n.parent.inlineContent||r.parent.inlineContent)return!1;let o=Ki(r.parent.contentMatchAt(r.indexAfter()));if(!o||!o.isTextblock)return!1;if(e){let s=(!n.parentOffset&&r.index()<r.parent.childCount?n:r).pos,a=i.tr.insert(s,o.createAndFill());a.setSelection(A.create(a.doc,s+1)),e(a.scrollIntoView())}return!0},Js=(i,e)=>{let{$cursor:t}=i.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let o=t.before();if(Ct(i.doc,o))return e&&e(i.tr.split(o).scrollIntoView()),!0}let n=t.blockRange(),r=n&&Xt(n);return null!=r&&(e&&e(i.tr.lift(n,r).scrollIntoView()),!0)},Yi=function wu(i){return(e,t)=>{let{$from:n,$to:r}=e.selection;if(e.selection instanceof v&&e.selection.node.isBlock)return!(!n.parentOffset||!Ct(e.doc,n.pos)||(t&&t(e.tr.split(n.pos).scrollIntoView()),0));if(!n.depth)return!1;let s,a,o=[],c=!1,d=!1;for(let p=n.depth;;p--){if(n.node(p).isBlock){c=n.end(p)==n.pos+(n.depth-p),d=n.start(p)==n.pos-(n.depth-p),a=Ki(n.node(p-1).contentMatchAt(n.indexAfter(p-1)));let g=i&&i(r.parent,c,n);o.unshift(g||(c&&a?{type:a}:null)),s=p;break}if(1==p)return!1;o.unshift(null)}let u=e.tr;(e.selection instanceof A||e.selection instanceof se)&&u.deleteSelection();let f=u.mapping.map(n.pos),h=Ct(u.doc,f,o.length,o);if(h||(o[0]=a?{type:a}:null,h=Ct(u.doc,f,o.length,o)),!h)return!1;if(u.split(f,o.length,o),!c&&d&&n.node(s).type!=a){let p=u.mapping.map(n.before(s)),m=u.doc.resolve(p);a&&n.node(s-1).canReplaceWith(m.index(),m.index()+1,a)&&u.setNodeMarkup(u.mapping.map(n.before(s)),a)}return t&&t(u.scrollIntoView()),!0}}();function Ks(i,e,t,n){let s,a,r=e.nodeBefore,o=e.nodeAfter,c=r.type.spec.isolating||o.type.spec.isolating;if(!c&&function vu(i,e,t){let n=e.nodeBefore,r=e.nodeAfter,o=e.index();return!(!(n&&r&&n.type.compatibleContent(r.type))||(!n.content.size&&e.parent.canReplace(o-1,o)?(t&&t(i.tr.delete(e.pos-n.nodeSize,e.pos).scrollIntoView()),0):!e.parent.canReplace(o,o+1)||!r.isTextblock&&!Tn(i.doc,e.pos)||(t&&t(i.tr.join(e.pos).scrollIntoView()),0)))}(i,e,t))return!0;let d=!c&&e.parent.canReplace(e.index(),e.index()+1);if(d&&(s=(a=r.contentMatchAt(r.childCount)).findWrapping(o.type))&&a.matchType(s[0]||o.type).validEnd){if(t){let p=e.pos+o.nodeSize,m=_.empty;for(let b=s.length-1;b>=0;b--)m=_.from(s[b].create(null,m));m=_.from(r.copy(m));let g=i.tr.step(new H(e.pos-1,p,e.pos,p,new x(m,1,0),s.length,!0)),y=g.doc.resolve(p+2*s.length);y.nodeAfter&&y.nodeAfter.type==r.type&&Tn(g.doc,y.pos)&&g.join(y.pos),t(g.scrollIntoView())}return!0}let u=o.type.spec.isolating||n>0&&c?null:I.findFrom(e,1),f=u&&u.$from.blockRange(u.$to),h=f&&Xt(f);if(null!=h&&h>=e.depth)return t&&t(i.tr.lift(f,h).scrollIntoView()),!0;if(d&&It(o,"start",!0)&&It(r,"end")){let p=r,m=[];for(;m.push(p),!p.isTextblock;)p=p.lastChild;let g=o,y=1;for(;!g.isTextblock;g=g.firstChild)y++;if(p.canReplace(p.childCount,p.childCount,g.content)){if(t){let b=_.empty;for(let k=m.length-1;k>=0;k--)b=_.from(m[k].copy(b));t(i.tr.step(new H(e.pos-m.length,e.pos+o.nodeSize,e.pos+y,e.pos+o.nodeSize-y,new x(b,m.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function Ys(i){return function(e,t){let n=e.selection,r=i<0?n.$from:n.$to,o=r.depth;for(;r.node(o).isInline;){if(!o)return!1;o--}return!!r.node(o).isTextblock&&(t&&t(e.tr.setSelection(A.create(e.doc,i<0?r.start(o):r.end(o)))),!0)}}const Tu=Ys(-1),Mu=Ys(1);function qi(i,e=null){return function(t,n){let r=!1;for(let o=0;o<t.selection.ranges.length&&!r;o++){let{$from:{pos:s},$to:{pos:a}}=t.selection.ranges[o];t.doc.nodesBetween(s,a,(c,d)=>{if(r)return!1;if(c.isTextblock&&!c.hasMarkup(i,e))if(c.type==i)r=!0;else{let u=t.doc.resolve(d),f=u.index();r=u.parent.canReplaceWith(f,f+1,i)}})}if(!r)return!1;if(n){let o=t.tr;for(let s=0;s<t.selection.ranges.length;s++){let{$from:{pos:a},$to:{pos:c}}=t.selection.ranges[s];o.setBlockType(a,c,i,e)}n(o.scrollIntoView())}return!0}}function At(i,e=null,t){let n=!1!==(t&&t.removeWhenPresent),r=!1!==(t&&t.enterInlineAtoms),o=!(t&&t.includeWhitespace);return function(s,a){let{empty:c,$cursor:d,ranges:u}=s.selection;if(c&&!d||!function Su(i,e,t,n){for(let r=0;r<e.length;r++){let{$from:o,$to:s}=e[r],a=0==o.depth&&i.inlineContent&&i.type.allowsMarkType(t);if(i.nodesBetween(o.pos,s.pos,(c,d)=>{if(a||!n&&c.isAtom&&c.isInline&&d>=o.pos&&d+c.nodeSize<=s.pos)return!1;a=c.inlineContent&&c.type.allowsMarkType(t)}),a)return!0}return!1}(s.doc,u,i,r))return!1;if(a)if(d)i.isInSet(s.storedMarks||d.marks())?a(s.tr.removeStoredMark(i)):a(s.tr.addStoredMark(i.create(e)));else{let f,h=s.tr;r||(u=function Nu(i){let e=[];for(let t=0;t<i.length;t++){let{$from:n,$to:r}=i[t];n.doc.nodesBetween(n.pos,r.pos,(o,s)=>{if(o.isAtom&&o.content.size&&o.isInline&&s>=n.pos&&s+o.nodeSize<=r.pos)return s+1>n.pos&&e.push(new bi(n,n.doc.resolve(s+1))),n=n.doc.resolve(s+1+o.content.size),!1}),n.pos<r.pos&&e.push(new bi(n,r))}return e}(u)),f=n?!u.some(p=>s.doc.rangeHasMark(p.$from.pos,p.$to.pos,i)):!u.every(p=>{let m=!1;return h.doc.nodesBetween(p.$from.pos,p.$to.pos,(g,y,b)=>{if(m)return!1;m=!i.isInSet(g.marks)&&!!b&&b.type.allowsMarkType(i)&&!(g.isText&&/^\s*$/.test(g.textBetween(Math.max(0,p.$from.pos-y),Math.min(g.nodeSize,p.$to.pos-y))))}),!m});for(let p=0;p<u.length;p++){let{$from:m,$to:g}=u[p];if(f){let y=m.pos,b=g.pos,C=m.nodeAfter,k=g.nodeBefore,E=o&&C&&C.isText?/^\s*/.exec(C.text)[0].length:0,S=o&&k&&k.isText?/\s*$/.exec(k.text)[0].length:0;y+E<b&&(y+=E,b-=S),h.addMark(y,b,i.create(e))}else h.removeMark(m.pos,g.pos,i)}a(h.scrollIntoView())}return!0}}function ln(...i){return function(e,t,n){for(let r=0;r<i.length;r++)if(i[r](e,t,n))return!0;return!1}}let Qi=ln($s,(i,e,t)=>{let n=function Hs(i,e){let{$cursor:t}=i.selection;return!t||(e?!e.endOfTextblock("backward",i):t.parentOffset>0)?null:t}(i,t);if(!n)return!1;let r=Ui(n);if(!r){let s=n.blockRange(),a=s&&Xt(s);return null!=a&&(e&&e(i.tr.lift(s,a).scrollIntoView()),!0)}let o=r.nodeBefore;if(Ks(i,r,e,-1))return!0;if(0==n.parent.content.size&&(It(o,"end")||v.isSelectable(o)))for(let s=n.depth;;s--){let a=hi(i.doc,n.before(s),n.after(s),x.empty);if(a&&a.slice.size<a.to-a.from){if(e){let c=i.tr.step(a);c.setSelection(It(o,"end")?I.findFrom(c.doc.resolve(c.mapping.map(r.pos,-1)),-1):v.create(c.doc,r.pos-o.nodeSize)),e(c.scrollIntoView())}return!0}if(1==s||n.node(s-1).childCount>1)break}return!(!o.isAtom||r.depth!=n.depth-1||(e&&e(i.tr.delete(r.pos-o.nodeSize,r.pos).scrollIntoView()),0))},(i,e,t)=>{let{$head:n,empty:r}=i.selection,o=n;if(!r)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("backward",i):n.parentOffset>0)return!1;o=Ui(n)}let s=o&&o.nodeBefore;return!(!s||!v.isSelectable(s)||(e&&e(i.tr.setSelection(v.create(i.doc,o.pos-s.nodeSize)).scrollIntoView()),0))}),qs=ln($s,(i,e,t)=>{let n=function Gs(i,e){let{$cursor:t}=i.selection;return!t||(e?!e.endOfTextblock("forward",i):t.parentOffset<t.parent.content.size)?null:t}(i,t);if(!n)return!1;let r=Ji(n);if(!r)return!1;let o=r.nodeAfter;if(Ks(i,r,e,1))return!0;if(0==n.parent.content.size&&(It(o,"start")||v.isSelectable(o))){let s=hi(i.doc,n.before(),n.after(),x.empty);if(s&&s.slice.size<s.to-s.from){if(e){let a=i.tr.step(s);a.setSelection(It(o,"start")?I.findFrom(a.doc.resolve(a.mapping.map(r.pos)),1):v.create(a.doc,a.mapping.map(r.pos))),e(a.scrollIntoView())}return!0}}return!(!o.isAtom||r.depth!=n.depth-1||(e&&e(i.tr.delete(r.pos,r.pos+o.nodeSize).scrollIntoView()),0))},(i,e,t)=>{let{$head:n,empty:r}=i.selection,o=n;if(!r)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("forward",i):n.parentOffset<n.parent.content.size)return!1;o=Ji(n)}let s=o&&o.nodeAfter;return!(!s||!v.isSelectable(s)||(e&&e(i.tr.setSelection(v.create(i.doc,o.pos)).scrollIntoView()),0))});const Fe={Enter:ln(Ws,Us,Js,Yi),"Mod-Enter":Xs,Backspace:Qi,"Mod-Backspace":Qi,"Shift-Backspace":Qi,Delete:qs,"Mod-Delete":qs,"Mod-a":(i,e)=>(e&&e(i.tr.setSelection(new se(i.doc))),!0)},Qs={"Ctrl-h":Fe.Backspace,"Alt-Backspace":Fe["Mod-Backspace"],"Ctrl-d":Fe.Delete,"Ctrl-Alt-Backspace":Fe["Mod-Delete"],"Alt-Delete":Fe["Mod-Delete"],"Alt-d":Fe["Mod-Delete"],"Ctrl-a":Tu,"Ctrl-e":Mu};for(let i in Fe)Qs[i]=Fe[i];const Du=(typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&"darwin"==os.platform())?Qs:Fe;class Pe{constructor(e,t,n={}){this.match=e,this.match=e,this.handler="string"==typeof t?function Iu(i){return function(e,t,n,r){let o=i;if(t[1]){let s=t[0].lastIndexOf(t[1]);o+=t[0].slice(s+t[1].length);let a=(n+=s)-r;a>0&&(o=t[0].slice(s-a,s)+o,n=r)}return e.tr.insertText(o,n,r)}}(t):t,this.undoable=!1!==n.undoable,this.inCode=n.inCode||!1,this.inCodeMark=!1!==n.inCodeMark}}function Zs(i,e,t,n,r,o){if(i.composing)return!1;let s=i.state,a=s.doc.resolve(e),c=a.parent.textBetween(Math.max(0,a.parentOffset-500),a.parentOffset,null,"\ufffc")+n;for(let d=0;d<r.length;d++){let u=r[d];if(!u.inCodeMark&&a.marks().some(p=>p.type.spec.code))continue;if(a.parent.type.spec.code){if(!u.inCode)continue}else if("only"===u.inCode)continue;let f=u.match.exec(c),h=f&&f[0].length>=n.length&&u.handler(s,f,e-(f[0].length-n.length),t);if(h)return u.undoable&&h.setMeta(o,{transform:h,from:e,to:t,text:n}),i.dispatch(h),!0}return!1}const Fu=new Pe(/--$/,"\u2014",{inCodeMark:!1}),Pu=new Pe(/\.\.\.$/,"\u2026",{inCodeMark:!1}),$u=[new Pe(/(?:^|[\s\{\[\(\<'"\u2018\u201C])(")$/,"\u201c",{inCodeMark:!1}),new Pe(/"$/,"\u201d",{inCodeMark:!1}),new Pe(/(?:^|[\s\{\[\(\<'"\u2018\u201C])(')$/,"\u2018",{inCodeMark:!1}),new Pe(/'$/,"\u2019",{inCodeMark:!1})];function Zi(i,e,t=null,n){return new Pe(i,(r,o,s,a)=>{let c=t instanceof Function?t(o):t,d=r.tr.delete(s,a),f=d.doc.resolve(s).blockRange(),h=f&&pi(f,e,c);if(!h)return null;d.wrap(f,h);let p=d.doc.resolve(s-1).nodeBefore;return p&&p.type==e&&Tn(d.doc,s-1)&&(!n||n(o,p))&&d.join(s-1),d})}function el(i,e,t=null){return new Pe(i,(n,r,o,s)=>{let a=n.doc.resolve(o),c=t instanceof Function?t(r):t;return a.node(-1).canReplaceWith(a.index(-1),a.indexAfter(-1),e)?n.tr.delete(o,s).setBlockType(o,o,e,c):null})}const er=(i,e)=>{const{from:t,$from:n,to:r,empty:o}=i.selection;return o?!!e.isInSet(i.storedMarks||n.marks()):i.doc.rangeHasMark(t,r,e)},tl=(i,e,t={})=>{const{selection:n}=i,{$from:r,to:o}=n,s=((i,e)=>{for(let t=e.depth;t>0;t-=1)if(e.node(t).type===i)return e.node(t).type;return null})(e,r);return Object.entries(t).length&&s?o<=r.end()&&r.parent.hasMarkup(e,t):!!s},nl=i=>{const e=[],{selection:{from:t,to:n}}=i;return i.doc.nodesBetween(t,n,r=>{e.push(r)}),e},il=(i,e,t)=>{for(const n of e){const{$from:r,$to:o}=n;let s=0===r.depth&&i.type.allowsMarkType(t);if(i.nodesBetween(r.pos,o.pos,a=>!s&&(s=a.inlineContent&&a.type.allowsMarkType(t),!0)),s)return!0}return!1},rl=(i,e,t)=>new Pe(i,(n,r,o,s)=>{const{tr:a}=n,c=o;let d=s;const[u,,f]=r,h=u.search(/\S/);if(f){const p=o+u.indexOf(f),m=p+f.length;m<s&&a.delete(m,s),p>o&&a.delete(o+h,p),d=o+f.length+h}return a.addMark(c,d,e.create(t)),a.removeStoredMark(e),a}),ol=(i,e={})=>(t,n)=>{const{tr:r,selection:o}=t,{empty:s,ranges:a,$from:c,$to:d}=o;if(s&&o instanceof A){const{$cursor:u}=o;if(!u||!il(t.doc,a,i)||(r.addStoredMark(i.create(e)),!r.storedMarksSet))return!1;n?.(r)}else{if(r.addMark(c.pos,d.pos,i.create(e)),!r.docChanged)return!1;n?.(r.scrollIntoView())}return!0};var G=function(){};G.prototype.append=function(e){return e.length?(e=G.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},G.prototype.prepend=function(e){return e.length?G.from(e).append(this):this},G.prototype.appendInner=function(e){return new Uu(this,e)},G.prototype.slice=function(e,t){return void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t?G.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},G.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)},G.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},G.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(o,s){return r.push(e(o,s))},t,n),r},G.from=function(e){return e instanceof G?e:e&&e.length?new sl(e):G.empty};var sl=function(i){function e(n){i.call(this),this.values=n}i&&(e.__proto__=i),(e.prototype=Object.create(i&&i.prototype)).constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(r,o){return 0==r&&o==this.length?this:new e(this.values.slice(r,o))},e.prototype.getInner=function(r){return this.values[r]},e.prototype.forEachInner=function(r,o,s,a){for(var c=o;c<s;c++)if(!1===r(this.values[c],a+c))return!1},e.prototype.forEachInvertedInner=function(r,o,s,a){for(var c=o-1;c>=s;c--)if(!1===r(this.values[c],a+c))return!1},e.prototype.leafAppend=function(r){if(this.length+r.length<=200)return new e(this.values.concat(r.flatten()))},e.prototype.leafPrepend=function(r){if(this.length+r.length<=200)return new e(r.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(G);G.empty=new sl([]);var Uu=function(i){function e(t,n){i.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return i&&(e.__proto__=i),(e.prototype=Object.create(i&&i.prototype)).constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(n){return n<this.left.length?this.left.get(n):this.right.get(n-this.left.length)},e.prototype.forEachInner=function(n,r,o,s){var a=this.left.length;if(r<a&&!1===this.left.forEachInner(n,r,Math.min(o,a),s)||o>a&&!1===this.right.forEachInner(n,Math.max(r-a,0),Math.min(this.length,o)-a,s+a))return!1},e.prototype.forEachInvertedInner=function(n,r,o,s){var a=this.left.length;if(r>a&&!1===this.right.forEachInvertedInner(n,r-a,Math.max(o,a)-a,s+a)||o<a&&!1===this.left.forEachInvertedInner(n,Math.min(r,a),o,s))return!1},e.prototype.sliceInner=function(n,r){if(0==n&&r==this.length)return this;var o=this.left.length;return r<=o?this.left.slice(n,r):n>=o?this.right.slice(n-o,r-o):this.left.slice(n,o).append(this.right.slice(0,r-o))},e.prototype.leafAppend=function(n){var r=this.right.leafAppend(n);if(r)return new e(this.left,r)},e.prototype.leafPrepend=function(n){var r=this.left.leafPrepend(n);if(r)return new e(r,this.right)},e.prototype.appendInner=function(n){return this.left.depth>=Math.max(this.right.depth,n.depth)+1?new e(this.left,new e(this.right,n)):new e(this,n)},e}(G);const ll=G;class be{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(0==this.eventCount)return null;let r,o,n=this.items.length;for(;;n--)if(this.items.get(n-1).selection){--n;break}t&&(r=this.remapping(n,this.items.length),o=r.maps.length);let a,c,s=e.tr,d=[],u=[];return this.items.forEach((f,h)=>{if(!f.step)return r||(r=this.remapping(n,h+1),o=r.maps.length),o--,void u.push(f);if(r){u.push(new Ee(f.map));let m,p=f.step.map(r.slice(o));p&&s.maybeStep(p).doc&&(m=s.mapping.maps[s.mapping.maps.length-1],d.push(new Ee(m,void 0,void 0,d.length+u.length))),o--,m&&r.appendMap(m,o)}else s.maybeStep(f.step);return f.selection?(a=r?f.selection.map(r.slice(o)):f.selection,c=new be(this.items.slice(0,n).append(u.reverse().concat(d)),this.eventCount-1),!1):void 0},this.items.length,0),{remaining:c,transform:s,selection:a}}addTransform(e,t,n,r){let o=[],s=this.eventCount,a=this.items,c=!r&&a.length?a.get(a.length-1):null;for(let u=0;u<e.steps.length;u++){let p,f=e.steps[u].invert(e.docs[u]),h=new Ee(e.mapping.maps[u],f,t);(p=c&&c.merge(h))&&(h=p,u?o.pop():a=a.slice(0,a.length-1)),o.push(h),t&&(s++,t=void 0),r||(c=h)}let d=s-n.depth;return d>Yu&&(a=function Ku(i,e){let t;return i.forEach((n,r)=>{if(n.selection&&0==e--)return t=r,!1}),i.slice(t)}(a,d),s-=d),new be(a.append(o),s)}remapping(e,t){let n=new Wt;return this.items.forEach((r,o)=>{n.appendMap(r.map,null!=r.mirrorOffset&&o-r.mirrorOffset>=e?n.maps.length-r.mirrorOffset:void 0)},e,t),n}addMaps(e){return 0==this.eventCount?this:new be(this.items.append(e.map(t=>new Ee(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),o=e.mapping,s=e.steps.length,a=this.eventCount;this.items.forEach(h=>{h.selection&&a--},r);let c=t;this.items.forEach(h=>{let p=o.getMirror(--c);if(null==p)return;s=Math.min(s,p);let m=o.maps[p];if(h.step){let g=e.steps[p].invert(e.docs[p]),y=h.selection&&h.selection.map(o.slice(c+1,p));y&&a++,n.push(new Ee(m,g,y))}else n.push(new Ee(m))},r);let d=[];for(let h=t;h<s;h++)d.push(new Ee(o.maps[h]));let u=this.items.slice(0,r).append(d).append(n),f=new be(u,a);return f.emptyItemCount()>500&&(f=f.compress(this.items.length-n.length)),f}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],o=0;return this.items.forEach((s,a)=>{if(a>=e)r.push(s),s.selection&&o++;else if(s.step){let c=s.step.map(t.slice(n)),d=c&&c.getMap();if(n--,d&&t.appendMap(d,n),c){let u=s.selection&&s.selection.map(t.slice(n));u&&o++;let h,f=new Ee(d.invert(),c,u),p=r.length-1;(h=r.length&&r[p].merge(f))?r[p]=h:r.push(f)}}else s.map&&n--},this.items.length,0),new be(ll.from(r.reverse()),o)}}be.empty=new be(ll.empty,0);class Ee{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new Ee(t.getMap().invert(),t,this.selection)}}}class Ye{constructor(e,t,n,r,o){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=o}}const Yu=20;function al(i){let e=[];for(let t=i.length-1;t>=0&&0==e.length;t--)i[t].forEach((n,r,o,s)=>e.push(o,s));return e}function tr(i,e){if(!i)return null;let t=[];for(let n=0;n<i.length;n+=2){let r=e.map(i[n],1),o=e.map(i[n+1],-1);r<=o&&t.push(r,o)}return t}let nr=!1,cl=null;function Vn(i){let e=i.plugins;if(cl!=e){nr=!1,cl=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){nr=!0;break}}return nr}const Le=new Oe("history"),dl=new Oe("closeHistory");function Bn(i,e){return(t,n)=>{let r=Le.getState(t);if(!r||0==(i?r.undone:r.done).eventCount)return!1;if(n){let o=function Zu(i,e,t){let n=Vn(e),r=Le.get(e).spec.config,o=(t?i.undone:i.done).popEvent(e,n);if(!o)return null;let s=o.selection.resolve(o.transform.doc),a=(t?i.done:i.undone).addTransform(o.transform,e.selection.getBookmark(),r,n),c=new Ye(t?a:o.remaining,t?o.remaining:a,null,0,-1);return o.transform.setSelection(s).setMeta(Le,{redo:t,historyState:c})}(r,t,i);o&&n(e?o.scrollIntoView():o)}return!0}}const ir=Bn(!1,!0),zn=Bn(!0,!0);Bn(!1,!1),Bn(!0,!1);const ul=["start","end"],fl=["top","right","bottom","left"].reduce((i,e)=>i.concat(e,e+"-"+ul[0],e+"-"+ul[1]),[]),rr=Math.min,Rt=Math.max,$n=Math.round,Se=(Math,i=>({x:i,y:i})),nf={left:"right",right:"left",bottom:"top",top:"bottom"},rf={start:"end",end:"start"};function or(i,e){return"function"==typeof i?i(e):i}function Ft(i){return i.split("-")[0]}function Ve(i){return i.split("-")[1]}function pl(i){return"y"===i?"height":"width"}const sf=new Set(["top","bottom"]);function sr(i){return sf.has(Ft(i))?"y":"x"}function hl(i){return function of(i){return"x"===i?"y":"x"}(sr(i))}function lr(i){return i.replace(/left|right|bottom|top/g,e=>nf[e])}function jn(i){const{x:e,y:t,width:n,height:r}=i;return{width:n,height:r,top:t,left:e,right:e+n,bottom:t+r,x:e,y:t}}function yl(i,e,t){let{reference:n,floating:r}=i;const o=sr(e),s=hl(e),a=pl(s),c=Ft(e),d="y"===o,u=n.x+n.width/2-r.width/2,f=n.y+n.height/2-r.height/2,h=n[a]/2-r[a]/2;let p;switch(c){case"top":p={x:u,y:n.y-r.height};break;case"bottom":p={x:u,y:n.y+n.height};break;case"right":p={x:n.x+n.width,y:f};break;case"left":p={x:n.x-r.width,y:f};break;default:p={x:n.x,y:n.y}}switch(Ve(e)){case"start":p[s]-=h*(t&&d?-1:1);break;case"end":p[s]+=h*(t&&d?-1:1)}return p}const pf=function(){var i=(0,re.A)(function*(e,t,n){const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:a}=n,c=s.filter(Boolean),d=yield null==a.isRTL?void 0:a.isRTL(t);let u=yield a.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:h}=yl(u,r,d),p=r,m={},g=0;for(let y=0;y<c.length;y++){const{name:b,fn:C}=c[y],{x:k,y:E,data:S,reset:O}=yield C({x:f,y:h,initialPlacement:r,placement:p,strategy:o,middlewareData:m,rects:u,platform:a,elements:{reference:e,floating:t}});f=k??f,h=E??h,m={...m,[b]:{...m[b],...S}},O&&g<=50&&(g++,"object"==typeof O&&(O.placement&&(p=O.placement),O.rects&&(u=!0===O.rects?yield a.getElementRects({reference:e,floating:t,strategy:o}):O.rects),({x:f,y:h}=yl(u,p,d))),y=-1)}return{x:f,y:h,placement:p,strategy:o,middlewareData:m}});return function(t,n,r){return i.apply(this,arguments)}}();function yt(i,e){return ar.apply(this,arguments)}function ar(){return(ar=(0,re.A)(function*(i,e){var t;void 0===e&&(e={});const{x:n,y:r,platform:o,rects:s,elements:a,strategy:c}=i,{boundary:d="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:h=!1,padding:p=0}=or(e,i),m=function ff(i){return"number"!=typeof i?function uf(i){return{top:0,right:0,bottom:0,left:0,...i}}(i):{top:i,right:i,bottom:i,left:i}}(p),y=a[h?"floating"===f?"reference":"floating":f],b=jn(yield o.getClippingRect({element:null==(t=yield null==o.isElement?void 0:o.isElement(y))||t?y:y.contextElement||(yield null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:d,rootBoundary:u,strategy:c})),C="floating"===f?{x:n,y:r,width:s.floating.width,height:s.floating.height}:s.reference,k=yield null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating),E=(yield null==o.isElement?void 0:o.isElement(k))&&(yield null==o.getScale?void 0:o.getScale(k))||{x:1,y:1},S=jn(o.convertOffsetParentRelativeRectToViewportRelativeRect?yield o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:C,offsetParent:k,strategy:c}):C);return{top:(b.top-S.top+m.top)/E.y,bottom:(S.bottom-b.bottom+m.bottom)/E.y,left:(b.left-S.left+m.left)/E.x,right:(S.right-b.right+m.right)/E.x}})).apply(this,arguments)}const Cl=new Set(["left","top"]);function cr(){return(cr=(0,re.A)(function*(i,e){const{placement:t,platform:n,elements:r}=i,o=yield null==n.isRTL?void 0:n.isRTL(r.floating),s=Ft(t),a=Ve(t),c="y"===sr(t),d=Cl.has(s)?-1:1,u=o&&c?-1:1,f=or(e,i);let{mainAxis:h,crossAxis:p,alignmentAxis:m}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),c?{x:p*u,y:h*d}:{x:h*d,y:p*u}})).apply(this,arguments)}function Gn(){return typeof window<"u"}function Pt(i){return wl(i)?(i.nodeName||"").toLowerCase():"#document"}function le(i){var e;return(null==i||null==(e=i.ownerDocument)?void 0:e.defaultView)||window}function Be(i){var e;return null==(e=(wl(i)?i.ownerDocument:i.document)||window.document)?void 0:e.documentElement}function wl(i){return!!Gn()&&(i instanceof Node||i instanceof le(i).Node)}function _e(i){return!!Gn()&&(i instanceof Element||i instanceof le(i).Element)}function Ne(i){return!!Gn()&&(i instanceof HTMLElement||i instanceof le(i).HTMLElement)}function kl(i){return!(!Gn()||typeof ShadowRoot>"u")&&(i instanceof ShadowRoot||i instanceof le(i).ShadowRoot)}const _f=new Set(["inline","contents"]);function an(i){const{overflow:e,overflowX:t,overflowY:n,display:r}=xe(i);return/auto|scroll|overlay|hidden|clip/.test(e+n+t)&&!_f.has(r)}const xf=new Set(["table","td","th"]);function Cf(i){return xf.has(Pt(i))}const wf=[":popover-open",":modal"];function Wn(i){return wf.some(e=>{try{return i.matches(e)}catch{return!1}})}const kf=["transform","translate","scale","rotate","perspective"],vf=["transform","translate","scale","rotate","perspective","filter"],Tf=["paint","layout","strict","content"];function dr(i){const e=ur(),t=_e(i)?xe(i):i;return kf.some(n=>!!t[n]&&"none"!==t[n])||!!t.containerType&&"normal"!==t.containerType||!e&&!!t.backdropFilter&&"none"!==t.backdropFilter||!e&&!!t.filter&&"none"!==t.filter||vf.some(n=>(t.willChange||"").includes(n))||Tf.some(n=>(t.contain||"").includes(n))}function ur(){return!(typeof CSS>"u"||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const Ef=new Set(["html","body","#document"]);function Lt(i){return Ef.has(Pt(i))}function xe(i){return le(i).getComputedStyle(i)}function Xn(i){return _e(i)?{scrollLeft:i.scrollLeft,scrollTop:i.scrollTop}:{scrollLeft:i.scrollX,scrollTop:i.scrollY}}function qe(i){if("html"===Pt(i))return i;const e=i.assignedSlot||i.parentNode||kl(i)&&i.host||Be(i);return kl(e)?e.host:e}function vl(i){const e=qe(i);return Lt(e)?i.ownerDocument?i.ownerDocument.body:i.body:Ne(e)&&an(e)?e:vl(e)}function fr(i,e,t){var n;void 0===e&&(e=[]),void 0===t&&(t=!0);const r=vl(i),o=r===(null==(n=i.ownerDocument)?void 0:n.body),s=le(r);if(o){const a=pr(s);return e.concat(s,s.visualViewport||[],an(r)?r:[],a&&t?fr(a):[])}return e.concat(r,fr(r,[],t))}function pr(i){return i.parent&&Object.getPrototypeOf(i.parent)?i.frameElement:null}function Tl(i){const e=xe(i);let t=parseFloat(e.width)||0,n=parseFloat(e.height)||0;const r=Ne(i),o=r?i.offsetWidth:t,s=r?i.offsetHeight:n,a=$n(t)!==o||$n(n)!==s;return a&&(t=o,n=s),{width:t,height:n,$:a}}function hr(i){return _e(i)?i:i.contextElement}function Vt(i){const e=hr(i);if(!Ne(e))return Se(1);const t=e.getBoundingClientRect(),{width:n,height:r,$:o}=Tl(e);let s=(o?$n(t.width):t.width)/n,a=(o?$n(t.height):t.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const Sf=Se(0);function Ml(i){const e=le(i);return ur()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:Sf}function bt(i,e,t,n){void 0===e&&(e=!1),void 0===t&&(t=!1);const r=i.getBoundingClientRect(),o=hr(i);let s=Se(1);e&&(n?_e(n)&&(s=Vt(n)):s=Vt(i));const a=function Nf(i,e,t){return void 0===e&&(e=!1),!(!t||e&&t!==le(i))&&e}(o,t,n)?Ml(o):Se(0);let c=(r.left+a.x)/s.x,d=(r.top+a.y)/s.y,u=r.width/s.x,f=r.height/s.y;if(o){const h=le(o),p=n&&_e(n)?le(n):n;let m=h,g=pr(m);for(;g&&n&&p!==m;){const y=Vt(g),b=g.getBoundingClientRect(),C=xe(g),k=b.left+(g.clientLeft+parseFloat(C.paddingLeft))*y.x,E=b.top+(g.clientTop+parseFloat(C.paddingTop))*y.y;c*=y.x,d*=y.y,u*=y.x,f*=y.y,c+=k,d+=E,m=le(g),g=pr(m)}}return jn({width:u,height:f,x:c,y:d})}function mr(i,e){const t=Xn(i).scrollLeft;return e?e.left+t:bt(Be(i)).left+t}function El(i,e,t){void 0===t&&(t=!1);const n=i.getBoundingClientRect();return{x:n.left+e.scrollLeft-(t?0:mr(i,n)),y:n.top+e.scrollTop}}const Rf=new Set(["absolute","fixed"]);function Sl(i,e,t){let n;if("viewport"===e)n=function Af(i,e){const t=le(i),n=Be(i),r=t.visualViewport;let o=n.clientWidth,s=n.clientHeight,a=0,c=0;if(r){o=r.width,s=r.height;const d=ur();(!d||d&&"fixed"===e)&&(a=r.offsetLeft,c=r.offsetTop)}return{width:o,height:s,x:a,y:c}}(i,t);else if("document"===e)n=function If(i){const e=Be(i),t=Xn(i),n=i.ownerDocument.body,r=Rt(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),o=Rt(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight);let s=-t.scrollLeft+mr(i);const a=-t.scrollTop;return"rtl"===xe(n).direction&&(s+=Rt(e.clientWidth,n.clientWidth)-r),{width:r,height:o,x:s,y:a}}(Be(i));else if(_e(e))n=function Ff(i,e){const t=bt(i,!0,"fixed"===e),n=t.top+i.clientTop,r=t.left+i.clientLeft,o=Ne(i)?Vt(i):Se(1);return{width:i.clientWidth*o.x,height:i.clientHeight*o.y,x:r*o.x,y:n*o.y}}(e,t);else{const r=Ml(i);n={x:e.x-r.x,y:e.y-r.y,width:e.width,height:e.height}}return jn(n)}function Nl(i,e){const t=qe(i);return!(t===e||!_e(t)||Lt(t))&&("fixed"===xe(t).position||Nl(t,e))}function Pf(i,e){const t=e.get(i);if(t)return t;let n=fr(i,[],!1).filter(a=>_e(a)&&"body"!==Pt(a)),r=null;const o="fixed"===xe(i).position;let s=o?qe(i):i;for(;_e(s)&&!Lt(s);){const a=xe(s),c=dr(s);!c&&"fixed"===a.position&&(r=null),(o?!c&&!r:!c&&"static"===a.position&&r&&Rf.has(r.position)||an(s)&&!c&&Nl(i,s))?n=n.filter(u=>u!==s):r=a,s=qe(s)}return e.set(i,n),n}function Bf(i,e,t){const n=Ne(e),r=Be(e),o="fixed"===t,s=bt(i,!0,o,e);let a={scrollLeft:0,scrollTop:0};const c=Se(0);function d(){c.x=mr(r)}if(n||!n&&!o)if(("body"!==Pt(e)||an(r))&&(a=Xn(e)),n){const p=bt(e,!0,o,e);c.x=p.x+e.clientLeft,c.y=p.y+e.clientTop}else r&&d();o&&!n&&r&&d();const u=!r||n||o?Se(0):El(r,a);return{x:s.left+a.scrollLeft-c.x-u.x,y:s.top+a.scrollTop-c.y-u.y,width:s.width,height:s.height}}function gr(i){return"static"===xe(i).position}function Ol(i,e){if(!Ne(i)||"fixed"===xe(i).position)return null;if(e)return e(i);let t=i.offsetParent;return Be(i)===t&&(t=t.ownerDocument.body),t}function Dl(i,e){const t=le(i);if(Wn(i))return t;if(!Ne(i)){let r=qe(i);for(;r&&!Lt(r);){if(_e(r)&&!gr(r))return r;r=qe(r)}return t}let n=Ol(i,e);for(;n&&Cf(n)&&gr(n);)n=Ol(n,e);return n&&Lt(n)&&gr(n)&&!dr(n)?t:n||function Mf(i){let e=qe(i);for(;Ne(e)&&!Lt(e);){if(dr(e))return e;if(Wn(e))return null;e=qe(e)}return null}(i)||t}const Hf={convertOffsetParentRelativeRectToViewportRelativeRect:function Of(i){let{elements:e,rect:t,offsetParent:n,strategy:r}=i;const o="fixed"===r,s=Be(n),a=!!e&&Wn(e.floating);if(n===s||a&&o)return t;let c={scrollLeft:0,scrollTop:0},d=Se(1);const u=Se(0),f=Ne(n);if((f||!f&&!o)&&(("body"!==Pt(n)||an(s))&&(c=Xn(n)),Ne(n))){const p=bt(n);d=Vt(n),u.x=p.x+n.clientLeft,u.y=p.y+n.clientTop}const h=!s||f||o?Se(0):El(s,c,!0);return{width:t.width*d.x,height:t.height*d.y,x:t.x*d.x-c.scrollLeft*d.x+u.x+h.x,y:t.y*d.y-c.scrollTop*d.y+u.y+h.y}},getDocumentElement:Be,getClippingRect:function Lf(i){let{element:e,boundary:t,rootBoundary:n,strategy:r}=i;const s=[..."clippingAncestors"===t?Wn(e)?[]:Pf(e,this._c):[].concat(t),n],c=s.reduce((d,u)=>{const f=Sl(e,u,r);return d.top=Rt(f.top,d.top),d.right=rr(f.right,d.right),d.bottom=rr(f.bottom,d.bottom),d.left=Rt(f.left,d.left),d},Sl(e,s[0],r));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:Dl,getElementRects:function(){var i=(0,re.A)(function*(e){const t=this.getOffsetParent||Dl,n=this.getDimensions,r=yield n(e.floating);return{reference:Bf(e.reference,yield t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}});return function(t){return i.apply(this,arguments)}}(),getClientRects:function Df(i){return Array.from(i.getClientRects())},getDimensions:function Vf(i){const{width:e,height:t}=Tl(i);return{width:e,height:t}},getScale:Vt,isElement:_e,isRTL:function $f(i){return"rtl"===xe(i).direction}},Gf=yt,Wf=function(i){return void 0===i&&(i=0),{name:"offset",options:i,fn:e=>(0,re.A)(function*(){var t,n;const{x:r,y:o,placement:s,middlewareData:a}=e,c=yield function yf(i,e){return cr.apply(this,arguments)}(e,i);return s===(null==(t=a.offset)?void 0:t.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:r+c.x,y:o+c.y,data:{...c,placement:s}}})()}},Xf=function(i){return void 0===i&&(i={}),{name:"autoPlacement",options:i,fn:e=>(0,re.A)(function*(){var t,n,r;const{rects:o,middlewareData:s,placement:a,platform:c,elements:d}=e,{crossAxis:u=!1,alignment:f,allowedPlacements:h=fl,autoAlignment:p=!0,...m}=or(i,e),g=void 0!==f||h===fl?function hf(i,e,t){return(i?[...t.filter(r=>Ve(r)===i),...t.filter(r=>Ve(r)!==i)]:t.filter(r=>Ft(r)===r)).filter(r=>!i||Ve(r)===i||!!e&&function Hn(i){return i.replace(/start|end/g,e=>rf[e])}(r)!==r)}(f||null,p,h):h,y=yield yt(e,m),b=(null==(t=s.autoPlacement)?void 0:t.index)||0,C=g[b];if(null==C)return{};const k=function lf(i,e,t){void 0===t&&(t=!1);const n=Ve(i),r=hl(i),o=pl(r);let s="x"===r?n===(t?"end":"start")?"right":"left":"start"===n?"bottom":"top";return e.reference[o]>e.floating[o]&&(s=lr(s)),[s,lr(s)]}(C,o,yield null==c.isRTL?void 0:c.isRTL(d.floating));if(a!==C)return{reset:{placement:g[0]}};const E=[y[Ft(C)],y[k[0]],y[k[1]]],S=[...(null==(n=s.autoPlacement)?void 0:n.overflows)||[],{placement:C,overflows:E}],O=g[b+1];if(O)return{data:{index:b+1,overflows:S},reset:{placement:O}};const F=S.map(T=>{const z=Ve(T.placement);return[T.placement,z&&u?T.overflows.slice(0,2).reduce((ee,ie)=>ee+ie,0):T.overflows[0],T.overflows]}).sort((T,z)=>T[1]-z[1]),D=(null==(r=F.filter(T=>T[2].slice(0,Ve(T[0])?2:3).every(z=>z<=0))[0])?void 0:r[0])||F[0][0];return D!==a?{data:{index:b+1,overflows:S},reset:{placement:D}}:{}})()}};for(var Qe={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Un={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Jf=typeof navigator<"u"&&/Mac/.test(navigator.platform),Kf=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),W=0;W<10;W++)Qe[48+W]=Qe[96+W]=String(W);for(W=1;W<=24;W++)Qe[W+111]="F"+W;for(W=65;W<=90;W++)Qe[W]=String.fromCharCode(W+32),Un[W]=String.fromCharCode(W);for(var yr in Qe)Un.hasOwnProperty(yr)||(Un[yr]=Qe[yr]);const qf=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),Qf=typeof navigator<"u"&&/Win/.test(navigator.platform);function Zf(i){let n,r,o,s,e=i.split(/-(?!$)/),t=e[e.length-1];"Space"==t&&(t=" ");for(let a=0;a<e.length-1;a++){let c=e[a];if(/^(cmd|meta|m)$/i.test(c))s=!0;else if(/^a(lt)?$/i.test(c))n=!0;else if(/^(c|ctrl|control)$/i.test(c))r=!0;else if(/^s(hift)?$/i.test(c))o=!0;else{if(!/^mod$/i.test(c))throw new Error("Unrecognized modifier name: "+c);qf?s=!0:r=!0}}return n&&(t="Alt-"+t),r&&(t="Ctrl-"+t),s&&(t="Meta-"+t),o&&(t="Shift-"+t),t}function br(i,e,t=!0){return e.altKey&&(i="Alt-"+i),e.ctrlKey&&(i="Ctrl-"+i),e.metaKey&&(i="Meta-"+i),t&&e.shiftKey&&(i="Shift-"+i),i}function Jn(i){return new ve({props:{handleKeyDown:tp(i)}})}function tp(i){let e=function ep(i){let e=Object.create(null);for(let t in i)e[Zf(t)]=i[t];return e}(i);return function(t,n){let o,r=function Yf(i){var t=!(Jf&&i.metaKey&&i.shiftKey&&!i.ctrlKey&&!i.altKey||Kf&&i.shiftKey&&i.key&&1==i.key.length||"Unidentified"==i.key)&&i.key||(i.shiftKey?Un:Qe)[i.keyCode]||i.key||"Unidentified";return"Esc"==t&&(t="Escape"),"Del"==t&&(t="Delete"),"Left"==t&&(t="ArrowLeft"),"Up"==t&&(t="ArrowUp"),"Right"==t&&(t="ArrowRight"),"Down"==t&&(t="ArrowDown"),t}(n),s=e[br(r,n)];if(s&&s(t.state,t.dispatch,t))return!0;if(1==r.length&&" "!=r){if(n.shiftKey){let a=e[br(r,n,!1)];if(a&&a(t.state,t.dispatch,t))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(Qf&&n.ctrlKey&&n.altKey)&&(o=Qe[n.keyCode])&&o!=r){let a=e[br(o,n)];if(a&&a(t.state,t.dispatch,t))return!0}}return!1}}const np=["imgEl"],ip=i=>({"NgxEditor__Resizer--Active":i});function rp(i,e){if(1&i){const t=l.RV6();l.j41(0,"span",4)(1,"span",5),l.bIt("mousedown",function(r){l.eBV(t);const o=l.XpG();return l.Njj(o.startResizing(r,"left"))}),l.k0s(),l.j41(2,"span",6),l.bIt("mousedown",function(r){l.eBV(t);const o=l.XpG();return l.Njj(o.startResizing(r,"right"))}),l.k0s(),l.j41(3,"span",7),l.bIt("mousedown",function(r){l.eBV(t);const o=l.XpG();return l.Njj(o.startResizing(r,"left"))}),l.k0s(),l.j41(4,"span",8),l.bIt("mousedown",function(r){l.eBV(t);const o=l.XpG();return l.Njj(o.startResizing(r,"right"))}),l.k0s()()}}const op=["ngxEditor"],Al=["*"],sp=(i,e)=>({backgroundColor:i,color:e}),lp=i=>({"NgxEditor__Color--Active":i});function ap(i,e){if(1&i){const t=l.RV6();l.j41(0,"button",7),l.bIt("mousedown",function(r){const o=l.eBV(t).$implicit,s=l.XpG(3);return l.Njj(s.onColorSelectMouseClick(r,o))})("keydown.enter",function(){const r=l.eBV(t).$implicit,o=l.XpG(3);return l.Njj(o.onColorSelectKeydown(r))})("keydown.space",function(){const r=l.eBV(t).$implicit,o=l.XpG(3);return l.Njj(o.onColorSelectKeydown(r))}),l.k0s()}if(2&i){const t=e.$implicit,n=l.XpG(3);l.Y8G("ngStyle",l.l_i(3,sp,t,n.getContrastYIQ(t)))("title",t)("ngClass",l.eq3(6,lp,n.activeColors.includes(t)))}}function cp(i,e){if(1&i&&(l.j41(0,"div",5),l.DNE(1,ap,1,8,"button",6),l.k0s()),2&i){const t=e.$implicit,n=l.XpG(2);l.R7$(),l.Y8G("ngForOf",t)("ngForTrackBy",n.trackByIndex)}}function dp(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",2),l.DNE(1,cp,2,2,"div",3),l.j41(2,"button",4),l.bIt("mousedown",function(r){l.eBV(t);const o=l.XpG();return l.Njj(o.onRemoveMouseClick(r))})("keydown.enter",function(){l.eBV(t);const r=l.XpG();return l.Njj(r.onRemoveKeydown())})("keydown.space",function(){l.eBV(t);const r=l.XpG();return l.Njj(r.onRemoveKeydown())}),l.EFF(3),l.nI1(4,"async"),l.k0s()()}if(2&i){const t=l.XpG();l.R7$(),l.Y8G("ngForOf",t.presets)("ngForTrackBy",t.trackByIndex),l.R7$(),l.Y8G("disabled",!t.isActive),l.R7$(),l.SpI(" ",l.bMT(4,4,t.getLabel("remove"))," ")}}const up=(i,e)=>({"NgxEditor__Dropdown--Active":i,"NgxEditor--Disabled":e});function fp(i,e){if(1&i){const t=l.RV6();l.j41(0,"button",4),l.nI1(1,"async"),l.bIt("mousedown",function(r){const o=l.eBV(t).$implicit,s=l.XpG(2);return l.Njj(s.onDropdownItemMouseClick(r,o))})("keydown.enter",function(r){const o=l.eBV(t).$implicit,s=l.XpG(2);return l.Njj(s.onDropdownItemKeydown(r,o))})("keydown.space",function(r){const o=l.eBV(t).$implicit,s=l.XpG(2);return l.Njj(s.onDropdownItemKeydown(r,o))}),l.EFF(2),l.nI1(3,"async"),l.k0s()}if(2&i){const t=e.$implicit,n=l.XpG(2);l.Y8G("ngClass",l.l_i(8,up,t===n.activeItem,n.disabledItems.includes(t)))("ariaLabel",l.bMT(1,4,n.getName(t))),l.BMQ("aria-selected",t===n.activeItem),l.R7$(2),l.SpI(" ",l.bMT(3,6,n.getName(t))," ")}}function pp(i,e){if(1&i&&(l.j41(0,"div",2),l.DNE(1,fp,4,11,"button",3),l.k0s()),2&i){const t=l.XpG();l.R7$(),l.Y8G("ngForOf",t.items)("ngForTrackBy",t.trackByIndex)}}function hp(i,e){if(1&i&&(l.j41(0,"div",12),l.EFF(1),l.nI1(2,"async"),l.k0s()),2&i){const t=l.XpG(2);l.R7$(),l.SpI(" ",l.bMT(2,1,(null==t.src.errors?null:t.src.errors.pattern)&&t.getLabel("enterValidUrl"))," ")}}function mp(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",2)(1,"form",3),l.bIt("ngSubmit",function(r){l.eBV(t);const o=l.XpG();return l.Njj(o.insertLink(r))}),l.j41(2,"div",4)(3,"div",5)(4,"label",6),l.EFF(5),l.nI1(6,"async"),l.k0s(),l.nrm(7,"input",7),l.DNE(8,hp,3,3,"div",8),l.k0s()(),l.j41(9,"div",4)(10,"div",5)(11,"label",6),l.EFF(12),l.nI1(13,"async"),l.k0s(),l.nrm(14,"input",9),l.k0s()(),l.j41(15,"div",4)(16,"div",5)(17,"label",6),l.EFF(18),l.nI1(19,"async"),l.k0s(),l.nrm(20,"input",10),l.k0s()(),l.j41(21,"button",11),l.EFF(22),l.nI1(23,"async"),l.k0s()()()}if(2&i){const t=l.XpG();l.R7$(),l.Y8G("formGroup",t.form),l.R7$(3),l.Y8G("htmlFor",t.getId("image-popup-url")),l.R7$(),l.JRh(l.bMT(6,13,t.getLabel("url"))),l.R7$(2),l.Y8G("id",t.getId("image-popup-url")),l.R7$(),l.Y8G("ngIf",t.src.touched&&t.src.invalid),l.R7$(3),l.Y8G("htmlFor",t.getId("image-popup-label")),l.R7$(),l.JRh(l.bMT(13,15,t.getLabel("altText"))),l.R7$(2),l.Y8G("id",t.getId("image-popup-label")),l.R7$(3),l.Y8G("htmlFor",t.getId("image-popup-title")),l.R7$(),l.JRh(l.bMT(19,17,t.getLabel("title"))),l.R7$(2),l.Y8G("id",t.getId("image-popup-title")),l.R7$(),l.Y8G("disabled",!t.form.valid||!t.form.dirty),l.R7$(),l.JRh(l.bMT(23,19,t.getLabel("insert")))}}function gp(i,e){if(1&i&&(l.j41(0,"div",12),l.EFF(1),l.nI1(2,"async"),l.k0s()),2&i){const t=l.XpG(2);l.R7$(),l.SpI(" ",l.bMT(2,1,(null==t.href.errors?null:t.href.errors.pattern)&&t.getLabel("enterValidUrl"))," ")}}function yp(i,e){if(1&i&&(l.j41(0,"div",12),l.EFF(1),l.k0s()),2&i){const t=l.XpG(2);l.R7$(),l.SpI(" ",(null==t.text.errors?null:t.text.errors.required)&&"This is required"," ")}}function bp(i,e){if(1&i&&(l.j41(0,"div",4)(1,"div",5)(2,"label"),l.nrm(3,"input",13),l.EFF(4),l.nI1(5,"async"),l.k0s()()()),2&i){const t=l.XpG(2);l.R7$(4),l.SpI(" ",l.bMT(5,1,t.getLabel("openInNewTab"))," ")}}function _p(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",2)(1,"form",3),l.bIt("ngSubmit",function(r){l.eBV(t);const o=l.XpG();return l.Njj(o.insertLink(r))}),l.j41(2,"div",4)(3,"div",5)(4,"label",6),l.EFF(5),l.nI1(6,"async"),l.k0s(),l.nrm(7,"input",7),l.DNE(8,gp,3,3,"div",8),l.k0s()(),l.j41(9,"div",4)(10,"div",5)(11,"label",6),l.EFF(12),l.nI1(13,"async"),l.k0s(),l.nrm(14,"input",9),l.DNE(15,yp,2,1,"div",8),l.k0s()(),l.DNE(16,bp,6,3,"div",10),l.j41(17,"button",11),l.EFF(18),l.nI1(19,"async"),l.k0s()()()}if(2&i){const t=l.XpG();l.R7$(),l.Y8G("formGroup",t.form),l.R7$(3),l.Y8G("htmlFor",t.getId("link-popup-url")),l.R7$(),l.JRh(l.bMT(6,12,t.getLabel("url"))),l.R7$(2),l.Y8G("id",t.getId("link-popup-url")),l.R7$(),l.Y8G("ngIf",t.href.touched&&t.href.invalid),l.R7$(3),l.Y8G("htmlFor",t.getId("link-popup-label")),l.R7$(),l.JRh(l.bMT(13,14,t.getLabel("text"))),l.R7$(2),l.Y8G("id",t.getId("link-popup-label")),l.R7$(),l.Y8G("ngIf",t.text.touched&&t.text.invalid),l.R7$(),l.Y8G("ngIf",t.options.showOpenInNewTab),l.R7$(),l.Y8G("disabled",!t.form.valid),l.R7$(),l.JRh(l.bMT(19,16,t.getLabel("insert")))}}const xp=(i,e)=>({"NgxEditor--Disabled":i,"NgxEditor__MenuBar--Reverse":e});function Cp(i,e){if(1&i&&l.nrm(0,"ngx-toggle-command",7),2&i){const t=l.XpG().$implicit,n=l.XpG(2);l.HbH(n.iconContainerClass),l.Y8G("toolbarItem",t)}}function wp(i,e){if(1&i&&l.nrm(0,"ngx-insert-command",7),2&i){const t=l.XpG().$implicit,n=l.XpG(2);l.HbH(n.iconContainerClass),l.Y8G("toolbarItem",t)}}function kp(i,e){if(1&i&&(l.qex(0),l.nrm(1,"ngx-link",8),l.bVm()),2&i){const t=l.XpG().$implicit,n=l.XpG(2);l.R7$(),l.HbH(n.iconContainerClass),l.Y8G("options",n.getLinkOptions(t))}}function vp(i,e){if(1&i&&l.nrm(0,"ngx-image"),2&i){const t=l.XpG(3);l.HbH(t.iconContainerClass)}}function Tp(i,e){if(1&i&&l.nrm(0,"ngx-dropdown",10),2&i){const t=e.$implicit,n=l.XpG(4);l.HbH(n.dropdownContainerClass),l.Y8G("group",t.key)("items",t.value)}}function Mp(i,e){if(1&i&&(l.qex(0),l.DNE(1,Tp,1,4,"ngx-dropdown",9),l.nI1(2,"keyvalue"),l.bVm()),2&i){const t=l.XpG().$implicit,n=l.XpG(2);l.R7$(),l.Y8G("ngForOf",l.bMT(2,2,n.getDropdownItems(t)))("ngForTrackBy",n.trackByIndex)}}function Ep(i,e){if(1&i&&l.nrm(0,"ngx-color-picker",11),2&i){const t=l.XpG(3);l.HbH(t.iconContainerClass),l.Y8G("presets",t.presets)}}function Sp(i,e){if(1&i&&l.nrm(0,"ngx-color-picker",12),2&i){const t=l.XpG(3);l.HbH(t.iconContainerClass),l.Y8G("presets",t.presets)}}function Np(i,e){if(1&i&&l.nrm(0,"div"),2&i){const t=l.XpG(3);l.HbH(t.seperatorClass)}}function Op(i,e){if(1&i&&(l.qex(0),l.DNE(1,Cp,1,3,"ngx-toggle-command",3)(2,wp,1,3,"ngx-insert-command",3)(3,kp,2,3,"ng-container",2)(4,vp,1,2,"ngx-image",4)(5,Mp,3,4,"ng-container",2)(6,Ep,1,3,"ngx-color-picker",5)(7,Sp,1,3,"ngx-color-picker",6)(8,Np,1,2,"div",4),l.bVm()),2&i){const t=e.$implicit,n=e.last,r=l.XpG().last,o=l.XpG();l.R7$(),l.Y8G("ngIf",o.toggleCommands.includes(t)),l.R7$(),l.Y8G("ngIf",o.insertCommands.includes(t)),l.R7$(),l.Y8G("ngIf",o.isLinkItem(t)),l.R7$(),l.Y8G("ngIf","image"===t),l.R7$(),l.Y8G("ngIf",o.isDropDown(t)),l.R7$(),l.Y8G("ngIf","text_color"===t),l.R7$(),l.Y8G("ngIf","background_color"===t),l.R7$(),l.Y8G("ngIf",n&&!r)}}function Dp(i,e){if(1&i&&(l.qex(0),l.DNE(1,Op,9,8,"ng-container",1),l.bVm()),2&i){const t=e.$implicit,n=l.XpG();l.R7$(),l.Y8G("ngForOf",t)("ngForTrackBy",n.trackByIndex)}}function Ip(i,e){if(1&i&&(l.qex(0),l.eu8(1,13),l.bVm()),2&i){const t=l.XpG();l.R7$(),l.Y8G("ngTemplateOutlet",t.customMenuRef)}}const Ap=(i,e)=>({"NgxBubbleMenu__Icon--Active":i,"NgxEditor--Disabled":e});function Rp(i,e){if(1&i){const t=l.RV6();l.j41(0,"button",3),l.nI1(1,"async"),l.bIt("mousedown",function(r){l.eBV(t);const o=l.XpG().$implicit,s=l.XpG(2);return l.Njj(s.onClick(r,o))}),l.k0s()}if(2&i){const t=l.XpG().$implicit,n=l.XpG(2);l.Y8G("ngClass",l.l_i(5,Ap,n.activeItems.includes(t),!n.execulableItems.includes(t)))("title",l.bMT(1,3,n.getTitle(t)))("innerHTML",n.getIcon(t),l.npT)}}function Fp(i,e){1&i&&l.nrm(0,"div",4)}function Pp(i,e){if(1&i&&(l.qex(0),l.DNE(1,Rp,2,8,"button",1)(2,Fp,1,0,"div",2),l.bVm()),2&i){const t=e.$implicit,n=e.last,r=l.XpG().last,o=l.XpG();l.R7$(),l.Y8G("ngIf",o.toggleCommands.includes(t)),l.R7$(),l.Y8G("ngIf",n&&!r)}}function Lp(i,e){if(1&i&&(l.qex(0),l.DNE(1,Pp,3,2,"ng-container",0),l.bVm()),2&i){const t=e.$implicit,n=l.XpG();l.R7$(),l.Y8G("ngForOf",t)("ngForTrackBy",n.trackByIndex)}}function Vp(i,e){if(1&i&&(l.qex(0),l.nrm(1,"ngx-bubble",2),l.bVm()),2&i){const t=l.XpG();l.R7$(),l.Y8G("editor",t.editor)}}const Rl=i=>"string"==typeof i,Fl=i=>(()=>{if(!(typeof window>"u"))return window.trustedTypes})()?.isHTML(i)??!1,Pl=i=>Rl(i)||Fl(i),Ll={type:"doc",content:[{type:"paragraph"}]},cn=(i,e,t)=>{if(!i)return e.nodeFromJSON(Ll);if(!Pl(i))return e.nodeFromJSON(i);const n=((i,e,t)=>{const n=e??Sn,r=document.createElement("div");return r.innerHTML=i,lt.fromSchema(n).parse(r,t).toJSON()})(i,e,t);return e.nodeFromJSON(n)},Hp=(i=!0)=>new ve({key:new Oe("editable"),state:{init:()=>i,apply:(e,t)=>e.getMeta("UPDATE_EDITABLE")??t},props:{editable(e){return this.getState(e)},attributes(e){return this.getState(e)?null:{class:"NgxEditor__Content--Disabled"}}}}),Gp=i=>new ve({key:new Oe("placeholder"),state:{init:()=>i??"",apply:(e,t)=>e.getMeta("UPDATE_PLACEHOLDER")??t},props:{decorations(e){const{doc:t}=e,{textContent:n,childCount:r}=t,o=this.getState(e);if(!o||r>1)return B.empty;const s=[];return t.descendants((c,d)=>{if(c.type.isBlock&&0===c.childCount&&0===n.length){const h=pe.node(d,d+c.nodeSize,{class:"NgxEditor__Placeholder","data-placeholder":o,"data-align":c.attrs.align??null});s.push(h)}return!1}),B.create(t,s)}}});let Vl=(()=>{class i{src;alt="";title="";outerWidth="";selected=!1;view;imageResize=new l.bkB;imgEl;startResizing(t,n){t.preventDefault(),this.resizeImage(t,n)}resizeImage(t,n){const r=t.pageX,o=this.imgEl.nativeElement.clientWidth,s="left"===n,{width:a}=window.getComputedStyle(this.view.dom),c=parseInt(a,10),d=f=>{const p=f.pageX-r,m=s?o-p:o+p;m>c||m<20||(this.outerWidth=`${m}px`)},u=f=>{f.preventDefault(),document.removeEventListener("mousemove",d),document.removeEventListener("mouseup",u),this.imageResize.emit()};document.addEventListener("mousemove",d),document.addEventListener("mouseup",u)}static \u0275fac=function(n){return new(n||i)};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-image-view"]],viewQuery:function(n,r){if(1&n&&l.GBs(np,7),2&n){let o;l.mGM(o=l.lsd())&&(r.imgEl=o.first)}},inputs:{src:"src",alt:"alt",title:"title",outerWidth:"outerWidth",selected:"selected",view:"view"},outputs:{imageResize:"imageResize"},standalone:!0,features:[l.aNF],decls:4,vars:9,consts:[["imgEl",""],[1,"NgxEditor__ImageWrapper",3,"ngClass"],["class","NgxEditor__ResizeHandle",4,"ngIf"],[3,"src","alt","title"],[1,"NgxEditor__ResizeHandle"],[1,"NgxEditor__ResizeHandle--TL",3,"mousedown"],[1,"NgxEditor__ResizeHandle--TR",3,"mousedown"],[1,"NgxEditor__ResizeHandle--BL",3,"mousedown"],[1,"NgxEditor__ResizeHandle--BR",3,"mousedown"]],template:function(n,r){1&n&&(l.j41(0,"span",1),l.DNE(1,rp,5,0,"span",2),l.nrm(2,"img",3,0),l.k0s()),2&n&&(l.xc7("width",r.outerWidth),l.Y8G("ngClass",l.eq3(7,ip,r.selected)),l.R7$(),l.Y8G("ngIf",r.selected),l.R7$(),l.Y8G("src",r.src,l.B4B)("alt",r.alt)("title",r.title))},dependencies:[N.MD,N.YU,N.bT],styles:["*[_ngcontent-%COMP%], *[_ngcontent-%COMP%]:before, *[_ngcontent-%COMP%]:after{box-sizing:border-box}img[_ngcontent-%COMP%]{width:100%;height:100%}.NgxEditor__ImageWrapper[_ngcontent-%COMP%]{position:relative;display:inline-block;line-height:0;padding:2px}.NgxEditor__ImageWrapper.NgxEditor__Resizer--Active[_ngcontent-%COMP%]{padding:1px;border:1px solid #1a73e8}.NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]{position:absolute;height:100%;width:100%}.NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--TL[_ngcontent-%COMP%], .NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--BL[_ngcontent-%COMP%], .NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--TR[_ngcontent-%COMP%], .NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--BR[_ngcontent-%COMP%]{position:absolute;width:7px;height:7px;background-color:#1a73e8;border:1px solid white}.NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--BR[_ngcontent-%COMP%]{bottom:-5px;right:-5px;cursor:se-resize}.NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--TR[_ngcontent-%COMP%]{top:-5px;right:-5px;cursor:ne-resize}.NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--TL[_ngcontent-%COMP%]{top:-5px;left:-5px;cursor:nw-resize}.NgxEditor__ImageWrapper[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle[_ngcontent-%COMP%]   .NgxEditor__ResizeHandle--BL[_ngcontent-%COMP%]{bottom:-5px;left:-5px;cursor:sw-resize}"]})}return i})();class Jp{dom;view;getPos;applicationRef;imageComponentRef;resizeSubscription;node;updating=!1;constructor(e,t,n,r){this.applicationRef=r.get(l.o8S),this.imageComponentRef=(0,l.a0P)(Vl,{environmentInjector:this.applicationRef.injector}),this.applicationRef.attachView(this.imageComponentRef.hostView),this.setNodeAttributes(e.attrs),this.imageComponentRef.instance.view=t,this.dom=this.imageComponentRef.location.nativeElement,this.view=t,this.node=e,this.getPos=n,this.resizeSubscription=this.imageComponentRef.instance.imageResize.subscribe(()=>{this.handleResize()})}computeChanges(e,t){return JSON.stringify(e)===JSON.stringify(t)}setNodeAttributes(e){this.imageComponentRef.instance.src=e.src,this.imageComponentRef.instance.alt=e.alt,this.imageComponentRef.instance.title=e.title,this.imageComponentRef.instance.outerWidth=e.width}handleResize=()=>{if(this.updating)return;const{state:e,dispatch:t}=this.view,{tr:n}=e,r=n.setNodeMarkup(this.getPos(),void 0,{...this.node.attrs,width:this.imageComponentRef.instance.outerWidth}),o=r.doc.resolve(this.getPos()),s=new v(o);r.setSelection(s),t(r)};update(e){return e.type===this.node.type&&(this.node=e,this.computeChanges(this.node.attrs,e.attrs)&&(this.updating=!0,this.setNodeAttributes(e.attrs),this.updating=!1),!0)}ignoreMutation(){return!0}selectNode(){this.imageComponentRef.instance.selected=!0}deselectNode(){this.imageComponentRef.instance.selected=!1}destroy(){this.resizeSubscription.unsubscribe(),this.applicationRef.detachView(this.imageComponentRef.hostView)}}const Yp=/(?:https?:\/\/)?[\w-]+(?:\.[\w-]+)+\.?(?:\d+)?(?:\/\S*)?$/,Bl=i=>{const e=[];return i.forEach(t=>{if(t.isText){const n=t.text;let r=0;const o=Yp.exec(n);if(o){const s=o.index,a=s+o[0].length,{link:c}=t.type.schema.marks;s>0&&e.push(t.cut(r,s));const d=n.slice(s,a);e.push(t.cut(s,a).mark(c.create({href:d}).addToSet(t.marks))),r=a}r<n.length&&e.push(t.cut(r))}else e.push(t.copy(Bl(t.content)))}),_.fromArray(e)};let Qp=(()=>{class i{renderer;injector;elementRef;constructor(t,n,r){this.renderer=t,this.injector=n,this.elementRef=r}ngxEditor;editor;outputFormat;placeholder="Type Here...";focusOut=new l.bkB;focusIn=new l.bkB;unsubscribe=new xt.B;onChange=()=>{};onTouched=()=>{};writeValue(t){!this.outputFormat&&Pl(t)&&(this.outputFormat="html"),this.editor.setContent(t??Ll)}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.setMeta("UPDATE_EDITABLE",!t),this.renderer.setProperty(this.elementRef.nativeElement,"disabled",t)}handleChange(t){if("html"!==this.outputFormat)this.onChange(t);else{const n=(i=>{const t=this.editor.schema??Sn,n=t.nodeFromJSON(i),r=at.fromSchema(t).serializeFragment(n.content),o=document.createElement("div");return o.appendChild(r),o.innerHTML})(t);this.onChange(n)}}setMeta(t,n){const{dispatch:r,state:{tr:o}}=this.editor.view;r(o.setMeta(t,n))}setPlaceholder(t){this.setMeta("UPDATE_PLACEHOLDER",t)}registerPlugins(){this.editor.registerPlugin(Hp()),this.editor.registerPlugin(Gp(this.placeholder)),this.editor.registerPlugin(((i={})=>new ve({key:new Oe("attributes"),props:{attributes:i}}))({class:"NgxEditor__Content"})),this.editor.registerPlugin((i=>new ve({key:new Oe("focus"),props:{handleDOMEvents:{focus:()=>(i(),!1)}}}))(()=>{this.focusIn.emit()})),this.editor.registerPlugin((i=>new ve({key:new Oe("blur"),props:{handleDOMEvents:{blur:()=>(i(),!1)}}}))(()=>{this.focusOut.emit(),this.onTouched()})),this.editor.features.resizeImage&&this.editor.registerPlugin((i=>new ve({key:new Oe("image-resize"),props:{nodeViews:{image:(e,t,n)=>new Jp(e,t,n,i)}}}))(this.injector)),this.editor.features.linkOnPaste&&this.editor.registerPlugin(new ve({key:new Oe("linkify"),props:{transformPasted:i=>new x(Bl(i.content),i.openStart,i.openEnd)}}))}ngOnInit(){if(!this.editor)throw new pn("Required editor instance for initializing editor component");this.registerPlugins(),this.renderer.appendChild(this.ngxEditor.nativeElement,this.editor.view.dom),this.editor.valueChanges.pipe((0,ua.Q)(this.unsubscribe)).subscribe(t=>{this.handleChange(t)})}ngOnChanges(t){t.placeholder&&!t.placeholder.isFirstChange()&&this.setPlaceholder(t.placeholder.currentValue)}ngOnDestroy(){this.unsubscribe.next(),this.unsubscribe.complete()}static \u0275fac=function(n){return new(n||i)(l.rXU(l.sFG),l.rXU(l.zZn),l.rXU(l.aKT))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-editor"]],viewQuery:function(n,r){if(1&n&&l.GBs(op,7),2&n){let o;l.mGM(o=l.lsd())&&(r.ngxEditor=o.first)}},inputs:{editor:"editor",outputFormat:"outputFormat",placeholder:"placeholder"},outputs:{focusOut:"focusOut",focusIn:"focusIn"},standalone:!0,features:[l.Jv_([{provide:w.kq,useExisting:(0,l.Rfq)(()=>i),multi:!0}]),l.OA$,l.aNF],ngContentSelectors:Al,decls:3,vars:0,consts:[["ngxEditor",""],[1,"NgxEditor"]],template:function(n,r){1&n&&(l.NAR(),l.j41(0,"div",1,0),l.SdG(2),l.k0s())},styles:[':root{--ngx-editor-border-radius: 4px;--ngx-editor-background-color: #fff;--ngx-editor-text-color: #000;--ngx-editor-placeholder-color: #6c757d;--ngx-editor-border-color: rgba(0, 0, 0, .2);--ngx-editor-wrapper-border-color: rgba(0, 0, 0, .2);--ngx-editor-menubar-bg-color: #fff;--ngx-editor-menubar-padding: 3px;--ngx-editor-menubar-height: 30px;--ngx-editor-blockquote-color: #ddd;--ngx-editor-blockquote-border-width: 3px;--ngx-editor-icon-size: 30px;--ngx-editor-popup-bg-color: #fff;--ngx-editor-popup-border-radius: 4px;--ngx-editor-popup-shadow: rgba(60, 64, 67, .15) 0px 2px 6px 2px;--ngx-editor-menu-item-border-radius: 2px;--ngx-editor-menu-item-active-color: #1a73e8;--ngx-editor-menu-item-hover-bg-color: #f1f1f1;--ngx-editor-menu-item-active-bg-color: #e8f0fe;--ngx-editor-seperator-color: #ccc;--ngx-editor-bubble-bg-color: #000;--ngx-editor-bubble-text-color: #fff;--ngx-editor-bubble-item-hover-color: #636262;--ngx-editor-bubble-seperator-color: #fff;--ngx-editor-focus-ring-color: #5e9ed6;--ngx-editor-error-color: red;--ngx-editor-click-pointer: default}.NgxEditor{background:var(--ngx-editor-background-color);color:var(--ngx-editor-text-color);background-clip:padding-box;border-radius:var(--ngx-editor-border-radius);border:1px solid var(--ngx-editor-border-color);position:relative}.NgxEditor--Disabled{opacity:.5!important;pointer-events:none!important}.NgxEditor__Placeholder:before{color:var(--ngx-editor-placeholder-color);opacity:1;-webkit-user-select:none;user-select:none;position:absolute;cursor:text;content:attr(data-placeholder)}.NgxEditor__Placeholder[data-align=right]:before{position:relative}.NgxEditor__Content{padding:8px;white-space:pre-wrap;outline:none;font-variant-ligatures:none;font-feature-settings:"liga" 0}.NgxEditor__Content p{margin:0 0 10px}.NgxEditor__Content blockquote{padding-left:16px;border-left:var(--ngx-editor-blockquote-border-width) solid var(--ngx-editor-blockquote-color);margin-left:0;margin-right:0}.NgxEditor__Content--Disabled{-webkit-user-select:none;user-select:none;pointer-events:none}.NgxEditor__Wrapper{border:1px solid var(--ngx-editor-wrapper-border-color);border-radius:var(--ngx-editor-border-radius)}.NgxEditor__Wrapper .NgxEditor__MenuBar{border-top-left-radius:var(--ngx-editor-border-radius);border-top-right-radius:var(--ngx-editor-border-radius);border-bottom:1px solid var(--ngx-editor-border-color)}.NgxEditor__Wrapper .NgxEditor{border-top-left-radius:0;border-top-right-radius:0;border:none}.NgxEditor__MenuBar{display:flex;flex-wrap:wrap;padding:var(--ngx-editor-menubar-padding);background-color:var(--ngx-editor-menubar-bg-color);gap:.25rem .1rem}.NgxEditor__MenuBar button:not(:disabled),.NgxEditor__MenuBar [role=button]:not(:disabled){cursor:var(--ngx-editor-click-pointer, default)}.NgxEditor__MenuItem{display:flex;align-items:center;justify-content:center;position:relative;flex-shrink:0}.NgxEditor__MenuItem.NgxEditor__MenuItem--IconContainer{display:flex;align-items:center;justify-content:center}.NgxEditor__MenuItem .NgxEditor__MenuItem--Icon{all:unset;appearance:none;height:var(--ngx-editor-icon-size);width:var(--ngx-editor-icon-size);transition:.2s ease-in-out;display:inline-flex;align-items:center;justify-content:center;border-radius:var(--ngx-editor-menu-item-border-radius)}.NgxEditor__MenuItem .NgxEditor__MenuItem--Icon+.NgxEditor__MenuItem--Icon{margin-left:2px}.NgxEditor__MenuItem .NgxEditor__MenuItem--Icon:focus-visible{outline:1px solid var(--ngx-editor-focus-ring-color)}.NgxEditor__MenuItem .NgxEditor__MenuItem--Icon:hover{background-color:var(--ngx-editor-menu-item-hover-bg-color)}.NgxEditor__MenuItem.NgxEditor__MenuItem--Text{padding:0 5px}.NgxEditor__MenuItem.NgxEditor__MenuItem--Active,.NgxEditor__MenuItem .NgxEditor__MenuItem--Active{background-color:var(--ngx-editor-menu-item-active-bg-color);color:var(--ngx-editor-menu-item-active-color)}.NgxEditor__Dropdown{min-width:64px;position:relative;display:flex;align-items:center;flex-shrink:0}.NgxEditor__Dropdown:hover{background-color:var(--ngx-editor-menu-item-hover-bg-color)}.NgxEditor__Dropdown .NgxEditor__Dropdown--Text{all:unset;appearance:none;display:flex;align-items:center;justify-content:center;padding:0 5px;height:100%;width:100%}.NgxEditor__Dropdown .NgxEditor__Dropdown--Text:focus-visible{outline:1px solid var(--ngx-editor-focus-ring-color)}.NgxEditor__Dropdown .NgxEditor__Dropdown--Text:after{display:inline-block;content:"";margin-left:24px;vertical-align:4px;border-top:4px solid;border-right:4px solid transparent;border-bottom:0;border-left:4px solid transparent}.NgxEditor__Dropdown .NgxEditor__Dropdown--DropdownMenu{position:absolute;left:0;box-shadow:var(--ngx-editor-popup-shadow);border-radius:var(--ngx-editor-popup-border-radius);background-color:var(--ngx-editor-popup-bg-color);z-index:10;width:100%;top:calc(var(--ngx-editor-menubar-height) + 2px);display:flex;flex-direction:column}.NgxEditor__Dropdown .NgxEditor__Dropdown--Item{all:unset;appearance:none;padding:8px;white-space:nowrap;color:inherit}.NgxEditor__Dropdown .NgxEditor__Dropdown--Item:focus-visible{outline:1px solid var(--ngx-editor-focus-ring-color)}.NgxEditor__Dropdown .NgxEditor__Dropdown--Item:hover{background-color:var(--ngx-editor-menu-item-hover-bg-color)}.NgxEditor__Dropdown .NgxEditor__Dropdown--Selected,.NgxEditor__Dropdown .NgxEditor__Dropdown--Open{color:var(--ngx-editor-menu-item-active-color);background-color:var(--ngx-editor-menu-item-active-bg-color)}.NgxEditor__Dropdown .NgxEditor__Dropdown--Active{background-color:var(--ngx-editor-menu-item-active-bg-color)}.NgxEditor__Dropdown .NgxEditor__Dropdown--Active:hover{background-color:var(--ngx-editor-menu-item-hover-bg-color)}.NgxEditor__MenuBar--Reverse .NgxEditor__Dropdown--DropdownMenu{top:unset;bottom:calc(var(--ngx-editor-menubar-height) + 2px)}.NgxEditor__MenuBar--Reverse .NgxEditor__Dropdown--Text:after{transform:rotate(180deg)}.NgxEditor__MenuBar--Reverse .NgxEditor__Popup{top:unset;bottom:calc(var(--ngx-editor-menubar-height) + 2px)}.NgxEditor__Popup{position:absolute;top:calc(var(--ngx-editor-menubar-height) + 2px);box-shadow:var(--ngx-editor-popup-shadow);border-radius:var(--ngx-editor-popup-border-radius);background-color:var(--ngx-editor-popup-bg-color);z-index:10;min-width:192px;padding:8px}.NgxEditor__Popup .NgxEditor__Popup--FormGroup{margin-bottom:8px}.NgxEditor__Popup .NgxEditor__Popup--FormGroup label{margin-bottom:3px}.NgxEditor__Popup .NgxEditor__Popup--FormGroup input[type=text],.NgxEditor__Popup .NgxEditor__Popup--FormGroup input[type=url]{padding:2px 4px}.NgxEditor__Popup .NgxEditor__Popup--Col{display:flex;flex-direction:column;position:relative}.NgxEditor__Popup .NgxEditor__Popup--Label{font-size:85%}.NgxEditor__Seperator{border-left:1px solid var(--ngx-editor-seperator-color);margin:0 5px}.NgxEditor__HelpText{font-size:80%}.NgxEditor__HelpText.NgxEditor__HelpText--Error{color:var(--ngx-editor-error-color)}\n'],encapsulation:2})}return i})(),Ze=(()=>{class i{sanitizer;constructor(t){this.sanitizer=t}transform(t){return Fl(t)?t:this.sanitizer.bypassSecurityTrustHtml(t)}static \u0275fac=function(n){return new(n||i)(l.rXU(gu.up,16))};static \u0275pipe=l.EJ8({name:"sanitizeHtml",type:i,pure:!0,standalone:!0})}return i})();class et{name;constructor(e){this.name=e}apply(){return(e,t)=>{const{schema:n}=e,r=n.marks[this.name];return!!r&&ol(r)(e,t)}}toggle(){return(e,t)=>{const{schema:n}=e,r=n.marks[this.name];return!!r&&At(r)(e,t)}}isActive(e){const{schema:t}=e,n=t.marks[this.name];return!!n&&er(e,n)}canExecute(e){return this.toggle()(e)}}class Kn{isBulletList=!1;constructor(e=!1){this.isBulletList=e}getType(e){return this.isBulletList?e.nodes.bullet_list:e.nodes.ordered_list}toggle(){return(e,t)=>{const{schema:n}=e,r=this.getType(n);return!!r&&(this.isActive(e)?To(n.nodes.list_item)(e,t):function yc(i,e=null){return function(t,n){let{$from:r,$to:o}=t.selection,s=r.blockRange(o);if(!s)return!1;let a=n?t.tr:null;return!!function bc(i,e,t,n=null){let r=!1,o=e,s=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(t)&&0==e.startIndex){if(0==e.$from.index(e.depth-1))return!1;let c=s.resolve(e.start-2);o=new bn(c,c,e.depth),e.endIndex<e.parent.childCount&&(e=new bn(e.$from,s.resolve(e.$to.end(e.depth)),e.depth)),r=!0}let a=pi(o,t,n,e);return!!a&&(i&&function _c(i,e,t,n,r){let o=_.empty;for(let u=t.length-1;u>=0;u--)o=_.from(t[u].type.create(t[u].attrs,o));i.step(new H(e.start-(n?2:0),e.end,e.start,e.end,new x(o,0,0),t.length,!0));let s=0;for(let u=0;u<t.length;u++)t[u].type==r&&(s=u+1);let a=t.length-s,c=e.start+t.length-(n?2:0),d=e.parent;for(let u=e.startIndex,f=e.endIndex,h=!0;u<f;u++,h=!1)!h&&Ct(i.doc,c,a)&&(i.split(c,a),c+=2*a),c+=d.child(u).nodeSize}(i,e,a,r,t),!0)}(a,s,i,e)&&(n&&n(a.scrollIntoView()),!0)}}(r)(e,t))}}isActive(e){const{schema:t}=e,n=this.getType(t);return!!n&&tl(e,n)}canExecute(e){return this.toggle()(e)}}class _t{level;constructor(e){this.level=e}apply(){return(e,t)=>{const{schema:n}=e,r=n.nodes.heading;return!!r&&qi(r)(e,t)}}toggle(){return(e,t)=>{const{schema:n,selection:r,doc:o}=e,s=n.nodes.heading;if(!s)return!1;const a=r.$from.before(1),d=o.nodeAt(a)?.attrs??{};return this.isActive(e)?qi(n.nodes.paragraph,d)(e,t):qi(s,{...d,level:this.level})(e,t)}}isActive(e){const{schema:t}=e,n=nl(e),r=t.nodes.heading;if(!r)return!1;const o=[r,t.nodes.text,t.nodes.blockquote];return!!n.filter(c=>o.includes(c.type)).find(c=>c.attrs.level===this.level)}canExecute(e){return this.toggle()(e)}}class dn{align;constructor(e){this.align=e}toggle(){return(e,t)=>{const{doc:n,selection:r,tr:o,schema:s}=e,{from:a,to:c}=r;let d=!1;return n.nodesBetween(a,c,(u,f)=>{const h=u.type;return[s.nodes.paragraph,s.nodes.heading].includes(h)&&(d=!0,o.setNodeMarkup(f,h,{...u.attrs,align:u.attrs.align===this.align?null:this.align})),!0}),!!d&&(o.docChanged&&t?.(o),!0)}}isActive(e){return!!nl(e).find(r=>r.attrs.align===this.align)}canExecute(e){return this.toggle()(e)}}const th={strict:!0};let _r=class{update(e){return(t,n)=>{const{schema:r,selection:o}=t,s=r.marks.link;return!(!s||o.empty)&&At(s,e)(t,n)}}insert(e,t){return(n,r)=>{const{schema:o,tr:s}=n;if(!o.marks.link)return!1;const d=o.text(e,[o.marks.link.create({href:t.href,title:t.title??e,target:t.target??"_blank"})]);return s.replaceSelectionWith(d,!1).scrollIntoView(),!!s.docChanged&&(r?.(s),!0)}}isActive(e,t=th){if(t.strict)return!0;const{schema:n}=e,r=n.marks.link;return!!r&&er(e,r)}remove(e,t){return((i,e)=>{const{doc:t,selection:n,tr:r,schema:o}=i,{$head:{pos:s},from:a,to:c}=n,d=o.marks.link;if(a===c){const u=t.resolve(s),f=s-u.textOffset,h=f+u.parent.child(u.index()).nodeSize;r.removeMark(f,h,d)}else r.removeMark(a,c,d);return!!r.docChanged&&(e?.(r),!0)})(e,t)}canExecute(e){const t={href:""};return this.insert("Exec",t)(e)||this.update(t)(e)}},zl=class{insert(e,t){return(n,r)=>{const{schema:o,tr:s,selection:a}=n,c=o.nodes.image;if(!c)return!1;const d={width:null,src:e,...t};!d.width&&a instanceof v&&a.node.type===c&&(d.width=a.node.attrs.width),s.replaceSelectionWith(c.createAndFill(d));const u=s.doc.resolve(s.selection.anchor-s.selection.$anchor.nodeBefore.nodeSize);return s.setSelection(new v(u)).scrollIntoView(),!!s.docChanged&&(r?.(s),!0)}}isActive(e){const{selection:t}=e;return t instanceof v&&"image"===t.node.type.name}},Bt=class{name;attrName;constructor(e,t="color"){this.name=e,this.attrName=t}apply(e){return(t,n)=>{const{schema:r,selection:o,doc:s}=t,a=r.marks[this.name];if(!a)return!1;const{from:c,to:d,empty:u}=o;if(!u&&c+1===d){const f=s.nodeAt(c);if(f?.isAtom&&!f.isText&&f.isLeaf)return!1}return ol(a,e)(t,n)}}isActive(e){const{schema:t}=e,n=t.marks[this.name];return!!n&&er(e,n)}getActiveColors(e){if(!this.isActive(e))return[];const{schema:t}=e;return(i=>{let e=[];const{selection:t,storedMarks:n}=i,{from:r,to:o,empty:s,$from:a}=t;return s?e=n||a.marks():i.doc.nodesBetween(r,o,c=>{e=[...e,...c.marks]}),e})(e).filter(o=>o.type===t.marks[this.name]).map(o=>o.attrs[this.attrName]).filter(Boolean)}remove(){return(e,t)=>{const{schema:n}=e,r=n.marks[this.name];return!!r&&(i=r,(e,t)=>{const{tr:n,selection:r,storedMarks:o,doc:s}=e,{empty:a,ranges:c}=r;if(a&&r instanceof A){const{$cursor:d}=r;if(!d||!il(e.doc,c,i))return!1;if(i.isInSet(o||d.marks()))return n.removeStoredMark(i),t?.(n),!0}else{for(const d of c){const{$from:u,$to:f}=d;s.rangeHasMark(u.pos,f.pos,i)&&n.removeMark(u.pos,f.pos,i)}if(!n.docChanged)return!1;t?.(n.scrollIntoView())}return!1})(e,t);var i}}canExecute(e){return this.apply("text_color"===this.name?{color:""}:{backgroundColor:""})(e)}};const nh=["link"],rh=["paragraph","heading","blockquote"];class Yn{method="increase";constructor(e){this.method=e}insert(){return(e,t)=>{const{tr:n,doc:r}=e,{from:o,to:s}=n.selection;let a=!1;return r.nodesBetween(o,s,(c,d)=>rh.includes(c.type.name)?(a=((i,e,t)=>{const n=i.doc.nodeAt(e);if(!n)return!1;const r=n.attrs.indent??0,o=(i=>Math.min(Math.max(i,0),10))(r+("increase"===t?1:-1));if(o===r||o<0||o>10)return!1;const s={...n.attrs,indent:o};return i.setNodeMarkup(e,n.type,s),!0})(n,d,this.method),!1):!c.type.name.includes("list")),!!a&&(n.docChanged&&t?.(n),!0)}}canExecute(e){return this.insert()(e)}}class jl{mode="undo";constructor(e){this.mode=e}insert(){return(e,t)=>"undo"===this.mode?ir(e,t):zn(e,t)}canExecute(e){return this.insert()(e)}}const sh=new et("strong"),lh=new et("em"),ah=new et("code"),ch=new et("u"),dh=new et("s"),uh=new class Zp{toggle(){return(e,t)=>{const{schema:n}=e,r=n.nodes.blockquote;return!!r&&(this.isActive(e)?((i,e)=>{let{$from:t,$to:n}=i.selection,r=t.blockRange(n),o=r&&Xt(r);return null!=o&&(e&&e(i.tr.lift(r,o).scrollIntoView()),!0)})(e,t):function Eu(i,e=null){return function(t,n){let{$from:r,$to:o}=t.selection,s=r.blockRange(o),a=s&&pi(s,i,e);return!!a&&(n&&n(t.tr.wrap(s,a).scrollIntoView()),!0)}}(r)(e,t))}}isActive(e){const{schema:t}=e,n=t.nodes.blockquote;return!!n&&tl(e,n)}canExecute(e){return this.toggle()(e)}},fh=new class eh{insert(){return(e,t)=>{const{schema:n,tr:r}=e,o=n.nodes.horizontal_rule;return!!o&&(t(r.replaceSelectionWith(o.create()).scrollIntoView()),!0)}}canExecute(e){return((i,e)=>{const{$from:t}=i.selection;for(let n=t.depth;n>=0;n-=1){const r=t.index(n);if(t.node(n).canReplaceWith(r,r,e))return!0}return!1})(e,e.schema.nodes.horizontal_rule)}},ph=new class ih{insert(){return(e,t)=>{const{tr:n}=e,{ranges:r,empty:o}=n.selection;return o||(Object.entries(e.schema.marks).forEach(([s,a])=>{nh.includes(s)||r.forEach(c=>{n.removeMark(c.$from.pos,c.$to.pos,a)})}),t(n)),!0}}canExecute(){return!0}},hh=new Kn(!0),mh=new Kn(!1),gh=new _t(1),yh=new _t(2),bh=new _t(3),_h=new _t(4),xh=new _t(5),Ch=new _t(6),wh=new dn("left"),kh=new dn("center"),vh=new dn("right"),Th=new dn("justify"),Mh=new _r,Eh=new zl,Sh=new Bt("text_color","color"),Nh=new Bt("text_background_color","backgroundColor"),Oh=new Yn("increase"),Dh=new Yn("decrease"),Ih=new et("sup"),Ah=new et("sub"),Rh=new jl("undo"),Fh=new jl("redo"),zt={bold:sh,italic:lh,code:ah,underline:ch,strike:dh,blockquote:uh,bullet_list:hh,ordered_list:mh,h1:gh,h2:yh,h3:bh,h4:_h,h5:xh,h6:Ch,align_left:wh,align_center:kh,align_right:vh,align_justify:Th,superscript:Ih,subscript:Ah},Gl={horizontal_rule:fh,format_clear:ph,indent:Oh,outdent:Dh,undo:Rh,redo:Fh},un=Mh,Wl=Eh,Ph=Sh,Lh=Nh;let tt=(()=>{class i{editor;customMenuRefChange=new xt.B;setCustomMenuRef(t){this.customMenuRefChange.next(t)}static \u0275fac=function(n){return new(n||i)};static \u0275prov=l.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();const xr={bold:"Bold",italic:"Italic",code:"Code",underline:"Underline",strike:"Strike",blockquote:"Blockquote",bullet_list:"Bullet List",ordered_list:"Ordered List",heading:"Heading",h1:"Header 1",h2:"Header 2",h3:"Header 3",h4:"Header 4",h5:"Header 5",h6:"Header 6",align_left:"Left Align",align_center:"Center Align",align_right:"Right Align",align_justify:"Justify",text_color:"Text Color",background_color:"Background Color",horizontal_rule:"Horizontal rule",format_clear:"Clear Formatting",insertLink:"Insert Link",removeLink:"Remove Link",insertImage:"Insert Image",indent:"Increase Indent",outdent:"Decrease Indent",superscript:"Superscript",subscript:"Subscript",undo:"Undo",redo:"Redo",url:"URL",text:"Text",openInNewTab:"Open in new tab",insert:"Insert",altText:"Alt Text",title:"Title",remove:"Remove",enterValidUrl:"Please enter a valid URL"};class Vh{locals=xr;constructor(e={}){this.locals={...xr,...e}}get=e=>{const t=this.locals[e];return t?(0,aa.A)(t)?t:(0,kr.of)(t):(0,kr.of)("")}}const Xl={bold:'\n  <path d="M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z" />\n',italic:'\n  <path d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4z" />\n',code:'\n<path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>\n',underline:'\n<path d="M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z"/>\n',strike:'\n<path d="M6.85,7.08C6.85,4.37,9.45,3,12.24,3c1.64,0,3,0.49,3.9,1.28c0.77,0.65,1.46,1.73,1.46,3.24h-3.01 c0-0.31-0.05-0.59-0.15-0.85c-0.29-0.86-1.2-1.28-2.25-1.28c-1.86,0-2.34,1.02-2.34,1.7c0,0.48,0.25,0.88,0.74,1.21 C10.97,8.55,11.36,8.78,12,9H7.39C7.18,8.66,6.85,8.11,6.85,7.08z M21,12v-2H3v2h9.62c1.15,0.45,1.96,0.75,1.96,1.97 c0,1-0.81,1.67-2.28,1.67c-1.54,0-2.93-0.54-2.93-2.51H6.4c0,0.55,0.08,1.13,0.24,1.58c0.81,2.29,3.29,3.3,5.67,3.3 c2.27,0,5.3-0.89,5.3-4.05c0-0.3-0.01-1.16-0.48-1.94H21V12z"/>\n',ordered_list:'\n<path d="M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z"/>\n',bullet_list:'\n<path d="M4 10.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0-6c-.83 0-1.5.67-1.5 1.5S3.17 7.5 4 7.5 5.5 6.83 5.5 6 4.83 4.5 4 4.5zm0 12c-.83 0-1.5.68-1.5 1.5s.68 1.5 1.5 1.5 1.5-.68 1.5-1.5-.67-1.5-1.5-1.5zM7 19h14v-2H7v2zm0-6h14v-2H7v2zm0-8v2h14V5H7z"/>\n',blockquote:'\n<path d="M0 0h24v24H0z" fill="none"/><path d="M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"/>\n',link:'\n<path d="M0 0h24v24H0z" fill="none"/><path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>\n',unlink:'\n<path d="M17 7h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1 0 1.43-.98 2.63-2.31 2.98l1.46 1.46C20.88 15.61 22 13.95 22 12c0-2.76-2.24-5-5-5zm-1 4h-2.19l2 2H16zM2 4.27l3.11 3.11C3.29 8.12 2 9.91 2 12c0 2.76 2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1 0-1.59 1.21-2.9 2.76-3.07L8.73 11H8v2h2.73L13 15.27V17h1.73l4.01 4L20 19.74 3.27 3 2 4.27z"/>\n',image:'\n<path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>\n',align_left:'\n<path d="M15 15H3v2h12v-2zm0-8H3v2h12V7zM3 13h18v-2H3v2zm0 8h18v-2H3v2zM3 3v2h18V3H3z"/>\n',align_center:'\n<path d="M7 15v2h10v-2H7zm-4 6h18v-2H3v2zm0-8h18v-2H3v2zm4-6v2h10V7H7zM3 3v2h18V3H3z"/>\n',align_right:'\n<path d="M3 21h18v-2H3v2zm6-4h12v-2H9v2zm-6-4h18v-2H3v2zm6-4h12V7H9v2zM3 3v2h18V3H3z"/>\n',align_justify:'\n<path d="M3 21h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18V7H3v2zm0-6v2h18V3H3z"/>\n',text_color:'\n<path d="M2,20h20v4H2V20z M5.49,17h2.42l1.27-3.58h5.65L16.09,17h2.42L13.25,3h-2.5L5.49,17z M9.91,11.39l2.03-5.79h0.12l2.03,5.79 H9.91z"/>\n',color_fill:'\n<path d="M16.56,8.94L7.62,0L6.21,1.41l2.38,2.38L3.44,8.94c-0.59,0.59-0.59,1.54,0,2.12l5.5,5.5C9.23,16.85,9.62,17,10,17 s0.77-0.15,1.06-0.44l5.5-5.5C17.15,10.48,17.15,9.53,16.56,8.94z M5.21,10L10,5.21L14.79,10H5.21z M19,11.5c0,0-2,2.17-2,3.5 c0,1.1,0.9,2,2,2s2-0.9,2-2C21,13.67,19,11.5,19,11.5z M2,20h20v4H2V20z"/>\n',horizontal_rule:'\n  <g>\n    <rect fill="none" fill-rule="evenodd" height="24" width="24"/>\n    <rect fill-rule="evenodd" height="2" width="16" x="4" y="11"/>\n  </g>\n',format_clear:'\n<path d="M0 0h24v24H0z" fill="none"/><path d="M3.27 5L2 6.27l6.97 6.97L6.5 19h3l1.57-3.66L16.73 21 18 19.73 3.55 5.27 3.27 5zM6 5v.18L8.82 8h2.4l-.72 1.68 2.1 2.1L14.21 8H20V5H6z"/>\n',indent:'<path d="M0 0h24v24H0V0z" fill="none"/><path d="M3 21h18v-2H3v2zM3 8v8l4-4-4-4zm8 9h10v-2H11v2zM3 3v2h18V3H3zm8 6h10V7H11v2zm0 4h10v-2H11v2z"/>',outdent:'<path d="M0 0h24v24H0V0z" fill="none"/><path d="M11 17h10v-2H11v2zm-8-5l4 4V8l-4 4zm0 9h18v-2H3v2zM3 3v2h18V3H3zm8 6h10V7H11v2zm0 4h10v-2H11v2z"/>',superscript:'<g><rect fill="none" height="20" width="20"/><path d="M17,6l-1,0v1h2v1l-3,0V6c0-0.55,0.45-1,1-1l1,0l0-1h-2V3l2,0c0.55,0,1,0.45,1,1v1C18,5.55,17.55,6,17,6z M5.63,16h1.9 l2.43-3.87h0.08L12.47,16h1.9l-3.32-5.2l3.1-4.8h-1.91l-2.19,3.56H9.96L7.75,6h-1.9l3.09,4.8L5.63,16z"/></g>',subscript:'<g><rect fill="none" height="20" width="20"/><path d="M17,15l-1,0v1h2v1h-3v-2c0-0.55,0.45-1,1-1l1,0l0-1h-2v-1l2,0c0.55,0,1,0.45,1,1v1C18,14.55,17.55,15,17,15z M5.63,14h1.9 l2.43-3.87h0.08L12.47,14h1.9l-3.32-5.2l3.1-4.8h-1.91l-2.19,3.56H9.96L7.75,4h-1.9l3.09,4.8L5.63,14z"/></g>',undo:'<path d="M0 0h24v24H0V0z" fill="none"/><path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>',redo:'<path d="M0 0h24v24H0V0z" fill="none"/><path d="M18.4 10.6C16.55 8.99 14.15 8 11.5 8c-4.65 0-8.58 3.03-9.96 7.22L3.9 16c1.05-3.19 4.05-5.5 7.6-5.5 1.95 0 3.73.72 5.12 1.88L13 16h9V7l-3.6 3.6z"/>',path:"<path></path>"};class pm{static get(e,t="currentColor"){const n=Xl[e];return n&&(n.includes("<path")||n.includes("<g"))?`\n        <svg\n          xmlns="http://www.w3.org/2000/svg"\n          viewBox="0 0 24 24"\n          fill=${t}\n          height=20\n          width=20\n        >\n          ${n}\n        </svg>\n      `:n}}let Cr=(()=>{class i{locals={};icons={};static \u0275fac=function(n){return new(n||i)};static \u0275prov=l.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})(),nt=(()=>{class i{config;constructor(t){this.config=t}get locals(){return new Vh(this.config.locals)}getIcon(t){return this.config.icons[t]?this.config.icons[t]:pm.get(t)}static \u0275fac=function(n){return new(n||i)(l.KVO(Cr,8))};static \u0275prov=l.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();const Ul=i=>({locals:i.locals??{},icons:i.icons??{}});let Jl=(()=>{class i{el;menuService;ngxeService;presets;type;constructor(t,n,r){this.el=t,this.menuService=n,this.ngxeService=r}get title(){return this.getLabel("text_color"===this.type?"text_color":"background_color")}get icon(){return this.ngxeService.getIcon("text_color"===this.type?"text_color":"color_fill")}get command(){return"text_color"===this.type?Ph:Lh}updateSubscription;editorView;showPopup=!1;isActive=!1;activeColors=[];canExecute=!0;getContrastYIQ(t){const n=t.replace("#","");return(299*parseInt(n.substring(0,2),16)+587*parseInt(n.substring(2,4),16)+114*parseInt(n.substring(4,6),16))/1e3>=128?"black":"white"}onDocumentClick(t){!this.el.nativeElement.contains(t.target)&&this.showPopup&&this.hidePopup()}hidePopup(){this.showPopup=!1}togglePopup(){this.showPopup=!this.showPopup}onTogglePopupMouseClick(t){t.preventDefault(),0===t.button&&this.togglePopup()}onTogglePopupKeydown(){this.togglePopup()}remove(){const{state:t,dispatch:n}=this.editorView;this.command.remove()(t,n),this.hidePopup()}onRemoveMouseClick(t){t.preventDefault(),0===t.button&&(t.preventDefault(),this.remove())}onRemoveKeydown(){this.remove()}trackByIndex(t){return t}selectColor(t){const{state:n,dispatch:r}=this.editorView;"text_color"===this.type?this.command.apply({color:t})(n,r):this.command.apply({backgroundColor:t})(n,r),this.editorView.hasFocus()||this.editorView.focus(),this.hidePopup()}onColorSelectMouseClick(t,n){t.preventDefault(),0===t.button&&this.selectColor(n)}onColorSelectKeydown(t){this.selectColor(t)}update=t=>{const{state:n}=t;this.canExecute=this.command.canExecute(n),this.isActive=this.command.isActive(n),this.activeColors=[],this.isActive&&(this.activeColors=this.command.getActiveColors(n))};getLabel(t){return this.ngxeService.locals.get(t)}ngOnInit(){this.editorView=this.menuService.editor.view,this.updateSubscription=this.menuService.editor.update.subscribe(t=>{this.update(t)})}ngOnDestroy(){this.updateSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(l.aKT),l.rXU(tt),l.rXU(nt))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-color-picker"]],hostBindings:function(n,r){1&n&&l.bIt("mousedown",function(s){return r.onDocumentClick(s)},!1,l.EBC)},inputs:{presets:"presets",type:"type"},standalone:!0,features:[l.aNF],decls:5,vars:15,consts:[["type","button",1,"NgxEditor__MenuItem--Icon",3,"mousedown","keydown.enter","keydown.space","disabled","innerHTML","title","ariaLabel"],["class","NgxEditor__Popup",4,"ngIf"],[1,"NgxEditor__Popup"],["class","NgxEditor__ColorContainer",4,"ngFor","ngForOf","ngForTrackBy"],[1,"NgxEditor__MenuItem--Button",3,"mousedown","keydown.enter","keydown.space","disabled"],[1,"NgxEditor__ColorContainer"],["class","NgxEditor__Color",3,"ngStyle","title","ngClass","mousedown","keydown.enter","keydown.space",4,"ngFor","ngForOf","ngForTrackBy"],[1,"NgxEditor__Color",3,"mousedown","keydown.enter","keydown.space","ngStyle","title","ngClass"]],template:function(n,r){1&n&&(l.j41(0,"button",0),l.nI1(1,"sanitizeHtml"),l.nI1(2,"async"),l.nI1(3,"async"),l.bIt("mousedown",function(s){return r.onTogglePopupMouseClick(s)})("keydown.enter",function(){return r.onTogglePopupKeydown()})("keydown.space",function(){return r.onTogglePopupKeydown()}),l.k0s(),l.DNE(4,dp,5,6,"div",1)),2&n&&(l.AVh("NgxEditor__MenuItem--Active",r.isActive||r.showPopup)("NgxEditor--Disabled",!r.canExecute),l.Y8G("disabled",!r.canExecute)("innerHTML",l.bMT(1,9,r.icon),l.npT)("title",l.bMT(2,11,r.title))("ariaLabel",l.bMT(3,13,r.title)),l.R7$(4),l.Y8G("ngIf",r.showPopup))},dependencies:[N.Jj,N.MD,N.YU,N.Sq,N.bT,N.B3,Ze],styles:['@charset "UTF-8";.NgxEditor__Popup[_ngcontent-%COMP%]{width:230px}.NgxEditor__ColorContainer[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.NgxEditor__ColorContainer[_ngcontent-%COMP%] + .NgxEditor__ColorContainer[_ngcontent-%COMP%]{margin-top:5px}.NgxEditor__Color[_ngcontent-%COMP%]{border:none;outline:none;border-radius:6px;width:24px;height:24px;flex-shrink:0}.NgxEditor__Color[_ngcontent-%COMP%]:focus-visible{outline:1px solid var(--ngx-editor-focus-ring-color);outline-offset:1px}.NgxEditor__Color--Active[_ngcontent-%COMP%]:after{content:"\\2714";font-size:90%}.NgxEditor__MenuItem--Button[_ngcontent-%COMP%]{margin-top:5px}']})}return i})(),Kl=(()=>{class i{ngxeService;menuService;el;editorView;updateSubscription;group;items;isDropdownOpen=!1;disabledItems=[];activeItem;constructor(t,n,r){this.ngxeService=t,this.menuService=n,this.el=r}get isSelected(){return!(!this.activeItem&&!this.isDropdownOpen)}get isDropdownDisabled(){return this.disabledItems.length===this.items.length}onDocumentClick(t){!this.el.nativeElement.contains(t)&&this.isDropdownOpen&&(this.isDropdownOpen=!1)}getName(t){return this.ngxeService.locals.get(t)}getIsDropdownActive(t){return this.activeItem===t}toggleDropdown(){this.isDropdownOpen=!this.isDropdownOpen}onToggleDropdownMouseClick(t){t.preventDefault(),0===t.button&&this.toggleDropdown()}onToggleDropdownKeydown(){this.toggleDropdown()}trackByIndex(t){return t}selectItem(t){const n=zt[t],{state:r,dispatch:o}=this.editorView;n.toggle()(r,o),this.isDropdownOpen=!1}onDropdownItemMouseClick(t,n){t.preventDefault(),0===t.button&&this.selectItem(n)}onDropdownItemKeydown(t,n){t.preventDefault(),this.selectItem(n)}update=t=>{const{state:n}=t;this.disabledItems=[];const r=[];this.items.forEach(o=>{const s=zt[o];s.isActive(n)&&r.push(o),s.canExecute(n)||this.disabledItems.push(o)}),1===r.length?[this.activeItem]=r:this.activeItem=null};ngOnInit(){this.editorView=this.menuService.editor.view,this.updateSubscription=this.menuService.editor.update.subscribe(t=>{this.update(t)})}ngOnDestroy(){this.updateSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(nt),l.rXU(tt),l.rXU(l.aKT))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-dropdown"]],hostBindings:function(n,r){1&n&&l.bIt("mousedown",function(s){return r.onDocumentClick(s.target)},!1,l.EBC)},inputs:{group:"group",items:"items"},standalone:!0,features:[l.aNF],decls:5,vars:13,consts:[["type","button","aria-haspopup","listbox",1,"NgxEditor__Dropdown--Text",3,"mousedown","keydown.enter","keydown.space","disabled","ariaLabel","ariaExpanded"],["class","NgxEditor__Dropdown--DropdownMenu","role","listbox",4,"ngIf"],["role","listbox",1,"NgxEditor__Dropdown--DropdownMenu"],["type","button","class","NgxEditor__Dropdown--Item","role","option",3,"ngClass","ariaLabel","mousedown","keydown.enter","keydown.space",4,"ngFor","ngForOf","ngForTrackBy"],["type","button","role","option",1,"NgxEditor__Dropdown--Item",3,"mousedown","keydown.enter","keydown.space","ngClass","ariaLabel"]],template:function(n,r){1&n&&(l.j41(0,"button",0),l.nI1(1,"async"),l.bIt("mousedown",function(s){return r.onToggleDropdownMouseClick(s)})("keydown.enter",function(){return r.onToggleDropdownKeydown()})("keydown.space",function(){return r.onToggleDropdownKeydown()}),l.EFF(2),l.nI1(3,"async"),l.k0s(),l.DNE(4,pp,2,2,"div",1)),2&n&&(l.AVh("NgxEditor__Dropdown--Selected",r.isSelected)("NgxEditor--Disabled",r.isDropdownDisabled),l.Y8G("disabled",r.isDropdownDisabled)("ariaLabel",l.bMT(1,9,r.getName(r.activeItem||r.group)))("ariaExpanded",r.isDropdownOpen),l.R7$(2),l.SpI(" ",l.bMT(3,11,r.getName(r.activeItem||r.group)),"\n"),l.R7$(2),l.Y8G("ngIf",r.isDropdownOpen))},dependencies:[N.Jj,N.MD,N.YU,N.Sq,N.bT]})}return i})(),Yl=(()=>{class i{el;ngxeService;menuService;showPopup=!1;isActive=!1;componentId=vr();updateSubscription;form=new w.gE({src:new w.MJ("",[w.k0.required,w.k0.pattern("(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/??([^#\n\r]*)?#?([^\n\r]*)")]),alt:new w.MJ(""),title:new w.MJ("")});editorView;constructor(t,n,r){this.el=t,this.ngxeService=n,this.menuService=r}get icon(){return this.ngxeService.getIcon("image")}get src(){return this.form.get("src")}onDocumentClick(t){!this.el.nativeElement.contains(t.target)&&this.showPopup&&this.hideForm()}getId(t){return`${t}-${this.componentId}`}getLabel(t){return this.ngxeService.locals.get(t)}hideForm(){this.showPopup=!1,this.form.reset({src:"",alt:"",title:""})}togglePopup(){this.showPopup=!this.showPopup,this.showPopup&&this.fillForm()}onTogglePopupMouseClick(t){0===t.button&&this.togglePopup()}onTogglePopupKeydown(){this.togglePopup()}fillForm(){const{state:t}=this.editorView,{selection:n}=t;if(n instanceof v&&this.isActive){const{src:r,alt:o="",title:s=""}=n.node.attrs;this.form.setValue({src:r,alt:o,title:s})}}update=t=>{const{state:n}=t;this.isActive=Wl.isActive(n)};insertLink(t){t.preventDefault();const{src:n,alt:r,title:o}=this.form.getRawValue(),{dispatch:s,state:a}=this.editorView;Wl.insert(n,{alt:r,title:o})(a,s),this.editorView.focus(),this.hideForm()}ngOnInit(){this.editorView=this.menuService.editor.view,this.updateSubscription=this.menuService.editor.update.subscribe(t=>{this.update(t)})}ngOnDestroy(){this.updateSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(l.aKT),l.rXU(nt),l.rXU(tt))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-image"]],hostBindings:function(n,r){1&n&&l.bIt("mousedown",function(s){return r.onDocumentClick(s)},!1,l.EBC)},standalone:!0,features:[l.aNF],decls:5,vars:13,consts:[["type","button","aria-haspopup","dialog",1,"NgxEditor__MenuItem--Icon",3,"mousedown","keydown.enter","keydown.space","innerHTML","title","ariaLabel","ariaExpanded"],["class","NgxEditor__Popup",4,"ngIf"],[1,"NgxEditor__Popup"],[1,"NgxEditor__Popup--Form",3,"ngSubmit","formGroup"],[1,"NgxEditor__Popup--FormGroup"],[1,"NgxEditor__Popup--Col"],[1,"NgxEditor__Popup--Label",3,"htmlFor"],["type","url","formControlName","src","autocomplete","off",3,"id"],["class","NgxEditor__HelpText NgxEditor__HelpText--Error",4,"ngIf"],["type","text","formControlName","alt","autocomplete","off",3,"id"],["type","text","formControlName","title","autocomplete","off",3,"id"],["type","submit",3,"disabled"],[1,"NgxEditor__HelpText","NgxEditor__HelpText--Error"]],template:function(n,r){1&n&&(l.j41(0,"button",0),l.nI1(1,"sanitizeHtml"),l.nI1(2,"async"),l.nI1(3,"async"),l.bIt("mousedown",function(s){return r.onTogglePopupMouseClick(s)})("keydown.enter",function(){return r.onTogglePopupKeydown()})("keydown.space",function(){return r.onTogglePopupKeydown()}),l.k0s(),l.DNE(4,mp,24,21,"div",1)),2&n&&(l.AVh("NgxEditor__MenuItem--Active",r.isActive||r.showPopup),l.Y8G("innerHTML",l.bMT(1,7,r.icon),l.npT)("title",l.bMT(2,9,r.getLabel("insertImage")))("ariaLabel",l.bMT(3,11,r.getLabel("insertImage")))("ariaExpanded",r.showPopup),l.R7$(4),l.Y8G("ngIf",r.showPopup))},dependencies:[N.Jj,Ze,w.X1,w.qT,w.me,w.BC,w.cb,w.j4,w.JD,N.MD,N.bT]})}return i})(),hm=(()=>{class i{ngxeService;menuService;toolbarItem;get name(){return this.toolbarItem}html;editorView;disabled=!1;updateSubscription;constructor(t,n){this.ngxeService=t,this.menuService=n}onMouseClick(t){t.preventDefault(),0===t.button&&this.insert()}onKeydown(){this.insert()}insert(){const{state:t,dispatch:n}=this.editorView;Gl[this.name].insert()(t,n)}update=t=>{const{state:n}=t;this.disabled=!Gl[this.name].canExecute(n)};getTitle(t){return this.ngxeService.locals.get(t)}ngOnInit(){this.html=this.ngxeService.getIcon(this.name),this.editorView=this.menuService.editor.view,this.updateSubscription=this.menuService.editor.update.subscribe(t=>{this.update(t)})}ngOnDestroy(){this.updateSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(nt),l.rXU(tt))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-insert-command"]],inputs:{toolbarItem:"toolbarItem"},standalone:!0,features:[l.aNF],decls:4,vars:12,consts:[["type","button",1,"NgxEditor__MenuItem--Icon",3,"mousedown","keydown.enter","keydown.space","disabled","innerHTML","title","ariaLabel"]],template:function(n,r){1&n&&(l.j41(0,"button",0),l.nI1(1,"sanitizeHtml"),l.nI1(2,"async"),l.nI1(3,"async"),l.bIt("mousedown",function(s){return r.onMouseClick(s)})("keydown.enter",function(){return r.onKeydown()})("keydown.space",function(){return r.onKeydown()}),l.k0s()),2&n&&(l.AVh("NgxEditor--Disabled",r.disabled),l.Y8G("disabled",r.disabled)("innerHTML",l.bMT(1,6,r.html),l.npT)("title",l.bMT(2,8,r.getTitle(r.name)))("ariaLabel",l.bMT(3,10,r.getTitle(r.name))))},dependencies:[N.Jj,Ze]})}return i})();const ql={showOpenInNewTab:!0};let Ql=(()=>{class i{el;ngxeService;menuService;options=ql;showPopup=!1;isActive=!1;canExecute=!0;componentId=vr();form;editorView;updateSubscription;constructor(t,n,r){this.el=t,this.ngxeService=n,this.menuService=r}get icon(){return this.ngxeService.getIcon(this.isActive?"unlink":"link")}get title(){return this.ngxeService.locals.get(this.isActive?"removeLink":"insertLink")}get href(){return this.form.get("href")}get text(){return this.form.get("text")}onDocumentClick(t){!this.el.nativeElement.contains(t.target)&&this.showPopup&&this.hidePopup()}getId(t){return`${t}-${this.componentId}`}getLabel(t){return this.ngxeService.locals.get(t)}hidePopup(){this.showPopup=!1,this.form.reset({href:"",text:"",openInNewTab:!0}),this.text.enable()}togglePopup(){const{state:t,dispatch:n}=this.editorView;this.isActive?un.remove(t,n):(this.showPopup=!this.showPopup,this.showPopup&&this.setText())}onTogglePopupMouseClick(t){0===t.button&&this.togglePopup()}onTogglePopupKeydown(){this.togglePopup()}setText=()=>{const{state:{selection:t,doc:n}}=this.editorView,{empty:r,from:o,to:s}=t,a=r?"":n.textBetween(o,s);a&&(this.text.patchValue(a),this.text.disable())};update=t=>{const{state:n}=t;this.isActive=un.isActive(n,{strict:!1}),this.canExecute=un.canExecute(n)};insertLink(t){t.preventDefault();const{text:n,href:r,openInNewTab:o}=this.form.getRawValue(),{dispatch:s,state:a}=this.editorView,{selection:c}=a;let d;this.options.showOpenInNewTab&&(d=o?"_blank":"_self");const u={title:r,href:r,target:d};c.empty?(un.insert(n,u)(a,s),this.editorView.focus()):un.update(u)(a,s),this.hidePopup()}ngOnInit(){this.editorView=this.menuService.editor.view,this.form=new w.gE({href:new w.MJ("",[w.k0.required,w.k0.pattern(this.menuService.editor.linkValidationPattern)]),text:new w.MJ("",[w.k0.required]),openInNewTab:new w.MJ(!0)}),this.updateSubscription=this.menuService.editor.update.subscribe(t=>{this.update(t)})}ngOnDestroy(){this.updateSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(l.aKT),l.rXU(nt),l.rXU(tt))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-link"]],hostBindings:function(n,r){1&n&&l.bIt("mousedown",function(s){return r.onDocumentClick(s)},!1,l.EBC)},inputs:{options:[2,"options","options",t=>({...ql,...t})]},standalone:!0,features:[l.GFd,l.aNF],decls:5,vars:16,consts:[["type","button","aria-haspopup","dialog",1,"NgxEditor__MenuItem--Icon",3,"mousedown","keydown.enter","keydown.space","disabled","innerHTML","title","ariaLabel","ariaExpanded"],["class","NgxEditor__Popup",4,"ngIf"],[1,"NgxEditor__Popup"],[1,"NgxEditor__Popup--Form",3,"ngSubmit","formGroup"],[1,"NgxEditor__Popup--FormGroup"],[1,"NgxEditor__Popup--Col"],[1,"NgxEditor__Popup--Label",3,"htmlFor"],["type","url","formControlName","href","autocomplete","off",3,"id"],["class","NgxEditor__HelpText NgxEditor__HelpText--Error",4,"ngIf"],["type","text","formControlName","text","autocomplete","off",3,"id"],["class","NgxEditor__Popup--FormGroup",4,"ngIf"],["type","submit",3,"disabled"],[1,"NgxEditor__HelpText","NgxEditor__HelpText--Error"],["type","checkbox","formControlName","openInNewTab"]],template:function(n,r){1&n&&(l.j41(0,"button",0),l.nI1(1,"sanitizeHtml"),l.nI1(2,"async"),l.nI1(3,"async"),l.bIt("mousedown",function(s){return r.onTogglePopupMouseClick(s)})("keydown.enter",function(){return r.onTogglePopupKeydown()})("keydown.space",function(){return r.onTogglePopupKeydown()}),l.k0s(),l.DNE(4,_p,20,18,"div",1)),2&n&&(l.AVh("NgxEditor__MenuItem--Active",r.isActive||r.showPopup)("NgxEditor--Disabled",!r.canExecute),l.Y8G("disabled",!r.canExecute)("innerHTML",l.bMT(1,10,r.icon),l.npT)("title",l.bMT(2,12,r.title))("ariaLabel",l.bMT(3,14,r.title))("ariaExpanded",r.showPopup),l.R7$(4),l.Y8G("ngIf",r.showPopup))},dependencies:[N.Jj,N.MD,N.bT,w.X1,w.qT,w.me,w.Zm,w.BC,w.cb,w.j4,w.JD,Ze]})}return i})(),mm=(()=>{class i{ngxeService;menuService;toolbarItem;get name(){return this.toolbarItem}html;editorView;isActive=!1;disabled=!1;updateSubscription;constructor(t,n){this.ngxeService=t,this.menuService=n}toggle(){const{state:t,dispatch:n}=this.editorView;zt[this.name].toggle()(t,n)}onMouseClick(t){t.preventDefault(),0===t.button&&this.toggle()}onKeydown(){this.toggle()}update=t=>{const{state:n}=t,r=zt[this.name];this.isActive=r.isActive(n),this.disabled=!r.canExecute(n)};getTitle(t){return this.ngxeService.locals.get(t)}ngOnInit(){this.html=this.ngxeService.getIcon(this.name),this.editorView=this.menuService.editor.view,this.updateSubscription=this.menuService.editor.update.subscribe(t=>{this.update(t)})}ngOnDestroy(){this.updateSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(nt),l.rXU(tt))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-toggle-command"]],inputs:{toolbarItem:"toolbarItem"},standalone:!0,features:[l.aNF],decls:4,vars:14,consts:[["type","button",1,"NgxEditor__MenuItem--Icon",3,"mousedown","keydown.enter","keydown.space","disabled","innerHTML","title","ariaLabel"]],template:function(n,r){1&n&&(l.j41(0,"button",0),l.nI1(1,"sanitizeHtml"),l.nI1(2,"async"),l.nI1(3,"async"),l.bIt("mousedown",function(s){return r.onMouseClick(s)})("keydown.enter",function(){return r.onKeydown()})("keydown.space",function(){return r.onKeydown()}),l.k0s()),2&n&&(l.AVh("NgxEditor__MenuItem--Active",r.isActive)("NgxEditor--Disabled",r.disabled),l.Y8G("disabled",r.disabled)("innerHTML",l.bMT(1,8,r.html),l.npT)("title",l.bMT(2,10,r.getTitle(r.name)))("ariaLabel",l.bMT(3,12,r.getTitle(r.name))))},dependencies:[N.Jj,Ze]})}return i})();const gm=[["bold","italic"],[{heading:["h1","h2","h3","h4","h5","h6"]}],["link","image"],["text_color","background_color"]],ym=["#b60205","#d93f0b","#fbca04","#0e8a16","#006b75","#1d76db","#0052cc","#5319e7","#e99695","#f9d0c4","#fef2c0","#c2e0c6","#bfdadc","#c5def5","#bfd4f2","#d4c5f9"];let Zl=(()=>{class i{menuService;toolbar=gm;colorPresets=ym;disabled=!1;editor;customMenuRef=null;dropdownPlacement="bottom";toggleCommands=["bold","italic","underline","strike","code","blockquote","ordered_list","bullet_list","align_left","align_center","align_right","align_justify","superscript","subscript"];insertCommands=["horizontal_rule","format_clear","indent","outdent","undo","redo"];iconContainerClass=["NgxEditor__MenuItem","NgxEditor__MenuItem--IconContainer"];dropdownContainerClass=["NgxEditor__Dropdown"];seperatorClass=["NgxEditor__Seperator"];constructor(t){this.menuService=t}get presets(){const n=[];return this.colorPresets.forEach((r,o)=>{const s=Math.floor(o/8);n[s]||n.push([]),n[s].push(r)}),n}trackByIndex(t){return t}isDropDown(t){return!!t?.heading}getDropdownItems(t){return t}isLinkItem(t){return"link"===t||"object"==typeof t&&"object"==typeof t?.link}isLinkWithOptions(t){return"object"==typeof t&&"object"==typeof t?.link}getLinkOptions(t){return t?.link}ngOnInit(){if(!this.editor)throw new pn("Required editor instance to initialize menu component");this.menuService.editor=this.editor}static \u0275fac=function(n){return new(n||i)(l.rXU(tt))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-editor-menu"]],inputs:{toolbar:"toolbar",colorPresets:"colorPresets",disabled:"disabled",editor:"editor",customMenuRef:"customMenuRef",dropdownPlacement:"dropdownPlacement"},standalone:!0,features:[l.Jv_([tt]),l.aNF],decls:3,vars:7,consts:[[1,"NgxEditor__MenuBar",3,"ngClass"],[4,"ngFor","ngForOf","ngForTrackBy"],[4,"ngIf"],[3,"toolbarItem","class",4,"ngIf"],[3,"class",4,"ngIf"],["type","text_color",3,"class","presets",4,"ngIf"],["type","background_color",3,"class","presets",4,"ngIf"],[3,"toolbarItem"],[3,"options"],[3,"class","group","items",4,"ngFor","ngForOf","ngForTrackBy"],[3,"group","items"],["type","text_color",3,"presets"],["type","background_color",3,"presets"],[3,"ngTemplateOutlet"]],template:function(n,r){1&n&&(l.j41(0,"div",0),l.DNE(1,Dp,2,2,"ng-container",1)(2,Ip,2,1,"ng-container",2),l.k0s()),2&n&&(l.Y8G("ngClass",l.l_i(4,xp,r.disabled,"top"===r.dropdownPlacement)),l.R7$(),l.Y8G("ngForOf",r.toolbar)("ngForTrackBy",r.trackByIndex),l.R7$(),l.Y8G("ngIf",r.customMenuRef))},dependencies:[N.MD,N.YU,N.Sq,N.bT,N.T3,N.lG,Jl,Kl,mm,hm,Ql,Yl]})}return i})(),ea=(()=>{class i{sanitizeHTML;ngxeService;constructor(t,n){this.sanitizeHTML=t,this.ngxeService=n}get view(){return this.editor.view}editor;updateSubscription;execulableItems=[];activeItems=[];toolbar=[["bold","italic","underline","strike"],["ordered_list","bullet_list","blockquote","code"],["align_left","align_center","align_right","align_justify"]];toggleCommands=["bold","italic","underline","strike","ordered_list","bullet_list","blockquote","code","align_left","align_center","align_right","align_justify"];getIcon(t){return this.sanitizeHTML.transform(this.ngxeService.getIcon(t))}getTitle(t){return this.ngxeService.locals.get(t)}trackByIndex(t){return t}onClick(t,n){if(t.preventDefault(),t.stopPropagation(),0!==t.button)return;const{state:r,dispatch:o}=this.view;zt[n].toggle()(r,o)}update(t){this.activeItems=[],this.execulableItems=[];const{state:n}=t;this.toggleCommands.forEach(r=>{const o=zt[r];o.isActive(n)&&this.activeItems.push(r),o.canExecute(n)&&this.execulableItems.push(r)})}ngOnInit(){this.updateSubscription=this.editor.update.subscribe(t=>{this.update(t)})}ngOnDestroy(){this.updateSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(Ze),l.rXU(nt))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-bubble"]],inputs:{editor:"editor"},standalone:!0,features:[l.Jv_([Ze]),l.aNF],decls:1,vars:2,consts:[[4,"ngFor","ngForOf","ngForTrackBy"],["type","button","class","NgxBubbleMenu__Icon",3,"ngClass","title","innerHTML","mousedown",4,"ngIf"],["class","NgxBubbleMenu__Seperator",4,"ngIf"],["type","button",1,"NgxBubbleMenu__Icon",3,"mousedown","ngClass","title","innerHTML"],[1,"NgxBubbleMenu__Seperator"]],template:function(n,r){1&n&&l.DNE(0,Lp,2,2,"ng-container",0),2&n&&l.Y8G("ngForOf",r.toolbar)("ngForTrackBy",r.trackByIndex)},dependencies:[N.Jj,N.MD,N.YU,N.Sq,N.bT],styles:["*[_ngcontent-%COMP%], *[_ngcontent-%COMP%]:before, *[_ngcontent-%COMP%]:after{box-sizing:border-box}[_nghost-%COMP%]{display:flex;flex-wrap:wrap;background-color:var(--ngx-editor-bubble-bg-color);color:var(--ngx-editor-bubble-text-color);padding:5px;border-radius:4px}.NgxBubbleMenu__Icon[_ngcontent-%COMP%]{all:unset;appearance:none;height:var(--ngx-editor-icon-size);width:var(--ngx-editor-icon-size);transition:.2s ease-in-out;border-radius:var(--ngx-editor-menu-item-border-radius);display:flex;align-items:center;justify-content:center;color:#fff}.NgxBubbleMenu__Icon[_ngcontent-%COMP%]:hover{background-color:var(--ngx-editor-bubble-item-hover-color)}.NgxBubbleMenu__Icon[_ngcontent-%COMP%] + .NgxBubbleMenu__Icon[_ngcontent-%COMP%]{margin-left:5px}.NgxBubbleMenu__Icon.NgxBubbleMenu__Icon--Active[_ngcontent-%COMP%]{background-color:var(--ngx-editor-bubble-text-color);color:var(--ngx-editor-bubble-bg-color)}.NgxBubbleMenu__Seperator[_ngcontent-%COMP%]{border-left:1px solid var(--ngx-editor-seperator-color);margin:0 5px}"]})}return i})(),bm=(()=>{class i{el;constructor(t){this.el=t}get display(){return{visibility:this.showMenu?"visible":"hidden",opacity:this.showMenu?"1":"0",top:`${this.posTop}px`,left:`${this.posLeft}px`}}get view(){return this.editor.view}editor;autoPlace=!1;posLeft=0;posTop=0;showMenu=!1;updateSubscription;dragging=!1;resizeSubscription;onMouseDown(t){const n=t.target;this.el.nativeElement.contains(n)&&"INPUT"!==n.nodeName?t.preventDefault():this.dragging=!0}onKeyDown(t){"INPUT"!==t.target.nodeName&&(this.dragging=!0,this.hide())}onMouseUp(t){const n=t.target;this.el.nativeElement.contains(n)||"INPUT"===n.nodeName?t.preventDefault():(this.dragging=!1,this.useUpdate())}onKeyUp(t){"INPUT"!==t.target.nodeName&&(this.dragging=!1,this.useUpdate())}useUpdate(){this.view&&this.update(this.view)}hide(){this.showMenu=!1}show(){this.showMenu=!0}calculateBubblePosition(t){var n=this;return(0,re.A)(function*(){const{state:{selection:r}}=t,{from:o,to:s}=r,a=t.coordsAtPos(o),c=t.coordsAtPos(s),d={getBoundingClientRect(){if(r instanceof v)return t.nodeDOM(o).getBoundingClientRect();const{top:p,left:m}=a,{bottom:g,right:y}=c;return{x:m,y:p,top:p,bottom:g,left:m,right:y,width:y-m,height:g-p}}},u=n.el.nativeElement,{x:f,y:h}=yield((i,e,t)=>{const r={platform:Hf,...t},o={...r.platform,_c:new Map};return pf(i,e,{...r,platform:o})})(d,u,{placement:"top",middleware:[Wf(5),n.autoPlace&&Xf({boundary:t.dom,padding:5,allowedPlacements:["top","bottom"]}),{name:"overflowMiddleware",fn:p=>(0,re.A)(function*(){const m=yield Gf(p,{boundary:t.dom,padding:5});return m.left>0?{x:p.x+m.left}:m.right>0?{x:p.x-m.right}:{}})()}].filter(Boolean)});return{left:f,top:h}})()}canShowMenu(t){const{state:n}=t,{selection:r}=n,{empty:o}=r;return!(r instanceof v&&"image"===r.node.type.name||(!this.view.hasFocus()||o||this.dragging)&&(this.hide(),1))}update(t){this.canShowMenu(t)?this.calculateBubblePosition(this.view).then(({top:r,left:o})=>{this.canShowMenu?(this.posLeft=o,this.posTop=r,this.show()):this.hide()}):this.hide()}ngOnInit(){if(!this.editor)throw new pn("Required editor instance to initialize floating menu component");this.updateSubscription=this.editor.update.subscribe(t=>{this.update(t)}),this.resizeSubscription=(0,ca.R)(window,"resize").pipe((0,fa.c)(500,da.E,{leading:!0,trailing:!0})).subscribe(()=>{this.useUpdate()})}ngOnDestroy(){this.updateSubscription.unsubscribe(),this.resizeSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||i)(l.rXU(l.aKT))};static \u0275cmp=l.VBU({type:i,selectors:[["ngx-editor-floating-menu"]],hostVars:2,hostBindings:function(n,r){1&n&&l.bIt("mousedown",function(s){return r.onMouseDown(s)},!1,l.EBC)("keydown",function(s){return r.onKeyDown(s)},!1,l.EBC)("mouseup",function(s){return r.onMouseUp(s)},!1,l.EBC)("keyup",function(s){return r.onKeyUp(s)},!1,l.EBC),2&n&&l.Aen(r.display)},inputs:{editor:"editor",autoPlace:"autoPlace"},standalone:!0,features:[l.aNF],ngContentSelectors:Al,decls:4,vars:1,consts:[["ref",""],[4,"ngIf"],[3,"editor"]],template:function(n,r){if(1&n&&(l.NAR(),l.j41(0,"div",null,0),l.SdG(2),l.k0s(),l.DNE(3,Vp,2,1,"ng-container",1)),2&n){const o=l.sdS(1);l.R7$(3),l.Y8G("ngIf",0===o.children.length)}},dependencies:[ea,N.MD,N.bT],styles:["*[_ngcontent-%COMP%], *[_ngcontent-%COMP%]:before, *[_ngcontent-%COMP%]:after{box-sizing:border-box}[_nghost-%COMP%]{position:absolute;z-index:20;margin-bottom:5px;visibility:hidden;opacity:0}"]})}return i})(),_m=(()=>{class i{static \u0275fac=function(n){return new(n||i)};static \u0275mod=l.$C({type:i});static \u0275inj=l.G2t({providers:[Ze],imports:[N.MD,w.X1,Zl,Ql,Kl,Yl,Jl,bm,ea]})}return i})();const qn=new l.nKC("NgxEditorConfig"),ta={locals:xr,icons:Xl};let xm=(()=>{class i{static forRoot(t=ta){return{ngModule:i,providers:[{provide:qn,useValue:t},{provide:Cr,useFactory:Ul,deps:[qn]}]}}static forChild(t=ta){return{ngModule:i,providers:[{provide:qn,useValue:t},{provide:Cr,useFactory:Ul,deps:[qn]},nt]}}static \u0275fac=function(n){return new(n||i)};static \u0275mod=l.$C({type:i});static \u0275inj=l.G2t({imports:[N.MD,_m,Vl]})}return i})();const ne=(i,e=!1)=>(t,n)=>{const r=new et(i);return e?r.toggle()(t,n):r.apply()(t,n)};class km{view;state;tr;constructor(e){if(!e)throw new pn("Required view to initialize commands.");this.view=e,this.state=e.state,this.tr=this.view.state.tr}applyTrx=e=>{this.state=this.state.apply(e??this.tr),this.tr=this.state.tr,this.tr.setMeta("APPLIED_TRX",!0)};dispatch=e=>{this.applyTrx(e)};exec(){if(!this.tr.getMeta("APPLIED_TRX"))return!1;const e=!this.view.state.doc.eq(this.state.doc);this.view.updateState(this.state);const t=this.tr.setMeta("FORCE_EMIT",e);return this.view.dispatch(t),!0}focus(e="end"){const t="start"===e?I.atStart(this.state.doc):I.atEnd(this.state.doc);return this.tr.setSelection(t),this.applyTrx(),this.view.focus(),this}scrollIntoView(){return this.tr.scrollIntoView(),this.applyTrx(),this}insertText(e){return this.tr.insertText(e),this.applyTrx(),this}insertNewLine(){return ln(Ws,Us,Js,Yi)(this.state,this.dispatch),this}applyMark(e){return ne(e,!1)(this.state,this.dispatch),this}toggleMark(e){return ne(e,!0)(this.state,this.dispatch),this}bold(){return ne("strong")(this.state,this.dispatch),this}toggleBold(){return ne("strong",!0)(this.state,this.dispatch),this}italics(){return ne("em")(this.state,this.dispatch),this}toggleItalics(){return ne("em",!0)(this.state,this.dispatch),this}underline(){return ne("u")(this.state,this.dispatch),this}toggleUnderline(){return ne("u",!0)(this.state,this.dispatch),this}strike(){return ne("s")(this.state,this.dispatch),this}toggleStrike(){return ne("s",!0)(this.state,this.dispatch),this}code(){return ne("code")(this.state,this.dispatch),this}toggleCode(){return ne("code",!0)(this.state,this.dispatch),this}superscript(){return ne("sup")(this.state,this.dispatch),this}subscript(){return ne("sub")(this.state,this.dispatch),this}toggleOrderedList(){return new Kn(!1).toggle()(this.state,this.dispatch),this}toggleBulletList(){return new Kn(!0).toggle()(this.state,this.dispatch),this}toggleHeading(e){return new _t(e).toggle()(this.state,this.dispatch),this}insertLink(e,t){return(new _r).insert(e,t)(this.state,this.dispatch),this}updateLink(e){return(new _r).update(e)(this.state,this.dispatch),this}insertImage(e,t={}){return(new zl).insert(e,t)(this.state,this.dispatch),this}textColor(e){return new Bt("text_color").apply({color:e})(this.state,this.dispatch),this}backgroundColor(e){return new Bt("text_background_color").apply({backgroundColor:e})(this.state,this.dispatch),this}removeTextColor(){return new Bt("text_color").remove()(this.state,this.dispatch),this}removeBackgroundColor(){return new Bt("text_background_color").remove()(this.state,this.dispatch),this}align(e){return new dn(e).toggle()(this.state,this.dispatch),this}insertHTML(e){const{selection:t,schema:n,tr:r}=this.state,{from:o,to:s}=t,a=document.createElement("div");a.innerHTML=Rl(e)?e.trim():String(e);const c=lt.fromSchema(n).parseSlice(a),d=r.replaceRange(o,s,c);return this.applyTrx(d),this}indent(){return new Yn("increase").insert()(this.state,this.dispatch),this}outdent(){return new Yn("decrease").insert()(this.state,this.dispatch),this}}const vm=typeof navigator<"u"&&/Mac/.test(navigator.platform),Im=i=>{const e=$u.concat(Pu,Fu);return e.push((i=>rl(/(?:^|\s)(?:(\*\*|__)(?:([^*_]+))(\*\*|__))$/,i))(i.marks.strong)),e.push((i=>rl(/(?:^|\s)(?:(\*|_)(?:([^*_]+))(\*|_))$/,i))(i.marks.em)),e.push((i=>Zi(/^\s*>\s$/,i))(i.nodes.blockquote)),e.push((i=>Zi(/^(?:\d+)\.\s$/,i,e=>({order:Number(e[1])}),(e,t)=>t.childCount+t.attrs.order===Number(e[1])))(i.nodes.ordered_list)),e.push((i=>Zi(/^\s*(?:[-+*])\s$/,i))(i.nodes.bullet_list)),e.push((i=>el(/^```$/,i))(i.nodes.code_block)),e.push((i=>el(new RegExp("^(#{1,6})\\s$"),i,t=>({level:t[1].length})))(i.nodes.heading)),function Ru({rules:i}){let e=new ve({state:{init:()=>null,apply(t,n){return t.getMeta(this)||(t.selectionSet||t.docChanged?null:n)}},props:{handleTextInput:(t,n,r,o)=>Zs(t,n,r,o,i,e),handleDOMEvents:{compositionend:t=>{setTimeout(()=>{let{$cursor:n}=t.state.selection;n&&Zs(t,n.pos,n.pos,"",i,e)})}}},isInputRules:!0});return e}({rules:e})},Rm=(i,e)=>{const t=[];return e.keyboardShortcuts&&t.push(...((i,e)=>{const t={};t["Mod-z"]=ir,vm?t["Shift-Mod-z"]=zn:t["Mod-y"]=zn;const n=[Jn({"Mod-b":At(i.marks.strong),"Mod-i":At(i.marks.em),"Mod-u":At(i.marks.u),"Mod-`":At(i.marks.code)}),Jn({Enter:vo(i.nodes.list_item),"Shift-Enter":ln(Xs,(r,o)=>{const{tr:s}=r;return o(s.replaceSelectionWith(i.nodes.hard_break.create()).scrollIntoView()),!0}),"Mod-[":To(i.nodes.list_item),"Mod-]":Mo(i.nodes.list_item),Tab:Mo(i.nodes.list_item)}),Jn(Du)];return e.history&&n.push(Jn(t)),n})(i,{history:e.history})),e.history&&t.push(function ef(i={}){return new ve({key:Le,state:{init:()=>new Ye(be.empty,be.empty,null,0,-1),apply:(e,t,n)=>function qu(i,e,t,n){let o,r=t.getMeta(Le);if(r)return r.historyState;t.getMeta(dl)&&(i=new Ye(i.done,i.undone,null,0,-1));let s=t.getMeta("appendedTransaction");if(0==t.steps.length)return i;if(s&&s.getMeta(Le))return s.getMeta(Le).redo?new Ye(i.done.addTransform(t,void 0,n,Vn(e)),i.undone,al(t.mapping.maps),i.prevTime,i.prevComposition):new Ye(i.done,i.undone.addTransform(t,void 0,n,Vn(e)),null,i.prevTime,i.prevComposition);if(!1===t.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))return(o=t.getMeta("rebased"))?new Ye(i.done.rebased(t,o),i.undone.rebased(t,o),tr(i.prevRanges,t.mapping),i.prevTime,i.prevComposition):new Ye(i.done.addMaps(t.mapping.maps),i.undone.addMaps(t.mapping.maps),tr(i.prevRanges,t.mapping),i.prevTime,i.prevComposition);{let a=t.getMeta("composition"),c=0==i.prevTime||!s&&i.prevComposition!=a&&(i.prevTime<(t.time||0)-n.newGroupDelay||!function Qu(i,e){if(!e)return!1;if(!i.docChanged)return!0;let t=!1;return i.mapping.maps[0].forEach((n,r)=>{for(let o=0;o<e.length;o+=2)n<=e[o+1]&&r>=e[o]&&(t=!0)}),t}(t,i.prevRanges)),d=s?tr(i.prevRanges,t.mapping):al(t.mapping.maps);return new Ye(i.done.addTransform(t,c?e.selection.getBookmark():void 0,n,Vn(e)),be.empty,d,t.time,a??i.prevComposition)}}(t,n,e,i)},config:i={depth:i.depth||100,newGroupDelay:i.newGroupDelay||500},props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?ir:"historyRedo"==n?zn:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}()),e.inputRules&&t.push(Im(i)),t},ia={linkOnPaste:!0,resizeImage:!0},ra={content:null,history:!0,keyboardShortcuts:!0,inputRules:!0,schema:Sn,plugins:[],nodeViews:{},attributes:{},features:ia,handleScrollToSelection:null,linkValidationPattern:"(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/??([^#\n\r]*)?#?([^\n\r]*)|(mailto:.*[@].*)"};class Qn{options;view;constructor(e=ra){this.options={...ra,...e},this.createEditor()}valueChangesSubject=new xt.B;updateSubject=new xt.B;get valueChanges(){return this.valueChangesSubject.asObservable()}get update(){return this.updateSubject.asObservable()}get schema(){return this.options.schema||Sn}get linkValidationPattern(){return this.options.linkValidationPattern}get commands(){return new km(this.view)}get features(){return{...ia,...this.options.features}}handleTransactions(e){const t=this.view.state.apply(e);if(this.view.updateState(t),this.updateSubject.next(this.view),!e.docChanged&&!e.getMeta("FORCE_EMIT"))return;const n=t.doc.toJSON();this.valueChangesSubject.next(n)}createEditor(){const{options:e,schema:t}=this,{content:n=null,nodeViews:r}=e,{history:o=!0,keyboardShortcuts:s=!0,inputRules:a=!0}=e,c=cn(n,t,e.parseOptions),d=e.plugins??[],u=e.attributes??{},f=Rm(t,{history:o,keyboardShortcuts:s,inputRules:a});this.view=new Fs(null,{state:Tt.create({doc:c,schema:t,plugins:[...f,...d]}),nodeViews:r,dispatchTransaction:this.handleTransactions.bind(this),attributes:u,handleScrollToSelection:e.handleScrollToSelection})}setContent(e){if(typeof(i=e)>"u"||null===i)return;var i;const{state:t}=this.view,{tr:n,doc:r}=t,o=cn(e,this.schema,this.options.parseOptions);n.replaceWith(0,t.doc.content.size,o),!r.eq(n.doc)&&n.docChanged&&this.view.dispatch(n)}registerPlugin(e){const{state:t}=this.view,n=[...t.plugins,e],r=t.reconfigure({plugins:n});this.view.updateState(r)}destroy(){this.view.destroy()}}var oa=R(54722),Fm=R(72510),Pm=R(534),Lm=R(78606),Vm=R(53733);let Bm=(()=>{class i{constructor(){this.allowCopyPasteOnly=!1}sanitizeInput(t){return t.replace(/<\/?(span|div|script|style|a|svg|img)(\s[^>]*)?>/gi,"")?.trimStart()}onInput(t){if(this.allowCopyPasteOnly&&!t.inputType.includes("paste"))return void t.preventDefault();const n=this.sanitizeInput(t.target.value);n!==t.target.value&&(t.target.value=n,t.target.dispatchEvent(new Event("input")))}onPaste(t){if(this.allowCopyPasteOnly){const a=(t.clipboardData||window.clipboardData).getData("text");return t.target.value=a,void t.target.dispatchEvent(new Event("input"))}t.preventDefault();const r=(t.clipboardData||window.clipboardData).getData("text"),o=this.sanitizeInput(r);t.target.value=o,t.target.dispatchEvent(new Event("input"))}onKeyDown(t){if(this.allowCopyPasteOnly){if((t.ctrlKey||t.metaKey)&&("c"===t.key||"v"===t.key||"x"===t.key||"a"===t.key))return;["Delete","Backspace","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End","Tab"].includes(t.key)||t.preventDefault()}}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275dir=l.FsC({type:i,selectors:[["","appRestrictHtmlTags",""]],hostBindings:function(n,r){1&n&&l.bIt("input",function(s){return r.onInput(s)})("paste",function(s){return r.onPaste(s)})("keydown",function(s){return r.onKeyDown(s)})},inputs:{allowCopyPasteOnly:"allowCopyPasteOnly"},standalone:!0})}}return i})();const zm=["templateVarConfig"],$m=(i,e)=>e.language,sa=(i,e)=>e.languageCode,Hm=()=>({standalone:!0}),wr=()=>["/communication/search-communication-templates"];function jm(i,e){1&i&&l.EFF(0," View Communication Template ")}function Gm(i,e){1&i&&l.EFF(0," Edit Communication Template ")}function Wm(i,e){1&i&&l.EFF(0," Create Communication Template ")}function Xm(i,e){1&i&&(l.j41(0,"button",7),l.nrm(1,"svg-icon",10),l.k0s())}function Um(i,e){1&i&&(l.j41(0,"div",8)(1,"div",11)(2,"div",12)(3,"span",13),l.EFF(4,"Loading..."),l.k0s()(),l.j41(5,"p",14),l.EFF(6,"Loading template data..."),l.k0s()()())}function Jm(i,e){if(1&i&&(l.j41(0,"div",21),l.EFF(1),l.k0s()),2&i){const t=l.XpG(2);l.R7$(),l.SpI(" ",t.getFieldErrorMessage("templateName")," ")}}function Km(i,e){if(1&i&&(l.j41(0,"select",28)(1,"option",23),l.EFF(2,"Select Entry Point"),l.k0s(),l.j41(3,"option",37),l.EFF(4,"Account"),l.k0s(),l.j41(5,"option",37),l.EFF(6,"User"),l.k0s()()),2&i){const t=l.XpG(2);l.R7$(3),l.Y8G("selected",t.currentTemplateData.entryPoint===t.Account),l.R7$(2),l.Y8G("selected",t.currentTemplateData.entryPoint===t.User)}}function Ym(i,e){if(1&i&&(l.j41(0,"div",41),l.EFF(1),l.k0s()),2&i){const t=l.XpG(3);l.R7$(),l.JRh(t.getFieldErrorMessage("entryPoint"))}}function qm(i,e){if(1&i&&(l.j41(0,"select",38)(1,"option",23),l.EFF(2,"Select Entry Point"),l.k0s(),l.j41(3,"option",39),l.EFF(4,"Account"),l.k0s(),l.j41(5,"option",40),l.EFF(6,"User"),l.k0s()(),l.DNE(7,Ym,2,1,"div",41)),2&i){const t=l.XpG(2);l.Y8G("disabled",t.isViewMode),l.R7$(7),l.vxM(t.isFieldInvalid("entryPoint")?7:-1)}}function Qm(i,e){if(1&i&&(l.j41(0,"select",29)(1,"option",23),l.EFF(2,"Select Recipient Type"),l.k0s(),l.j41(3,"option",37),l.EFF(4,"Customer"),l.k0s(),l.j41(5,"option",37),l.EFF(6,"Agent"),l.k0s()()),2&i){const t=l.XpG(2);l.R7$(3),l.Y8G("selected",t.currentTemplateData.recipientType===t.Customer),l.R7$(2),l.Y8G("selected",t.currentTemplateData.recipientType===t.Agent)}}function Zm(i,e){if(1&i&&(l.j41(0,"div",41),l.EFF(1),l.k0s()),2&i){const t=l.XpG(3);l.R7$(),l.JRh(t.getFieldErrorMessage("recipientType"))}}function eg(i,e){if(1&i&&(l.j41(0,"select",42)(1,"option",23),l.EFF(2,"Select Recipient Type"),l.k0s(),l.j41(3,"option",43),l.EFF(4,"Customer"),l.k0s(),l.j41(5,"option",44),l.EFF(6,"Agent"),l.k0s()(),l.DNE(7,Zm,2,1,"div",41)),2&i){const t=l.XpG(2);l.Y8G("disabled",t.isViewMode),l.R7$(7),l.vxM(t.isFieldInvalid("recipientType")?7:-1)}}function tg(i,e){if(1&i&&(l.j41(0,"div",45)(1,"label"),l.nrm(2,"input",47),l.EFF(3," Yes "),l.k0s(),l.j41(4,"label"),l.nrm(5,"input",48),l.EFF(6," No "),l.k0s()()),2&i){const t=l.XpG(3);l.R7$(2),l.Y8G("checked",!0===t.currentTemplateData.isAvailableInAccountDetails),l.R7$(3),l.Y8G("checked",!1===t.currentTemplateData.isAvailableInAccountDetails)}}function ng(i,e){if(1&i&&(l.j41(0,"div",49)(1,"label"),l.nrm(2,"input",50),l.EFF(3," Yes "),l.k0s(),l.j41(4,"label"),l.nrm(5,"input",51),l.EFF(6," No "),l.k0s()()),2&i){const t=l.XpG(3);l.AVh("disabled",t.isViewMode),l.R7$(2),l.Y8G("disabled",t.isViewMode),l.R7$(3),l.Y8G("disabled",t.isViewMode)}}function ig(i,e){if(1&i&&(l.j41(0,"div",30)(1,"div",18)(2,"label",19),l.EFF(3,"Allow Access from Account Details"),l.k0s(),l.DNE(4,tg,7,2,"div",45)(5,ng,7,4,"div",46),l.k0s()()),2&i){const t=l.XpG(2);l.R7$(4),l.vxM(t.isViewMode&&t.currentTemplateData?4:5)}}function rg(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",52)(1,"button",53),l.bIt("click",function(){const r=l.eBV(t).$index,o=l.XpG(3);return l.Njj(o.selectViewLanguage(r))}),l.EFF(2),l.k0s()()}if(2&i){const t=e.$implicit,n=e.$index,r=l.XpG(3);l.R7$(),l.AVh("active",r.selectedViewLanguageIndex===n),l.R7$(),l.SpI(" ",t.language," ")}}function og(i,e){if(1&i&&(l.j41(0,"div",32),l.Z7z(1,rg,3,3,"div",52,$m),l.k0s()),2&i){const t=l.XpG(2);l.R7$(),l.Dyx(t.currentTemplateData.communicationTemplateDetails)}}function sg(i,e){if(1&i){const t=l.RV6();l.j41(0,"span",56),l.bIt("click",function(r){l.eBV(t);const o=l.XpG().$index;return l.XpG(3).removeLanguage(o),l.Njj(r.stopPropagation())}),l.EFF(1," \xd7 "),l.k0s()}if(2&i){const t=l.XpG().$implicit;l.Mz_("title","Remove ",null==t?null:t.languageName,"")}}function lg(i,e){if(1&i&&(l.j41(0,"div",52)(1,"button",54),l.EFF(2),l.DNE(3,sg,2,2,"span",55),l.k0s()()),2&i){const t=e.$implicit,n=l.XpG(3);l.R7$(),l.Mz_("id","template-language-",null==t?null:t.languageCode,""),l.Y8G("btnRadio",null==t?null:t.languageCode),l.R7$(),l.SpI(" ",null==t?null:t.languageName," "),l.R7$(),l.vxM((null==n.fValue||null==n.fValue.languages?null:n.fValue.languages.length)>1&&!n.isViewMode?3:-1)}}function ag(i,e){if(1&i&&(l.j41(0,"div",33),l.Z7z(1,lg,4,5,"div",52,sa),l.k0s()),2&i){const t=l.XpG(2);l.R7$(),l.Dyx(null==t.fValue?null:t.fValue.languages)}}function cg(i,e){if(1&i){const t=l.RV6();l.j41(0,"button",57),l.bIt("click",function(){l.eBV(t);const r=l.XpG(2),o=l.sdS(13);return l.Njj(r.openAddLangModal(o))}),l.nrm(1,"svg-icon",58),l.j41(2,"span"),l.EFF(3,"Add Language"),l.k0s()()}}function dg(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",59)(1,"div",18)(2,"label",60),l.EFF(3,"Upload Header"),l.k0s(),l.j41(4,"input",61),l.bIt("change",function(r){l.eBV(t);const o=l.XpG(2);return l.Njj(o.onHeaderUpload(r))}),l.k0s()()(),l.j41(5,"div",59)(6,"div",18)(7,"label",60),l.EFF(8,"Upload Footer"),l.k0s(),l.j41(9,"input",61),l.bIt("change",function(r){l.eBV(t);const o=l.XpG(2);return l.Njj(o.onFooterUpload(r))}),l.k0s()()()}}function ug(i,e){}function fg(i,e){if(1&i&&(l.j41(0,"div",30)(1,"div",18)(2,"label",19),l.EFF(3,"Subject Line"),l.k0s(),l.nrm(4,"input",64),l.k0s()()),2&i){let t;const n=l.XpG(3);l.R7$(4),l.Y8G("value",(null==(t=n.getSelectedLanguageDetail())?null:t.subject)||"")}}function pg(i,e){if(1&i&&(l.DNE(0,ug,0,0)(1,fg,5,1,"div",30),l.j41(2,"div",30)(3,"div",18)(4,"label",19),l.EFF(5,"Template Body"),l.k0s(),l.nrm(6,"textarea",62),l.k0s()(),l.j41(7,"div",30)(8,"div",18)(9,"label",60),l.EFF(10,"Template Preview"),l.k0s(),l.nrm(11,"div",63),l.k0s()()),2&i){let t,n;const r=l.XpG(2);l.vxM(r.currentTemplateData.communicationTemplateDetails.length>1?0:-1),l.R7$(),l.vxM("email"===(null==r.currentTemplateData.templateType?null:r.currentTemplateData.templateType.toLowerCase())?1:-1),l.R7$(5),l.Y8G("value",(null==(t=r.getSelectedLanguageDetail())?null:t.body)||""),l.R7$(5),l.Y8G("template",(null==(n=r.getSelectedLanguageDetail())?null:n.body)||"")("variables",r.variables)}}function hg(i,e){if(1&i&&(l.j41(0,"div",21),l.EFF(1),l.k0s()),2&i){const t=l.XpG(3).$index,n=l.XpG(3);l.R7$(),l.SpI(" ",n.getLanguageFieldErrorMessage(t,"emailSubject")," ")}}function mg(i,e){if(1&i&&(l.j41(0,"div",30)(1,"div",18)(2,"label",19),l.EFF(3,"Subject Line"),l.k0s(),l.nrm(4,"input",72),l.DNE(5,hg,2,1,"div",21),l.k0s()()),2&i){const t=l.XpG(2).$index,n=l.XpG(3);l.R7$(4),l.AVh("is-invalid",n.isLanguageFieldInvalid(t,"emailSubject")),l.R7$(),l.vxM(n.isLanguageFieldInvalid(t,"emailSubject")?5:-1)}}function gg(i,e){if(1&i&&l.nrm(0,"ngx-editor-menu",74),2&i){const t=l.XpG(3).$index,n=l.XpG(3);l.Y8G("editor",n.editors[t]||n.defaultEditor)("toolbar",n.toolbar)}}function yg(i,e){if(1&i){const t=l.RV6();l.j41(0,"ngx-editor",80),l.mxI("ngModelChange",function(r){l.eBV(t);const o=l.XpG(3).$index,s=l.XpG(3);return l.DH7(s.htmlContents[o],r)||(s.htmlContents[o]=r),l.Njj(r)}),l.bIt("ngModelChange",function(){l.eBV(t);const r=l.XpG(3).$index,o=l.XpG(3);return l.Njj(o.onEditorContentChange(r))}),l.k0s()}if(2&i){const t=l.XpG(3).$index,n=l.XpG(3);l.Y8G("editor",n.editors[t]),l.R50("ngModel",n.htmlContents[t]),l.Y8G("ngModelOptions",l.lJ4(5,Hm))("placeholder","Enter Email Template Content")("disabled",!1)}}function bg(i,e){1&i&&(l.j41(0,"div",76)(1,"p"),l.EFF(2,"Initializing editor..."),l.k0s()())}function _g(i,e){if(1&i&&(l.j41(0,"div",21),l.EFF(1),l.k0s()),2&i){const t=l.XpG(3).$index,n=l.XpG(3);l.R7$(),l.SpI(" ",n.getLanguageFieldErrorMessage(t,"emailBody")," ")}}function xg(i,e){if(1&i&&(l.j41(0,"div",73),l.DNE(1,gg,1,2,"ngx-editor-menu",74)(2,yg,1,6,"ngx-editor",75)(3,bg,3,0,"div",76),l.j41(4,"div",77)(5,"small",78),l.nrm(6,"i",79),l.EFF(7," Email templates support rich text formatting. Use the toolbar for styling. "),l.k0s()()(),l.DNE(8,_g,2,1,"div",21)),2&i){const t=l.XpG(2).$index,n=l.XpG(3);l.R7$(),l.vxM(n.editors[t]||n.defaultEditor?1:-1),l.R7$(),l.vxM(n.editors[t]?2:3),l.R7$(6),l.vxM(n.isLanguageFieldInvalid(t,"emailBody")?8:-1)}}function Cg(i,e){if(1&i&&(l.j41(0,"div",21),l.EFF(1),l.k0s()),2&i){const t=l.XpG(3).$index,n=l.XpG(3);l.R7$(),l.SpI(" ",n.getLanguageFieldErrorMessage(t,"templateBody")," ")}}function wg(i,e){1&i&&(l.j41(0,"div",82),l.nrm(1,"i",83),l.j41(2,"i"),l.EFF(3,"SMS templates: Copy, paste and delete operations only allowed."),l.k0s()())}function kg(i,e){if(1&i&&(l.nrm(0,"textarea",81),l.DNE(1,Cg,2,1,"div",21)(2,wg,4,0,"div",82)),2&i){const t=l.XpG(2).$index,n=l.XpG(3);l.AVh("is-invalid",n.isLanguageFieldInvalid(t,"templateBody")),l.Y8G("allowCopyPasteOnly","sms"===(null==n.fValue?null:n.fValue.channelType)||n.isEditMode&&"sms"===(null==n.currentTemplateData||null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase())),l.R7$(),l.vxM(n.isLanguageFieldInvalid(t,"templateBody")?1:-1),l.R7$(),l.vxM("sms"===(null==n.fValue?null:n.fValue.channelType)||n.isEditMode&&"sms"===(null==n.currentTemplateData||null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase())?2:-1)}}function vg(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",30)(1,"div",18)(2,"div",31)(3,"label",19),l.EFF(4,"Template Variable Mapping"),l.k0s(),l.j41(5,"div",84)(6,"label",85),l.EFF(7,"Unmapped Variable"),l.k0s(),l.j41(8,"label",86),l.EFF(9,"Mapped Variable"),l.k0s()()(),l.j41(10,"div",87,2),l.bIt("selectVar",function(r){l.eBV(t);const o=l.XpG(5),s=l.sdS(11);return l.Njj(o.openMapVariableModal(r,s))})("change",function(r){l.eBV(t);const o=l.XpG(2).$index,s=l.XpG(3);return l.Njj(s.updateTemplateValue(r,o))}),l.k0s()()(),l.j41(12,"div",30)(13,"div",18)(14,"div",31)(15,"label",19),l.EFF(16,"Message Preview Using Template"),l.k0s()(),l.nrm(17,"div",63),l.k0s()()}if(2&i){const t=l.XpG(2).$index,n=l.XpG(3);l.R7$(10),l.Y8G("template",n.getCurrentTemplateBody(t)),l.R7$(7),l.Y8G("template",n.getCurrentTemplateBody(t))("variables",n.variables)}}function Tg(i,e){if(1&i){const t=l.RV6();l.DNE(0,mg,6,3,"div",30),l.j41(1,"div",30)(2,"div",18)(3,"div",67)(4,"label",19),l.EFF(5,"Template Body"),l.k0s(),l.j41(6,"label",60),l.nrm(7,"i",68),l.k0s()(),l.j41(8,"div",69)(9,"button",70),l.bIt("click",function(){l.eBV(t);const r=l.XpG(4);return l.Njj(r.insertVariable())}),l.nrm(10,"svg-icon",71),l.k0s(),l.DNE(11,xg,9,3)(12,kg,3,5),l.k0s()()(),l.DNE(13,vg,18,3)}if(2&i){const t=l.XpG().$index,n=l.XpG(3);l.vxM("email"===(null==n.fValue?null:n.fValue.channelType)||n.isEditMode&&"email"===(null==n.currentTemplateData||null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase())?0:-1),l.R7$(11),l.vxM("email"===(null==n.fValue?null:n.fValue.channelType)||n.isEditMode&&"email"===(null==n.currentTemplateData||null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase())?11:12),l.R7$(2),l.vxM(n.getCurrentTemplateBody(t)&&!n.isViewMode?13:-1)}}function Mg(i,e){if(1&i&&(l.qex(0,65)(1,66),l.DNE(2,Tg,14,3),l.bVm()()),2&i){const t=e.$index,n=l.XpG(3);l.Y8G("formArrayName","languages"),l.R7$(),l.Y8G("formGroupName",t),l.R7$(),l.vxM(n.shouldShowLanguageFields(t)?2:-1)}}function Eg(i,e){if(1&i&&l.Z7z(0,Mg,3,3,"ng-container",65,sa),2&i){const t=l.XpG(2);l.Dyx(null==t.fValue?null:t.fValue.languages)}}function Sg(i,e){1&i&&(l.j41(0,"button",36),l.EFF(1,"Back to Search"),l.k0s()),2&i&&l.Y8G("routerLink",l.lJ4(1,wr))}function Ng(i,e){if(1&i){const t=l.RV6();l.j41(0,"button",88),l.bIt("click",function(){l.eBV(t);const r=l.XpG(2);return l.Njj(r.updateTemplate())}),l.EFF(1,"Update Template"),l.k0s(),l.j41(2,"button",36),l.EFF(3,"Cancel"),l.k0s()}2&i&&(l.R7$(2),l.Y8G("routerLink",l.lJ4(1,wr)))}function Og(i,e){if(1&i){const t=l.RV6();l.j41(0,"button",88),l.bIt("click",function(){l.eBV(t);const r=l.XpG(2);return l.Njj(r.createTemplate())}),l.EFF(1,"Create Template"),l.k0s(),l.j41(2,"button",36),l.EFF(3,"Cancel"),l.k0s()}2&i&&(l.R7$(2),l.Y8G("routerLink",l.lJ4(1,wr)))}function Dg(i,e){if(1&i&&(l.j41(0,"div",9)(1,"div",15)(2,"div",16)(3,"div",17)(4,"div",18)(5,"label",19),l.EFF(6,"Template Name"),l.k0s(),l.nrm(7,"input",20),l.DNE(8,Jm,2,1,"div",21),l.k0s()(),l.j41(9,"div",17)(10,"div",18)(11,"label",19),l.EFF(12,"Channel Type"),l.k0s(),l.j41(13,"select",22)(14,"option",23),l.EFF(15,"Select Channel Type"),l.k0s(),l.j41(16,"option",24),l.EFF(17,"Email"),l.k0s(),l.j41(18,"option",25),l.EFF(19,"SMS"),l.k0s(),l.j41(20,"option",26),l.EFF(21,"Letter"),l.k0s()()()(),l.j41(22,"div",17)(23,"div",27)(24,"div",18)(25,"label",19),l.EFF(26,"Entry Point"),l.k0s(),l.DNE(27,Km,7,2,"select",28)(28,qm,8,2),l.k0s()()(),l.j41(29,"div",17)(30,"div",27)(31,"div",18)(32,"label",19),l.EFF(33,"Recipient Type"),l.k0s(),l.DNE(34,Qm,7,2,"select",29)(35,eg,8,2),l.k0s()()(),l.DNE(36,ig,6,1,"div",30),l.j41(37,"div",30)(38,"div",18)(39,"label",19),l.EFF(40,"Template Language"),l.k0s(),l.j41(41,"div",31),l.DNE(42,og,3,0,"div",32)(43,ag,3,0,"div",33)(44,cg,4,0,"button",34),l.k0s()()(),l.DNE(45,dg,10,0)(46,pg,12,5)(47,Eg,2,0),l.k0s()(),l.j41(48,"div",35),l.DNE(49,Sg,2,2,"button",36)(50,Ng,4,2)(51,Og,4,2),l.k0s()()),2&i){let t;const n=l.XpG();l.Y8G("formGroup",n.createForm),l.R7$(7),l.AVh("is-invalid",n.isFieldInvalid("templateName"))("form-control-readonly",n.isEditMode||n.isViewMode),l.Y8G("readonly",n.isEditMode||n.isViewMode),l.R7$(),l.vxM(n.isFieldInvalid("templateName")?8:-1),l.R7$(5),l.Y8G("disabled",n.isEditMode||n.isViewMode),l.R7$(14),l.vxM(n.isViewMode&&n.currentTemplateData?27:28),l.R7$(7),l.vxM(n.isViewMode&&n.currentTemplateData?34:35),l.R7$(2),l.vxM(n.isViewMode&&n.currentTemplateData&&("email"===(null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase())||"sms"===(null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase()))||n.isEditMode&&n.currentTemplateData&&("email"===(null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase())||"sms"===(null==n.currentTemplateData.templateType?null:n.currentTemplateData.templateType.toLowerCase()))||!n.isViewMode&&!n.isEditMode&&("email"===(null==n.fValue?null:n.fValue.channelType)||"sms"===(null==n.fValue?null:n.fValue.channelType))?36:-1),l.R7$(6),l.vxM(n.isViewMode&&(null==n.currentTemplateData||null==n.currentTemplateData.communicationTemplateDetails?null:n.currentTemplateData.communicationTemplateDetails.length)>0?42:43),l.R7$(2),l.vxM(n.isViewMode?-1:44),l.R7$(),l.vxM("letter"===(null==n.createForm||null==(t=n.createForm.get("channelType"))?null:t.value)?45:-1),l.R7$(),l.vxM(n.isViewMode&&(null==n.currentTemplateData||null==n.currentTemplateData.communicationTemplateDetails?null:n.currentTemplateData.communicationTemplateDetails.length)>0?46:-1),l.R7$(),l.vxM(n.isViewMode?-1:47),l.R7$(2),l.vxM(n.isViewMode?49:n.isEditMode?50:51)}}function Ig(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",89)(1,"h4",90),l.EFF(2,"Template Variable Mapping"),l.k0s(),l.j41(3,"img",91),l.bIt("click",function(){l.eBV(t);const r=l.XpG();return l.Njj(null==r.mapVarModalRef?null:r.mapVarModalRef.hide())}),l.k0s()(),l.j41(4,"div",92)(5,"div",18)(6,"label",19),l.EFF(7,"Mapping Fields"),l.k0s(),l.j41(8,"ng-select",93),l.mxI("ngModelChange",function(r){l.eBV(t);const o=l.XpG();return l.DH7(o.selectedVariable.value,r)||(o.selectedVariable.value=r),l.Njj(r)}),l.k0s()()(),l.j41(9,"div",94)(10,"button",95),l.bIt("click",function(){l.eBV(t);const r=l.XpG();return l.Njj(r.assignVariable())}),l.EFF(11," Map "),l.k0s()()}if(2&i){const t=l.XpG();l.R7$(8),l.Y8G("items",t.variables),l.R50("ngModel",t.selectedVariable.value),l.Y8G("clearable",!1)("searchable",!0),l.R7$(2),l.Y8G("disabled",!(null!=t.selectedVariable&&t.selectedVariable.value))}}function Ag(i,e){if(1&i){const t=l.RV6();l.j41(0,"div",89)(1,"h4",90),l.EFF(2,"Add New Language"),l.k0s(),l.j41(3,"img",91),l.bIt("click",function(){l.eBV(t);const r=l.XpG();return l.Njj(null==r.addLangModalRef?null:r.addLangModalRef.hide())}),l.k0s()(),l.j41(4,"div",92)(5,"div",18)(6,"label",19),l.EFF(7,"Language"),l.k0s(),l.j41(8,"ng-select",96),l.mxI("ngModelChange",function(r){l.eBV(t);const o=l.XpG();return l.DH7(o.selectedLanguage,r)||(o.selectedLanguage=r),l.Njj(r)}),l.k0s()()(),l.j41(9,"div",94)(10,"button",95),l.bIt("click",function(){l.eBV(t);const r=l.XpG();return l.Njj(r.addLanguage())}),l.EFF(11," Add Language "),l.k0s()()}if(2&i){const t=l.XpG();l.R7$(8),l.Y8G("items",t.allLanguages),l.R50("ngModel",t.selectedLanguage),l.Y8G("clearable",!1)("searchable",!0),l.R7$(2),l.Y8G("disabled",!t.selectedLanguage)}}let Rg=(()=>{class i{constructor(){this.fb=(0,l.WQX)(w.ok),this.modalService=(0,l.WQX)(Zn.I8),this.route=(0,l.WQX)(L.nX),this.router=(0,l.WQX)(L.Ix),this.templateService=(0,l.WQX)(ge.I),this.toastr=(0,l.WQX)(ei.tw),this.breadcrumbData=[{label:"Communication"},{label:"Create Communication Template"}],this.isViewMode=!1,this.isEditMode=!1,this.templateId=null,this.isLoading=!1,this.templateData=null,this.currentTemplateData=null,this.selectedViewLanguageIndex=0,this.allLanguages=[],this.variables=[],this.isLoadingFields=!1,this.selectedVariable={value:null,index:-1},this.selectedLanguage=null,this.editors=[],this.htmlContents=[],this.defaultEditor=null,this.isUpdatingFromMapping=!1,this.toolbar=[["bold","italic"],["underline"],["code","blockquote"],["ordered_list","bullet_list"],[{heading:["h1","h2","h3","h4","h5","h6"]}],["link"],["align_left","align_center","align_right","align_justify"]],this.buildCreateTemplateForm(),this.initializeDefaultEditor()}ngOnInit(){this.loadDatabaseFields(),this.loadLanguageList(),this.route.params.subscribe(t=>{this.templateId=t.id||null;const n=this.router.url;n.includes("view-communication-template")?(this.isViewMode=!0,this.isEditMode=!1,this.updateBreadcrumb("View Communication Template")):n.includes("edit-communication-template")?(this.isViewMode=!1,this.isEditMode=!0,this.updateBreadcrumb("Edit Communication Template")):(this.isViewMode=!1,this.isEditMode=!1,this.updateBreadcrumb("Create Communication Template")),this.templateId&&(this.isViewMode||this.isEditMode)&&this.loadTemplateData(this.templateId)})}ngOnDestroy(){this.editors.forEach(t=>{t&&t.destroy()}),this.defaultEditor&&this.defaultEditor.destroy()}initializeDefaultEditor(){try{this.defaultEditor=new Qn({history:!0,keyboardShortcuts:!0,inputRules:!0}),console.log("Default editor initialized successfully")}catch(t){console.error("Failed to initialize default editor:",t);try{this.defaultEditor=new Qn,console.log("Default editor initialized with minimal config")}catch(n){console.error("Failed to initialize default editor even with minimal config:",n),this.defaultEditor=null}}}updateBreadcrumb(t){this.breadcrumbData=[{label:"Communication"},{label:t}]}loadTemplateData(t){this.isLoading=!0,0===this.allLanguages.length?this.templateService.languageList().subscribe({next:n=>{this.mapLanguageResponse(n),this.fetchAndPopulateTemplate(t)},error:()=>{this.allLanguages=[{name:"English",code:"en"}],this.fetchAndPopulateTemplate(t)}}):this.fetchAndPopulateTemplate(t)}fetchAndPopulateTemplate(t){this.templateService.fetchTemplateById(t).subscribe({next:n=>{this.templateData=n,this.populateFormWithTemplateData(n),this.isLoading=!1},error:n=>{this.isLoading=!1,this.toastr.error(n,"Error!")}})}get isSMS(){return"sms"===this.createForm?.get("channelType")?.value?.toLowerCase()}populateFormWithTemplateData(t){if(!t)return;const n=t.data||t,r=n.communicationTemplateDetails||[];this.currentTemplateData=n;const o=!0===n.isAvailableInAccountDetails?"true":"false";this.createForm.patchValue({channelType:n.templateType?.toLowerCase()||"email",templateName:n.name||"",allowAccessFromAccount:o,entryPoint:n.entryPoint||"",recipientType:n.recipientType||""});const s=this.createForm.get("languages");for(;0!==s.length;)s.removeAt(0);if(r.length>0)r.forEach(a=>{let c="en";const d=a.language||"English";if(this.allLanguages&&this.allLanguages.length>0){const f=this.allLanguages.find(h=>h.name.toLowerCase()===d.toLowerCase());f&&(c=f.code)}const u=this.buildLanguageFormGroup({code:c,name:d});u.patchValue({languageCode:c,languageName:d,emailSubject:a.subject||"",templateBody:"email"!==this.templateData.templateType?.toLowerCase()&&a.body||"",emailBody:"email"===this.templateData.templateType?.toLowerCase()&&a.body||""}),"email"===this.templateData.templateType?.toLowerCase()&&(this.htmlContents[s.length]=this.plainTextToHtml(a.body||"")),s.push(u)});else{const a=this.buildLanguageFormGroup({code:"en",name:"English"});s.push(a)}if(s.length>0){const c=s.at(0)?.value?.languageCode||"en";this.createForm.patchValue({activeLanguage:c})}this.updateLanguageValidation(this.createForm.get("channelType")?.value),setTimeout(()=>{this.isViewMode?this.createForm.disable():this.isEditMode&&(this.createForm.get("channelType")?.disable(),this.createForm.get("templateName")?.disable())},200),this.isViewMode?(this.updateBreadcrumb("View Communication Template"),this.selectedViewLanguageIndex=0):this.isEditMode&&this.updateBreadcrumb("Edit Communication Template"),"email"===this.templateData.templateType?.toLowerCase()&&(this.initializeEditorsForAllLanguages(),setTimeout(()=>{this.initializeEditorsForAllLanguages(),this.ensureEditorsAreReady()},100),setTimeout(()=>{this.ensureEditorsAreReady()},300))}loadDatabaseFields(){this.isLoadingFields=!0,this.templateService.getFieldsList().subscribe({next:t=>{this.mapFieldsToVariables(t),this.isLoadingFields=!1},error:()=>{this.isLoadingFields=!1,this.toastr.error("error","Error!"),this.variables=[]}})}mapFieldsToVariables(t){this.variables=Array.isArray(t)?t.map(n=>({name:n.name||n.fieldName||"Unknown Field",id:n.id||n.code||n.name?.toUpperCase().replace(/\s+/g,"_")||"UNKNOWN",code:n.code||n.id||"UNKNOWN_CODE"})):[]}buildCreateTemplateForm(){this.createForm=this.fb.group({channelType:["email",[w.k0.required]],templateName:[null,[w.k0.required]],allowAccessFromAccount:["true"],activeLanguage:["en"],languages:this.fb.array([]),entryPoint:["Account",[w.k0.required]],recipientType:["Customer",[w.k0.required]]}),this.createForm.get("channelType")?.valueChanges.subscribe(t=>{this.updateLanguageValidation(t),this.handleChannelTypeChange(t)}),this.updateLanguageValidation(this.createForm.get("channelType")?.value)}updateLanguageValidation(t){this.createForm.get("languages").controls.forEach(r=>{const o=r.get("emailSubject"),s=r.get("templateBody"),a=r.get("emailBody");"email"===t?(o?.setValidators([w.k0.required]),a?.setValidators([w.k0.required]),s?.clearValidators()):(o?.clearValidators(),a?.clearValidators(),s?.setValidators([w.k0.required])),o?.updateValueAndValidity(),s?.updateValueAndValidity(),a?.updateValueAndValidity()})}buildLanguagesFormArray(t){const n=new w.Yp([]);return t&&t.length>0?t.forEach(r=>{n.push(this.buildLanguageFormGroup(r))}):this.allLanguages&&this.allLanguages.length>0&&n.push(this.buildLanguageFormGroup(this.allLanguages[0])),n}buildLanguageFormGroup(t){return this.fb.group({languageCode:t?.code,languageName:t?.name,emailSubject:[null],templateBody:[null],emailBody:[null]})}get fValue(){return this.createForm.value}shouldShowLanguageFields(t){if(this.isViewMode)return 0===t;const n=this.fValue?.activeLanguage,r=this.fValue?.languages?.[t];return n===r?.languageCode}selectViewLanguage(t){this.selectedViewLanguageIndex=t}getSelectedLanguageDetail(){return this.isViewMode&&this.currentTemplateData?.communicationTemplateDetails?.length>0?this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex]||this.currentTemplateData.communicationTemplateDetails[0]:null}getFormValidationErrors(){let t={};Object.keys(this.createForm.controls).forEach(r=>{const o=this.createForm.get(r)?.errors;o&&(t[r]=o)});const n=this.createForm.get("languages");return n&&n.controls.forEach((r,o)=>{const s=r;Object.keys(s.controls).forEach(a=>{const c=s.get(a);c?.errors&&(t.languages||(t.languages={}),t.languages[o]||(t.languages[o]={}),t.languages[o][a]=c.errors)})}),t}openMapVariableModal(t,n){this.selectedVariable=t,this.mapVarModalRef=this.modalService.show(n,{animated:!0})}assignVariable(){this.mapVarModalRef.hide(),this.selectedVariable?.value&&(this.templateVarConfig.onUpdateVariable(this.selectedVariable),this.selectedVariable={value:null,index:-1})}updateTemplateValue(t,n){const r=this.createForm.get("languages").at(n),o=this.createForm.get("channelType")?.value;if(console.log("updateTemplateValue called with:",{template:t,index:n,channelType:o}),"email"===o){r.patchValue({emailBody:t}),r.get("emailBody")?.updateValueAndValidity(),this.isUpdatingFromMapping=!0;const s=this.plainTextToHtml(t);this.htmlContents[n]=s,console.log("Template value updated for email at index:",n),console.log("Plain text template:",t),console.log("HTML content for editor:",s),setTimeout(()=>{this.isUpdatingFromMapping=!1},200)}else r.patchValue({templateBody:t}),r.get("templateBody")?.updateValueAndValidity();this.createForm.updateValueAndValidity()}openAddLangModal(t){this.addLangModalRef=this.modalService.show(t,{animated:!0})}addLanguage(){this.addLangModalRef?.hide();const t=this.allLanguages.find(s=>s?.code===this.selectedLanguage),n=this.createForm.get("languages");if(n.controls.some(s=>s.get("languageCode")?.value===this.selectedLanguage))return void this.toastr.error("The same language is already selected.","Error!");const o=this.buildLanguageFormGroup(t);n.push(o),this.updateLanguageValidation(this.createForm.get("channelType")?.value),this.createForm.updateValueAndValidity(),"email"===this.createForm.get("channelType")?.value&&this.initializeEditorForLanguage(n.length-1)}removeLanguage(t){const n=this.createForm.get("languages");if(n.length<=1)return;const r=n.at(t).value;if(n.removeAt(t),this.fValue?.activeLanguage===r?.languageCode){const o=n.at(0)?.value;o&&this.createForm.patchValue({activeLanguage:o.languageCode})}this.createForm.updateValueAndValidity()}createTemplate(){if(this.markFormGroupTouched(this.createForm),this.createForm.invalid)return void this.toastr.error("Please fill all required fields.","Error");const t=this.createForm.value;if(!("email"===t.channelType?t.languages?.some(c=>c.emailBody&&c.emailBody.trim()):t.languages?.some(c=>c.templateBody&&c.templateBody.trim())))return void this.toastr.error("Please enter template body content.","Error");if(this.hasUnmappedVariables(t.languages))return void this.toastr.error("Please map all variables and continue creating template.","Error");if("email"===t.channelType&&!t.languages?.every(d=>d.emailSubject&&d.emailSubject.trim()))return void this.toastr.error("Please enter email subject for all languages.","Error");const o=c=>{const d=this.allLanguages.find(u=>u.code===c);return d?d.name:"English"},s=t.languages.map(c=>{let d="";d="email"===t.channelType?c.emailBody||"":c.templateBody||"";const u={Language:o(c.languageCode),body:d};return"email"===t.channelType&&(u.Subject=c.emailSubject||""),u}),a={templateType:(c=>{switch(c.toLowerCase()){case"email":default:return"Email";case"sms":return"SMS";case"letter":return"Letter"}})(t.channelType),Name:t.templateName,isAvailableInAccountDetails:"true"===t.allowAccessFromAccount,CommunicationTemplateDetails:s,entryPoint:t.entryPoint,recipientType:t.recipientType};this.templateService.saveCommunicationTemplate(a).subscribe({next:()=>{this.toastr.success(`The Template "${a.Name}" has been created successfully.`,"Success!"),this.router.navigate(["communication/search-communication-templates"])},error:c=>{this.toastr.error(c,"Error!")}})}updateTemplate(){if(this.markFormGroupTouched(this.createForm),this.createForm.invalid)return void this.toastr.error("Please fill in all required fields.","Error");const t=this.createForm.getRawValue();if(!("email"===t.channelType?t.languages?.some(c=>c.emailBody&&c.emailBody.trim()):t.languages?.some(c=>c.templateBody&&c.templateBody.trim())))return void this.toastr.error("Please enter template body content.","Error");if(this.hasUnmappedVariables(t.languages))return void this.toastr.error("Please map all variables and continue creating template.","Error");if(!t.channelType)return void this.toastr.error("Please select a channel type.","Error");if("email"===t.channelType&&!t.languages?.every(d=>d.emailSubject&&d.emailSubject.trim()))return void this.toastr.error("Please enter email subject for all languages.","Error");const o=c=>{if(!c)return"English";const d=this.allLanguages.find(u=>u.code===c);return d?d.name:"English"},s=t.languages?.map(c=>{let d="";d="email"===t.channelType?c.emailBody||"":c.templateBody||"";const u={Language:o(c.languageCode),body:d};return"email"===t.channelType&&(u.Subject=c.emailSubject||""),u})||[],a={id:this.templateId,templateType:(c=>{if(!c||"string"!=typeof c)return"Email";switch(c.toLowerCase()){case"email":default:return"Email";case"sms":return"SMS";case"letter":return"Letter"}})(t.channelType),Name:t.templateName||"",IsAvailableInAccountDetails:"true"===t.allowAccessFromAccount,CommunicationTemplateDetails:s,entryPoint:t.entryPoint,recipientType:t.recipientType};this.templateService.updateCcmTemplate(a).subscribe({next:()=>{this.toastr.success(`The Template "${a.Name}" has been updated successfully.`,"Success!"),this.router.navigate(["communication/search-communication-templates"])},error:c=>{this.toastr.error(c,"Error!")}})}loadLanguageList(){this.templateService.languageList().subscribe({next:t=>{this.mapLanguageResponse(t)},error:t=>{this.toastr.error(t,"Error"),this.allLanguages=[{name:"English",code:"en"}],this.initializeDefaultLanguage()}})}mapLanguageResponse(t){if(!t||!Array.isArray(t))return this.allLanguages=[{name:"English",code:"en"}],void this.initializeDefaultLanguage();this.allLanguages=t.map(n=>({name:n.name||n.itemName||"Unknown Language",code:n.code||n.itemCode||n.name?.toLowerCase().substring(0,2)||"en"})),0===this.allLanguages.length&&(this.allLanguages=[{name:"English",code:"en"}]),this.initializeDefaultLanguage()}initializeDefaultLanguage(){if(this.createForm&&this.allLanguages.length>0){const t=this.createForm.get("activeLanguage")?.value;if(!t||!this.allLanguages.find(n=>n.code===t)){this.createForm.patchValue({activeLanguage:this.allLanguages[0].code});const n=this.createForm.get("languages");if(0===n.length){const r=this.buildLanguageFormGroup(this.allLanguages[0]);n.push(r),this.updateLanguageValidation(this.createForm.get("channelType")?.value)}}}}markFormGroupTouched(t){Object.keys(t.controls).forEach(n=>{const r=t.get(n);r instanceof w.gE||r instanceof w.Yp?this.markFormGroupTouched(r):r?.markAsTouched()})}isFieldInvalid(t){const n=this.createForm.get(t);return!(!n||!n.invalid||!n.dirty&&!n.touched)}getFieldErrorMessage(t){const n=this.createForm.get(t);return n&&n.errors&&(n.dirty||n.touched)&&n.errors.required?`${this.getFieldDisplayName(t)} is required`:""}isLanguageFieldInvalid(t,n){const s=this.createForm.get("languages").at(t)?.get(n);return!(!s||!s.invalid||!s.dirty&&!s.touched)}getLanguageFieldErrorMessage(t,n){const s=this.createForm.get("languages").at(t)?.get(n);return s&&s.errors&&(s.dirty||s.touched)&&s.errors.required?`${this.getFieldDisplayName(n)} is required`:""}hasUnmappedVariables(t){if(!t||0===t.length)return!1;const n=this.createForm.get("channelType")?.value;return t.some(r=>("email"===n?r.emailBody||"":r.templateBody||"").includes("<<Var>>"))}getFieldDisplayName(t){return{templateName:"Template Name",channelType:"Channel Type",emailSubject:"Subject Line",templateBody:"Template Body",entryPoint:"Entry Point",recipientType:"Recipient Type"}[t]||t}insertVariable(){const t=this.getActiveLanguageIndex();if(-1===t)return void console.warn("No active language index found");const n=this.createForm.get("channelType")?.value||this.templateData?.templateType?.toLowerCase(),r=this.createForm.get("languages").at(t);if(console.log("Inserting variable for channel type:",n,"at index:",t),"email"===n){const o=this.editors[t];if(o&&o.view)try{o.commands.insertText("<<Var>>").exec(),console.log("Variable inserted into editor at index:",t)}catch(s){console.error("Error inserting variable into editor:",s)}else console.warn("Editor not available at index:",t),this.initializeEditorForLanguage(t),setTimeout(()=>{const s=this.editors[t];if(s&&s.view)try{s.commands.insertText("<<Var>>").exec(),console.log("Variable inserted into editor after retry at index:",t)}catch(a){console.error("Error inserting variable into editor after retry:",a),this.fallbackInsertVariableToEmailBody(t)}else this.fallbackInsertVariableToEmailBody(t)},500)}else{const o=r.get("templateBody"),s=o?.value||"",a=document.getElementById("templateBody");if(!a)return console.warn("Textarea element not found, adding variable to form control directly"),o?.setValue(s+"<<Var>>"),void o?.updateValueAndValidity();const c=a.selectionStart,d=s.substring(0,c)+"<<Var>>"+s.substring(a.selectionEnd||c);o?.setValue(d),o?.updateValueAndValidity(),setTimeout(()=>{a.focus(),a.setSelectionRange(c+7,c+7)},0)}}fallbackInsertVariableToEmailBody(t){const r=this.createForm.get("languages").at(t).get("emailBody"),o=r?.value||"";r?.setValue(o+"<<Var>>"),r?.updateValueAndValidity(),this.htmlContents[t]=this.plainTextToHtml(r?.value||""),console.log("Variable added to email body via fallback method at index:",t)}ensureEditorsAreReady(){if("email"===(this.createForm.get("channelType")?.value||this.templateData?.templateType?.toLowerCase())){const n=this.createForm.get("languages");for(let r=0;r<n.length;r++)if(!this.editors[r]||!this.editors[r].view){console.log("Re-initializing editor for index:",r),this.initializeEditorForLanguage(r);const o=n.at(r).get("emailBody");o?.value&&!this.htmlContents[r]&&(this.htmlContents[r]=this.plainTextToHtml(o.value))}}}getActiveLanguageIndex(){const t=this.createForm.get("activeLanguage").value,n=this.createForm.get("languages");for(let r=0;r<n.length;r++)if(n.at(r).get("languageCode").value===t)return r;return-1}shouldRestrictHtmlTags(){return"sms"!==this.createForm?.get("channelType")?.value}getCurrentTemplateBody(t){const n=this.createForm.get("channelType")?.value,r=this.createForm.get("languages").at(t);return"email"===n?r.get("emailBody")?.value||"":r.get("templateBody")?.value||""}isLanguageFieldInvalidForCurrentChannel(t){const r="email"===this.createForm.get("channelType")?.value?"emailBody":"templateBody";return this.isLanguageFieldInvalid(t,r)}getLanguageFieldErrorMessageForCurrentChannel(t){const r="email"===this.createForm.get("channelType")?.value?"emailBody":"templateBody";return this.getLanguageFieldErrorMessage(t,r)}initializeEditorForLanguage(t){if(!this.editors[t])try{this.editors[t]=new Qn({history:!0,keyboardShortcuts:!0,inputRules:!0}),this.htmlContents[t]="",console.log("Editor initialized successfully for index:",t)}catch(n){console.error("Failed to initialize editor for index:",t,n);try{this.editors[t]=new Qn,this.htmlContents[t]="",console.log("Editor initialized with minimal config for index:",t)}catch(r){console.error("Failed to initialize editor even with minimal config:",r)}}}initializeEditorsForAllLanguages(){const t=this.createForm.get("channelType")?.value||this.templateData?.templateType?.toLowerCase();if(console.log("Initializing editors for channel type:",t),"email"===t){const n=this.createForm.get("languages");console.log("Languages array length:",n.length);for(let r=0;r<n.length;r++)try{this.initializeEditorForLanguage(r);const o=n.at(r).get("emailBody");o?.value?(this.htmlContents[r]=this.plainTextToHtml(o.value),console.log("Initialized editor content for index:",r),console.log("Plain text from form:",o.value),console.log("HTML for editor:",this.htmlContents[r])):(this.htmlContents[r]="",console.log("Initialized empty editor content for index:",r))}catch(o){console.error("Error initializing editor for language index:",r,o)}}}onEditorContentChange(t){if(this.isUpdatingFromMapping)return void console.log("Skipping editor content change - updating from mapping");const r=this.createForm.get("languages").at(t).get("emailBody");if(r){const o=this.htmlToPlainText(this.htmlContents[t]||"");console.log("Editor content changed at index:",t),console.log("Raw HTML content:",this.htmlContents[t]),console.log("Plain text content:",o),r.setValue(o),r.updateValueAndValidity()}}destroyEditor(t){this.editors[t]&&(this.editors[t].destroy(),this.editors[t]=null,this.htmlContents[t]="")}handleChannelTypeChange(t){"email"===t?(this.defaultEditor||this.initializeDefaultEditor(),this.initializeEditorsForAllLanguages(),setTimeout(()=>{this.initializeEditorsForAllLanguages()},100)):this.editors.forEach((n,r)=>{n&&this.destroyEditor(r)})}normalizeTemplateVariables(t){return t.replace(/&lt;&lt;/g,"<<").replace(/&gt;&gt;/g,">>").replace(/&amp;/g,"&")}htmlToPlainText(t){if(!t)return"";console.log("Converting HTML to plain text:",t);const n=document.createElement("div");n.innerHTML=t;let r=n.textContent||n.innerText||"";return console.log("Extracted plain text:",r),r=this.normalizeTemplateVariables(r),console.log("After normalization:",r),r}plainTextToHtml(t){if(!t)return"";console.log("Converting plain text to HTML:",t);let n=t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");console.log("After HTML escaping:",n);const s=n.split("\n").map(a=>""===a.trim()?"<p><br></p>":`<p>${a}</p>`).join("");return console.log("Final HTML result:",s),s}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275cmp=l.VBU({type:i,selectors:[["app-create-template"]],viewQuery:function(n,r){if(1&n&&l.GBs(zm,5),2&n){let o;l.mGM(o=l.lsd())&&(r.templateVarConfig=o.first)}},standalone:!0,features:[l.Jv_([ge.I]),l.aNF],decls:14,vars:4,consts:[["mapVariableModal",""],["addLangModal",""],["templateVarConfig","appTemplateVarConfig"],[1,"inner-layout-container"],[3,"data"],[1,"d-flex","align-items-center","mb-4"],[1,"title","mb-0"],["tooltip","Design templates for different communication channels with variable\n        support and multilingual capabilities","type","button",1,"ms-3","p-0","border-0"],[1,"enc-card"],[1,"enc-card",3,"formGroup"],["src","assets/new/svgs/instruction-info.svg"],[1,"card-content","text-center","p-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3"],[1,"card-content"],[1,"row"],[1,"col-md-6","col-12"],[1,"form-control-group"],[1,"form-label","required"],["type","text","placeholder","Enter Template Name","id","templateName","formControlName","templateName",1,"form-control",3,"readonly"],[1,"form-error"],["id","channelType","formControlName","channelType",1,"form-select",3,"disabled"],["value",""],["value","email"],["value","sms"],["value","letter"],[1,"form-control-group","h-100","d-flex","flex-column","justify-content-between"],["formControlName","entryPoint","disabled","",1,"form-select"],["formControlName","recipientType","disabled","",1,"form-select"],[1,"col-md-12"],[1,"d-flex","align-items-center","justify-content-between"],[1,"form-button-group"],["btnRadioGroup","","formControlName","activeLanguage",1,"form-button-group"],["id","addLanguageBtn",1,"btn","btn-outline-primary"],[1,"card-footer"],[1,"btn","btn-outline-primary","mw-150px",3,"routerLink"],[3,"selected"],["formControlName","entryPoint",1,"form-select",3,"disabled"],["value","Account"],["value","User"],[1,"text-danger","small","mt-1"],["formControlName","recipientType",1,"form-select",3,"disabled"],["value","Customer"],["value","Agent"],[1,"form-radio-group","disabled"],[1,"form-radio-group",3,"disabled"],["type","radio","name","allowAccessView","id","allowAccessYes","value","true","disabled","",3,"checked"],["type","radio","name","allowAccessView","id","allowAccessNo","value","false","disabled","",3,"checked"],[1,"form-radio-group"],["type","radio","name","allowAccessFromAccount","id","allowAccessYes","value","true","formControlName","allowAccessFromAccount",3,"disabled"],["type","radio","name","allowAccessFromAccount","id","allowAccessNo","value","false","formControlName","allowAccessFromAccount",3,"disabled"],[1,"language-button-wrapper"],[1,"language-btn",3,"click"],[1,"language-btn",3,"btnRadio","id"],[1,"remove-language-btn",3,"title"],[1,"remove-language-btn",3,"click","title"],["id","addLanguageBtn",1,"btn","btn-outline-primary",3,"click"],["src","assets/new/svgs/language.svg",1,"me-2"],[1,"col-md-6"],[1,"form-label"],["type","file","accept",".jpg,.jpeg,.png,.pdf","disabled","",1,"form-control",3,"change"],["rows","7","readonly","",1,"form-control","form-control-readonly",3,"value"],["appTemplatePreview","",3,"template","variables"],["type","text","readonly","",1,"form-control","form-control-readonly",3,"value"],[3,"formArrayName"],[3,"formGroupName"],[1,"d-flex","align-items-center"],["tooltip","Use <<Var>> to add variables that can be mapped to template variables","placement","right","container","body","tooltipClass","enc-tooltip",1,"fa","fa-info-circle","text-primary","info-icon","ms-2"],[1,"position-relative"],["type","button","tooltip","Add Variable",1,"btn","btn-primary","rounded","rounded-5","icon-btn","position-absolute","d-flex","align-items-center","justify-content-center","add-var-btn",3,"click"],["src","assets/new/svgs/var.svg","width","16","height","16"],["type","text","placeholder","Enter Email Subject","id","emailSubject","formControlName","emailSubject",1,"form-control"],[1,"email-body-container"],[3,"editor","toolbar"],[3,"editor","ngModel","ngModelOptions","placeholder","disabled"],[1,"editor-loading"],[1,"email-body-help-text","mt-1"],[1,"text-muted"],[1,"fas","fa-info-circle","me-1"],[3,"ngModelChange","editor","ngModel","ngModelOptions","placeholder","disabled"],["placeholder","Enter Approved Template Content","id","templateBody","rows","7","formControlName","templateBody","appRestrictHtmlTags","",1,"form-control",3,"allowCopyPasteOnly"],[1,"text-muted","mt-1","small"],[1,"fas","fa-mobile-alt","me-1"],[1,"variable-map-legends"],[1,"unmapped"],[1,"mapped"],["appTemplateVarConfig","",3,"selectVar","change","template"],[1,"btn","btn-secondary","mw-150px","me-4",3,"click"],[1,"modal-header","d-flex","align-items-center","justify-content-between"],[1,"modal-title"],["src","assets/new/svgs/side-drawer-close.svg","alt","Close",1,"modal-close-btn",3,"click"],[1,"modal-body"],["bindLabel","name","bindValue","name","placeholder","Select Database Field",1,"form-ng-select",3,"ngModelChange","items","ngModel","clearable","searchable"],[1,"modal-footer","justify-content-center"],["type","button",1,"btn","btn-success","mw-150px",3,"click","disabled"],["bindLabel","name","bindValue","code","placeholder","Select Language",1,"form-ng-select",3,"ngModelChange","items","ngModel","clearable","searchable"]],template:function(n,r){1&n&&(l.j41(0,"div",3),l.nrm(1,"app-breadcrumb",4),l.j41(2,"div",5)(3,"h2",6),l.DNE(4,jm,1,0)(5,Gm,1,0)(6,Wm,1,0),l.k0s(),l.DNE(7,Xm,2,0,"button",7),l.k0s(),l.DNE(8,Um,7,0,"div",8)(9,Dg,52,17,"div",9),l.k0s(),l.DNE(10,Ig,12,5,"ng-template",null,0,l.C5r)(12,Ag,12,5,"ng-template",null,1,l.C5r)),2&n&&(l.R7$(),l.Y8G("data",r.breadcrumbData),l.R7$(3),l.vxM(r.isViewMode?4:r.isEditMode?5:6),l.R7$(3),l.vxM(r.isViewMode?-1:7),l.R7$(),l.vxM(r.isLoading?8:9))},dependencies:[w.YN,w.xH,w.y7,w.me,w.wz,w.Fm,w.BC,w.cb,w.vS,w.X1,w.j4,w.JD,w.$R,w.v8,ti.Gg,L.Wk,oa.p4,oa.ew,Fm.vr,Pm.dg,Lm.DM,Vm.D,Bm,me,ni,xm,Qp,Zl],styles:['.variable-map-legends[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 2.5rem;\n}\n.variable-map-legends[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  position: relative;\n}\n.variable-map-legends[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]::before {\n  position: absolute;\n  content: "";\n  width: 0.825rem;\n  height: 0.825rem;\n  border: solid 1px gray;\n  background-color: lightgray;\n  border-radius: 50%;\n  left: -1.25rem;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.variable-map-legends[_ngcontent-%COMP%]   label.mapped[_ngcontent-%COMP%]::before {\n  background-color: #dcfce7;\n  border-color: #86efac;\n}\n.variable-map-legends[_ngcontent-%COMP%]   label.unmapped[_ngcontent-%COMP%]::before {\n  background-color: #fef9c3;\n  border-color: #fde047;\n}\n\n.language-button-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  display: inline-block;\n}\n\n.language-btn[_ngcontent-%COMP%] {\n  position: relative;\n  padding-right: 2rem !important;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.language-btn.active[_ngcontent-%COMP%] {\n  font-weight: 600;\n  border-width: 2px;\n}\n.language-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: #f8f9fa;\n  border-color: #dee2e6;\n}\n.language-btn[_ngcontent-%COMP%]:disabled {\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n.remove-language-btn[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 50%;\n  right: 0.5rem;\n  transform: translateY(-50%);\n  color: #ef4444;\n  cursor: pointer;\n  font-size: 1.2rem;\n  font-weight: bold;\n  line-height: 1;\n  width: 1.2rem;\n  height: 1.2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.2s ease;\n  z-index: 10;\n}\n.remove-language-btn[_ngcontent-%COMP%]:hover {\n  background-color: #fef2f2;\n  color: #dc2626;\n  transform: translateY(-50%) scale(1.1);\n}\n\n  .modal-close-btn {\n  width: 1.625rem;\n  height: 1.625rem;\n  cursor: pointer;\n  border-radius: 50%;\n}\n\n.form-control-readonly[_ngcontent-%COMP%] {\n  background-color: #f8f9fa !important;\n  color: #6c757d !important;\n  cursor: not-allowed !important;\n  opacity: 0.8;\n}\n\n.form-radio-group.disabled[_ngcontent-%COMP%] {\n  opacity: 0.6;\n  pointer-events: none;\n}\n.form-radio-group.disabled[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  color: #6c757d;\n  cursor: not-allowed;\n}\n.form-radio-group.disabled[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\n  cursor: not-allowed;\n}\n\n.text-muted[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n  display: block;\n}\n\n.form-control.is-invalid[_ngcontent-%COMP%] {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.invalid-feedback[_ngcontent-%COMP%] {\n  display: block;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 0.875rem;\n  color: #dc3545;\n}\n\ntextarea.form-control.is-invalid[_ngcontent-%COMP%] {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.add-var-btn[_ngcontent-%COMP%] {\n  top: 10px;\n  right: 10px;\n  z-index: 5;\n}\n\n.email-body-container[_ngcontent-%COMP%] {\n  position: relative;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor {\n  min-height: 200px !important;\n  border: 2px solid #e1e5e9;\n  border-radius: 6px;\n  transition: all 0.2s ease;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor:focus-within {\n  border-color: #0d6efd;\n  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor .NgxEditor__Content {\n  min-height: 180px;\n  padding: 12px;\n  font-size: 0.9rem;\n  line-height: 1.4;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor .NgxEditor__Content:has-text("<<") {\n  background-color: #fff9c4;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar {\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n  padding: 8px 12px;\n  border-radius: 6px 6px 0 0;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem {\n  opacity: 1 !important;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem:disabled {\n  opacity: 0.6 !important;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar {\n  border-bottom: 1px solid #e1e5e9;\n  background-color: #f8f9fa;\n  padding: 8px;\n  border-radius: 6px 6px 0 0;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem {\n  margin: 2px;\n  border-radius: 4px;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem:hover {\n  background-color: #e9ecef;\n}\n.email-body-container[_ngcontent-%COMP%]     .NgxEditor__MenuBar .NgxEditor__MenuItem.NgxEditor__MenuItem--Active {\n  background-color: #0d6efd;\n  color: white;\n}\n.email-body-container[_ngcontent-%COMP%]   .email-body-input[_ngcontent-%COMP%] {\n  font-family: "Courier New", monospace;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  background-color: #fafbfc;\n  border: 2px solid #e1e5e9;\n  border-radius: 6px;\n  transition: all 0.2s ease;\n}\n.email-body-container[_ngcontent-%COMP%]   .email-body-input[_ngcontent-%COMP%]:focus {\n  background-color: #ffffff;\n  border-color: #0d6efd;\n  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);\n}\n.email-body-container[_ngcontent-%COMP%]   .email-body-input.is-invalid[_ngcontent-%COMP%] {\n  border-color: #dc3545;\n  background-color: #fff5f5;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);\n}\n.email-body-container[_ngcontent-%COMP%]   .email-body-help-text[_ngcontent-%COMP%] {\n  margin-top: 0.5rem;\n}\n.email-body-container[_ngcontent-%COMP%]   .email-body-help-text[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #6c757d;\n  font-size: 0.8rem;\n  display: flex;\n  align-items: center;\n}\n.email-body-container[_ngcontent-%COMP%]   .email-body-help-text[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  color: #0d6efd;\n  margin-right: 0.25rem;\n}\n.email-body-container[_ngcontent-%COMP%]   .editor-loading[_ngcontent-%COMP%] {\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #f8f9fa;\n  border: 2px solid #e1e5e9;\n  border-radius: 0 0 6px 6px;\n  border-top: none;\n}\n.email-body-container[_ngcontent-%COMP%]   .editor-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #6c757d;\n  margin: 0;\n  font-style: italic;\n  font-size: 0.9rem;\n}\n\n  .NgxEditor__Content p:has-text("<<") {\n  background-color: rgba(255, 249, 196, 0.3);\n  padding: 2px 4px;\n  border-radius: 3px;\n}']})}}return i})()},53733:(la,fn,R)=>{R.d(fn,{D:()=>ti});var l=R(60177),w=R(51188),L=R(54438);const Zn=me=>({active:me});function ei(me,ni){if(1&me&&(L.j41(0,"a",3),L.EFF(1),L.k0s()),2&me){const ge=ni.$implicit;L.Y8G("routerLink",null==ge?null:ge.path)("ngClass",L.eq3(3,Zn,null==ge?null:ge.active)),L.R7$(),L.JRh(null==ge?null:ge.label)}}let ti=(()=>{class me{constructor(){this.data=[{label:"Home",path:"/"}]}static{this.\u0275fac=function(re){return new(re||me)}}static{this.\u0275cmp=L.VBU({type:me,selectors:[["app-breadcrumb"]],inputs:{data:"data"},standalone:!0,features:[L.aNF],decls:5,vars:1,consts:[[1,"enc-breadcrumb"],[1,"link",3,"routerLink"],["src","assets/new/svgs/bc_home_icon.svg","alt","Home"],[1,"link",3,"routerLink","ngClass"]],template:function(re,xt){1&re&&(L.j41(0,"div",0)(1,"a",1),L.nrm(2,"img",2),L.k0s(),L.Z7z(3,ei,2,5,"a",3,L.Vm6),L.k0s()),2&re&&(L.R7$(),L.Y8G("routerLink","/home"),L.R7$(2),L.Dyx(xt.data))},dependencies:[w.Wk,l.YU],styles:['[_nghost-%COMP%] {\n  display: inline-block;\n  margin-bottom: 1.5rem;\n}\n\n.enc-breadcrumb[_ngcontent-%COMP%] {\n  display: inline-flex;\n  pointer-events: none;\n  gap: 1rem;\n  background-color: #ffffff;\n  border-radius: 6px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  overflow: hidden;\n  height: 1.75rem;\n}\n.enc-breadcrumb[_ngcontent-%COMP%]   a.link[_ngcontent-%COMP%] {\n  padding: 0 0.75rem;\n  text-decoration: none;\n  color: #5a5962;\n  display: flex;\n  align-items: center;\n  line-height: 1;\n  position: relative;\n}\n.enc-breadcrumb[_ngcontent-%COMP%]   a.link[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  height: 0.875rem;\n}\n.enc-breadcrumb[_ngcontent-%COMP%]   a.link[_ngcontent-%COMP%]:first-child {\n  background-color: #e7e4f0;\n  padding: 0 0.5rem 0 0.75rem;\n}\n.enc-breadcrumb[_ngcontent-%COMP%]   a.link[_ngcontent-%COMP%]:first-child::after {\n  content: "";\n  position: absolute;\n  border-top: 0.95rem solid transparent;\n  border-left: 0.75rem solid #e7e4f0;\n  border-bottom: 0.975rem solid transparent;\n  width: 0;\n  height: 0;\n  right: -0.65rem;\n}\n.enc-breadcrumb[_ngcontent-%COMP%]   a.link.active[_ngcontent-%COMP%], .enc-breadcrumb[_ngcontent-%COMP%]   a.link[_ngcontent-%COMP%]:hover, .enc-breadcrumb[_ngcontent-%COMP%]   a.link[_ngcontent-%COMP%]:last-child {\n  color: #3f3b53;\n}\n.enc-breadcrumb[_ngcontent-%COMP%]   a.link[_ngcontent-%COMP%]:not(.enc-breadcrumb   a.link[_ngcontent-%COMP%]:first-child)::after {\n  content: ">";\n  position: absolute;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  right: -0.75rem;\n}']})}}return me})()}}]);