{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./cure-media.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./cure-media.component.css?ngResource\";\nimport { Component, Output, Input, ChangeDetectorRef, EventEmitter } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { SearchFilter } from '../../shared/pipe/search-filter.pipe';\nimport { ClicktoCureService } from \"../clicktocure.service\";\nlet CureMediaComponent = class CureMediaComponent {\n  constructor(toastr, changeDetector, searchFilterPipe, clicktoCureService) {\n    this.toastr = toastr;\n    this.changeDetector = changeDetector;\n    this.searchFilterPipe = searchFilterPipe;\n    this.clicktoCureService = clicktoCureService;\n    this.fileuploaded = false;\n    this.mediafilterList = new EventEmitter();\n    this.viewDetails = new EventEmitter();\n    this.documentList = [];\n    this.isSubmit = false;\n    this.uploadDocument = {\n      id: \"\",\n      fileName: \"\",\n      mediaName: \"\",\n      description: \"\"\n    };\n  }\n  ngOnInit() {}\n  fileUpload(event) {\n    const fileList = event.target.files;\n    if (fileList.length > 0) {\n      const file = fileList[0];\n      const fd = new FormData();\n      fd.append('file', file);\n      this.clicktoCureService.uploadFile(fd).subscribe(data => {\n        this.fileuploaded = true;\n        this.toastr.success(\"File uploaded successfully\");\n        this.uploadDocument[\"fileName\"] = data[\"fileName\"];\n      }, err => {\n        this.toastr.error(err, \"Error!\");\n        this.fileuploaded = false;\n      });\n    }\n  }\n  filePreview(fileName) {\n    var filetype = fileName.split('.')[1];\n    this.clicktoCureService.filePreviewDoc(fileName).subscribe(res => {\n      if (filetype == \"pdf\" || filetype == \"PDF\") {\n        var mediaType = 'application/pdf';\n      } else if (filetype == \"ocx\" || filetype == \"docx\") {\n        var mediaType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n      } else if (filetype == \"xlsx\" || filetype == \"xls\") {\n        var mediaType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\n      } else if (filetype == 'mp4' || filetype == 'mov') {\n        var mediaType = 'video/mp4';\n      } else if (filetype == 'flv') {\n        var mediaType = 'video/flv';\n      } else if (filetype == 'mp3') {\n        var mediaType = 'audio/mp3';\n      } else if (filetype == 'jpg') {\n        var mediaType = 'image/jpeg';\n      } else {\n        var mediaType = 'image/jpeg';\n      }\n      const arrayBufferView = new Uint8Array(res);\n      const file = new Blob([arrayBufferView], {\n        type: mediaType\n      });\n      const url = window.URL.createObjectURL(file);\n      window.open(url);\n    });\n  }\n  saveDocuments() {\n    if (this.uploadDocument[\"fileName\"] == \"\") {\n      this.toastr.warning(\"Please upload the document\", \"Warning!\");\n      return false;\n    }\n    this.uploadDocument.cureId = this.cureId;\n    this.isSubmit = true;\n    this.clicktoCureService.updateDocuments(this.uploadDocument).subscribe(res => {\n      this.toastr.success(\"Document details submitted successfully\", \"Success!\");\n      this.uploadDocument = {\n        id: \"\",\n        fileName: \"\",\n        mediaName: \"\",\n        description: \"\"\n      };\n      this.viewDetails.emit({\n        id: this.cureId\n      });\n      this.isSubmit = false;\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.fileuploaded = false;\n      this.isSubmit = false;\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: ChangeDetectorRef\n    }, {\n      type: SearchFilter\n    }, {\n      type: ClicktoCureService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      cureMediaList: [{\n        type: Input\n      }],\n      filterMediaDataList: [{\n        type: Input\n      }],\n      cureId: [{\n        type: Input\n      }],\n      mediafilterList: [{\n        type: Output,\n        args: [\"mediafilterList\"]\n      }],\n      viewDetails: [{\n        type: Output,\n        args: [\"viewDetails\"]\n      }]\n    };\n  }\n};\nCureMediaComponent = __decorate([Component({\n  selector: 'cure-cure-media',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CureMediaComponent);\nexport { CureMediaComponent };", "map": {"version": 3, "names": ["Component", "Output", "Input", "ChangeDetectorRef", "EventEmitter", "ToastrService", "SearchFilter", "ClicktoCureService", "CureMediaComponent", "constructor", "toastr", "changeDetector", "searchFilterPipe", "clicktoCureService", "fileuploaded", "mediafilterList", "viewDetails", "documentList", "isSubmit", "uploadDocument", "id", "fileName", "mediaName", "description", "ngOnInit", "fileUpload", "event", "fileList", "target", "files", "length", "file", "fd", "FormData", "append", "uploadFile", "subscribe", "data", "success", "err", "error", "filePreview", "filetype", "split", "filePreviewDoc", "res", "mediaType", "arrayBufferView", "Uint8Array", "Blob", "type", "url", "window", "URL", "createObjectURL", "open", "saveDocuments", "warning", "cureId", "updateDocuments", "emit", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\click-to-cure\\cure-media\\cure-media.component.ts"], "sourcesContent": ["import { Component, OnInit,ViewChild,Output,TemplateRef, Input,ChangeDetectorRef,EventEmitter } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { SearchFilter } from '../../shared/pipe/search-filter.pipe'\r\n\r\nimport { ClicktoCureConfigService} from \"../clicktocureconfig.service\"\r\nimport { ClicktoCureService} from \"../clicktocure.service\"\r\n\r\n@Component({\r\n  selector: 'cure-cure-media',\r\n  templateUrl: './cure-media.component.html',\r\n  styleUrls: ['./cure-media.component.css']\r\n})\r\nexport class CureMediaComponent implements OnInit {\r\n  fileuploaded = false;\r\n  @Input() cureMediaList: any;\r\n  @Input() filterMediaDataList: any;\r\n  @Input() cureId: any;\r\n  @Output(\"mediafilterList\") mediafilterList: EventEmitter<any> = new EventEmitter();\r\n  @Output(\"viewDetails\") viewDetails: EventEmitter<any> = new EventEmitter();\r\n  uploadDocument: any;\r\n  documentList = []\r\n  mediasearchFilter: any;\r\n  isSubmit= false\r\n\r\n  constructor(public toastr: ToastrService, private changeDetector : ChangeDetectorRef,\r\n              private searchFilterPipe : SearchFilter,\r\n              private clicktoCureService: ClicktoCureService) {\r\n  \tthis.uploadDocument = {\r\n  \t\tid:\"\",\r\n  \t\tfileName:\"\",\r\n  \t\tmediaName:\"\",\r\n\t\t  description:\"\"\r\n  \t}\r\n  }\r\n\r\n  ngOnInit() {}\r\n  fileUpload(event): void {\r\n    const fileList: FileList = event.target.files;\r\n    if (fileList.length > 0) {\r\n      const file: File = fileList[0];\r\n      const fd: FormData = new FormData();\r\n      fd.append('file', file);\r\n      this.clicktoCureService\r\n        .uploadFile(fd)\r\n        .subscribe((data: any) => {\r\n        \tthis.fileuploaded = true\r\n        \tthis.toastr.success(\"File uploaded successfully\")\r\n            this.uploadDocument[\"fileName\"] = data[\"fileName\"]\r\n        },err=>{\r\n        \tthis.toastr.error(err, \"Error!\")\r\n        \tthis.fileuploaded = false\r\n        })\r\n    }\r\n  }\r\n\r\n\r\n  filePreview(fileName: string) {\r\n    var filetype = fileName.split('.')[1];\r\n    this.clicktoCureService.filePreviewDoc(fileName).subscribe((res: any) => {\r\n        if (filetype == \"pdf\" || filetype == \"PDF\" ) {\r\n          var mediaType = 'application/pdf';\r\n        } else if (filetype == \"ocx\" || filetype==\"docx\") {\r\n          var mediaType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\r\n        } else if(filetype == \"xlsx\" || filetype == \"xls\") {\r\n          var mediaType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n        } else if (filetype == 'mp4' || filetype == 'mov') {\r\n          var mediaType = 'video/mp4';\r\n        } else if (filetype == 'flv') {\r\n          var mediaType = 'video/flv';\r\n        } else if (filetype == 'mp3') {\r\n          var mediaType = 'audio/mp3';\r\n        } else if (filetype == 'jpg') {\r\n          var mediaType = 'image/jpeg';\r\n        } else {\r\n          var mediaType = 'image/jpeg';\r\n        }\r\n        const arrayBufferView = new Uint8Array( res );\r\n        const file = new Blob( [ arrayBufferView ], { type: mediaType } );\r\n        const url = window.URL.createObjectURL(file);\r\n        window.open(url);\r\n     });\r\n  }\r\n\r\n  saveDocuments(){\r\n     if(this.uploadDocument[\"fileName\"]==\"\"){\r\n       this.toastr.warning(\"Please upload the document\", \"Warning!\");\r\n       return false\r\n     }\r\n  \t this.uploadDocument.cureId = this.cureId\r\n     this.isSubmit = true;\r\n  \t this.clicktoCureService\r\n      .updateDocuments(this.uploadDocument)\r\n      .subscribe(res => {\r\n      \tthis.toastr.success(\"Document details submitted successfully\", \"Success!\");\r\n        this.uploadDocument = {\r\n          id:\"\",\r\n          fileName:\"\",\r\n          mediaName:\"\",\r\n          description:\"\"\r\n        }\r\n        this.viewDetails.emit({id: this.cureId})\r\n        this.isSubmit = false;\r\n\r\n  \t },err=>{\r\n    \t  this.toastr.error(err, \"Error!\")\r\n    \t  this.fileuploaded = false\r\n        this.isSubmit = false;\r\n     })\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAmBC,MAAM,EAAcC,KAAK,EAACC,iBAAiB,EAACC,YAAY,QAAQ,eAAe;AAEpH,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,sCAAsC;AAGnE,SAASC,kBAAkB,QAAO,wBAAwB;AAOnD,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAY7BC,YAAmBC,MAAqB,EAAUC,cAAkC,EAChEC,gBAA+B,EAC/BC,kBAAsC;IAFvC,KAAAH,MAAM,GAANA,MAAM;IAAyB,KAAAC,cAAc,GAAdA,cAAc;IAC5C,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAbtC,KAAAC,YAAY,GAAG,KAAK;IAIO,KAAAC,eAAe,GAAsB,IAAIX,YAAY,EAAE;IAC3D,KAAAY,WAAW,GAAsB,IAAIZ,YAAY,EAAE;IAE1E,KAAAa,YAAY,GAAG,EAAE;IAEjB,KAAAC,QAAQ,GAAE,KAAK;IAKd,IAAI,CAACC,cAAc,GAAG;MACrBC,EAAE,EAAC,EAAE;MACLC,QAAQ,EAAC,EAAE;MACXC,SAAS,EAAC,EAAE;MACZC,WAAW,EAAC;KACZ;EACF;EAEAC,QAAQA,CAAA,GAAI;EACZC,UAAUA,CAACC,KAAK;IACd,MAAMC,QAAQ,GAAaD,KAAK,CAACE,MAAM,CAACC,KAAK;IAC7C,IAAIF,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMC,IAAI,GAASJ,QAAQ,CAAC,CAAC,CAAC;MAC9B,MAAMK,EAAE,GAAa,IAAIC,QAAQ,EAAE;MACnCD,EAAE,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;MACvB,IAAI,CAAClB,kBAAkB,CACpBsB,UAAU,CAACH,EAAE,CAAC,CACdI,SAAS,CAAEC,IAAS,IAAI;QACxB,IAAI,CAACvB,YAAY,GAAG,IAAI;QACxB,IAAI,CAACJ,MAAM,CAAC4B,OAAO,CAAC,4BAA4B,CAAC;QAC9C,IAAI,CAACnB,cAAc,CAAC,UAAU,CAAC,GAAGkB,IAAI,CAAC,UAAU,CAAC;MACtD,CAAC,EAACE,GAAG,IAAE;QACN,IAAI,CAAC7B,MAAM,CAAC8B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;QAChC,IAAI,CAACzB,YAAY,GAAG,KAAK;MAC1B,CAAC,CAAC;IACN;EACF;EAGA2B,WAAWA,CAACpB,QAAgB;IAC1B,IAAIqB,QAAQ,GAAGrB,QAAQ,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,IAAI,CAAC9B,kBAAkB,CAAC+B,cAAc,CAACvB,QAAQ,CAAC,CAACe,SAAS,CAAES,GAAQ,IAAI;MACpE,IAAIH,QAAQ,IAAI,KAAK,IAAIA,QAAQ,IAAI,KAAK,EAAG;QAC3C,IAAII,SAAS,GAAG,iBAAiB;MACnC,CAAC,MAAM,IAAIJ,QAAQ,IAAI,KAAK,IAAIA,QAAQ,IAAE,MAAM,EAAE;QAChD,IAAII,SAAS,GAAG,yEAAyE;MAC3F,CAAC,MAAM,IAAGJ,QAAQ,IAAI,MAAM,IAAIA,QAAQ,IAAI,KAAK,EAAE;QACjD,IAAII,SAAS,GAAG,mEAAmE;MACrF,CAAC,MAAM,IAAIJ,QAAQ,IAAI,KAAK,IAAIA,QAAQ,IAAI,KAAK,EAAE;QACjD,IAAII,SAAS,GAAG,WAAW;MAC7B,CAAC,MAAM,IAAIJ,QAAQ,IAAI,KAAK,EAAE;QAC5B,IAAII,SAAS,GAAG,WAAW;MAC7B,CAAC,MAAM,IAAIJ,QAAQ,IAAI,KAAK,EAAE;QAC5B,IAAII,SAAS,GAAG,WAAW;MAC7B,CAAC,MAAM,IAAIJ,QAAQ,IAAI,KAAK,EAAE;QAC5B,IAAII,SAAS,GAAG,YAAY;MAC9B,CAAC,MAAM;QACL,IAAIA,SAAS,GAAG,YAAY;MAC9B;MACA,MAAMC,eAAe,GAAG,IAAIC,UAAU,CAAEH,GAAG,CAAE;MAC7C,MAAMd,IAAI,GAAG,IAAIkB,IAAI,CAAE,CAAEF,eAAe,CAAE,EAAE;QAAEG,IAAI,EAAEJ;MAAS,CAAE,CAAE;MACjE,MAAMK,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACvB,IAAI,CAAC;MAC5CqB,MAAM,CAACG,IAAI,CAACJ,GAAG,CAAC;IACnB,CAAC,CAAC;EACL;EAEAK,aAAaA,CAAA;IACV,IAAG,IAAI,CAACrC,cAAc,CAAC,UAAU,CAAC,IAAE,EAAE,EAAC;MACrC,IAAI,CAACT,MAAM,CAAC+C,OAAO,CAAC,4BAA4B,EAAE,UAAU,CAAC;MAC7D,OAAO,KAAK;IACd;IACD,IAAI,CAACtC,cAAc,CAACuC,MAAM,GAAG,IAAI,CAACA,MAAM;IACvC,IAAI,CAACxC,QAAQ,GAAG,IAAI;IACrB,IAAI,CAACL,kBAAkB,CACpB8C,eAAe,CAAC,IAAI,CAACxC,cAAc,CAAC,CACpCiB,SAAS,CAACS,GAAG,IAAG;MAChB,IAAI,CAACnC,MAAM,CAAC4B,OAAO,CAAC,yCAAyC,EAAE,UAAU,CAAC;MACzE,IAAI,CAACnB,cAAc,GAAG;QACpBC,EAAE,EAAC,EAAE;QACLC,QAAQ,EAAC,EAAE;QACXC,SAAS,EAAC,EAAE;QACZC,WAAW,EAAC;OACb;MACD,IAAI,CAACP,WAAW,CAAC4C,IAAI,CAAC;QAACxC,EAAE,EAAE,IAAI,CAACsC;MAAM,CAAC,CAAC;MACxC,IAAI,CAACxC,QAAQ,GAAG,KAAK;IAEzB,CAAC,EAACqB,GAAG,IAAE;MACJ,IAAI,CAAC7B,MAAM,CAAC8B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACzB,YAAY,GAAG,KAAK;MACxB,IAAI,CAACI,QAAQ,GAAG,KAAK;IACxB,CAAC,CAAC;EACL;;;;;;;;;;;;;;;cA9FChB;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cACLD,MAAM;QAAA4D,IAAA,GAAC,iBAAiB;MAAA;;cACxB5D,MAAM;QAAA4D,IAAA,GAAC,aAAa;MAAA;;;;AANVrD,kBAAkB,GAAAsD,UAAA,EAL9B9D,SAAS,CAAC;EACT+D,QAAQ,EAAE,iBAAiB;EAC3BC,QAAA,EAAAC,oBAA0C;;CAE3C,CAAC,C,EACWzD,kBAAkB,CAiG9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}