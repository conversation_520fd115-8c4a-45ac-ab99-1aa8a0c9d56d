{"ast": null, "code": "import { async, TestBed } from '@angular/core/testing';\nimport { BulkStatusComponent } from './bulk-status.component';\ndescribe('BulkStatusComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(async(() => {\n    TestBed.configureTestingModule({\n      declarations: [BulkStatusComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(BulkStatusComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["async", "TestBed", "BulkStatusComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\payments\\v1\\bulk-payments-status\\bulk-payments-status.component.spec.ts"], "sourcesContent": ["import { async, ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { BulkStatusComponent } from './bulk-status.component';\r\n\r\ndescribe('BulkStatusComponent', () => {\r\n  let component: BulkStatusComponent;\r\n  let fixture: ComponentFixture<BulkStatusComponent>;\r\n\r\n  beforeEach(async(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ BulkStatusComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(BulkStatusComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAASA,KAAK,EAAoBC,OAAO,QAAQ,uBAAuB;AAExE,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,SAA8B;EAClC,IAAIC,OAA8C;EAElDC,UAAU,CAACN,KAAK,CAAC,MAAK;IACpBC,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,mBAAmB;KACpC,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGJ,OAAO,CAACS,eAAe,CAACR,mBAAmB,CAAC;IACtDE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}