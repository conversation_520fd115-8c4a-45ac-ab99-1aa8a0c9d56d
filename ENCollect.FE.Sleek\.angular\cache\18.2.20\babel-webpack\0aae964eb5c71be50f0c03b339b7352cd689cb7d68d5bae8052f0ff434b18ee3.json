{"ast": null, "code": "import { async, TestBed } from '@angular/core/testing';\nimport { BulkTrailComponent } from './bulk-trail.component';\ndescribe('BulkTrailComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(async(() => {\n    TestBed.configureTestingModule({\n      declarations: [BulkTrailComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(BulkTrailComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["async", "TestBed", "BulkTrailComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\payments\\v1\\bulk-payments\\bulk-payments.component.spec.ts"], "sourcesContent": ["import { async, ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { BulkTrailComponent } from './bulk-trail.component';\r\n\r\ndescribe('BulkTrailComponent', () => {\r\n  let component: BulkTrailComponent;\r\n  let fixture: ComponentFixture<BulkTrailComponent>;\r\n\r\n  beforeEach(async(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ BulkTrailComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(BulkTrailComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAASA,KAAK,EAAoBC,OAAO,QAAQ,uBAAuB;AAExE,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3DC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,SAA6B;EACjC,IAAIC,OAA6C;EAEjDC,UAAU,CAACN,KAAK,CAAC,MAAK;IACpBC,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,kBAAkB;KACnC,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGJ,OAAO,CAACS,eAAe,CAACR,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}