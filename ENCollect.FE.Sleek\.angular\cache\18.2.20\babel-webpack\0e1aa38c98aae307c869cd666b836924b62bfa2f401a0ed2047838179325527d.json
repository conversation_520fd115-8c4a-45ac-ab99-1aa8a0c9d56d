{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\ndescribe('ViewLinkedAccountsComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [SendPaymentLinkComponent]\n    });\n    fixture = TestBed.createComponent(SendPaymentLinkComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "SendPaymentLinkComponent", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\view-linked-accounts\\view-linked-accounts.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { ViewLinkedAccountsComponent } from './view-linked-accounts.component';\r\n\r\ndescribe('ViewLinkedAccountsComponent', () => {\r\n  let component: ViewLinkedAccountsComponent;\r\n  let fixture: ComponentFixture<SendPaymentLinkComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [SendPaymentLinkComponent]\r\n    });\r\n    fixture = TestBed.createComponent(SendPaymentLinkComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAIjEC,QAAQ,CAAC,6BAA6B,EAAE,MAAK;EAC3C,IAAIC,SAAsC;EAC1C,IAAIC,OAAmD;EAEvDC,UAAU,CAAC,MAAK;IACdJ,OAAO,CAACK,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACC,wBAAwB;KACxC,CAAC;IACFJ,OAAO,GAAGH,OAAO,CAACQ,eAAe,CAACD,wBAAwB,CAAC;IAC3DL,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}