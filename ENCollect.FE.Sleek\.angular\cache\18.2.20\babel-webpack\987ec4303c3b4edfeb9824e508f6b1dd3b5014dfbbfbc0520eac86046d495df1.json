{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { InstructionStepsComponent } from './instruction-steps.component';\ndescribe('InstructionStepsComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [InstructionStepsComponent]\n    });\n    fixture = TestBed.createComponent(InstructionStepsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "InstructionStepsComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\instruction-steps\\instruction-steps.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { InstructionStepsComponent } from './instruction-steps.component';\r\n\r\ndescribe('InstructionStepsComponent', () => {\r\n  let component: InstructionStepsComponent;\r\n  let fixture: ComponentFixture<InstructionStepsComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [InstructionStepsComponent]\r\n    });\r\n    fixture = TestBed.createComponent(InstructionStepsComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,yBAAyB,QAAQ,+BAA+B;AAEzEC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EAExDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,yBAAyB;KACzC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,yBAAyB,CAAC;IAC5DE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}