{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./account-detail-label-config.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./account-detail-label-config.component.css?ngResource\";\nimport { Component } from \"@angular/core\";\nimport { SettingsService } from \"../settings.service\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { Router } from \"@angular/router\";\nimport { FormsModule } from \"@angular/forms\";\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\nlet AccountDetailLabelConfigComponent = class AccountDetailLabelConfigComponent {\n  constructor(settingsService, toastr, router) {\n    this.settingsService = settingsService;\n    this.toastr = toastr;\n    this.router = router;\n    this.breadcrumbData = [{\n      label: \"Settings\",\n      path: \"/settings/account-detail-label-customization\"\n    }, {\n      label: \"Account Details Labels Customization\",\n      path: \"/settings/account-detail-label-customization\"\n    }];\n    this.searchCtrl = \"\";\n    this.accountLabels = Array.from({\n      length: 30\n    }, (_, i) => ({\n      id: i,\n      name: `Label Name ${i}`,\n      field: `Field Name ${i}`,\n      selected: false\n    }));\n    this.filteredAccountLabels = [];\n    this.isAllSelected = false;\n  }\n  ngOnInit() {\n    this.loadAccountLabels();\n  }\n  loadAccountLabels() {\n    this.settingsService.getAccountDetailsLabelConfig().subscribe(res => {\n      this.accountLabels = res || [];\n      this.filteredAccountLabels = res || [];\n    }, err => {\n      this.accountLabels = [];\n    });\n  }\n  onSearch() {\n    this.filteredAccountLabels = this.accountLabels.filter(a => {\n      if (!this.searchCtrl) return true;\n      return a.label.toLowerCase().includes(this.searchCtrl.toLowerCase());\n    });\n    this.onSelectItem();\n  }\n  onSelectAll() {\n    for (const item of this.filteredAccountLabels) {\n      item.selected = !!this.isAllSelected;\n    }\n  }\n  onSelectItem() {\n    this.isAllSelected = this.filteredAccountLabels.every(i => i.selected);\n  }\n  updateAccountLabels() {\n    const data = this.accountLabels.filter(item => item.selected).map(item => ({\n      ...item,\n      selected: undefined\n    }));\n    this.settingsService.updateAccountDetailsLabelConfig({\n      labels: data\n    }).subscribe(res => {\n      this.toastr.success(\"Successfully Updated.\");\n    }, err => {\n      this.toastr.error(\"Something went wrong, Please try after some time.\");\n    });\n  }\n  cancel() {\n    this.router.navigate([\"/home\"]);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: SettingsService\n    }, {\n      type: ToastrService\n    }, {\n      type: Router\n    }];\n  }\n};\nAccountDetailLabelConfigComponent = __decorate([Component({\n  selector: \"app-account-detail-label-config\",\n  standalone: true,\n  imports: [FormsModule, BreadcrumbComponent],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AccountDetailLabelConfigComponent);\nexport { AccountDetailLabelConfigComponent };", "map": {"version": 3, "names": ["Component", "SettingsService", "ToastrService", "Router", "FormsModule", "BreadcrumbComponent", "AccountDetailLabelConfigComponent", "constructor", "settingsService", "toastr", "router", "breadcrumbData", "label", "path", "searchCtrl", "accountLabels", "Array", "from", "length", "_", "i", "id", "name", "field", "selected", "filteredAccountLabels", "isAllSelected", "ngOnInit", "loadAccountLabels", "getAccountDetailsLabelConfig", "subscribe", "res", "err", "onSearch", "filter", "a", "toLowerCase", "includes", "onSelectItem", "onSelectAll", "item", "every", "updateAccountLabels", "data", "map", "undefined", "updateAccountDetailsLabelConfig", "labels", "success", "error", "cancel", "navigate", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\account-detail-label-config\\account-detail-label-config.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\";\r\nimport { SettingsService } from \"../settings.service\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { Router } from \"@angular/router\";\r\nimport { FormsModule } from \"@angular/forms\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\n\r\n@Component({\r\n  selector: \"app-account-detail-label-config\",\r\n  standalone: true,\r\n  imports: [FormsModule, BreadcrumbComponent],\r\n  templateUrl: \"./account-detail-label-config.component.html\",\r\n  styleUrls: [\"./account-detail-label-config.component.css\"],\r\n})\r\nexport class AccountDetailLabelConfigComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Settings\", path: \"/settings/account-detail-label-customization\" },\r\n\t\t{ label: \"Account Details Labels Customization\", path: \"/settings/account-detail-label-customization\" },\r\n\t  ]\r\n  public searchCtrl = \"\";\r\n  public accountLabels: any[] = Array.from({ length: 30 }, (_, i) => ({\r\n    id: i,\r\n    name: `Label Name ${i}`,\r\n    field: `Field Name ${i}`,\r\n    selected: false,\r\n  }));\r\n  public filteredAccountLabels: any[] = [];\r\n  public isAllSelected: boolean = false;\r\n\r\n  constructor(\r\n    private settingsService: SettingsService,\r\n    private toastr: ToastrService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadAccountLabels();\r\n  }\r\n\r\n  loadAccountLabels() {\r\n    this.settingsService.getAccountDetailsLabelConfig().subscribe(\r\n      (res: any) => {\r\n        this.accountLabels = res || [];\r\n        this.filteredAccountLabels = res || [];\r\n      },\r\n      (err: any) => {\r\n        this.accountLabels = [];\r\n      }\r\n    );\r\n  }\r\n\r\n  onSearch() {\r\n    this.filteredAccountLabels = this.accountLabels.filter((a) => {\r\n      if (!this.searchCtrl) return true;\r\n      return a.label.toLowerCase().includes(this.searchCtrl.toLowerCase());\r\n    });\r\n    this.onSelectItem();\r\n  }\r\n\r\n  onSelectAll() {\r\n    for (const item of this.filteredAccountLabels) {\r\n      item.selected = !!this.isAllSelected;\r\n    }\r\n  }\r\n\r\n  onSelectItem() {\r\n    this.isAllSelected = this.filteredAccountLabels.every((i) => i.selected);\r\n  }\r\n\r\n  updateAccountLabels() {\r\n    const data = this.accountLabels\r\n      .filter((item: any) => item.selected)\r\n      .map((item: any) => ({ ...item, selected: undefined }));\r\n    this.settingsService.updateAccountDetailsLabelConfig({labels: data}).subscribe(\r\n      (res: any) => {\r\n        this.toastr.success(\"Successfully Updated.\");\r\n      },\r\n      (err: any) => {\r\n        this.toastr.error(\"Something went wrong, Please try after some time.\");\r\n      }\r\n    );\r\n  }\r\n\r\n  cancel() {\r\n    this.router.navigate([\"/home\"]);\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,QAAQ,2DAA2D;AASxF,IAAMC,iCAAiC,GAAvC,MAAMA,iCAAiC;EAe5CC,YACUC,eAAgC,EAChCC,MAAqB,EACrBC,MAAc;IAFd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IAjBT,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAA8C,CAAE,EAC3E;MAAED,KAAK,EAAE,sCAAsC;MAAEC,IAAI,EAAE;IAA8C,CAAE,CACrG;IACK,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,aAAa,GAAUC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAClEC,EAAE,EAAED,CAAC;MACLE,IAAI,EAAE,cAAcF,CAAC,EAAE;MACvBG,KAAK,EAAE,cAAcH,CAAC,EAAE;MACxBI,QAAQ,EAAE;KACX,CAAC,CAAC;IACI,KAAAC,qBAAqB,GAAU,EAAE;IACjC,KAAAC,aAAa,GAAY,KAAK;EAMlC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACpB,eAAe,CAACqB,4BAA4B,EAAE,CAACC,SAAS,CAC1DC,GAAQ,IAAI;MACX,IAAI,CAAChB,aAAa,GAAGgB,GAAG,IAAI,EAAE;MAC9B,IAAI,CAACN,qBAAqB,GAAGM,GAAG,IAAI,EAAE;IACxC,CAAC,EACAC,GAAQ,IAAI;MACX,IAAI,CAACjB,aAAa,GAAG,EAAE;IACzB,CAAC,CACF;EACH;EAEAkB,QAAQA,CAAA;IACN,IAAI,CAACR,qBAAqB,GAAG,IAAI,CAACV,aAAa,CAACmB,MAAM,CAAEC,CAAC,IAAI;MAC3D,IAAI,CAAC,IAAI,CAACrB,UAAU,EAAE,OAAO,IAAI;MACjC,OAAOqB,CAAC,CAACvB,KAAK,CAACwB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACvB,UAAU,CAACsB,WAAW,EAAE,CAAC;IACtE,CAAC,CAAC;IACF,IAAI,CAACE,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,KAAK,MAAMC,IAAI,IAAI,IAAI,CAACf,qBAAqB,EAAE;MAC7Ce,IAAI,CAAChB,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACE,aAAa;IACtC;EACF;EAEAY,YAAYA,CAAA;IACV,IAAI,CAACZ,aAAa,GAAG,IAAI,CAACD,qBAAqB,CAACgB,KAAK,CAAErB,CAAC,IAAKA,CAAC,CAACI,QAAQ,CAAC;EAC1E;EAEAkB,mBAAmBA,CAAA;IACjB,MAAMC,IAAI,GAAG,IAAI,CAAC5B,aAAa,CAC5BmB,MAAM,CAAEM,IAAS,IAAKA,IAAI,CAAChB,QAAQ,CAAC,CACpCoB,GAAG,CAAEJ,IAAS,KAAM;MAAE,GAAGA,IAAI;MAAEhB,QAAQ,EAAEqB;IAAS,CAAE,CAAC,CAAC;IACzD,IAAI,CAACrC,eAAe,CAACsC,+BAA+B,CAAC;MAACC,MAAM,EAAEJ;IAAI,CAAC,CAAC,CAACb,SAAS,CAC3EC,GAAQ,IAAI;MACX,IAAI,CAACtB,MAAM,CAACuC,OAAO,CAAC,uBAAuB,CAAC;IAC9C,CAAC,EACAhB,GAAQ,IAAI;MACX,IAAI,CAACvB,MAAM,CAACwC,KAAK,CAAC,mDAAmD,CAAC;IACxE,CAAC,CACF;EACH;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;;;;;;;;;AAvEW7C,iCAAiC,GAAA8C,UAAA,EAP7CpD,SAAS,CAAC;EACTqD,QAAQ,EAAE,iCAAiC;EAC3CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACnD,WAAW,EAAEC,mBAAmB,CAAC;EAC3CmD,QAAA,EAAAC,oBAA2D;;CAE5D,CAAC,C,EACWnD,iCAAiC,CAwE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}