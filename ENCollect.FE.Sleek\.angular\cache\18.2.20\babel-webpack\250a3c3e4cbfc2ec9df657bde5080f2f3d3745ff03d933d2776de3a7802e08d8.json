{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./upload-budgeted-target.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./upload-budgeted-target.component.css?ngResource\";\nexport class UploadControls {\n  constructor() {\n    this.template = \"staff\";\n    this.allocationType = \"staff\";\n  }\n}\nimport { Component, ViewChild } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { Router } from '@angular/router';\nimport { TargetService } from '../target.service';\nlet UploadBudgetedTargetComponent = class UploadBudgetedTargetComponent {\n  constructor(targetService, toastr, modalService, router) {\n    this.targetService = targetService;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.router = router;\n    this.breadcrumbData = [{\n      label: \"Target\",\n      path: \"/target/upload-budgeted-target\"\n    }, {\n      label: \"Upload-Targets\",\n      path: \"/target/upload-budgeted-target\"\n    }];\n    this.uploadControls = new UploadControls();\n    this.uploadTemp = false;\n    this.loader = {\n      donwloadAcc: false\n    };\n  }\n  fileUploadConfirmation(event, confirmation) {\n    this.fileList = event.target.files;\n    var file_extension = this.fileList[0].name.split('.').pop();\n    this.uploadControls.fileName = this.fileList[0].name;\n    if (file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension != \"XLS\") {\n      this.fileList = [];\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.info('You can only upload the file with extension xls or xlsx');\n      return;\n    } else {\n      this.uploadTemp = true;\n      let config = {\n        ignoreBackdropClick: true\n      };\n      this.modalRef = this.modalService.show(confirmation, config);\n    }\n  }\n  fIleUpload() {\n    this.modalRef?.hide();\n    const file = this.fileList[0];\n    const fd = new FormData();\n    fd.append('file', file);\n    this.targetService.uploadFile(fd).subscribe(data => {\n      this.targetUpload(data);\n    }, err => {\n      this.fileUploader.nativeElement.value = null;\n      this.toastr.error(err, \"Error!\");\n      this.uploadTemp = false;\n    });\n  }\n  targetUpload(data) {\n    let inputParams = {\n      \"FileName\": data[\"name\"],\n      \"FileType\": \"BudgetTarget\"\n    };\n    this.targetService.budgetTargetUpload(inputParams).subscribe(data => {\n      this.fileUploader.nativeElement.value = null;\n      setTimeout(() => {\n        this.uploadControls = new UploadControls();\n        this.toastr.success(\"File Uploaded Successfully. Transaction ID : \" + data.transactionId);\n        this.uploadTemp = false;\n        this.router.navigateByUrl('/', {\n          skipLocationChange: true\n        }).then(() => this.router.navigate(['target/upload-budgeted-target']));\n      }, 1000);\n    }, err => {\n      this.fileUploader.nativeElement.value = null;\n      this.uploadTemp = false;\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  cancel() {\n    this.uploadTemp = false;\n    this.modalRef?.hide();\n    this.fileUploader.nativeElement.value = null;\n    this.fileList = [];\n    this.uploadControls = new UploadControls();\n  }\n  downloadTemplate() {\n    this.dataToCsv = [];\n    this.results = [];\n    this.loader.donwloadAcc = true;\n    this.targetService.downloadAllocatedAcc().subscribe(data => {\n      this.loader.donwloadAcc = false;\n      this.results = data;\n      if (this.results.length == 0) {\n        this.toastr.info('No Results');\n      } else {\n        this.results.forEach(ele => {\n          this.dataToCsv.push({\n            accountNumber: ele[\"accountNumber\"],\n            product: ele[\"product\"],\n            subProduct: ele[\"subProduct\"],\n            currentDPD: ele[\"currentDPD\"],\n            bucket: ele[\"bucket\"],\n            zone: ele[\"zone\"],\n            region: ele[\"region\"],\n            pos: ele[\"pos\"],\n            arrear: ele[\"arrear\"],\n            allocationOwnerName: ele[\"allocationOwnerName\"],\n            tCallingAgencyName: ele[\"tCallingAgencyName\"],\n            agencyName: ele[\"agencyName\"]\n          });\n        });\n        var csvData = this.targetService.convertToCSV(this.dataToCsv);\n        var a = document.createElement(\"a\");\n        a.setAttribute('style', 'display:none;');\n        document.body.appendChild(a);\n        var blob = new Blob([csvData], {\n          type: 'text/csv'\n        });\n        var url = window.URL.createObjectURL(blob);\n        a.href = url;\n        a.download = 'AllocatedAccounts.csv';\n        a.click();\n      }\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.loader.donwloadAcc = false;\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: TargetService\n    }, {\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: Router\n    }];\n  }\n  static {\n    this.propDecorators = {\n      fileUploader: [{\n        type: ViewChild,\n        args: ['fileUploader']\n      }]\n    };\n  }\n};\nUploadBudgetedTargetComponent = __decorate([Component({\n  selector: 'upload-budgeted-target',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], UploadBudgetedTargetComponent);\nexport { UploadBudgetedTargetComponent };", "map": {"version": 3, "names": ["UploadControls", "constructor", "template", "allocationType", "Component", "ViewChild", "ToastrService", "BsModalService", "Router", "TargetService", "UploadBudgetedTargetComponent", "targetService", "toastr", "modalService", "router", "breadcrumbData", "label", "path", "uploadControls", "uploadTemp", "loader", "donwloadAcc", "fileUploadConfirmation", "event", "confirmation", "fileList", "target", "files", "file_extension", "name", "split", "pop", "fileName", "fileUploader", "nativeElement", "value", "info", "config", "ignoreBackdropClick", "modalRef", "show", "fIleUpload", "hide", "file", "fd", "FormData", "append", "uploadFile", "subscribe", "data", "targetUpload", "err", "error", "inputParams", "budgetTargetUpload", "setTimeout", "success", "transactionId", "navigateByUrl", "skipLocationChange", "then", "navigate", "cancel", "downloadTemplate", "dataToCsv", "results", "downloadAllocatedAcc", "length", "for<PERSON>ach", "ele", "push", "accountNumber", "product", "subProduct", "currentDPD", "bucket", "zone", "region", "pos", "arrear", "allocationOwnerName", "tCallingAgencyName", "agencyName", "csvData", "convertToCSV", "a", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "href", "download", "click", "args", "__decorate", "selector", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\target\\upload-budgeted-target\\upload-budgeted-target.component.ts"], "sourcesContent": ["export class UploadControls{\r\n  template: string = \"staff\";\r\n  allocationType: any = \"staff\";\r\n  fileName: string;\r\n}\r\n\r\nimport { Component, ElementRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\nimport { Router } from '@angular/router';\r\nimport { TargetService } from '../target.service'\r\n\r\n@Component({\r\n  selector: 'upload-budgeted-target',\r\n  templateUrl: './upload-budgeted-target.component.html',\r\n  styleUrls: ['./upload-budgeted-target.component.css']\r\n})\r\nexport class UploadBudgetedTargetComponent {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Target\", path: \"/target/upload-budgeted-target\" },\r\n\t\t{ label: \"Upload-Targets\", path: \"/target/upload-budgeted-target\" },\r\n\t  ]\r\n  @ViewChild('fileUploader') fileUploader:ElementRef;\r\n  FileList: any;\r\n  fileName: any;\r\n  uploadControls: UploadControls = new UploadControls();\r\n  modalRef: BsModalRef;\r\n  fileList:any;\r\n  uploadTemp:boolean=false;\r\n  loader= {\r\n    donwloadAcc: false\r\n  }\r\n\r\n\r\n  constructor(public targetService:TargetService,\r\n              public toastr: ToastrService,\r\n              private modalService: BsModalService,\r\n              private router: Router) { }\r\n\r\n\r\n    fileUploadConfirmation(event,confirmation: TemplateRef<any>) {\r\n       this.fileList = event.target.files;\r\n       var file_extension =  this.fileList[0].name.split('.').pop();\r\n       this.uploadControls.fileName = this.fileList[0].name\r\n       if(file_extension != \"XLSX\" && file_extension != \"xlsx\" && file_extension != \"xls\" && file_extension !=\"XLS\" ){\r\n         this.fileList=[]\r\n        this.fileUploader.nativeElement.value = null;\r\n        this.toastr.info('You can only upload the file with extension xls or xlsx');\r\n        return;\r\n       }else {\r\n         this.uploadTemp = true\r\n         let config = {\r\n           ignoreBackdropClick: true,\r\n         };\r\n         this.modalRef = this.modalService.show(confirmation,config);\r\n       }\r\n   }\r\n\r\n   fIleUpload() {\r\n      this.modalRef?.hide()\r\n      const file: File = this.fileList[0];\r\n      const fd: FormData = new FormData();\r\n      fd.append('file', file);\r\n      this.targetService.uploadFile(fd).subscribe(data => {\r\n         this.targetUpload(data);\r\n      }, err=>{\r\n         this.fileUploader.nativeElement.value = null;\r\n         this.toastr.error(err, \"Error!\")\r\n         this.uploadTemp = false;\r\n      });\r\n  }\r\n\r\n  targetUpload(data){\r\n    let inputParams = {\r\n      \"FileName\": data[\"name\"],\r\n      \"FileType\": \"BudgetTarget\"\r\n    }\r\n    this.targetService.budgetTargetUpload(inputParams)\r\n      .subscribe(data => {\r\n        this.fileUploader.nativeElement.value = null;\r\n        setTimeout(()=>{\r\n          this.uploadControls = new UploadControls()\r\n          this.toastr.success(\"File Uploaded Successfully. Transaction ID : \"+ data.transactionId);\r\n          this.uploadTemp = false\r\n            this.router.navigateByUrl('/', {skipLocationChange: true}).then(()=>\r\n            this.router.navigate(['target/upload-budgeted-target']));\r\n        }, 1000);\r\n     },err=>{\r\n       this.fileUploader.nativeElement.value = null;\r\n       this.uploadTemp = false\r\n       this.toastr.error(err, \"Error!\");\r\n     });\r\n   }\r\n\r\n  cancel(){\r\n    this.uploadTemp = false\r\n    this.modalRef?.hide()\r\n    this.fileUploader.nativeElement.value = null;\r\n    this.fileList = []\r\n    this.uploadControls = new UploadControls();\r\n  }\r\n  results: any;\r\n  dataToCsv: any;\r\n  downloadTemplate(){\r\n     this.dataToCsv = []\r\n     this.results = []\r\n     this.loader.donwloadAcc = true\r\n     this.targetService.downloadAllocatedAcc()\r\n      .subscribe(data => {\r\n        this.loader.donwloadAcc = false\r\n        this.results = data;\r\n          if(this.results.length==0){\r\n            this.toastr.info('No Results');\r\n          }else{\r\n              this.results.forEach(ele => {\r\n                this.dataToCsv.push({\r\n                  accountNumber: ele[\"accountNumber\"],\r\n                  product: ele[\"product\"],\r\n                  subProduct: ele[\"subProduct\"],\r\n                  currentDPD: ele[\"currentDPD\"],\r\n                  bucket: ele[\"bucket\"],\r\n                  zone: ele[\"zone\"],\r\n                  region: ele[\"region\"],\r\n                  pos: ele[\"pos\"],\r\n                  arrear: ele[\"arrear\"],\r\n                  allocationOwnerName: ele[\"allocationOwnerName\"],\r\n                  tCallingAgencyName: ele[\"tCallingAgencyName\"],\r\n                  agencyName: ele[\"agencyName\"]\r\n                })\r\n               });\r\n              var csvData = this.targetService.convertToCSV(this.dataToCsv);\r\n              var a = document.createElement(\"a\");\r\n              a.setAttribute('style', 'display:none;');\r\n              document.body.appendChild(a);\r\n              var blob = new Blob([csvData], { type: 'text/csv' });\r\n              var url= window.URL.createObjectURL(blob);\r\n              a.href = url;\r\n              a.download = 'AllocatedAccounts.csv';\r\n              a.click();\r\n         }\r\n\r\n      },err=>{\r\n       this.toastr.error(err, \"Error!\");\r\n       this.loader.donwloadAcc = false\r\n\r\n     });\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;EAA3BC,YAAA;IACE,KAAAC,QAAQ,GAAW,OAAO;IAC1B,KAAAC,cAAc,GAAQ,OAAO;EAE/B;;AAEA,SAASC,SAAS,EAA2BC,SAAS,QAAQ,eAAe;AAC7E,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAoB,qBAAqB;AAChE,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,aAAa,QAAQ,mBAAmB;AAO1C,IAAMC,6BAA6B,GAAnC,MAAMA,6BAA6B;EAiBxCT,YAAmBU,aAA2B,EAC3BC,MAAqB,EACpBC,YAA4B,EAC5BC,MAAc;IAHf,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IAnBnB,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAgC,CAAE,EAC3D;MAAED,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAgC,CAAE,CACjE;IAIF,KAAAC,cAAc,GAAmB,IAAIlB,cAAc,EAAE;IAGrD,KAAAmB,UAAU,GAAS,KAAK;IACxB,KAAAC,MAAM,GAAE;MACNC,WAAW,EAAE;KACd;EAMqC;EAGpCC,sBAAsBA,CAACC,KAAK,EAACC,YAA8B;IACxD,IAAI,CAACC,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK;IAClC,IAAIC,cAAc,GAAI,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAC5D,IAAI,CAACb,cAAc,CAACc,QAAQ,GAAG,IAAI,CAACP,QAAQ,CAAC,CAAC,CAAC,CAACI,IAAI;IACpD,IAAGD,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,MAAM,IAAIA,cAAc,IAAI,KAAK,IAAIA,cAAc,IAAG,KAAK,EAAE;MAC5G,IAAI,CAACH,QAAQ,GAAC,EAAE;MACjB,IAAI,CAACQ,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAACvB,MAAM,CAACwB,IAAI,CAAC,yDAAyD,CAAC;MAC3E;IACD,CAAC,MAAK;MACJ,IAAI,CAACjB,UAAU,GAAG,IAAI;MACtB,IAAIkB,MAAM,GAAG;QACXC,mBAAmB,EAAE;OACtB;MACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC1B,YAAY,CAAC2B,IAAI,CAAChB,YAAY,EAACa,MAAM,CAAC;IAC7D;EACJ;EAEAI,UAAUA,CAAA;IACP,IAAI,CAACF,QAAQ,EAAEG,IAAI,EAAE;IACrB,MAAMC,IAAI,GAAS,IAAI,CAAClB,QAAQ,CAAC,CAAC,CAAC;IACnC,MAAMmB,EAAE,GAAa,IAAIC,QAAQ,EAAE;IACnCD,EAAE,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IACvB,IAAI,CAAChC,aAAa,CAACoC,UAAU,CAACH,EAAE,CAAC,CAACI,SAAS,CAACC,IAAI,IAAG;MAChD,IAAI,CAACC,YAAY,CAACD,IAAI,CAAC;IAC1B,CAAC,EAAEE,GAAG,IAAE;MACL,IAAI,CAAClB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAACvB,MAAM,CAACwC,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAAChC,UAAU,GAAG,KAAK;IAC1B,CAAC,CAAC;EACN;EAEA+B,YAAYA,CAACD,IAAI;IACf,IAAII,WAAW,GAAG;MAChB,UAAU,EAAEJ,IAAI,CAAC,MAAM,CAAC;MACxB,UAAU,EAAE;KACb;IACD,IAAI,CAACtC,aAAa,CAAC2C,kBAAkB,CAACD,WAAW,CAAC,CAC/CL,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAAChB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5CoB,UAAU,CAAC,MAAI;QACb,IAAI,CAACrC,cAAc,GAAG,IAAIlB,cAAc,EAAE;QAC1C,IAAI,CAACY,MAAM,CAAC4C,OAAO,CAAC,+CAA+C,GAAEP,IAAI,CAACQ,aAAa,CAAC;QACxF,IAAI,CAACtC,UAAU,GAAG,KAAK;QACrB,IAAI,CAACL,MAAM,CAAC4C,aAAa,CAAC,GAAG,EAAE;UAACC,kBAAkB,EAAE;QAAI,CAAC,CAAC,CAACC,IAAI,CAAC,MAChE,IAAI,CAAC9C,MAAM,CAAC+C,QAAQ,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC;MAC5D,CAAC,EAAE,IAAI,CAAC;IACX,CAAC,EAACV,GAAG,IAAE;MACL,IAAI,CAAClB,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC5C,IAAI,CAAChB,UAAU,GAAG,KAAK;MACvB,IAAI,CAACP,MAAM,CAACwC,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IAClC,CAAC,CAAC;EACJ;EAEDW,MAAMA,CAAA;IACJ,IAAI,CAAC3C,UAAU,GAAG,KAAK;IACvB,IAAI,CAACoB,QAAQ,EAAEG,IAAI,EAAE;IACrB,IAAI,CAACT,YAAY,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;IAC5C,IAAI,CAACV,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACP,cAAc,GAAG,IAAIlB,cAAc,EAAE;EAC5C;EAGA+D,gBAAgBA,CAAA;IACb,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC7C,MAAM,CAACC,WAAW,GAAG,IAAI;IAC9B,IAAI,CAACV,aAAa,CAACuD,oBAAoB,EAAE,CACvClB,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAAC7B,MAAM,CAACC,WAAW,GAAG,KAAK;MAC/B,IAAI,CAAC4C,OAAO,GAAGhB,IAAI;MACjB,IAAG,IAAI,CAACgB,OAAO,CAACE,MAAM,IAAE,CAAC,EAAC;QACxB,IAAI,CAACvD,MAAM,CAACwB,IAAI,CAAC,YAAY,CAAC;MAChC,CAAC,MAAI;QACD,IAAI,CAAC6B,OAAO,CAACG,OAAO,CAACC,GAAG,IAAG;UACzB,IAAI,CAACL,SAAS,CAACM,IAAI,CAAC;YAClBC,aAAa,EAAEF,GAAG,CAAC,eAAe,CAAC;YACnCG,OAAO,EAAEH,GAAG,CAAC,SAAS,CAAC;YACvBI,UAAU,EAAEJ,GAAG,CAAC,YAAY,CAAC;YAC7BK,UAAU,EAAEL,GAAG,CAAC,YAAY,CAAC;YAC7BM,MAAM,EAAEN,GAAG,CAAC,QAAQ,CAAC;YACrBO,IAAI,EAAEP,GAAG,CAAC,MAAM,CAAC;YACjBQ,MAAM,EAAER,GAAG,CAAC,QAAQ,CAAC;YACrBS,GAAG,EAAET,GAAG,CAAC,KAAK,CAAC;YACfU,MAAM,EAAEV,GAAG,CAAC,QAAQ,CAAC;YACrBW,mBAAmB,EAAEX,GAAG,CAAC,qBAAqB,CAAC;YAC/CY,kBAAkB,EAAEZ,GAAG,CAAC,oBAAoB,CAAC;YAC7Ca,UAAU,EAAEb,GAAG,CAAC,YAAY;WAC7B,CAAC;QACH,CAAC,CAAC;QACH,IAAIc,OAAO,GAAG,IAAI,CAACxE,aAAa,CAACyE,YAAY,CAAC,IAAI,CAACpB,SAAS,CAAC;QAC7D,IAAIqB,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACnCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACxCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,CAAC,CAAC;QAC5B,IAAIM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACT,OAAO,CAAC,EAAE;UAAEU,IAAI,EAAE;QAAU,CAAE,CAAC;QACpD,IAAIC,GAAG,GAAEC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QACzCN,CAAC,CAACa,IAAI,GAAGJ,GAAG;QACZT,CAAC,CAACc,QAAQ,GAAG,uBAAuB;QACpCd,CAAC,CAACe,KAAK,EAAE;MACd;IAEH,CAAC,EAACjD,GAAG,IAAE;MACN,IAAI,CAACvC,MAAM,CAACwC,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAAC/B,MAAM,CAACC,WAAW,GAAG,KAAK;IAEjC,CAAC,CAAC;EACL;;;;;;;;;;;;;;;cA5HChB,SAAS;QAAAgG,IAAA,GAAC,cAAc;MAAA;;;;AALd3F,6BAA6B,GAAA4F,UAAA,EALzClG,SAAS,CAAC;EACTmG,QAAQ,EAAE,wBAAwB;EAClCrG,QAAA,EAAAsG,oBAAsD;;CAEvD,CAAC,C,EACW9F,6BAA6B,CAmIzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}