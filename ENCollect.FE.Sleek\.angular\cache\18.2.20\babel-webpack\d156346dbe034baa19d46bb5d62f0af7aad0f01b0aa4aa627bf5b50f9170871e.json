{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./request-cure.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./request-cure.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { ToastrService } from 'ngx-toastr';\nimport { ClicktoCureConfigService } from \"../clicktocureconfig.service\";\nimport { ClicktoCureService } from \"../clicktocure.service\";\nlet RequestCureComponent = class RequestCureComponent {\n  constructor(router, toastr, clicktoCureService, clicktoCureConfigService) {\n    this.router = router;\n    this.toastr = toastr;\n    this.clicktoCureService = clicktoCureService;\n    this.clicktoCureConfigService = clicktoCureConfigService;\n    this.breadcrumbData = [{\n      label: \"Curing Tools\",\n      path: \"/encollect/cure/request-cure\"\n    }, {\n      label: \"Request for Cure\",\n      path: \"/encollect/cure/request-cure\"\n    }];\n    this.isSubmit = false;\n    this.isSearching = false;\n    this.cureInformation = {\n      \"accountid\": \"\",\n      \"accountno\": \"\",\n      \"mobileno\": \"\",\n      \"emailid\": \"\",\n      \"OutStandingPrincipalAmount\": \"\",\n      \"OutstandingInterestAmount\": \"\",\n      \"LoanAmount\": \"\",\n      \"CurrentInterestRate\": \"\",\n      \"BalanceTenure\": \"\",\n      \"PendingEMIInstallements\": \"\",\n      \"MoratoriumPeriod\": \"\",\n      \"ChangeEMI\": \"\",\n      \"ChangeTenure\": \"\",\n      \"ChangeInterestRate\": \"\",\n      \"PrepaymentAmount\": \"\",\n      \"CureOption\": \"\"\n    };\n  }\n  ngOnInit() {}\n  getAccount() {\n    this.isSearching = true;\n    if (this.cureInformation[\"accountno\"] == '') {\n      return false;\n    }\n    let accountInfo = {\n      \"accountno\": this.cureInformation[\"accountno\"]\n    };\n    this.clicktoCureService.getAccountDetails(accountInfo).subscribe(response => {\n      this.isSearching = false;\n      this.cureInformation[\"accountid\"] = response[\"id\"];\n      this.cureInformation[\"accountno\"] = response[\"accountno\"];\n      this.cureInformation[\"customerId\"] = response[\"customerId\"];\n      this.cureInformation[\"applicantName\"] = response[\"customerName\"];\n      this.cureInformation[\"mobileno\"] = response[\"mobilenumber\"];\n      this.cureInformation[\"OutStandingPrincipalAmount\"] = response[\"outStandingPrincipalAmount\"];\n      this.cureInformation[\"OutstandingInterestAmount\"] = response[\"outstandingInterestAmount\"];\n      this.cureInformation[\"LoanAmount\"] = response[\"loanAmount\"];\n      this.cureInformation[\"CurrentInterestRate\"] = response[\"currentInterestRate\"];\n      this.cureInformation[\"BalanceTenure\"] = response[\"balanceTenure\"];\n      this.cureInformation[\"PendingEMIInstallements\"] = response[\"pendingEMIInstallements\"];\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.isSearching = false;\n    });\n  }\n  submit() {\n    this.isSubmit = true;\n    this.clicktoCureService.saveCure(this.cureInformation).subscribe(response => {\n      this.isSubmit = false;\n      this.toastr.success(\"Cure request created and  request id \" + Number(response));\n      this.router.navigateByUrl('/encollect/cure/my-cure');\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.isSubmit = false;\n    });\n  }\n  applicantWish() {\n    // console.log(this.cureInformation.CureOption)\n    if (this.cureInformation.CureOption == \"PaymentHoliday\") {\n      this.cureInformation.ChangeEMI = \"\";\n      this.cureInformation.ChangeTenure = \"\";\n      this.cureInformation.ChangeInterestRate = \"\";\n      this.cureInformation.PrepaymentAmount = \"\";\n      this.cureInformation.ChangeInterestRate = \"\";\n    } else if (this.cureInformation.CureOption == \"RevisedEMI\") {\n      this.cureInformation.ChangeInterestRate = \"\";\n      this.cureInformation.PrepaymentAmount = \"\";\n      this.cureInformation.MoratoriumPeriod = \"\";\n      this.cureInformation.ChangeInterestRate = \"\";\n    } else if (this.cureInformation.CureOption == \"Decreasemyinterestrale\") {\n      this.cureInformation.ChangeEMI = \"\";\n      this.cureInformation.ChangeTenure = \"\";\n      this.cureInformation.PrepaymentAmount = \"\";\n      this.cureInformation.MoratoriumPeriod = \"\";\n      this.cureInformation.ChangeInterestRate = \"\";\n    } else if (this.cureInformation.CureOption == \"PrePartPayment\") {\n      this.cureInformation.ChangeEMI = \"\";\n      this.cureInformation.ChangeTenure = \"\";\n      this.cureInformation.ChangeInterestRate = \"\";\n      this.cureInformation.MoratoriumPeriod = \"\";\n      this.cureInformation.ChangeInterestRate = \"\";\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: Router\n    }, {\n      type: ToastrService\n    }, {\n      type: ClicktoCureService\n    }, {\n      type: ClicktoCureConfigService\n    }];\n  }\n};\nRequestCureComponent = __decorate([Component({\n  selector: 'cure-request-cure',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], RequestCureComponent);\nexport { RequestCureComponent };", "map": {"version": 3, "names": ["Component", "Router", "ToastrService", "ClicktoCureConfigService", "ClicktoCureService", "RequestCureComponent", "constructor", "router", "toastr", "clicktoCureService", "clicktoCureConfigService", "breadcrumbData", "label", "path", "isSubmit", "isSearching", "cureInformation", "ngOnInit", "getAccount", "accountInfo", "getAccountDetails", "subscribe", "response", "err", "error", "submit", "saveCure", "success", "Number", "navigateByUrl", "applicantWish", "CureOption", "ChangeEMI", "ChangeTenure", "ChangeInterestRate", "PrepaymentAmount", "MoratoriumPeriod", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\click-to-cure\\request-cure\\request-cure.component.ts"], "sourcesContent": ["import { Component, OnInit,ViewChild,Output,TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { ClicktoCureConfigService} from \"../clicktocureconfig.service\"\r\nimport { ClicktoCureService} from \"../clicktocure.service\"\r\n\r\n@Component({\r\n  selector: 'cure-request-cure',\r\n  templateUrl: './request-cure.component.html',\r\n  styleUrls: ['./request-cure.component.css']\r\n})\r\nexport class RequestCureComponent implements OnInit {\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Curing Tools\", path: \"/encollect/cure/request-cure\" },\r\n\t\t{ label: \"Request for Cure\", path: \"/encollect/cure/request-cure\" },\r\n\t  ];\r\n  isSubmit= false;\r\n  isSearching = false;\r\n  cureInformation: any;\r\n  constructor(private router: Router,\r\n  \t\t\t  public toastr: ToastrService,\r\n              private clicktoCureService: ClicktoCureService,\r\n              private clicktoCureConfigService: ClicktoCureConfigService) {\r\n\r\n  \tthis.cureInformation = {\r\n          \"accountid\":\"\",\r\n          \"accountno\":\"\",\r\n          \"mobileno\":\"\",\r\n          \"emailid\":\"\",\r\n          \"OutStandingPrincipalAmount\":\"\",\r\n          \"OutstandingInterestAmount\":\"\",\r\n          \"LoanAmount\":\"\",\r\n          \"CurrentInterestRate\":\"\",\r\n          \"BalanceTenure\":\"\",\r\n          \"PendingEMIInstallements\":\"\",\r\n          \"MoratoriumPeriod\":\"\",\r\n          \"ChangeEMI\":\"\",\r\n          \"ChangeTenure\":\"\",\r\n          \"ChangeInterestRate\":\"\",\r\n          \"PrepaymentAmount\":\"\",\r\n          \"CureOption\":\"\"\r\n  \t}\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n  getAccount(){\r\n     this.isSearching = true\r\n     if(this.cureInformation[\"accountno\"]==''){\r\n       return false\r\n     }\r\n     let accountInfo = {\r\n   \t\t\"accountno\": this.cureInformation[\"accountno\"]\r\n     }\r\n  \t this.clicktoCureService.getAccountDetails(accountInfo).subscribe(response => {\r\n        this.isSearching = false\r\n        this.cureInformation[\"accountid\"]= response[\"id\"]\r\n  \t    this.cureInformation[\"accountno\"]= response[\"accountno\"]\r\n  \t    this.cureInformation[\"customerId\"]= response[\"customerId\"]\r\n  \t    this.cureInformation[\"applicantName\"]= response[\"customerName\"];\r\n        this.cureInformation[\"mobileno\"]= response[\"mobilenumber\"]\r\n        this.cureInformation[\"OutStandingPrincipalAmount\"]= response[\"outStandingPrincipalAmount\"]\r\n        this.cureInformation[\"OutstandingInterestAmount\"]= response[\"outstandingInterestAmount\"]\r\n        this.cureInformation[\"LoanAmount\"]= response[\"loanAmount\"]\r\n        this.cureInformation[\"CurrentInterestRate\"]= response[\"currentInterestRate\"]\r\n        this.cureInformation[\"BalanceTenure\"]= response[\"balanceTenure\"]\r\n        this.cureInformation[\"PendingEMIInstallements\"]= response[\"pendingEMIInstallements\"]\r\n      },err=>{\r\n         this.toastr.error(err, \"Error!\")\r\n         this.isSearching = false\r\n      });\r\n  }\r\n\r\n  submit(){\r\n  \t this.isSubmit = true\r\n  \t this.clicktoCureService.saveCure(this.cureInformation).subscribe(response => {\r\n        this.isSubmit = false\r\n        this.toastr.success(\"Cure request created and  request id \"+ Number(response))\r\n        this.router.navigateByUrl('/encollect/cure/my-cure')\r\n      },err=>{\r\n         this.toastr.error(err, \"Error!\")\r\n         this.isSubmit = false\r\n      });\r\n  }\r\n\r\n  applicantWish(){\r\n    // console.log(this.cureInformation.CureOption)\r\n    if(this.cureInformation.CureOption==\"PaymentHoliday\"){\r\n        this.cureInformation.ChangeEMI=\"\"\r\n        this.cureInformation.ChangeTenure=\"\"\r\n        this.cureInformation.ChangeInterestRate=\"\"\r\n        this.cureInformation.PrepaymentAmount=\"\"\r\n        this.cureInformation.ChangeInterestRate=\"\"\r\n    }else if(this.cureInformation.CureOption==\"RevisedEMI\"){\r\n        this.cureInformation.ChangeInterestRate=\"\"\r\n        this.cureInformation.PrepaymentAmount=\"\"\r\n        this.cureInformation.MoratoriumPeriod=\"\"\r\n        this.cureInformation.ChangeInterestRate=\"\"\r\n    }else if(this.cureInformation.CureOption==\"Decreasemyinterestrale\"){\r\n        this.cureInformation.ChangeEMI=\"\"\r\n        this.cureInformation.ChangeTenure=\"\"\r\n        this.cureInformation.PrepaymentAmount=\"\"\r\n        this.cureInformation.MoratoriumPeriod=\"\"\r\n        this.cureInformation.ChangeInterestRate=\"\"\r\n    }else if(this.cureInformation.CureOption==\"PrePartPayment\"){\r\n        this.cureInformation.ChangeEMI=\"\"\r\n        this.cureInformation.ChangeTenure=\"\"\r\n        this.cureInformation.ChangeInterestRate=\"\"\r\n        this.cureInformation.MoratoriumPeriod=\"\"\r\n        this.cureInformation.ChangeInterestRate=\"\"\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAA6C,eAAe;AAC9E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,wBAAwB,QAAO,8BAA8B;AACtE,SAASC,kBAAkB,QAAO,wBAAwB;AAOnD,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAQ/BC,YAAoBC,MAAc,EACtBC,MAAqB,EACbC,kBAAsC,EACtCC,wBAAkD;IAHlD,KAAAH,MAAM,GAANA,MAAM;IACd,KAAAC,MAAM,GAANA,MAAM;IACE,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAVrC,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE;IAA8B,CAAE,EAC/D;MAAED,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAA8B,CAAE,CACjE;IACF,KAAAC,QAAQ,GAAE,KAAK;IACf,KAAAC,WAAW,GAAG,KAAK;IAOlB,IAAI,CAACC,eAAe,GAAG;MAChB,WAAW,EAAC,EAAE;MACd,WAAW,EAAC,EAAE;MACd,UAAU,EAAC,EAAE;MACb,SAAS,EAAC,EAAE;MACZ,4BAA4B,EAAC,EAAE;MAC/B,2BAA2B,EAAC,EAAE;MAC9B,YAAY,EAAC,EAAE;MACf,qBAAqB,EAAC,EAAE;MACxB,eAAe,EAAC,EAAE;MAClB,yBAAyB,EAAC,EAAE;MAC5B,kBAAkB,EAAC,EAAE;MACrB,WAAW,EAAC,EAAE;MACd,cAAc,EAAC,EAAE;MACjB,oBAAoB,EAAC,EAAE;MACvB,kBAAkB,EAAC,EAAE;MACrB,YAAY,EAAC;KACnB;EAEF;EAEAC,QAAQA,CAAA,GACR;EAEAC,UAAUA,CAAA;IACP,IAAI,CAACH,WAAW,GAAG,IAAI;IACvB,IAAG,IAAI,CAACC,eAAe,CAAC,WAAW,CAAC,IAAE,EAAE,EAAC;MACvC,OAAO,KAAK;IACd;IACA,IAAIG,WAAW,GAAG;MAClB,WAAW,EAAE,IAAI,CAACH,eAAe,CAAC,WAAW;KAC5C;IACF,IAAI,CAACP,kBAAkB,CAACW,iBAAiB,CAACD,WAAW,CAAC,CAACE,SAAS,CAACC,QAAQ,IAAG;MACxE,IAAI,CAACP,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,eAAe,CAAC,WAAW,CAAC,GAAEM,QAAQ,CAAC,IAAI,CAAC;MAClD,IAAI,CAACN,eAAe,CAAC,WAAW,CAAC,GAAEM,QAAQ,CAAC,WAAW,CAAC;MACxD,IAAI,CAACN,eAAe,CAAC,YAAY,CAAC,GAAEM,QAAQ,CAAC,YAAY,CAAC;MAC1D,IAAI,CAACN,eAAe,CAAC,eAAe,CAAC,GAAEM,QAAQ,CAAC,cAAc,CAAC;MAC9D,IAAI,CAACN,eAAe,CAAC,UAAU,CAAC,GAAEM,QAAQ,CAAC,cAAc,CAAC;MAC1D,IAAI,CAACN,eAAe,CAAC,4BAA4B,CAAC,GAAEM,QAAQ,CAAC,4BAA4B,CAAC;MAC1F,IAAI,CAACN,eAAe,CAAC,2BAA2B,CAAC,GAAEM,QAAQ,CAAC,2BAA2B,CAAC;MACxF,IAAI,CAACN,eAAe,CAAC,YAAY,CAAC,GAAEM,QAAQ,CAAC,YAAY,CAAC;MAC1D,IAAI,CAACN,eAAe,CAAC,qBAAqB,CAAC,GAAEM,QAAQ,CAAC,qBAAqB,CAAC;MAC5E,IAAI,CAACN,eAAe,CAAC,eAAe,CAAC,GAAEM,QAAQ,CAAC,eAAe,CAAC;MAChE,IAAI,CAACN,eAAe,CAAC,yBAAyB,CAAC,GAAEM,QAAQ,CAAC,yBAAyB,CAAC;IACtF,CAAC,EAACC,GAAG,IAAE;MACJ,IAAI,CAACf,MAAM,CAACgB,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACR,WAAW,GAAG,KAAK;IAC3B,CAAC,CAAC;EACN;EAEAU,MAAMA,CAAA;IACJ,IAAI,CAACX,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACL,kBAAkB,CAACiB,QAAQ,CAAC,IAAI,CAACV,eAAe,CAAC,CAACK,SAAS,CAACC,QAAQ,IAAG;MACxE,IAAI,CAACR,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACN,MAAM,CAACmB,OAAO,CAAC,uCAAuC,GAAEC,MAAM,CAACN,QAAQ,CAAC,CAAC;MAC9E,IAAI,CAACf,MAAM,CAACsB,aAAa,CAAC,yBAAyB,CAAC;IACtD,CAAC,EAACN,GAAG,IAAE;MACJ,IAAI,CAACf,MAAM,CAACgB,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACT,QAAQ,GAAG,KAAK;IACxB,CAAC,CAAC;EACN;EAEAgB,aAAaA,CAAA;IACX;IACA,IAAG,IAAI,CAACd,eAAe,CAACe,UAAU,IAAE,gBAAgB,EAAC;MACjD,IAAI,CAACf,eAAe,CAACgB,SAAS,GAAC,EAAE;MACjC,IAAI,CAAChB,eAAe,CAACiB,YAAY,GAAC,EAAE;MACpC,IAAI,CAACjB,eAAe,CAACkB,kBAAkB,GAAC,EAAE;MAC1C,IAAI,CAAClB,eAAe,CAACmB,gBAAgB,GAAC,EAAE;MACxC,IAAI,CAACnB,eAAe,CAACkB,kBAAkB,GAAC,EAAE;IAC9C,CAAC,MAAK,IAAG,IAAI,CAAClB,eAAe,CAACe,UAAU,IAAE,YAAY,EAAC;MACnD,IAAI,CAACf,eAAe,CAACkB,kBAAkB,GAAC,EAAE;MAC1C,IAAI,CAAClB,eAAe,CAACmB,gBAAgB,GAAC,EAAE;MACxC,IAAI,CAACnB,eAAe,CAACoB,gBAAgB,GAAC,EAAE;MACxC,IAAI,CAACpB,eAAe,CAACkB,kBAAkB,GAAC,EAAE;IAC9C,CAAC,MAAK,IAAG,IAAI,CAAClB,eAAe,CAACe,UAAU,IAAE,wBAAwB,EAAC;MAC/D,IAAI,CAACf,eAAe,CAACgB,SAAS,GAAC,EAAE;MACjC,IAAI,CAAChB,eAAe,CAACiB,YAAY,GAAC,EAAE;MACpC,IAAI,CAACjB,eAAe,CAACmB,gBAAgB,GAAC,EAAE;MACxC,IAAI,CAACnB,eAAe,CAACoB,gBAAgB,GAAC,EAAE;MACxC,IAAI,CAACpB,eAAe,CAACkB,kBAAkB,GAAC,EAAE;IAC9C,CAAC,MAAK,IAAG,IAAI,CAAClB,eAAe,CAACe,UAAU,IAAE,gBAAgB,EAAC;MACvD,IAAI,CAACf,eAAe,CAACgB,SAAS,GAAC,EAAE;MACjC,IAAI,CAAChB,eAAe,CAACiB,YAAY,GAAC,EAAE;MACpC,IAAI,CAACjB,eAAe,CAACkB,kBAAkB,GAAC,EAAE;MAC1C,IAAI,CAAClB,eAAe,CAACoB,gBAAgB,GAAC,EAAE;MACxC,IAAI,CAACpB,eAAe,CAACkB,kBAAkB,GAAC,EAAE;IAC9C;EACF;;;;;;;;;;;;;AAtGW7B,oBAAoB,GAAAgC,UAAA,EALhCrC,SAAS,CAAC;EACTsC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA4C;;CAE7C,CAAC,C,EACWnC,oBAAoB,CAwGhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}