{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./account-search-scope.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./account-search-scope.component.scss?ngResource\";\nimport { Component } from \"@angular/core\";\nimport { SettingsService } from \"../settings.service\";\nimport { ActivatedRoute } from \"@angular/router\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { SideDrawerService } from \"src/app/shared/services/side-drawer.service\";\nimport { InstructionStepsComponent } from \"src/app/shared/components/instruction-steps/instruction-steps.component\";\nlet AccountSearchScopeComponent = class AccountSearchScopeComponent {\n  constructor(settingsService, route, toastr, sideDrawerService) {\n    this.settingsService = settingsService;\n    this.route = route;\n    this.toastr = toastr;\n    this.sideDrawerService = sideDrawerService;\n    this.breadcrumbData = [{\n      label: \"Settings\",\n      path: \"/settings/account-search-scope\"\n    }, {\n      label: \"Account Search Scope\",\n      path: \"/settings/account-search-scope\"\n    }];\n    // This will hold the data from the API\n    this.accountSearchScopes = [];\n  }\n  ngOnInit() {\n    this.getAccountSearchScope();\n  }\n  getAccountSearchScope() {\n    this.settingsService.getAccountSearchScope().subscribe(response => {\n      if (response.length === 0) {\n        this.toastr.info(\"No results found!\", \"Info!\");\n        return false;\n      }\n      this.accountSearchScopes = response;\n      this.accountSearchScopes.forEach(a => {\n        if (a.userType == \"AgencyToFrontEndExternalFOS\") {\n          if (a.scope == \"parent\") {\n            this.allocatedToAgency = 1;\n          } else if (a.scope == \"self\") {\n            this.allocatedToAgency = 2;\n          } else {\n            this.allocatedToAgency = 3;\n          }\n        } else if (a.userType == \"AgencyToFrontEndExternalTC\") {\n          if (a.scope == \"parent\") {\n            this.allocatedToAgencyOfTelecallerAgent = 6;\n          } else if (a.scope == \"self\") {\n            this.allocatedToAgencyOfTelecallerAgent = 5;\n          } else {\n            this.allocatedToAgencyOfTelecallerAgent = 4;\n          }\n        } else if (a.userType == \"BankToFrontEndInternalFOS\") {\n          if (a.scope == \"parent\") {\n            this.allocatedToBranch = 1;\n          } else if (a.scope == \"self\") {\n            this.allocatedToBranch = 2;\n          } else {\n            this.allocatedToBranch = 3;\n          }\n        } else if (a.userType == \"BankToFrontEndInternalTC\") {\n          if (a.scope == \"parent\") {\n            this.allocatedToTelecallerStaff = 6;\n          } else if (a.scope == \"self\") {\n            this.allocatedToTelecallerStaff = 5;\n          } else {\n            this.allocatedToTelecallerStaff = 4;\n          }\n        }\n      });\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  save() {\n    this.accountSearchScopes.forEach(a => {\n      if (a.userType == \"AgencyToFrontEndExternalFOS\") {\n        if (this.allocatedToAgency == 1 || this.allocatedToAgency == \"1\") {\n          a.scope = \"parent\";\n        } else if (this.allocatedToAgency == 2 || this.allocatedToAgency == \"2\") {\n          a.scope = \"self\";\n        } else {\n          a.scope = \"all\";\n        }\n      } else if (a.userType == \"AgencyToFrontEndExternalTC\") {\n        if (this.allocatedToAgencyOfTelecallerAgent == 6 || this.allocatedToAgencyOfTelecallerAgent == \"6\") {\n          a.scope = \"parent\";\n        } else if (this.allocatedToAgencyOfTelecallerAgent == 5 || this.allocatedToAgencyOfTelecallerAgent == \"5\") {\n          a.scope = \"self\";\n        } else {\n          a.scope = \"all\";\n        }\n      } else if (a.userType == \"BankToFrontEndInternalFOS\") {\n        if (this.allocatedToBranch == 1 || this.allocatedToBranch == \"1\") {\n          a.scope = \"parent\";\n        } else if (this.allocatedToBranch == 2 || this.allocatedToBranch == \"2\") {\n          a.scope = \"self\";\n        } else {\n          a.scope = \"all\";\n        }\n      } else if (a.userType == \"BankToFrontEndInternalTC\") {\n        if (this.allocatedToTelecallerStaff == 6 || this.allocatedToTelecallerStaff == \"6\") {\n          a.scope = \"parent\";\n        } else if (this.allocatedToTelecallerStaff == 5 || this.allocatedToTelecallerStaff == \"5\") {\n          a.scope = \"self\";\n        } else {\n          a.scope = \"all\";\n        }\n      }\n    });\n    let data = {\n      \"SearchScopes\": this.accountSearchScopes\n    };\n    this.settingsService.updateAccountSearchScope(data).subscribe(response => {\n      this.toastr.success(\"Account Scope of work updated successfully.\", \"Success!\");\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n    });\n  }\n  showInstructions() {\n    const instructions = [`The Account search scope screen is designed to help end users efficiently manage and view\n      accounts based on their assigned role or agency.`, `By providing an easy-to-use interface, users can filter and update account visibility according to their specific needs( e.g, by Branch or Agency).`, `This ensures that users have access to the relevant accounts they are authorized to view and take appropriate actions on, enhancing workflow efficiency and ensuring data security.`];\n    this.sideDrawerService.open(InstructionStepsComponent, {\n      title: \"Information\",\n      data: {\n        instructions\n      }\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: SettingsService\n    }, {\n      type: ActivatedRoute\n    }, {\n      type: ToastrService\n    }, {\n      type: SideDrawerService\n    }];\n  }\n};\nAccountSearchScopeComponent = __decorate([Component({\n  selector: 'app-account-search-scope',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AccountSearchScopeComponent);\nexport { AccountSearchScopeComponent };", "map": {"version": 3, "names": ["Component", "SettingsService", "ActivatedRoute", "ToastrService", "SideDrawerService", "InstructionStepsComponent", "AccountSearchScopeComponent", "constructor", "settingsService", "route", "toastr", "sideDrawerService", "breadcrumbData", "label", "path", "accountSearchScopes", "ngOnInit", "getAccountSearchScope", "subscribe", "response", "length", "info", "for<PERSON>ach", "a", "userType", "scope", "allocatedToAgency", "allocatedToAgencyOfTelecallerAgent", "allocatedToBranch", "allocatedToTelecallerStaff", "err", "error", "save", "data", "updateAccountSearchScope", "success", "showInstructions", "instructions", "open", "title", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\account-search-scope\\account-search-scope.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\";\r\nimport { SettingsService } from \"../settings.service\";\r\nimport { FormBuilder, FormGroup, Validators } from \"@angular/forms\";\r\nimport { ActivatedRoute } from \"@angular/router\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { SideDrawerService } from \"src/app/shared/services/side-drawer.service\";\r\nimport { InstructionStepsComponent } from \"src/app/shared/components/instruction-steps/instruction-steps.component\";\r\n\r\n@Component({\r\n  selector: 'app-account-search-scope',\r\n  templateUrl: './account-search-scope.component.html',\r\n  styleUrls: ['./account-search-scope.component.scss']\r\n})\r\nexport class AccountSearchScopeComponent implements OnInit {\r\n  public breadcrumbData = [\r\n    { label: \"Settings\", path: \"/settings/account-search-scope\" },\r\n    { label: \"Account Search Scope\", path: \"/settings/account-search-scope\" },\r\n  ];\r\n  // This will hold the data from the API\r\n  accountSearchScopes: any[] = [];\r\n\r\n  // These variables will be used for ngModel binding to capture user selections\r\n  allocatedToBranch: any;\r\n  allocatedToTelecallerStaff: any;\r\n  allocatedToAgency: any;\r\n  allocatedToAgencyOfTelecallerAgent: any;\r\n  constructor(\r\n    private settingsService: SettingsService,\r\n    private route: ActivatedRoute,\r\n    private toastr: ToastrService,\r\n    private sideDrawerService: SideDrawerService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.getAccountSearchScope();\r\n  }\r\n\r\n  getAccountSearchScope() {\r\n\r\n    this.settingsService.getAccountSearchScope().subscribe(\r\n      (response) => {\r\n        if (response.length === 0) {\r\n\r\n          this.toastr.info(\"No results found!\", \"Info!\");\r\n          return false;\r\n        }\r\n        this.accountSearchScopes = response;\r\n        this.accountSearchScopes.forEach(a => {\r\n          if (a.userType == \"AgencyToFrontEndExternalFOS\") {\r\n            if (a.scope == \"parent\") {\r\n              this.allocatedToAgency = 1\r\n            } else if (a.scope == \"self\") {\r\n              this.allocatedToAgency = 2\r\n            } else {\r\n              this.allocatedToAgency = 3;\r\n            }\r\n          } else if (a.userType == \"AgencyToFrontEndExternalTC\") {\r\n            if (a.scope == \"parent\") {\r\n              this.allocatedToAgencyOfTelecallerAgent = 6\r\n            } else if (a.scope == \"self\") {\r\n              this.allocatedToAgencyOfTelecallerAgent = 5\r\n            } else {\r\n              this.allocatedToAgencyOfTelecallerAgent = 4;\r\n            }\r\n          } else if (a.userType == \"BankToFrontEndInternalFOS\") {\r\n            if (a.scope == \"parent\") {\r\n              this.allocatedToBranch = 1\r\n            } else if (a.scope == \"self\") {\r\n              this.allocatedToBranch = 2\r\n            } else {\r\n              this.allocatedToBranch = 3;\r\n            }\r\n          } else if (a.userType == \"BankToFrontEndInternalTC\") {\r\n            if (a.scope == \"parent\") {\r\n              this.allocatedToTelecallerStaff = 6\r\n            } else if (a.scope == \"self\") {\r\n              this.allocatedToTelecallerStaff = 5\r\n            } else {\r\n              this.allocatedToTelecallerStaff = 4;\r\n            }\r\n          }\r\n        })\r\n      },\r\n      (err) => {\r\n        this.toastr.error(err, \"Error!\");\r\n      },\r\n    );\r\n  }\r\n\r\n  save() {\r\n    this.accountSearchScopes.forEach(a => {\r\n      if (a.userType == \"AgencyToFrontEndExternalFOS\") {\r\n        if (this.allocatedToAgency == 1 || this.allocatedToAgency == \"1\") {\r\n          a.scope = \"parent\"\r\n        } else if (this.allocatedToAgency == 2 || this.allocatedToAgency == \"2\") {\r\n          a.scope = \"self\"\r\n        } else {\r\n          a.scope = \"all\"\r\n        }\r\n      } else if (a.userType == \"AgencyToFrontEndExternalTC\") {\r\n        if (this.allocatedToAgencyOfTelecallerAgent == 6 || this.allocatedToAgencyOfTelecallerAgent == \"6\") {\r\n          a.scope = \"parent\"\r\n        } else if (this.allocatedToAgencyOfTelecallerAgent == 5 || this.allocatedToAgencyOfTelecallerAgent == \"5\") {\r\n          a.scope = \"self\"\r\n        } else {\r\n          a.scope = \"all\";\r\n        }\r\n      } else if (a.userType == \"BankToFrontEndInternalFOS\") {\r\n        if (this.allocatedToBranch == 1 || this.allocatedToBranch == \"1\") {\r\n          a.scope = \"parent\"\r\n        } else if (this.allocatedToBranch == 2 || this.allocatedToBranch == \"2\") {\r\n          a.scope = \"self\"\r\n        } else {\r\n          a.scope = \"all\"\r\n        }\r\n      } else if (a.userType == \"BankToFrontEndInternalTC\") {\r\n        if (this.allocatedToTelecallerStaff == 6 || this.allocatedToTelecallerStaff == \"6\") {\r\n          a.scope = \"parent\"\r\n        } else if (this.allocatedToTelecallerStaff == 5 || this.allocatedToTelecallerStaff == \"5\") {\r\n          a.scope = \"self\"\r\n        } else {\r\n          a.scope = \"all\"\r\n        }\r\n      }\r\n    })\r\n    let data = {\r\n      \"SearchScopes\": this.accountSearchScopes\r\n    }\r\n    this.settingsService.updateAccountSearchScope(data).subscribe(\r\n      (response) => {\r\n        this.toastr.success(\"Account Scope of work updated successfully.\",\"Success!\");\r\n      },\r\n      (err) => {\r\n        this.toastr.error(err, \"Error!\");\r\n      },\r\n    );\r\n  }\r\n\r\n  showInstructions() {\r\n    const instructions = [\r\n      `The Account search scope screen is designed to help end users efficiently manage and view\r\n      accounts based on their assigned role or agency.`,\r\n      `By providing an easy-to-use interface, users can filter and update account visibility according to their specific needs( e.g, by Branch or Agency).`,\r\n      `This ensures that users have access to the relevant accounts they are authorized to view and take appropriate actions on, enhancing workflow efficiency and ensuring data security.`\r\n    ];\r\n\r\n    this.sideDrawerService.open(InstructionStepsComponent, {\r\n      title: \"Information\",\r\n      data: {\r\n        instructions,\r\n      },\r\n    });\r\n  }\r\n}\r\n\r\n\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,yBAAyB,QAAQ,yEAAyE;AAO5G,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAatCC,YACUC,eAAgC,EAChCC,KAAqB,EACrBC,MAAqB,EACrBC,iBAAoC;IAHpC,KAAAH,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAhBpB,KAAAC,cAAc,GAAG,CACtB;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAgC,CAAE,EAC7D;MAAED,KAAK,EAAE,sBAAsB;MAAEC,IAAI,EAAE;IAAgC,CAAE,CAC1E;IACD;IACA,KAAAC,mBAAmB,GAAU,EAAE;EAY3B;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAA,qBAAqBA,CAAA;IAEnB,IAAI,CAACT,eAAe,CAACS,qBAAqB,EAAE,CAACC,SAAS,CACnDC,QAAQ,IAAI;MACX,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;QAEzB,IAAI,CAACV,MAAM,CAACW,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC;QAC9C,OAAO,KAAK;MACd;MACA,IAAI,CAACN,mBAAmB,GAAGI,QAAQ;MACnC,IAAI,CAACJ,mBAAmB,CAACO,OAAO,CAACC,CAAC,IAAG;QACnC,IAAIA,CAAC,CAACC,QAAQ,IAAI,6BAA6B,EAAE;UAC/C,IAAID,CAAC,CAACE,KAAK,IAAI,QAAQ,EAAE;YACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;UAC5B,CAAC,MAAM,IAAIH,CAAC,CAACE,KAAK,IAAI,MAAM,EAAE;YAC5B,IAAI,CAACC,iBAAiB,GAAG,CAAC;UAC5B,CAAC,MAAM;YACL,IAAI,CAACA,iBAAiB,GAAG,CAAC;UAC5B;QACF,CAAC,MAAM,IAAIH,CAAC,CAACC,QAAQ,IAAI,4BAA4B,EAAE;UACrD,IAAID,CAAC,CAACE,KAAK,IAAI,QAAQ,EAAE;YACvB,IAAI,CAACE,kCAAkC,GAAG,CAAC;UAC7C,CAAC,MAAM,IAAIJ,CAAC,CAACE,KAAK,IAAI,MAAM,EAAE;YAC5B,IAAI,CAACE,kCAAkC,GAAG,CAAC;UAC7C,CAAC,MAAM;YACL,IAAI,CAACA,kCAAkC,GAAG,CAAC;UAC7C;QACF,CAAC,MAAM,IAAIJ,CAAC,CAACC,QAAQ,IAAI,2BAA2B,EAAE;UACpD,IAAID,CAAC,CAACE,KAAK,IAAI,QAAQ,EAAE;YACvB,IAAI,CAACG,iBAAiB,GAAG,CAAC;UAC5B,CAAC,MAAM,IAAIL,CAAC,CAACE,KAAK,IAAI,MAAM,EAAE;YAC5B,IAAI,CAACG,iBAAiB,GAAG,CAAC;UAC5B,CAAC,MAAM;YACL,IAAI,CAACA,iBAAiB,GAAG,CAAC;UAC5B;QACF,CAAC,MAAM,IAAIL,CAAC,CAACC,QAAQ,IAAI,0BAA0B,EAAE;UACnD,IAAID,CAAC,CAACE,KAAK,IAAI,QAAQ,EAAE;YACvB,IAAI,CAACI,0BAA0B,GAAG,CAAC;UACrC,CAAC,MAAM,IAAIN,CAAC,CAACE,KAAK,IAAI,MAAM,EAAE;YAC5B,IAAI,CAACI,0BAA0B,GAAG,CAAC;UACrC,CAAC,MAAM;YACL,IAAI,CAACA,0BAA0B,GAAG,CAAC;UACrC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,EACAC,GAAG,IAAI;MACN,IAAI,CAACpB,MAAM,CAACqB,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IAClC,CAAC,CACF;EACH;EAEAE,IAAIA,CAAA;IACF,IAAI,CAACjB,mBAAmB,CAACO,OAAO,CAACC,CAAC,IAAG;MACnC,IAAIA,CAAC,CAACC,QAAQ,IAAI,6BAA6B,EAAE;QAC/C,IAAI,IAAI,CAACE,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,GAAG,EAAE;UAChEH,CAAC,CAACE,KAAK,GAAG,QAAQ;QACpB,CAAC,MAAM,IAAI,IAAI,CAACC,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,GAAG,EAAE;UACvEH,CAAC,CAACE,KAAK,GAAG,MAAM;QAClB,CAAC,MAAM;UACLF,CAAC,CAACE,KAAK,GAAG,KAAK;QACjB;MACF,CAAC,MAAM,IAAIF,CAAC,CAACC,QAAQ,IAAI,4BAA4B,EAAE;QACrD,IAAI,IAAI,CAACG,kCAAkC,IAAI,CAAC,IAAI,IAAI,CAACA,kCAAkC,IAAI,GAAG,EAAE;UAClGJ,CAAC,CAACE,KAAK,GAAG,QAAQ;QACpB,CAAC,MAAM,IAAI,IAAI,CAACE,kCAAkC,IAAI,CAAC,IAAI,IAAI,CAACA,kCAAkC,IAAI,GAAG,EAAE;UACzGJ,CAAC,CAACE,KAAK,GAAG,MAAM;QAClB,CAAC,MAAM;UACLF,CAAC,CAACE,KAAK,GAAG,KAAK;QACjB;MACF,CAAC,MAAM,IAAIF,CAAC,CAACC,QAAQ,IAAI,2BAA2B,EAAE;QACpD,IAAI,IAAI,CAACI,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,GAAG,EAAE;UAChEL,CAAC,CAACE,KAAK,GAAG,QAAQ;QACpB,CAAC,MAAM,IAAI,IAAI,CAACG,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,GAAG,EAAE;UACvEL,CAAC,CAACE,KAAK,GAAG,MAAM;QAClB,CAAC,MAAM;UACLF,CAAC,CAACE,KAAK,GAAG,KAAK;QACjB;MACF,CAAC,MAAM,IAAIF,CAAC,CAACC,QAAQ,IAAI,0BAA0B,EAAE;QACnD,IAAI,IAAI,CAACK,0BAA0B,IAAI,CAAC,IAAI,IAAI,CAACA,0BAA0B,IAAI,GAAG,EAAE;UAClFN,CAAC,CAACE,KAAK,GAAG,QAAQ;QACpB,CAAC,MAAM,IAAI,IAAI,CAACI,0BAA0B,IAAI,CAAC,IAAI,IAAI,CAACA,0BAA0B,IAAI,GAAG,EAAE;UACzFN,CAAC,CAACE,KAAK,GAAG,MAAM;QAClB,CAAC,MAAM;UACLF,CAAC,CAACE,KAAK,GAAG,KAAK;QACjB;MACF;IACF,CAAC,CAAC;IACF,IAAIQ,IAAI,GAAG;MACT,cAAc,EAAE,IAAI,CAAClB;KACtB;IACD,IAAI,CAACP,eAAe,CAAC0B,wBAAwB,CAACD,IAAI,CAAC,CAACf,SAAS,CAC1DC,QAAQ,IAAI;MACX,IAAI,CAACT,MAAM,CAACyB,OAAO,CAAC,6CAA6C,EAAC,UAAU,CAAC;IAC/E,CAAC,EACAL,GAAG,IAAI;MACN,IAAI,CAACpB,MAAM,CAACqB,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;IAClC,CAAC,CACF;EACH;EAEAM,gBAAgBA,CAAA;IACd,MAAMC,YAAY,GAAG,CACnB;uDACiD,EACjD,qJAAqJ,EACrJ,qLAAqL,CACtL;IAED,IAAI,CAAC1B,iBAAiB,CAAC2B,IAAI,CAACjC,yBAAyB,EAAE;MACrDkC,KAAK,EAAE,aAAa;MACpBN,IAAI,EAAE;QACJI;;KAEH,CAAC;EACJ;;;;;;;;;;;;;AA3IW/B,2BAA2B,GAAAkC,UAAA,EALvCxC,SAAS,CAAC;EACTyC,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;;CAErD,CAAC,C,EACWrC,2BAA2B,CA4IvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}