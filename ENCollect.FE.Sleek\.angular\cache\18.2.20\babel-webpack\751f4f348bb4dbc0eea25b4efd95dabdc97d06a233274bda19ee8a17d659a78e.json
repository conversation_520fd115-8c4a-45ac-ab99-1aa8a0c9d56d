{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./bulk-initiate-status.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./bulk-initiate-status.component.css?ngResource\";\nexport class SearchControls {}\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { repoConfigService } from '../../legal-custom-config.service';\nimport { repoService } from '../../legal-custom.service';\nimport { PaginationsComponent } from './../../common/paginations/paginations.component';\nlet BulkInitiateStatusComponent = class BulkInitiateStatusComponent extends PaginationsComponent {\n  constructor(toastr, modalService, legalService, legalConfigService) {\n    super();\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.legalService = legalService;\n    this.legalConfigService = legalConfigService;\n    this.breadcrumbData = [{\n      label: \"Legal Management\",\n      path: \"/encollect/legal-custom/bulkupload-initiate-status\"\n    }, {\n      label: \"Status of Bulk Upload - Initiate Legal\",\n      path: \"/encollect/legal-custom/bulkupload-initiate-status\"\n    }];\n    this.searchControls = new SearchControls();\n    this.variableInit();\n    this.statusList = this.legalConfigService.legalAllocationStatusList();\n  }\n  variableInit() {\n    this.results = [];\n    this.searchControls = {\n      fileName: '',\n      fileuploadDate: '',\n      status: '',\n      transactionId: ''\n    };\n    this.maxDate = new Date();\n    this.loader = {\n      isSearching: false\n    };\n  }\n  ngOnInit() {}\n  searchStatus() {\n    if (this.searchControls.fileName == '' && this.searchControls.status == '' && this.searchControls.transactionId == '' && this.searchControls.fileuploadDate == '') {\n      this.toastr.warning(\"Enter at least one filter value\");\n      return false;\n    }\n    this.loader.isSearching = true;\n    this.results = [];\n    this.currentRecords = [];\n    this.legalService.initateLegalUploadStatus(this.searchControls).subscribe(response => {\n      this.loader.isSearching = false;\n      if (response.length === 0) {\n        this.toastr.info('No results found!');\n        return false;\n      }\n      this.results = response;\n      this.currentRecords = super.fetchRecordsByPage(1);\n    }, err => {\n      this.toastr.error(err, \"Error!\");\n      this.loader.isSearching = false;\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: repoService\n    }, {\n      type: repoConfigService\n    }];\n  }\n};\nBulkInitiateStatusComponent = __decorate([Component({\n  selector: 'app-bulk-initiate-status',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], BulkInitiateStatusComponent);\nexport { BulkInitiateStatusComponent };", "map": {"version": 3, "names": ["SearchControls", "Component", "ToastrService", "BsModalService", "repoConfigService", "repoService", "PaginationsComponent", "BulkInitiateStatusComponent", "constructor", "toastr", "modalService", "legalService", "legalConfigService", "breadcrumbData", "label", "path", "searchControls", "variableInit", "statusList", "legalAllocationStatusList", "results", "fileName", "fileuploadDate", "status", "transactionId", "maxDate", "Date", "loader", "isSearching", "ngOnInit", "searchStatus", "warning", "currentRecords", "initateLegalUploadStatus", "subscribe", "response", "length", "info", "fetchRecordsByPage", "err", "error", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal-custom\\bulk-initiate\\bulk-initiate-status\\bulk-initiate-status.component.ts"], "sourcesContent": ["\r\nexport class SearchControls {\r\n\tfileName: string;\r\n\tfileuploadDate: string;\r\n\tstatus: string;\r\n\ttransactionId: string;\r\n}\r\nexport interface Loader {\r\n\tisSearching: boolean;\r\n}\r\nimport { Component, OnInit, TemplateRef } from '@angular/core';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\nimport { JwtService } from '../../../authentication/jwt.service';\r\nimport { repoConfigService } from '../../legal-custom-config.service';\r\nimport { repoService } from '../../legal-custom.service';\r\n\r\n\r\nimport { PaginationsComponent } from './../../common/paginations/paginations.component';\r\n\r\n@Component({\r\n\tselector: 'app-bulk-initiate-status',\r\n\ttemplateUrl: './bulk-initiate-status.component.html',\r\n\tstyleUrls: ['./bulk-initiate-status.component.css']\r\n})\r\nexport class BulkInitiateStatusComponent extends PaginationsComponent implements OnInit {\r\n\tpublic breadcrumbData = [\r\n\t\t{ label: \"Legal Management\", path: \"/encollect/legal-custom/bulkupload-initiate-status\" },\r\n\t\t{ label: \"Status of Bulk Upload - Initiate Legal\", path: \"/encollect/legal-custom/bulkupload-initiate-status\" },\r\n\t  ];\r\n\tsearchControls: SearchControls = new SearchControls();\r\n\tloader: Loader;\r\n\tmaxDate: Date;\r\n\tstatusList: Array<string>;\r\n\tconstructor(public toastr: ToastrService,\r\n\t\tprivate modalService: BsModalService,\r\n\t\tprivate legalService: repoService,\r\n\t\tprivate legalConfigService: repoConfigService) {\r\n\t\tsuper();\r\n\t\tthis.variableInit();\r\n\t\tthis.statusList = this.legalConfigService.legalAllocationStatusList()\r\n\t}\r\n\r\n\tvariableInit() {\r\n\t\tthis.results = []\r\n\t\tthis.searchControls = {\r\n\t\t\tfileName: '',\r\n\t\t\tfileuploadDate: '',\r\n\t\t\tstatus: '',\r\n\t\t\ttransactionId: ''\r\n\t\t}\r\n\t\tthis.maxDate = new Date();\r\n\t\tthis.loader = {\r\n\t\t\tisSearching: false\r\n\t\t}\r\n\t}\r\n\tngOnInit() {\r\n\t}\r\n\tsearchStatus() {\r\n\t\tif (this.searchControls.fileName == '' && this.searchControls.status == ''\r\n\t\t\t&& this.searchControls.transactionId == '' && this.searchControls.fileuploadDate == '') {\r\n\t\t\tthis.toastr.warning(\"Enter at least one filter value\");\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tthis.loader.isSearching = true\r\n\t\tthis.results = []\r\n\t\tthis.currentRecords = []\r\n\t\tthis.legalService\r\n\t\t\t.initateLegalUploadStatus(this.searchControls)\r\n\t\t\t.subscribe(response => {\r\n\t\t\t\tthis.loader.isSearching = false\r\n\t\t\t\tif (response.length === 0) {\r\n\t\t\t\t\tthis.toastr.info('No results found!');\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\tthis.results = response\r\n\t\t\t\tthis.currentRecords = super.fetchRecordsByPage(1);\r\n\t\t\t}, err => {\r\n\t\t\t\tthis.toastr.error(err, \"Error!\")\r\n\t\t\t\tthis.loader.isSearching = false\r\n\t\t\t})\r\n\t}\r\n\r\n}\r\n"], "mappings": ";;;AACA,OAAM,MAAOA,cAAc;AAS3B,SAASC,SAAS,QAA6B,eAAe;AAE9D,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAoB,qBAAqB;AAGhE,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,WAAW,QAAQ,4BAA4B;AAGxD,SAASC,oBAAoB,QAAQ,kDAAkD;AAOhF,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA4B,SAAQD,oBAAoB;EASpEE,YAAmBC,MAAqB,EAC/BC,YAA4B,EAC5BC,YAAyB,EACzBC,kBAAqC;IAC7C,KAAK,EAAE;IAJW,KAAAH,MAAM,GAANA,MAAM;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAXpB,KAAAC,cAAc,GAAG,CACvB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAAoD,CAAE,EACzF;MAAED,KAAK,EAAE,wCAAwC;MAAEC,IAAI,EAAE;IAAoD,CAAE,CAC7G;IACH,KAAAC,cAAc,GAAmB,IAAIhB,cAAc,EAAE;IASpD,IAAI,CAACiB,YAAY,EAAE;IACnB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACN,kBAAkB,CAACO,yBAAyB,EAAE;EACtE;EAEAF,YAAYA,CAAA;IACX,IAAI,CAACG,OAAO,GAAG,EAAE;IACjB,IAAI,CAACJ,cAAc,GAAG;MACrBK,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAE,EAAE;MACVC,aAAa,EAAE;KACf;IACD,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE;IACzB,IAAI,CAACC,MAAM,GAAG;MACbC,WAAW,EAAE;KACb;EACF;EACAC,QAAQA,CAAA,GACR;EACAC,YAAYA,CAAA;IACX,IAAI,IAAI,CAACd,cAAc,CAACK,QAAQ,IAAI,EAAE,IAAI,IAAI,CAACL,cAAc,CAACO,MAAM,IAAI,EAAE,IACtE,IAAI,CAACP,cAAc,CAACQ,aAAa,IAAI,EAAE,IAAI,IAAI,CAACR,cAAc,CAACM,cAAc,IAAI,EAAE,EAAE;MACxF,IAAI,CAACb,MAAM,CAACsB,OAAO,CAAC,iCAAiC,CAAC;MACtD,OAAO,KAAK;IACb;IACA,IAAI,CAACJ,MAAM,CAACC,WAAW,GAAG,IAAI;IAC9B,IAAI,CAACR,OAAO,GAAG,EAAE;IACjB,IAAI,CAACY,cAAc,GAAG,EAAE;IACxB,IAAI,CAACrB,YAAY,CACfsB,wBAAwB,CAAC,IAAI,CAACjB,cAAc,CAAC,CAC7CkB,SAAS,CAACC,QAAQ,IAAG;MACrB,IAAI,CAACR,MAAM,CAACC,WAAW,GAAG,KAAK;MAC/B,IAAIO,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC3B,MAAM,CAAC4B,IAAI,CAAC,mBAAmB,CAAC;QACrC,OAAO,KAAK;MACb;MACA,IAAI,CAACjB,OAAO,GAAGe,QAAQ;MACvB,IAAI,CAACH,cAAc,GAAG,KAAK,CAACM,kBAAkB,CAAC,CAAC,CAAC;IAClD,CAAC,EAAEC,GAAG,IAAG;MACR,IAAI,CAAC9B,MAAM,CAAC+B,KAAK,CAACD,GAAG,EAAE,QAAQ,CAAC;MAChC,IAAI,CAACZ,MAAM,CAACC,WAAW,GAAG,KAAK;IAChC,CAAC,CAAC;EACJ;;;;;;;;;;;;;AAxDYrB,2BAA2B,GAAAkC,UAAA,EALvCxC,SAAS,CAAC;EACVyC,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;;CAEpD,CAAC,C,EACWrC,2BAA2B,CA0DvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}