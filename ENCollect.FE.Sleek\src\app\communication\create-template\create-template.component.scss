.variable-map-legends {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  label {
    position: relative;
    &::before {
      position: absolute;
      content: "";
      width: 0.825rem;
      height: 0.825rem;
      border: solid 1px gray;
      background-color: lightgray;
      border-radius: 50%;
      left: -1.25rem;
      top: 50%;
      transform: translateY(-50%);
    }
    &.mapped::before {
      background-color: #dcfce7;
      border-color: #86efac;
    }
    &.unmapped::before {
      background-color: #fef9c3;
      border-color: #fde047;
    }
  }
}

.language-button-wrapper {
  position: relative;
  display: inline-block;
}

.language-btn {
  position: relative;
  padding-right: 2rem !important;
  cursor: pointer;
  transition: all 0.2s ease;

  &.active {
    font-weight: 600;
    border-width: 2px;
  }

  &:hover:not(:disabled) {
    background-color: #f8f9fa;
    border-color: #dee2e6;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}
.remove-language-btn {
  position: absolute;
  top: 50%;
  right: 0.5rem;
  transform: translateY(-50%);
  color: #ef4444;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  line-height: 1;
  width: 1.2rem;
  height: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  z-index: 10;

  &:hover {
    background-color: #fef2f2;
    color: #dc2626;
    transform: translateY(-50%) scale(1.1);
  }
}

::ng-deep .modal-close-btn {
  width: 1.625rem;
  height: 1.625rem;
  cursor: pointer;
  border-radius: 50%;
}
.form-control-readonly {
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
  opacity: 0.8;
}

.form-radio-group.disabled {
  opacity: 0.6;
  pointer-events: none;

  label {
    color: #6c757d;
    cursor: not-allowed;
  }

  input[type="radio"] {
    cursor: not-allowed;
  }
}

.text-muted {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: block;
}

// Validation error styles
.form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

textarea.form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.add-var-btn {
  top: 10px;
  right: 10px;
  z-index: 5;
}

// Email body specific styles
.email-body-container {
  position: relative;

  // NGX Editor Styles
  ::ng-deep .NgxEditor {
    min-height: 200px !important;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:focus-within {
      border-color: #0d6efd;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    }

    .NgxEditor__Content {
      min-height: 180px;
      padding: 12px;
      font-size: 0.9rem;
      line-height: 1.4;

      // Style for template variables in the editor
      &:has-text("<<") {
        // This will help highlight template variables
        background-color: #fff9c4;
      }
    }
  }

  // Ensure toolbar is always visible and styled properly
  ::ng-deep .NgxEditor__MenuBar {
    border-bottom: 1px solid #e1e5e9;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px 6px 0 0;

    .NgxEditor__MenuItem {
      opacity: 1 !important;

      &:disabled {
        opacity: 0.6 !important;
      }
    }
  }

  ::ng-deep .NgxEditor__MenuBar {
    border-bottom: 1px solid #e1e5e9;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 6px 6px 0 0;

    .NgxEditor__MenuItem {
      margin: 2px;
      border-radius: 4px;

      &:hover {
        background-color: #e9ecef;
      }

      &.NgxEditor__MenuItem--Active {
        background-color: #0d6efd;
        color: white;
      }
    }
  }

  // Legacy textarea styles (kept for fallback)
  .email-body-input {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    background-color: #fafbfc;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:focus {
      background-color: #ffffff;
      border-color: #0d6efd;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    }

    &.is-invalid {
      border-color: #dc3545;
      background-color: #fff5f5;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
    }
  }

  .email-body-help-text {
    margin-top: 0.5rem;

    small {
      color: #6c757d;
      font-size: 0.8rem;
      display: flex;
      align-items: center;

      i {
        color: #0d6efd;
        margin-right: 0.25rem;
      }
    }
  }

  .editor-loading {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-radius: 0 0 6px 6px;
    border-top: none;

    p {
      color: #6c757d;
      margin: 0;
      font-style: italic;
      font-size: 0.9rem;
    }
  }
}

// Global styles for template variables in ngx-editor
::ng-deep .NgxEditor__Content {
  // Style template variables to make them more visible
  &:contains("<<") {
    // This is a fallback approach
  }
}

// Alternative approach - style any text that looks like template variables
::ng-deep .NgxEditor__Content p {
  // This will help make template variables more visible
  &:has-text("<<") {
    background-color: rgba(255, 249, 196, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
  }
}
