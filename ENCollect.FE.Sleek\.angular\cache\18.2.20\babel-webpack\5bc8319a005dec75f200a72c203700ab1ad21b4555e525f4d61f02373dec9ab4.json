{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SettlementRequestSearchComponent } from './settlement-request-search.component';\ndescribe('SettlementRequestSearchComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SettlementRequestSearchComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SettlementRequestSearchComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should be created', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SettlementRequestSearchComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settlement\\acs\\acs-mysettlement\\settlement-request-search.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\r\n\r\nimport { SettlementRequestSearchComponent } from './settlement-request-search.component';\r\n\r\ndescribe('SettlementRequestSearchComponent', () => {\r\n  let component: SettlementRequestSearchComponent;\r\n  let fixture: ComponentFixture<SettlementRequestSearchComponent>;\r\n\r\n  beforeEach(waitForAsync(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ SettlementRequestSearchComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(SettlementRequestSearchComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should be created', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,gCAAgC,QAAQ,uCAAuC;AAExFC,QAAQ,CAAC,kCAAkC,EAAE,MAAK;EAChD,IAAIC,SAA2C;EAC/C,IAAIC,OAA2D;EAE/DC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,gCAAgC;KACjD,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,gCAAgC,CAAC;IACnEE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}