{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nimport { BehaviorSubject } from \"rxjs\";\nlet LoaderService = class LoaderService {\n  constructor() {\n    this.showLoader$ = new BehaviorSubject(false);\n    this.count = 0;\n  }\n  get loader() {\n    return this.showLoader$.asObservable();\n  }\n  show() {\n    this.count++;\n    this.showLoader$.next(true);\n  }\n  hide() {\n    if (this.count > 0) {\n      this.count--;\n    } else {\n      this.count = 0;\n      this.showLoader$.next(false);\n    }\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nLoaderService = __decorate([Injectable({\n  providedIn: \"root\"\n})], LoaderService);\nexport { LoaderService };", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "LoaderService", "constructor", "showLoader$", "count", "loader", "asObservable", "show", "next", "hide", "__decorate", "providedIn"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\services\\loader.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { BehaviorSubject, Observable } from \"rxjs\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class LoaderService {\r\n  private showLoader$ = new BehaviorSubject<boolean>(false);\r\n  private count: number = 0;\r\n\r\n  constructor() {}\r\n\r\n  public get loader(): Observable<boolean> {\r\n    return this.showLoader$.asObservable();\r\n  }\r\n\r\n  public show() {\r\n    this.count++;\r\n    this.showLoader$.next(true);\r\n  }\r\n\r\n  public hide() {\r\n    if (this.count > 0) {\r\n      this.count--;\r\n    } else {\r\n      this.count = 0;\r\n      this.showLoader$.next(false);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAoB,MAAM;AAK3C,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAIxBC,YAAA;IAHQ,KAAAC,WAAW,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;IACjD,KAAAI,KAAK,GAAW,CAAC;EAEV;EAEf,IAAWC,MAAMA,CAAA;IACf,OAAO,IAAI,CAACF,WAAW,CAACG,YAAY,EAAE;EACxC;EAEOC,IAAIA,CAAA;IACT,IAAI,CAACH,KAAK,EAAE;IACZ,IAAI,CAACD,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEOC,IAAIA,CAAA;IACT,IAAI,IAAI,CAACL,KAAK,GAAG,CAAC,EAAE;MAClB,IAAI,CAACA,KAAK,EAAE;IACd,CAAC,MAAM;MACL,IAAI,CAACA,KAAK,GAAG,CAAC;MACd,IAAI,CAACD,WAAW,CAACK,IAAI,CAAC,KAAK,CAAC;IAC9B;EACF;;;;;AAtBWP,aAAa,GAAAS,UAAA,EAHzBX,UAAU,CAAC;EACVY,UAAU,EAAE;CACb,CAAC,C,EACWV,aAAa,CAuBzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}