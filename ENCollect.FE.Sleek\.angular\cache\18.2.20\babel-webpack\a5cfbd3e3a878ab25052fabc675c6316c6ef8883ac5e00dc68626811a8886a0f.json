{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { CreateBatchOfPaymentsComponent } from './create-batch-of-payments.component';\ndescribe('CreateBatchOfPaymentsComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [CreateBatchOfPaymentsComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(CreateBatchOfPaymentsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "CreateBatchOfPaymentsComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\payments\\v1\\create-batch-of-payments\\create-batch-of-payments.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\r\n\r\nimport { CreateBatchOfPaymentsComponent } from './create-batch-of-payments.component';\r\n\r\ndescribe('CreateBatchOfPaymentsComponent', () => {\r\n  let component: CreateBatchOfPaymentsComponent;\r\n  let fixture: ComponentFixture<CreateBatchOfPaymentsComponent>;\r\n\r\n  beforeEach(waitForAsync(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ CreateBatchOfPaymentsComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(CreateBatchOfPaymentsComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,8BAA8B,QAAQ,sCAAsC;AAErFC,QAAQ,CAAC,gCAAgC,EAAE,MAAK;EAC9C,IAAIC,SAAyC;EAC7C,IAAIC,OAAyD;EAE7DC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,8BAA8B;KAC/C,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,8BAA8B,CAAC;IACjEE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}