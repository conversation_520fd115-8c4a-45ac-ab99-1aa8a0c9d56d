{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./upload-bank-master-status.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./upload-bank-master-status.component.css?ngResource\";\nexport class SearchControls {}\nimport { Component } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { SettingsService } from '../../settings.service';\nimport { SettingsConfigService } from '../../settingsconfig.service';\nimport { PaginationsComponent } from './../../common/paginations/paginations.component';\nlet UploadBankMasterStatusComponent = class UploadBankMasterStatusComponent extends PaginationsComponent {\n  constructor(toastr, modalService, settingsService, settingsConfigService) {\n    super();\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.settingsService = settingsService;\n    this.settingsConfigService = settingsConfigService;\n    this.breadcrumbData = [{\n      label: \"Settings\",\n      path: \"/settings/upload-bank-master-status\"\n    }, {\n      label: \"Bank Master File Upload Status\",\n      path: \"/settings/upload-bank-master-status\"\n    }];\n    this.searchControls = new SearchControls();\n    this.variableInit();\n    this.statusList = this.settingsConfigService.statusList();\n  }\n  variableInit() {\n    this.searchControls = {\n      fileName: '',\n      fileuploadDate: '',\n      status: '',\n      transactionId: ''\n    };\n    this.maxDate = new Date();\n    this.loader = {\n      isSearching: false\n    };\n  }\n  ngOnInit() {}\n  searchStatus() {\n    if (this.searchControls.fileName == '' && this.searchControls.status == '' && this.searchControls.transactionId == '' && this.searchControls.fileuploadDate == '') {\n      this.toastr.warning(\"Enter at least one filter value\");\n      return false;\n    }\n    this.loader.isSearching = true;\n    this.settingsService.bankBulkuploadStatus(this.searchControls).subscribe(response => {\n      this.loader.isSearching = false;\n      if (response.length === 0) {\n        this.toastr.info('No results found!');\n        return false;\n      }\n      this.results = response;\n      this.currentRecords = super.fetchRecordsByPage(1);\n    }, err => {\n      this.toastr.error(err);\n      this.loader.isSearching = false;\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: BsModalService\n    }, {\n      type: SettingsService\n    }, {\n      type: SettingsConfigService\n    }];\n  }\n};\nUploadBankMasterStatusComponent = __decorate([Component({\n  selector: 'app-upload-bank-master-status',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], UploadBankMasterStatusComponent);\nexport { UploadBankMasterStatusComponent };", "map": {"version": 3, "names": ["SearchControls", "Component", "ToastrService", "BsModalService", "SettingsService", "SettingsConfigService", "PaginationsComponent", "UploadBankMasterStatusComponent", "constructor", "toastr", "modalService", "settingsService", "settingsConfigService", "breadcrumbData", "label", "path", "searchControls", "variableInit", "statusList", "fileName", "fileuploadDate", "status", "transactionId", "maxDate", "Date", "loader", "isSearching", "ngOnInit", "searchStatus", "warning", "bankBulkuploadStatus", "subscribe", "response", "length", "info", "results", "currentRecords", "fetchRecordsByPage", "err", "error", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\settings\\bank-config\\upload-bank-master-status\\upload-bank-master-status.component.ts"], "sourcesContent": ["export class SearchControls{\r\n\t  fileName: string;\r\n      fileuploadDate: string;\r\n      status: string;\r\n      transactionId: string;\r\n}\r\nexport interface Loader{\r\n    isSearching: boolean;\r\n}\r\nimport { Component, OnInit,TemplateRef } from '@angular/core';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';\r\n\r\nimport { JwtService } from '../../../authentication/jwt.service';\r\nimport { SettingsService } from '../../settings.service';\r\nimport { SettingsConfigService } from '../../settingsconfig.service';\r\n\r\n\r\nimport { PaginationsComponent } from './../../common/paginations/paginations.component';\r\n\r\n@Component({\r\n  selector: 'app-upload-bank-master-status',\r\n  templateUrl: './upload-bank-master-status.component.html',\r\n  styleUrls: ['./upload-bank-master-status.component.css']\r\n})\r\nexport class UploadBankMasterStatusComponent extends PaginationsComponent implements OnInit{\r\n  public breadcrumbData = [\r\n\t\t{ label: \"Settings\", path: \"/settings/upload-bank-master-status\" },\r\n\t\t{ label: \"Bank Master File Upload Status\", path: \"/settings/upload-bank-master-status\" },\r\n\t  ]\r\nsearchControls: SearchControls = new SearchControls();\r\n  loader: Loader;\r\n  maxDate: Date;\r\n  statusList: Array<string>;\r\n  constructor(public toastr: ToastrService, \r\n           private modalService: BsModalService,\r\n  \t\t\t private settingsService: SettingsService,\r\n  \t\t      private settingsConfigService: SettingsConfigService) { \r\n  \tsuper();\r\n  \tthis.variableInit();\r\n  \tthis.statusList= this.settingsConfigService.statusList()\r\n  }\r\n  variableInit(){\r\n      this.searchControls = {\r\n    \t    fileName: '',\r\n          fileuploadDate: '',\r\n          status: '',\r\n          transactionId: ''\r\n    \t}\r\n    \tthis.maxDate = new Date();\r\n    \tthis.loader = {\r\n    \t\tisSearching: false\r\n    \t}\r\n  }\r\n  ngOnInit() {\r\n  }\r\n  searchStatus(){\r\n  \t  if( this.searchControls.fileName=='' && this.searchControls.status==''\r\n        && this.searchControls.transactionId=='' &&this.searchControls.fileuploadDate==''){\r\n       this.toastr.warning(\"Enter at least one filter value\");\r\n       return false;\r\n    }\r\n    this.loader.isSearching =  true\r\n    this.settingsService\r\n      .bankBulkuploadStatus(this.searchControls)\r\n      .subscribe(response => {\r\n      \t this.loader.isSearching =  false\r\n    \t if (response.length === 0) {\r\n    \t \tthis.toastr.info('No results found!');\r\n    \t \treturn false\r\n    \t }\r\n        this.results = response\r\n        this.currentRecords = super.fetchRecordsByPage(1);\r\n      },err=>{\r\n      \tthis.toastr.error(err)\r\n      \tthis.loader.isSearching =  false\r\n      })\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,OAAM,MAAOA,cAAc;AAS3B,SAASC,SAAS,QAA4B,eAAe;AAE7D,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,cAAc,QAAoB,qBAAqB;AAGhE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,qBAAqB,QAAQ,8BAA8B;AAGpE,SAASC,oBAAoB,QAAQ,kDAAkD;AAOhF,IAAMC,+BAA+B,GAArC,MAAMA,+BAAgC,SAAQD,oBAAoB;EASvEE,YAAmBC,MAAqB,EACvBC,YAA4B,EACjCC,eAAgC,EAC5BC,qBAA4C;IAC3D,KAAK,EAAE;IAJW,KAAAH,MAAM,GAANA,MAAM;IACR,KAAAC,YAAY,GAAZA,YAAY;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACX,KAAAC,qBAAqB,GAArBA,qBAAqB;IAX9B,KAAAC,cAAc,GAAG,CACxB;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAqC,CAAE,EAClE;MAAED,KAAK,EAAE,gCAAgC;MAAEC,IAAI,EAAE;IAAqC,CAAE,CACtF;IACJ,KAAAC,cAAc,GAAmB,IAAIhB,cAAc,EAAE;IASlD,IAAI,CAACiB,YAAY,EAAE;IACnB,IAAI,CAACC,UAAU,GAAE,IAAI,CAACN,qBAAqB,CAACM,UAAU,EAAE;EACzD;EACAD,YAAYA,CAAA;IACR,IAAI,CAACD,cAAc,GAAG;MACnBG,QAAQ,EAAE,EAAE;MACXC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAE,EAAE;MACVC,aAAa,EAAE;KACnB;IACD,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE;IACzB,IAAI,CAACC,MAAM,GAAG;MACbC,WAAW,EAAE;KACb;EACJ;EACAC,QAAQA,CAAA,GACR;EACAC,YAAYA,CAAA;IACT,IAAI,IAAI,CAACZ,cAAc,CAACG,QAAQ,IAAE,EAAE,IAAI,IAAI,CAACH,cAAc,CAACK,MAAM,IAAE,EAAE,IAChE,IAAI,CAACL,cAAc,CAACM,aAAa,IAAE,EAAE,IAAG,IAAI,CAACN,cAAc,CAACI,cAAc,IAAE,EAAE,EAAC;MACnF,IAAI,CAACX,MAAM,CAACoB,OAAO,CAAC,iCAAiC,CAAC;MACtD,OAAO,KAAK;IACf;IACA,IAAI,CAACJ,MAAM,CAACC,WAAW,GAAI,IAAI;IAC/B,IAAI,CAACf,eAAe,CACjBmB,oBAAoB,CAAC,IAAI,CAACd,cAAc,CAAC,CACzCe,SAAS,CAACC,QAAQ,IAAG;MACpB,IAAI,CAACP,MAAM,CAACC,WAAW,GAAI,KAAK;MAClC,IAAIM,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACxB,MAAM,CAACyB,IAAI,CAAC,mBAAmB,CAAC;QACrC,OAAO,KAAK;MACb;MACE,IAAI,CAACC,OAAO,GAAGH,QAAQ;MACvB,IAAI,CAACI,cAAc,GAAG,KAAK,CAACC,kBAAkB,CAAC,CAAC,CAAC;IACnD,CAAC,EAACC,GAAG,IAAE;MACN,IAAI,CAAC7B,MAAM,CAAC8B,KAAK,CAACD,GAAG,CAAC;MACtB,IAAI,CAACb,MAAM,CAACC,WAAW,GAAI,KAAK;IACjC,CAAC,CAAC;EACN;;;;;;;;;;;;;AApDWnB,+BAA+B,GAAAiC,UAAA,EAL3CvC,SAAS,CAAC;EACTwC,QAAQ,EAAE,+BAA+B;EACzCC,QAAA,EAAAC,oBAAyD;;CAE1D,CAAC,C,EACWpC,+BAA+B,CAsD3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}