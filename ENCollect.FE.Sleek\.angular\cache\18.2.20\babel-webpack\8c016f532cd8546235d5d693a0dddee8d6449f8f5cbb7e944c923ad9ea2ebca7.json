{"ast": null, "code": "import { async, TestBed } from '@angular/core/testing';\nimport { PaymentReportComponent } from './payment-report.component';\ndescribe('PaymentReportComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(async(() => {\n    TestBed.configureTestingModule({\n      declarations: [PaymentReportComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(PaymentReportComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should be created', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["async", "TestBed", "PaymentReportComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\payments\\v1\\payment-report\\payment-report.component.spec.ts"], "sourcesContent": ["import { async, ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { PaymentReportComponent } from './payment-report.component';\r\n\r\ndescribe('PaymentReportComponent', () => {\r\n  let component: PaymentReportComponent;\r\n  let fixture: ComponentFixture<PaymentReportComponent>;\r\n\r\n  beforeEach(async(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ PaymentReportComponent ]\r\n    })\r\n    .compileComponents();\r\n  }));\r\n\r\n  beforeEach(() => {\r\n    fixture = TestBed.createComponent(PaymentReportComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should be created', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAASA,KAAK,EAAoBC,OAAO,QAAQ,uBAAuB;AAExE,SAASC,sBAAsB,QAAQ,4BAA4B;AAEnEC,QAAQ,CAAC,wBAAwB,EAAE,MAAK;EACtC,IAAIC,SAAiC;EACrC,IAAIC,OAAiD;EAErDC,UAAU,CAACN,KAAK,CAAC,MAAK;IACpBC,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAAEN,sBAAsB;KACvC,CAAC,CACDO,iBAAiB,EAAE;EACtB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGJ,OAAO,CAACS,eAAe,CAACR,sBAAsB,CAAC;IACzDE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}