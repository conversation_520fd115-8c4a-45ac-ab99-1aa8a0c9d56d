{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-legal.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-legal.component.css?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { legalService } from '../legal.service';\nimport { legalConfigService } from '../legalconfig.service';\nlet CreateLegalComponent = class CreateLegalComponent {\n  constructor(toastr, legalService, legalConfigService) {\n    this.toastr = toastr;\n    this.legalService = legalService;\n    this.legalConfigService = legalConfigService;\n    this.breadcrumbData = [{\n      label: \"legal\",\n      path: \"/encollect/legal/initiate-legal-request\"\n    }, {\n      label: \"Initiate Legal Case\",\n      path: \"/encollect/legal/initiate-legal-request\"\n    }];\n    this.legalId = \"\";\n  }\n  ngOnInit() {}\n  viewDetails(data) {\n    this.addLegalId = data.legalId;\n    // this.legalService.getLegalDetails(data).subscribe(response => {\n    // },err=>{\n    //    this.toastr.error(err)\n    // });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: legalService\n    }, {\n      type: legalConfigService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      ci: [{\n        type: ViewChild,\n        args: ['ci']\n      }],\n      arb: [{\n        type: ViewChild,\n        args: ['arb']\n      }],\n      ep: [{\n        type: ViewChild,\n        args: ['ep']\n      }],\n      cf: [{\n        type: ViewChild,\n        args: ['cf']\n      }],\n      lm: [{\n        type: ViewChild,\n        args: ['lm']\n      }]\n    };\n  }\n};\nCreateLegalComponent = __decorate([Component({\n  selector: 'app-create-legal',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateLegalComponent);\nexport { CreateLegalComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "ToastrService", "legalService", "legalConfigService", "CreateLegalComponent", "constructor", "toastr", "breadcrumbData", "label", "path", "legalId", "ngOnInit", "viewDetails", "data", "addLegalId", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\create-legal\\create-legal.component.ts"], "sourcesContent": ["import { Component, OnInit,ViewChild  } from '@angular/core';\r\nimport { CaseInitiationComponent } from '../case-initiation/case-initiation.component';\r\nimport { ArbitrationComponent } from '../arbitration/arbitration.component';\r\nimport { ExecutionPetitionComponent } from '../execution-petition/execution-petition.component';\r\nimport { CaseFilingComponent } from '../case-filing/case-filing.component';\r\nimport { LegalMediaComponent } from '../legal-media/legal-media.component';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { legalService } from '../legal.service';\r\nimport { legalConfigService } from '../legalconfig.service';\r\n\r\n@Component({\r\n  selector: 'app-create-legal',\r\n  templateUrl: './create-legal.component.html',\r\n  styleUrls: ['./create-legal.component.css']\r\n})\r\nexport class CreateLegalComponent implements OnInit {\r\n  public breadcrumbData = [\r\n    { label: \"legal\", path: \"/encollect/legal/initiate-legal-request\" },\r\n    { label: \"Initiate Legal Case\", path: \"/encollect/legal/initiate-legal-request\" },\r\n  ];\r\n  @ViewChild('ci') ci: CaseInitiationComponent;\r\n  @ViewChild('arb') arb: ArbitrationComponent;\r\n  @ViewChild('ep') ep: ExecutionPetitionComponent;\r\n  @ViewChild('cf') cf: CaseFilingComponent;\r\n  @ViewChild('lm') lm: LegalMediaComponent;\r\n\r\n  legalId = \"\"\r\n  addLegalId:any;\r\n\r\n  constructor(public toastr: ToastrService,private legalService: legalService,\r\n    private legalConfigService: legalConfigService) { }\r\n\r\n  ngOnInit() {}\r\n\r\n  viewDetails(data){\r\n    this.addLegalId = data.legalId\r\n    // this.legalService.getLegalDetails(data).subscribe(response => {\r\n\r\n    // },err=>{\r\n    //    this.toastr.error(err)\r\n    // });\r\n}\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAASC,SAAS,QAAS,eAAe;AAM5D,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,wBAAwB;AAOpD,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAc/BC,YAAmBC,MAAqB,EAASJ,YAA0B,EACjEC,kBAAsC;IAD7B,KAAAG,MAAM,GAANA,MAAM;IAAwB,KAAAJ,YAAY,GAAZA,YAAY;IACnD,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAdrB,KAAAI,cAAc,GAAG,CACtB;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAyC,CAAE,EACnE;MAAED,KAAK,EAAE,qBAAqB;MAAEC,IAAI,EAAE;IAAyC,CAAE,CAClF;IAOD,KAAAC,OAAO,GAAG,EAAE;EAIwC;EAEpDC,QAAQA,CAAA,GAAI;EAEZC,WAAWA,CAACC,IAAI;IACd,IAAI,CAACC,UAAU,GAAGD,IAAI,CAACH,OAAO;IAC9B;IAEA;IACA;IACA;EACJ;;;;;;;;;;;;;cArBGV,SAAS;QAAAe,IAAA,GAAC,IAAI;MAAA;;cACdf,SAAS;QAAAe,IAAA,GAAC,KAAK;MAAA;;cACff,SAAS;QAAAe,IAAA,GAAC,IAAI;MAAA;;cACdf,SAAS;QAAAe,IAAA,GAAC,IAAI;MAAA;;cACdf,SAAS;QAAAe,IAAA,GAAC,IAAI;MAAA;;;;AATJX,oBAAoB,GAAAY,UAAA,EALhCjB,SAAS,CAAC;EACTkB,QAAQ,EAAE,kBAAkB;EAC5BC,QAAA,EAAAC,oBAA4C;;CAE7C,CAAC,C,EACWf,oBAAoB,CA4BhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}