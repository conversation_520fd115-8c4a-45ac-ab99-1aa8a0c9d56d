{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { ConfirmDialogComponent } from './confirm-dialog.component';\ndescribe('ConfirmDialogComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [ConfirmDialogComponent]\n    });\n    fixture = TestBed.createComponent(ConfirmDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "ConfirmDialogComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\confirm-dialog\\confirm-dialog.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { ConfirmDialogComponent } from './confirm-dialog.component';\r\n\r\ndescribe('ConfirmDialogComponent', () => {\r\n  let component: ConfirmDialogComponent;\r\n  let fixture: ComponentFixture<ConfirmDialogComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [ConfirmDialogComponent]\r\n    });\r\n    fixture = TestBed.createComponent(ConfirmDialogComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,sBAAsB,QAAQ,4BAA4B;AAEnEC,QAAQ,CAAC,wBAAwB,EAAE,MAAK;EACtC,IAAIC,SAAiC;EACrC,IAAIC,OAAiD;EAErDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,sBAAsB;KACtC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,sBAAsB,CAAC;IACzDE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}