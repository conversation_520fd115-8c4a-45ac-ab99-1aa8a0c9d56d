{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./arbitration.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./arbitration.component.css?ngResource\";\nimport { Component, Output, EventEmitter, Input } from '@angular/core';\nimport { ToastrService } from 'ngx-toastr';\nimport { legalService } from '../legal.service';\nimport { legalConfigService } from '../legalconfig.service';\nlet ArbitrationComponent = class ArbitrationComponent {\n  constructor(toastr, legalService, legalConfigService) {\n    this.toastr = toastr;\n    this.legalService = legalService;\n    this.legalConfigService = legalConfigService;\n    this.viewDetails = new EventEmitter();\n    this.arbitration = {};\n    this.workflowStatusList = [];\n    this.loader = {\n      isSearching: false,\n      statusSearch: false\n    };\n    this.lastDate = new Date();\n    this.lastDate.setDate(this.lastDate.getDate() - 1);\n    this.nextDate = new Date();\n    this.nextDate.setDate(this.nextDate.getDate() + 1);\n  }\n  ngOnInit() {\n    // this.getWorkflowStatus();\n    this.setArbitrationParams();\n  }\n  setArbitrationParams() {\n    this.arbitration = {\n      \"legalManager\": \"\",\n      \"advocate\": \"\",\n      \"advocateMobile\": \"\",\n      \"advocateEmailId\": \"\",\n      \"caseNo\": \"\",\n      \"lastDateOfHearing\": \"\",\n      \"nextDateOfHearing\": \"\",\n      // \"caseStatus\":\"\",\n      \"awardDate\": \"\",\n      \"initiationRemarks\": \"\"\n    };\n  }\n  //   getWorkflowStatus(){\n  //     this.loader.statusSearch = true\n  //     this.legalService.getworkflowStatusList().subscribe(response => { \n  //       this.loader.statusSearch = false\n  //       this.workflowStatusList= response\n  //     },err=>{\n  //         this.toastr.error(err)\n  //         this.loader.statusSearch = false\n  //      });\n  //  }\n  addUpdateArbitration() {\n    if (this.legalId) {\n      this.arbitration[\"legalId\"] = this.custId; // custom id will be here\n      this.loader.isSearching = true;\n      this.legalService.addUpdateArbitration(this.arbitration).subscribe(response => {\n        this.toastr.success(\"Arbitration successfully\");\n        this.loader.isSearching = false;\n        // this.setArbitrationParams()\n        this.viewDetails.emit({\n          id: this.legalId\n        });\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSearching = false;\n      });\n    } else {\n      this.arbitration[\"legalId\"] = this.addLegalId;\n      this.loader.isSearching = true;\n      this.legalService.addUpdateArbitration(this.arbitration).subscribe(response => {\n        this.toastr.success(\"Arbitration successfully\");\n        this.loader.isSearching = false;\n        // this.setArbitrationParams()\n        this.viewDetails.emit({\n          \"legalId\": response\n        });\n      }, err => {\n        this.toastr.error(err);\n        this.loader.isSearching = false;\n      });\n    }\n  }\n  sendData(data, custId) {\n    this.custId = custId;\n    // this.getWorkflowStatus()\n    this.legalId = data.id;\n    this.arbitration = data;\n    this.arbitration[\"advocateMobile\"] = data.advocateContactNumber;\n    this.arbitration[\"initiationRemarks\"] = data.remarks;\n    if (data.lastDateOfHearing) {\n      this.arbitration[\"lastDateOfHearing\"] = new Date(data.lastDateOfHearing);\n    }\n    if (data.nextDateOfHearing) {\n      this.arbitration[\"nextDateOfHearing\"] = new Date(data.nextDateOfHearing);\n    }\n    if (data.awardDate) {\n      this.arbitration[\"awardDate\"] = new Date(data.awardDate);\n    }\n    // this.arbitration[\"caseStatus\"] = data.caseStatus\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ToastrService\n    }, {\n      type: legalService\n    }, {\n      type: legalConfigService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      legalId: [{\n        type: Input\n      }],\n      addLegalId: [{\n        type: Input\n      }],\n      viewDetails: [{\n        type: Output,\n        args: [\"viewDetails\"]\n      }]\n    };\n  }\n};\nArbitrationComponent = __decorate([Component({\n  selector: 'legal-arbitration',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ArbitrationComponent);\nexport { ArbitrationComponent };", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "Input", "ToastrService", "legalService", "legalConfigService", "ArbitrationComponent", "constructor", "toastr", "viewDetails", "arbitration", "workflowStatusList", "loader", "isSearching", "statusSearch", "lastDate", "Date", "setDate", "getDate", "nextDate", "ngOnInit", "setArbitrationParams", "addUpdateArbitration", "legalId", "custId", "subscribe", "response", "success", "emit", "id", "err", "error", "addLegalId", "sendData", "data", "advocateContactNumber", "remarks", "lastDateOfHearing", "nextDateOfHearing", "awardDate", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\legal\\arbitration\\arbitration.component.ts"], "sourcesContent": ["import { Component, OnInit, Output,EventEmitter,Input } from '@angular/core';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { legalService } from '../legal.service';\r\nimport { legalConfigService } from '../legalconfig.service';\r\nexport interface Loader{\r\n  isSearching: boolean;\r\n  statusSearch: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'legal-arbitration',\r\n  templateUrl: './arbitration.component.html',\r\n  styleUrls: ['./arbitration.component.css']\r\n})\r\nexport class ArbitrationComponent implements OnInit {\r\n  @Input() legalId: any;\r\n  @Input() addLegalId: any;\r\n  @Output(\"viewDetails\") viewDetails: EventEmitter<any> = new EventEmitter();\r\n  loader: Loader;\r\n  arbitration : any = {}\r\n  workflowStatusList = [];\r\n  custId:any;\r\n  lastDate: any;\r\n  nextDate: any;\r\n\r\n  constructor(public toastr: ToastrService,private legalService: legalService,\r\n    private legalConfigService: legalConfigService) { \r\n    this.loader = {\r\n      isSearching: false,\r\n      statusSearch: false\r\n    }\r\n    this.lastDate =new Date();\r\n     this.lastDate.setDate(this.lastDate.getDate()-1)\r\n     this.nextDate =new Date();\r\n     this.nextDate.setDate(this.nextDate.getDate()+1)\r\n    }\r\n\r\n  ngOnInit() {\r\n    // this.getWorkflowStatus();\r\n    this.setArbitrationParams(); \r\n  }\r\n\r\n  setArbitrationParams(){\r\n    this.arbitration={\r\n      \"legalManager\":\"\",\r\n      \"advocate\":\"\",\r\n      \"advocateMobile\":\"\",\r\n      \"advocateEmailId\":\"\",\r\n      \"caseNo\":\"\",\r\n      \"lastDateOfHearing\":\"\",\r\n      \"nextDateOfHearing\":\"\",\r\n      // \"caseStatus\":\"\",\r\n      \"awardDate\":\"\",\r\n      \"initiationRemarks\":\"\"\r\n    }\r\n  }\r\n\r\n//   getWorkflowStatus(){\r\n//     this.loader.statusSearch = true\r\n//     this.legalService.getworkflowStatusList().subscribe(response => { \r\n//       this.loader.statusSearch = false\r\n//       this.workflowStatusList= response\r\n//     },err=>{\r\n//         this.toastr.error(err)\r\n//         this.loader.statusSearch = false\r\n//      });\r\n//  }\r\n\r\n  addUpdateArbitration(){\r\n    if(this.legalId){\r\n      this.arbitration[\"legalId\"] = this.custId // custom id will be here\r\n      this.loader.isSearching = true\r\n      this.legalService.addUpdateArbitration(this.arbitration).subscribe(response => { \r\n        this.toastr.success(\"Arbitration successfully\")\r\n        this.loader.isSearching = false\r\n        // this.setArbitrationParams()\r\n        this.viewDetails.emit({id: this.legalId})\r\n     },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching = false\r\n     });\r\n\r\n    }else{\r\n      this.arbitration[\"legalId\"] = this.addLegalId\r\n      this.loader.isSearching = true\r\n      this.legalService.addUpdateArbitration(this.arbitration).subscribe(response => { \r\n        this.toastr.success(\"Arbitration successfully\")\r\n        this.loader.isSearching = false\r\n        // this.setArbitrationParams()\r\n        this.viewDetails.emit({\"legalId\": response})\r\n     },err=>{\r\n        this.toastr.error(err)\r\n        this.loader.isSearching = false\r\n     });\r\n    }\r\n      \r\n  }\r\n\r\n  sendData(data,custId){\r\n    \r\n    this.custId = custId\r\n    // this.getWorkflowStatus()\r\n    this.legalId=data.id\r\n    this.arbitration=data\r\n    this.arbitration[\"advocateMobile\"] = data.advocateContactNumber\r\n    this.arbitration[\"initiationRemarks\"] = data.remarks\r\n\r\n    if(data.lastDateOfHearing){\r\n    this.arbitration[\"lastDateOfHearing\"] = new Date(data.lastDateOfHearing) \r\n    }\r\n    if(data.nextDateOfHearing){\r\n    this.arbitration[\"nextDateOfHearing\"] = new Date(data.nextDateOfHearing)\r\n    }\r\n    if(data.awardDate){\r\n    this.arbitration[\"awardDate\"] = new Date(data.awardDate)\r\n    }\r\n    // this.arbitration[\"caseStatus\"] = data.caseStatus\r\n\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAUC,MAAM,EAACC,YAAY,EAACC,KAAK,QAAQ,eAAe;AAC5E,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,wBAAwB;AAWpD,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAW/BC,YAAmBC,MAAqB,EAASJ,YAA0B,EACjEC,kBAAsC;IAD7B,KAAAG,MAAM,GAANA,MAAM;IAAwB,KAAAJ,YAAY,GAAZA,YAAY;IACnD,KAAAC,kBAAkB,GAAlBA,kBAAkB;IATL,KAAAI,WAAW,GAAsB,IAAIR,YAAY,EAAE;IAE1E,KAAAS,WAAW,GAAS,EAAE;IACtB,KAAAC,kBAAkB,GAAG,EAAE;IAOrB,IAAI,CAACC,MAAM,GAAG;MACZC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;KACf;IACD,IAAI,CAACC,QAAQ,GAAE,IAAIC,IAAI,EAAE;IACxB,IAAI,CAACD,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACF,QAAQ,CAACG,OAAO,EAAE,GAAC,CAAC,CAAC;IAChD,IAAI,CAACC,QAAQ,GAAE,IAAIH,IAAI,EAAE;IACzB,IAAI,CAACG,QAAQ,CAACF,OAAO,CAAC,IAAI,CAACE,QAAQ,CAACD,OAAO,EAAE,GAAC,CAAC,CAAC;EACjD;EAEFE,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACX,WAAW,GAAC;MACf,cAAc,EAAC,EAAE;MACjB,UAAU,EAAC,EAAE;MACb,gBAAgB,EAAC,EAAE;MACnB,iBAAiB,EAAC,EAAE;MACpB,QAAQ,EAAC,EAAE;MACX,mBAAmB,EAAC,EAAE;MACtB,mBAAmB,EAAC,EAAE;MACtB;MACA,WAAW,EAAC,EAAE;MACd,mBAAmB,EAAC;KACrB;EACH;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEEY,oBAAoBA,CAAA;IAClB,IAAG,IAAI,CAACC,OAAO,EAAC;MACd,IAAI,CAACb,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAACc,MAAM,EAAC;MAC1C,IAAI,CAACZ,MAAM,CAACC,WAAW,GAAG,IAAI;MAC9B,IAAI,CAACT,YAAY,CAACkB,oBAAoB,CAAC,IAAI,CAACZ,WAAW,CAAC,CAACe,SAAS,CAACC,QAAQ,IAAG;QAC5E,IAAI,CAAClB,MAAM,CAACmB,OAAO,CAAC,0BAA0B,CAAC;QAC/C,IAAI,CAACf,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B;QACA,IAAI,CAACJ,WAAW,CAACmB,IAAI,CAAC;UAACC,EAAE,EAAE,IAAI,CAACN;QAAO,CAAC,CAAC;MAC5C,CAAC,EAACO,GAAG,IAAE;QACJ,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAAClB,MAAM,CAACC,WAAW,GAAG,KAAK;MAClC,CAAC,CAAC;IAEH,CAAC,MAAI;MACH,IAAI,CAACH,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAACsB,UAAU;MAC7C,IAAI,CAACpB,MAAM,CAACC,WAAW,GAAG,IAAI;MAC9B,IAAI,CAACT,YAAY,CAACkB,oBAAoB,CAAC,IAAI,CAACZ,WAAW,CAAC,CAACe,SAAS,CAACC,QAAQ,IAAG;QAC5E,IAAI,CAAClB,MAAM,CAACmB,OAAO,CAAC,0BAA0B,CAAC;QAC/C,IAAI,CAACf,MAAM,CAACC,WAAW,GAAG,KAAK;QAC/B;QACA,IAAI,CAACJ,WAAW,CAACmB,IAAI,CAAC;UAAC,SAAS,EAAEF;QAAQ,CAAC,CAAC;MAC/C,CAAC,EAACI,GAAG,IAAE;QACJ,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACD,GAAG,CAAC;QACtB,IAAI,CAAClB,MAAM,CAACC,WAAW,GAAG,KAAK;MAClC,CAAC,CAAC;IACH;EAEF;EAEAoB,QAAQA,CAACC,IAAI,EAACV,MAAM;IAElB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB;IACA,IAAI,CAACD,OAAO,GAACW,IAAI,CAACL,EAAE;IACpB,IAAI,CAACnB,WAAW,GAACwB,IAAI;IACrB,IAAI,CAACxB,WAAW,CAAC,gBAAgB,CAAC,GAAGwB,IAAI,CAACC,qBAAqB;IAC/D,IAAI,CAACzB,WAAW,CAAC,mBAAmB,CAAC,GAAGwB,IAAI,CAACE,OAAO;IAEpD,IAAGF,IAAI,CAACG,iBAAiB,EAAC;MAC1B,IAAI,CAAC3B,WAAW,CAAC,mBAAmB,CAAC,GAAG,IAAIM,IAAI,CAACkB,IAAI,CAACG,iBAAiB,CAAC;IACxE;IACA,IAAGH,IAAI,CAACI,iBAAiB,EAAC;MAC1B,IAAI,CAAC5B,WAAW,CAAC,mBAAmB,CAAC,GAAG,IAAIM,IAAI,CAACkB,IAAI,CAACI,iBAAiB,CAAC;IACxE;IACA,IAAGJ,IAAI,CAACK,SAAS,EAAC;MAClB,IAAI,CAAC7B,WAAW,CAAC,WAAW,CAAC,GAAG,IAAIM,IAAI,CAACkB,IAAI,CAACK,SAAS,CAAC;IACxD;IACA;EAEF;;;;;;;;;;;;;cAvGCrC;MAAK;;cACLA;MAAK;;cACLF,MAAM;QAAAwC,IAAA,GAAC,aAAa;MAAA;;;;AAHVlC,oBAAoB,GAAAmC,UAAA,EALhC1C,SAAS,CAAC;EACT2C,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA2C;;CAE5C,CAAC,C,EACWtC,oBAAoB,CA0GhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}