{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from \"@angular/core\";\nimport { ApiService } from \"../shared/services/api.service\";\nlet ClicktoCureService = class ClicktoCureService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  getAccountDetails(data) {\n    return this.apiService.post(\"api/mvp/cure/account/search\", data);\n  }\n  saveCure(data) {\n    return this.apiService.post(\"api/mvp/cure/add\", data);\n  }\n  getworkflowStatusList() {\n    return this.apiService.get(\"api/mvp/cure/getAllWorkFlowStatus\");\n  }\n  searchCure(data) {\n    return this.apiService.post(\"api/mvp/mycure/search\", data);\n  }\n  searchCureQueue(data) {\n    return this.apiService.post(\"api/mvp/myqueue/cure/search\", data);\n  }\n  getCureDetails(data) {\n    return this.apiService.get(\"api/mvp/mycure/getCureDetails/\" + data);\n  }\n  getAllDespositionGroups() {\n    return this.apiService.get(\"api/mvp/dispositiongroupmaster\");\n  }\n  dispostionCode(val) {\n    return this.apiService.post(\"api/mvp/dispositionCodemaster\", val);\n  }\n  postTrailDetails(data) {\n    return this.apiService.post(\"api/mvp/cure/addfeedback\", data);\n  }\n  getTrailHistory(data) {\n    return this.apiService.post(\"api/mvp/cure/getallfeedback\", data);\n  }\n  uploadFile(formData) {\n    const options = {};\n    return this.apiService.post(\"api/document/cure/upload\", formData, options);\n  }\n  uploadFileCure(formData) {\n    const options = {};\n    return this.apiService.post(\"api/UploadFile\", formData, options);\n  }\n  filePreview(data) {\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\n  }\n  filePreviewDoc(data) {\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\n  }\n  updateDocuments(data) {\n    return this.apiService.post(\"api/uploaddocument/Save\", data);\n  }\n  getWorkflowStatus(data) {\n    return this.apiService.post(\"api/mvp/cure/getNextWorkFlowStatus\", data);\n  }\n  updateWorkflowStatus(data) {\n    return this.apiService.post(\"api/mvp/cure/UpdateWorkFlowStatus\", data);\n  }\n  getDocumentList(data) {\n    return this.apiService.get(\"api/mvp/CureDocuments/\" + data);\n  }\n  sendDocument(data) {\n    return this.apiService.post(\"api/mvp/cure/senddocumentemail\", data);\n  }\n  sendEmail(data) {\n    return this.apiService.post(\"api/mvp/cure/sendemail\", data);\n  }\n  getcureQueue(data) {\n    return this.apiService.post(\"api/mvp/queuetab/cure\", data);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ApiService\n    }];\n  }\n};\nClicktoCureService = __decorate([Injectable({\n  providedIn: 'root'\n})], ClicktoCureService);\nexport { ClicktoCureService };", "map": {"version": 3, "names": ["Injectable", "ApiService", "ClicktoCureService", "constructor", "apiService", "getAccountDetails", "data", "post", "saveCure", "getworkflowStatusList", "get", "searchCure", "searchCureQueue", "getCureDetails", "getAllDespositionGroups", "dispostionCode", "val", "postTrailDetails", "getTrailHistory", "uploadFile", "formData", "options", "uploadFileCure", "filePreview", "getRawImage", "filePreviewDoc", "updateDocuments", "getWorkflowStatus", "updateWorkflowStatus", "getDocumentList", "sendDocument", "sendEmail", "getcureQueue", "__decorate", "providedIn"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\click-to-cure\\clicktocure.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ApiService } from \"../shared/services/api.service\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClicktoCureService {\r\n  constructor(private apiService: ApiService) {}\r\n\r\n  getAccountDetails(data) {\r\n    return this.apiService.post(\"api/mvp/cure/account/search\", data);\r\n  }\r\n\r\n  saveCure(data) {\r\n    return this.apiService.post(\"api/mvp/cure/add\", data);\r\n  }\r\n\r\n  getworkflowStatusList() {\r\n    return this.apiService.get(\"api/mvp/cure/getAllWorkFlowStatus\");\r\n  }\r\n\r\n  searchCure(data) {\r\n    return this.apiService.post(\"api/mvp/mycure/search\", data);\r\n  }\r\n\r\n  searchCureQueue(data) {\r\n    return this.apiService.post(\"api/mvp/myqueue/cure/search\", data);\r\n  }\r\n\r\n  getCureDetails(data) {\r\n    return this.apiService.get(\"api/mvp/mycure/getCureDetails/\" + data);\r\n  }\r\n\r\n  getAllDespositionGroups() {\r\n    return this.apiService.get(\"api/mvp/dispositiongroupmaster\");\r\n  }\r\n\r\n  dispostionCode(val) {\r\n    return this.apiService.post(\"api/mvp/dispositionCodemaster\", val);\r\n  }\r\n\r\n  postTrailDetails(data) {\r\n    return this.apiService.post(\"api/mvp/cure/addfeedback\", data);\r\n  }\r\n\r\n  getTrailHistory(data) {\r\n    return this.apiService.post(\"api/mvp/cure/getallfeedback\", data);\r\n  }\r\n\r\n  uploadFile(formData: FormData) {\r\n    const options = {};\r\n    return this.apiService.post(\"api/document/cure/upload\", formData, options);\r\n  }\r\n\r\n  uploadFileCure(formData: FormData) {\r\n    const options = {};\r\n    return this.apiService.post(\"api/UploadFile\", formData, options);\r\n  }\r\n\r\n  filePreview(data) {\r\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\r\n  }\r\n\r\n  filePreviewDoc(data) {\r\n    return this.apiService.getRawImage(\"api/mvp/getimage?filename=\" + data);\r\n  }\r\n\r\n  updateDocuments(data) {\r\n    return this.apiService.post(\"api/uploaddocument/Save\", data);\r\n  }\r\n\r\n  getWorkflowStatus(data) {\r\n    return this.apiService.post(\"api/mvp/cure/getNextWorkFlowStatus\", data);\r\n  }\r\n\r\n  updateWorkflowStatus(data) {\r\n    return this.apiService.post(\"api/mvp/cure/UpdateWorkFlowStatus\", data);\r\n  }\r\n\r\n  getDocumentList(data) {\r\n    return this.apiService.get(\"api/mvp/CureDocuments/\" + data);\r\n  }\r\n\r\n  sendDocument(data) {\r\n    return this.apiService.post(\"api/mvp/cure/senddocumentemail\", data);\r\n  }\r\n\r\n  sendEmail(data) {\r\n    return this.apiService.post(\"api/mvp/cure/sendemail\", data);\r\n  }\r\n\r\n  getcureQueue(data) {\r\n    return this.apiService.post(\"api/mvp/queuetab/cure\", data);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,gCAAgC;AAKpD,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAC7BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAe;EAE7CC,iBAAiBA,CAACC,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,6BAA6B,EAAED,IAAI,CAAC;EAClE;EAEAE,QAAQA,CAACF,IAAI;IACX,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,kBAAkB,EAAED,IAAI,CAAC;EACvD;EAEAG,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACL,UAAU,CAACM,GAAG,CAAC,mCAAmC,CAAC;EACjE;EAEAC,UAAUA,CAACL,IAAI;IACb,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,uBAAuB,EAAED,IAAI,CAAC;EAC5D;EAEAM,eAAeA,CAACN,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,6BAA6B,EAAED,IAAI,CAAC;EAClE;EAEAO,cAAcA,CAACP,IAAI;IACjB,OAAO,IAAI,CAACF,UAAU,CAACM,GAAG,CAAC,gCAAgC,GAAGJ,IAAI,CAAC;EACrE;EAEAQ,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACV,UAAU,CAACM,GAAG,CAAC,gCAAgC,CAAC;EAC9D;EAEAK,cAAcA,CAACC,GAAG;IAChB,OAAO,IAAI,CAACZ,UAAU,CAACG,IAAI,CAAC,+BAA+B,EAAES,GAAG,CAAC;EACnE;EAEAC,gBAAgBA,CAACX,IAAI;IACnB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,0BAA0B,EAAED,IAAI,CAAC;EAC/D;EAEAY,eAAeA,CAACZ,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,6BAA6B,EAAED,IAAI,CAAC;EAClE;EAEAa,UAAUA,CAACC,QAAkB;IAC3B,MAAMC,OAAO,GAAG,EAAE;IAClB,OAAO,IAAI,CAACjB,UAAU,CAACG,IAAI,CAAC,0BAA0B,EAAEa,QAAQ,EAAEC,OAAO,CAAC;EAC5E;EAEAC,cAAcA,CAACF,QAAkB;IAC/B,MAAMC,OAAO,GAAG,EAAE;IAClB,OAAO,IAAI,CAACjB,UAAU,CAACG,IAAI,CAAC,gBAAgB,EAAEa,QAAQ,EAAEC,OAAO,CAAC;EAClE;EAEAE,WAAWA,CAACjB,IAAI;IACd,OAAO,IAAI,CAACF,UAAU,CAACoB,WAAW,CAAC,4BAA4B,GAAGlB,IAAI,CAAC;EACzE;EAEAmB,cAAcA,CAACnB,IAAI;IACjB,OAAO,IAAI,CAACF,UAAU,CAACoB,WAAW,CAAC,4BAA4B,GAAGlB,IAAI,CAAC;EACzE;EAEAoB,eAAeA,CAACpB,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,yBAAyB,EAAED,IAAI,CAAC;EAC9D;EAEAqB,iBAAiBA,CAACrB,IAAI;IACpB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,oCAAoC,EAAED,IAAI,CAAC;EACzE;EAEAsB,oBAAoBA,CAACtB,IAAI;IACvB,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,mCAAmC,EAAED,IAAI,CAAC;EACxE;EAEAuB,eAAeA,CAACvB,IAAI;IAClB,OAAO,IAAI,CAACF,UAAU,CAACM,GAAG,CAAC,wBAAwB,GAAGJ,IAAI,CAAC;EAC7D;EAEAwB,YAAYA,CAACxB,IAAI;IACf,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,gCAAgC,EAAED,IAAI,CAAC;EACrE;EAEAyB,SAASA,CAACzB,IAAI;IACZ,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,wBAAwB,EAAED,IAAI,CAAC;EAC7D;EAEA0B,YAAYA,CAAC1B,IAAI;IACf,OAAO,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,uBAAuB,EAAED,IAAI,CAAC;EAC5D;;;;;;;AAvFWJ,kBAAkB,GAAA+B,UAAA,EAH9BjC,UAAU,CAAC;EACVkC,UAAU,EAAE;CACb,CAAC,C,EACWhC,kBAAkB,CAwF9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}