{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { FullDegreeViewComponent } from './full-degree-view.component';\ndescribe('FullDegreeViewComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      declarations: [FullDegreeViewComponent]\n    });\n    fixture = TestBed.createComponent(FullDegreeViewComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "FullDegreeViewComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["D:\\ProjectNewrepo\\Communctionscb\\ENCollect.FE.Sleek\\src\\app\\dashboard\\v1\\common-tab\\full-degree-view\\full-degree-view.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { FullDegreeViewComponent } from './full-degree-view.component';\r\n\r\ndescribe('FullDegreeViewComponent', () => {\r\n  let component: FullDegreeViewComponent;\r\n  let fixture: ComponentFixture<FullDegreeViewComponent>;\r\n\r\n  beforeEach(() => {\r\n    TestBed.configureTestingModule({\r\n      declarations: [FullDegreeViewComponent]\r\n    });\r\n    fixture = TestBed.createComponent(FullDegreeViewComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtEC,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,SAAkC;EACtC,IAAIC,OAAkD;EAEtDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,uBAAuB;KACvC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,uBAAuB,CAAC;IAC1DE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}